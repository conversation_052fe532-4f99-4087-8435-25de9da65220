Shader "Custom/MacroVariationWater"
{
	Properties
	{
		_Albedo("Albedo", 2D) = "white" {}
		_Color("Color", Color) = (1,1,1,1)
		_Tiling("Tiling", Float) = 0
		_SmoothnessMap("Metallic Map", 2D) = "white" {}
		_Metallic("Metallic", Range(0 , 1)) = 0
		_Smoothness("Smoothness", Range(0 , 1)) = 0
		_VariationTexture("Variation Texture", 2D) = "white" {}
		_VariationTiling("Variation Tiling", Float) = 1
		_VariationStrength("Variation Strength", Range(0 , 1)) = 0.5
		[HideInInspector] _texcoord("", 2D) = "white" {}
		[HideInInspector] __dirty("", Int) = 1
	}

		SubShader
		{
		Tags {"Queue" = "Transparent+1000" "IgnoreProjector"="True" "RenderType" = "Transparent" }
		Blend SrcAlpha
		OneMinusSrcAlpha
        ZWrite Off
        LOD 200

		CGPROGRAM
		
        #pragma surface surf Standard fullforwardshadows alpha

   		#pragma target 3.0

			struct Input
			{
				float2 uv_texcoord;
			};

			//uniform sampler2D _NormalMap;
			uniform float _Tiling;
			uniform sampler2D _Albedo;
			uniform float4 _Color;
			uniform sampler2D _VariationTexture;
			uniform float _VariationTiling;
			uniform float _VariationStrength;
			uniform sampler2D _SmoothnessMap;
			uniform float _Metallic;
			uniform float _Smoothness;
			uniform sampler2D _OcclusionMap;
			uniform float _Occlusion;

			void surf(Input i , inout SurfaceOutputStandard o)
			{
				//float2 temp_output_4_0_g7 = (i.uv_texcoord * _Tiling);
				////o.Normal = UnpackNormal(tex2D(_NormalMap, temp_output_4_0_g7));
				//float4 temp_output_28_0_g9 = (tex2D(_Albedo, temp_output_4_0_g7) * _Color);
				//float2 temp_cast_0 = (0.0).xx;
				//float2 uv_TexCoord5_g9 = i.uv_texcoord + temp_cast_0;
				//float3 _UVScale = float3(6,4,2);
				//float2 temp_cast_1 = (0.0).xx;
				//float2 uv_TexCoord7_g9 = i.uv_texcoord + temp_cast_1;
				//float2 temp_cast_2 = (0.0).xx;
				//float2 uv_TexCoord6_g9 = i.uv_texcoord + temp_cast_2;
				//float4 lerpResult23_g9 = lerp(temp_output_28_0_g9 , (temp_output_28_0_g9 * ((tex2D(_VariationTexture, (uv_TexCoord5_g9 * _UVScale.x * _VariationTiling)).r + 0.5) * ((tex2D(_VariationTexture, (uv_TexCoord7_g9 * _UVScale.y * _VariationTiling)).r + 0.5) * (tex2D(_VariationTexture, (uv_TexCoord6_g9 * _UVScale.z * _VariationTiling)).r + 0.5)))) , _VariationStrength);
				//o.Albedo = lerpResult23_g9.rgb;
				//float4 tex2DNode2_g7 = tex2D(_MetallicMap, temp_output_4_0_g7);
				//o.Metallic = (tex2DNode2_g7.r * _Metallic);
				//o.Smoothness = (tex2DNode2_g7.a * _Smoothness);
				////float lerpResult13_g7 = lerp(tex2D(_OcclusionMap, temp_output_4_0_g7).g , 1.0 , (1.0 - _Occlusion));
				////o.Occlusion = lerpResult13_g7;
				//o.Alpha = _Color.a;


				float2 Move1 = (i.uv_texcoord * _Tiling);
				float4 Move2 = (tex2D(_Albedo, Move1) * _Color);
				float2 Move3 = (0.0).xx;
				float2 TexCoords = i.uv_texcoord + Move3;
				float3 _UVScale = float3(6, 4, 2);
				float2 temp_cast_1 = (0.0).xx;
				float2 Scale_2 = i.uv_texcoord + temp_cast_1;
				float2 temp_cast_2 = (0.0).xx;
				float2 Scale_3 = i.uv_texcoord + temp_cast_2;
				
				float4 ResultWithVariation = lerp(Move2, (Move2 * ((tex2D(_VariationTexture, (TexCoords * _UVScale.x * _VariationTiling)).r + 0.5) * ((tex2D(_VariationTexture, (Scale_2 * _UVScale.y * _VariationTiling)).r + 0.5) * (tex2D(_VariationTexture, (Scale_3 * _UVScale.z * _VariationTiling)).r + 0.5)))), _VariationStrength);
	
				float4 TexCoordsSM = tex2D(_SmoothnessMap, Move1);
				o.Metallic = (TexCoordsSM.r * _Metallic);
				o.Smoothness = (TexCoordsSM.a * _Smoothness);
				o.Albedo = ResultWithVariation.rgb;
				o.Alpha = _Color.a;
			}

			ENDCG
		}
			Fallback "Diffuse"
				//CustomEditor "ASEMaterialInspector"
}
