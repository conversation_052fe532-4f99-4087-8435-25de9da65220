// Author: <PERSON>, email: <EMAIL>
// Created: 2021/02/05

#if UNITY_EDITOR

using System;
using System.IO;

namespace Plugins.KrvDarkArt.UltraFastReferenceFinder.Editor.Utils
{
    public static class FileUtils
    {
        public static string GetParentPath(string path, int steps = 1)
        {
            bool IsSeparator(char ch)
            {
                return ch == Path.DirectorySeparatorChar || ch == Path.AltDirectorySeparatorChar;
            }

            return GetParentPath(path, steps, IsSeparator);
        }

        private static string GetParentPath(string path, int steps, Func<char, bool> isSeparator)
        {
            var length = path.Length;

            for (var i = 0; i < steps; i++)
            {
                var last = true;

                for (var j = length - 1; j >= 0; j--)
                {
                    if (isSeparator(path[j]))
                    {
                        if (!last)
                        {
                            length = j;
                            break;
                        }

                        continue;
                    }

                    last = false;
                }
            }

            return path.Length == length ? path : path.Substring(0, length);
        }
    }
}

#endif
