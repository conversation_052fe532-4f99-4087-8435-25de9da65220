using System.Collections.Generic;

namespace Core.Haptic
{
    public class Haptics
    {
        public static Haptics Instance { get; } = new();

        private Haptics()
        {
        }

        // (!) do not forget add here new ones to allow debug/polishing via debug screen
        public IEnumerable<string> Enumerate()
        {
            yield return TimerCountdown;
            yield return AllCampfiresLit;
            yield return IndicatorAppeared;
            yield return TrapTriggered;
            yield return CampfireLitParticipant;
            yield return CampfireLitOthers;
            yield return EscapeeCaged;
            yield return CageRelease;
            yield return GateInteractionPulse;
            yield return GatesFullyOpened;
            yield return MiniGameFailure;
            yield return MiniGamePerfect;
            yield return CatcherHitEscapee;
            yield return EscapeeKnockdown;
            yield return EscapeeStunnedCatcher;
            yield return CatcherStunned;
            yield return Healing;
            yield return ChestOpened;
            yield return Victory;
        }

        #region Patterns

        public string TimerCountdown { get; } = "timer-countdown";
        public string AllCampfiresLit { get; } = "all-campfires-lit";
        public string IndicatorAppeared { get; } = "indicator-appeared";
        public string TrapTriggered { get; } = "trap-triggered";
        public string CampfireLitParticipant { get; } = "campfire-lit-participant";
        public string CampfireLitOthers { get; } = "campfire-lit-others";
        public string EscapeeCaged { get; } = "escapee-caged";
        public string CageRelease { get; } = "cage-release";
        public string GateInteractionPulse { get; } = "gate-interaction-pulse";
        public string GatesFullyOpened { get; } = "gates-fully-opened";
        public string MiniGameFailure { get; } = "mini-game-failure";
        public string MiniGamePerfect { get; } = "mini-game-perfect";
        public string CatcherHitEscapee { get; } = "catcher-hit-escapee";
        public string EscapeeKnockdown { get; } = "escapee-knockdown";
        public string EscapeeStunnedCatcher { get; } = "escapee-stunned-catcher";
        public string CatcherStunned { get; } = "catcher-stunned";
        public string Healing { get; } = "healing";
        public string ChestOpened { get; } = "chest-opened";
        public string Victory { get; } = "victory";

        #endregion
    }
}
