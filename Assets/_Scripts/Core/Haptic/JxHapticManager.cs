using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Audio.Debug;
using Configs.Game;
using Configs.Haptic;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using Jx.Utils.Collections;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using Lofelt.NiceVibrations;
using Savings;
using UnityEngine.Scripting;

namespace Core.Haptic
{
    [Preserve]
    public class JxHapticManager : IJxHapticManager
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxHapticManager));
        
        private readonly IGameConfigProvider _gameConfigProvider;
        private readonly ILocalSave _localSave;
        private readonly PatternProvider _patternProvider;
        private readonly AvailabilityController _availabilityController;

        private CancellationTokenSource? _lastCts;
        private IHapticConfig? _hapticConfig;
        
        private bool _initialized;

        [Preserve]
        public JxHapticManager(
            IGameConfigProvider gameConfigProvider,
            ILocalSave localSave)
        {
            _gameConfigProvider = gameConfigProvider;
            _localSave = localSave;
            
            _patternProvider = new PatternProvider(this);
            _availabilityController = new AvailabilityController(this);

            Instance = this;
        }
        
        public static IJxHapticManager Instance { get; private set; } = null!;
        public bool Enabled { get => _availabilityController.Enabled; set => _availabilityController.Enabled = value; }
        public bool IsSupportedOnDevice => _availabilityController.IsSupportedOnDevice;
        
        public async UniTask InitializeAsync()
        {
            var config =  await _gameConfigProvider.Haptic.GetAsync();
            _hapticConfig = config.Value;

            var foundConfig = _hapticConfig != null;
            _initialized = foundConfig;
            
            if (!foundConfig)
                _logger.LogError("Failed to load haptic config");
        }
        
        public void Play(string patternId)
        {
            if (!_patternProvider.ValidateAndGetPattern(patternId, out var pattern)) 
                return;

            Stop();
            AnalyticsLogger.LogPlayed(patternId);
            JxHapticPlayer.PlayAsync(patternId, pattern!).Forget();
        }

        public IDisposable PlayCycled(string patternId, TimeSpan? delay = null)
        {
            if (!_patternProvider.ValidateAndGetPattern(patternId, out var pattern)) 
                return JxDisposableAction.Empty;

            Stop();
            Interlocked.Exchange(ref _lastCts, new CancellationTokenSource())?.CancelAndDispose();
            AnalyticsLogger.LogPlayed(patternId);
            JxHapticPlayer.PlayCycledAsync(patternId, pattern!, delay, _lastCts!.Token).Forget();
            return JxDisposableAction.Build().AppendCallback(() => Interlocked.Exchange(ref _lastCts, null)?.CancelAndDispose());
        }
        
        public void Stop()
        {
#if HEADLESS
            return;
#else
            if (!IsSupportedOnDevice || !_initialized || !Enabled)
                return;
            
            JxHapticPlayer.Stop(); 
            _lastCts?.CancelAndDispose();
            _lastCts = null;
#endif
        }

        #region CLASSES

        private static class AnalyticsLogger
        {
            public static void LogPlayed(string message)
            {
#if DEV && !PRODUCTION_BUILD
                DebugLogAnalytics.Instance.Emit($"HAPTIC | {message}");
#endif
            }
            
//             public static void LogIgnored(string message)
//             {
// #if DEV && !PRODUCTION_BUILD
//                 DebugLogAnalytics.Instance.Emit($"IGNORE HAPTIC | {message}");
// #endif
//             }
        }

        private class AvailabilityController
        {
            private readonly JxHapticManager _manager;

            public AvailabilityController(
                JxHapticManager manager)
            {
                _manager = manager;
            }
            
            public bool Enabled
            {
                get => _manager._localSave.Value.HapticsEnabled;
                set
                {
                    if (_manager._localSave.Value.HapticsEnabled == value)
                        return;
                
                    _manager._localSave.Change(s => s.HapticsEnabled = value);
                
                    if (!IsSupportedOnDevice || !_manager._initialized)
                        return;
                
                    if (!value)
                        _manager.Stop();
                
                    HapticController.hapticsEnabled = value;
                }
            }

            public bool IsSupportedOnDevice
            {
                get
                {
#if HEADLESS
                    return false;
#elif UNITY_EDITOR
                    return true;
#elif UNITY_ANDROID || UNITY_IOS || UNITY_IPHONE
                    return DeviceCapabilities.isVersionSupported;
#else
                    return false;
#endif
                }
            }
        }

        private class PatternProvider
        {
            private readonly JxHapticManager _manager;

            public PatternProvider(
                JxHapticManager manager)
            {
                _manager = manager;
            }
            
            public bool ValidateAndGetPattern(string patternId, out IReadOnlyList<HapticPatternPointPresentation>? pattern)
            {
#if HEADLESS
                pattern = null;
                return false;
#else
                if (!_manager.IsSupportedOnDevice || !_manager._initialized || !_manager.Enabled)
                {
                    pattern = null;
                    return false;
                }

                if (string.IsNullOrEmpty(patternId))
                {
                    _logger.LogError($"Invalid id '{patternId}'");
                    pattern = null;
                    return false;
                }

                pattern = _manager._hapticConfig!.FindPattern(patternId);
                if (pattern == null)
                {
                    _logger.LogError("Failed to find haptic pattern in config", trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["PatternId"] = patternId
                    });
                    return false;
                }

                if (pattern.IsEmpty())
                {
                    _logger.LogError("Can't play EMPTY haptic pattern", trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["PatternId"] = patternId
                    });

                    return false;
                }

                return true;
#endif
            }
        }

        private static class JxHapticPlayer
        {
            public static async UniTaskVoid PlayCycledAsync(
                string patternId, 
                IReadOnlyList<HapticPatternPointPresentation> pattern, 
                TimeSpan? delay,
                CancellationToken ct)
            {
                while (!ct.IsCancellationRequested)
                {
                    await PlayAsync(patternId, pattern);
                    await UniTask.Delay(delay.GetValueOrDefault(TimeSpan.FromMilliseconds(100)), cancellationToken: ct).SuppressCancellationThrow();
                }
            }
        
            public static async UniTask PlayAsync(string patternId, IReadOnlyList<HapticPatternPointPresentation> pattern)
            {
                try
                {
                    PlayPoint(0, pattern.First());

                    for (var i = 1; i < pattern.Count; ++i)
                    {
                        var current = pattern[i];
                        var previous = pattern[i - 1];
                        await UniTask.Delay(TimeSpan.FromMilliseconds(current.PlayAtMills - previous.PlayAtMills));
                        PlayPoint(i, current);
                    }
                }
                catch (Exception exception)
                {
                    _logger.LogError("Failed to play haptic pattern", exception, trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["PatternId"] = patternId
                    });
                
                    return;
                }
            }

            public static void Stop()
            {
                HapticController.Stop();
            }

            private static void PlayPoint(int index, HapticPatternPointPresentation point)
            {
                if (!point.IsValid())
                    throw new Exception("Invalid haptic point").SetData("Index", index.ToString());
            
                HapticPatterns.PlayConstant(
                    amplitude: point.Amplitude01, 
                    frequency: point.Frequency01,
                    duration: point.DurationSeconds
                );
            }
        }

        #endregion
    }
}