using System;
using UnityEngine;

namespace Core.Inspector.ConditionalFieldInspector
{
    [AttributeUsage(AttributeTargets.Field)]
    public class ConditionalFieldAttribute : PropertyAttribute
    {
        public ConditionalFieldAttribute(string propertyToCheck, object propertyValue = null)
        {
            PropertyToCheck = propertyToCheck;
            PropertyValue = propertyValue;
        }

        public string PropertyToCheck { get; }

        public object PropertyValue { get; }
    }
}