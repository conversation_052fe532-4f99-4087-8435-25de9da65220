using UnityEngine;

namespace Core.Editor.ServerBuildUpload.Configuration
{
    public class ServerBuildUploadConfiguration : ScriptableObject
    {
        [field: SerializeField]
        public string Token { get; private set; } = null!;

        [field: SerializeField]
        public string ServerAddress { get; private set; } = null!;

        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Token) &&
                   !string.IsNullOrEmpty(ServerAddress);
        }
    }
}
