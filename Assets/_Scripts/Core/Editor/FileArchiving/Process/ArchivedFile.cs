using System.IO;
using File = UnityEngine.Windows.File;

namespace Core.Editor.FileArchiving.Process
{
    public class ArchivedFile : IArchivedFile
    {
        private readonly string _filePath;

        public ArchivedFile(string filePath)
        {
            _filePath = filePath;
        }

        public string Path => _filePath;

        public long GetSize() => new FileInfo(_filePath).Length;

        public void Remove()
        {
            if (FileExists())
            {
                File.Delete(_filePath);
            }
        }

        private bool FileExists()
        {
            return File.Exists(_filePath);
        }
    }
}