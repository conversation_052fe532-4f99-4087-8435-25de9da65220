#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace Core.Editor.UnityToolbarHelpers
{
	[InitializeOnLoad]
	public static class ToolbarExt
	{
		private const float SPACE = 8;
		private const float BUTTON_WIDTH = 32;
		private const float BUTTON_HEIGHT = 22;
		private const float BUTTON_Y_POS = 4;
		private const float BUTTON_DELTA = 64 * 2;
		private const float UNITY_BUTTONS_WIDTH = 140;
		
		public static readonly List<Action> Toolbar = new();

		private static readonly int _toolsCount;
		private static GUIStyle? _style;

		static ToolbarExt()
		{
			var toolbarType = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.Toolbar");
			
			var toolIcons = toolbarType.GetField("k_ToolCount", 
				BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);
			
			_toolsCount = toolIcons != null ? ((int) toolIcons.GetValue(null)) : 8;
	
			ToolbarBinder.OnToolbar = OnGUI;
			ToolbarBinder.OnToolbarActions = () =>
			{
				GUILayout.BeginHorizontal();
				foreach (var action in Toolbar)
					action();

				GUILayout.EndHorizontal();
			};
		}

		private static void OnGUI()
		{
			if (_style == null)
				_style = new GUIStyle("CommandLeft");

			var viewWidth = EditorGUIUtility.currentViewWidth;
			var pos = Mathf.RoundToInt((viewWidth - UNITY_BUTTONS_WIDTH) / 2);

			var rt = new Rect(0, 0, viewWidth, Screen.height);
			rt.xMin += SPACE;
			rt.xMin += BUTTON_WIDTH * _toolsCount;
			rt.xMin += SPACE;
			rt.xMin += BUTTON_DELTA;
			rt.xMax = pos;
			rt.xMin += SPACE;
			rt.xMax -= SPACE;
			rt.y = BUTTON_Y_POS;
			rt.height = BUTTON_HEIGHT;
			
			if (rt.width > 0)
			{
				GUILayout.BeginArea(rt);
				GUILayout.BeginHorizontal();
				foreach (var action in Toolbar)
					action();

				GUILayout.EndHorizontal();
				GUILayout.EndArea();
			}
		}
	}
}

#endif
