using UnityEditor;
using UnityEngine;

namespace Core.Attribution
{
#if UNITY_EDITOR
    [CustomPropertyDrawer(typeof(JxCommentAttribute))]
    public class JxCommentDrawer : DecoratorDrawer
    {
        public override float GetHeight()
        {
            return EditorGUIUtility.singleLineHeight + 10;
        }

        public override void OnGUI(Rect position)
        {
            var jxComment = (JxCommentAttribute)attribute;
            
            var commentRect = new Rect(position.x, position.y + 2, position.width, EditorGUIUtility.singleLineHeight + 6);
            EditorGUI.HelpBox(commentRect, GUIContent.none.text, MessageType.Info);
            
            var style = new GUIStyle(EditorStyles.label)
            {
                normal = { textColor = Color.gray },
                fontStyle = FontStyle.Bold
            };
            
            var textRect = new Rect(commentRect.x + 28, commentRect.y + 4, commentRect.width - 28, commentRect.height - 5);
            GUI.Label(textRect, jxComment.Comment, style);
        }
    }
#endif
}