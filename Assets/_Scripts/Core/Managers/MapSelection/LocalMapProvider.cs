#if !HEADLESS
using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Stats;
using Jx.Utils.Collections;
using Jx.Utils.Extensions;
using Jx.Utils.Logging;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Matchmaking.Dto.Maps;
using Savings;
using UnityEngine.Scripting;

namespace Core.Managers.MapSelection
{
    [Preserve]
    public class LocalMapProvider : ILocalMapProvider
    {
        private static readonly IReadOnlyList<JxMonsterLandMap> _debugMaps = new[]
        {
            JxMonsterLandMap.ClassicTest1,
            JxMonsterLandMap.ClassicTest2,
            JxMonsterLandMap.ClassicTest3,
            JxMonsterLandMap.ClassicTest4,
            JxMonsterLandMap.ClassicTest5,
            JxMonsterLandMap.ClassicTest6,
            JxMonsterLandMap.ClassicTest7,
            JxMonsterLandMap.ClassicTest8,
            
            JxMonsterLandMap.TurboTest1,
            JxMonsterLandMap.TurboTest2,
            JxMonsterLandMap.TurboTest3,
            JxMonsterLandMap.TurboTest4,
            JxMonsterLandMap.TurboTest5,
            JxMonsterLandMap.TurboTest6,
            JxMonsterLandMap.TurboTest7,
            JxMonsterLandMap.TurboTest8,
        };
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(LocalMapProvider));

        private readonly ILocalSave _localSave;
        private readonly IUserContext _userContext;

        [Preserve]
        public LocalMapProvider(
            ILocalSave localSave,
            IUserContext userContext
        )
        {
            _localSave = localSave;
            _userContext = userContext;
        }

        public JxMonsterLandMap Get(JxMonsterLandGameModeKind gameMode)
        {
            if (gameMode == JxMonsterLandGameModeKind.Tutorial)
                return JxMonsterLandMap.Tutorial;
            
            var supportedMaps = JxMonsterLandGameModeSettingsRepository.Instance.GetSupportedMaps(gameMode);
#if PRODUCTION_BUILD
           supportedMaps = Enumerable.ToHashSet(supportedMaps.Except(_debugMaps));
#endif

            try
            {
#if !PRODUCTION_BUILD || UNITY_EDITOR
                var debugMap = FindDebugMap(supportedMaps);
                if (debugMap != null)
                    return debugMap.Value;
#endif

                return GetMap(gameMode, supportedMaps);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }

            return supportedMaps.RandomOrDefault();
        }

        private JxMonsterLandMap? FindDebugMap(ICollection<JxMonsterLandMap> supportedMaps)
        {
            var debugMap = _localSave.Debug.Map;
            if (debugMap == null || !supportedMaps.Contains(debugMap.Value))
                return null;

            return debugMap.Value;
        }

        private JxMonsterLandMap GetMap(JxMonsterLandGameModeKind gameMode, ISet<JxMonsterLandMap> supportedMaps)
        {
            var playedMatchCount = _userContext.Stats.Value.Value.GetPlayedMatchCount(gameMode: gameMode);
            JxMonsterLandMap map = default;

            switch (gameMode)
            {
                case JxMonsterLandGameModeKind.Classic:
                    if (playedMatchCount < 2)
                    {
                        map = supportedMaps.First();
                        break;
                    }

                    if (playedMatchCount < 4)
                    {
                        map = supportedMaps.Skip(1).First();
                        break;
                    }

                    map = supportedMaps.RandomOrDefault();
                    break;

                case JxMonsterLandGameModeKind.Turbo:
                    if (playedMatchCount < supportedMaps.Count)
                    {
                        map = supportedMaps.Skip(playedMatchCount).First();
                        break;
                    }

                    map = supportedMaps.RandomOrDefault();
                    break;
            }

            if (!supportedMaps.Contains(map))
            {
                throw new InvalidOperationException("Map can't be determined")
                     .SetData("GameMode", gameMode.ToString())
                     .SetData("Map", map.ToString());
            }

            return map;
        }
    }
}
#endif