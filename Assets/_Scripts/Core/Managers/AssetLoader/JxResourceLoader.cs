using System;
using System.Collections.Generic;
using System.IO;
using Core.Helpers;
using Jx.Utils.Ensurance;
using Jx.Utils.Logging;
using ModestTree;
using TMPro;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Core.Managers.AssetLoader
{
    public class JxResourceLoader : IJxResourceLoader
    {
        private const string SpritesPath = "Sprites";
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxResourceLoader));

        private const string FallbackIconPath = "unknown";

        public static IJxResourceLoader Instance { get; } = new JxResourceLoader();
        
        private JxResourceLoader() { }

        public IReadOnlyList<Sprite> LoadSpriteAtlasOrFallback(params string[] paths)
        {
            var path = Path.Combine(paths);
            var atlas = Resources.LoadAll<Sprite>($"{SpritesPath}/{path}");
            if (atlas.IsNullOrEmpty())
            {
                _logger.LogError($"Not found atlas '{path}'");
                return Resources.LoadAll<Sprite>($"{SpritesPath}/gif/fallback");
            }
            return atlas;
        }
        
        Material? IJxResourceLoader.LoadMaterialOrNull(params string[] materialPaths)
        {
            return LoadAsset<Material>(Path.Combine("materials", Path.Combine(materialPaths)));
        }

        public T LoadConfigOrThrow<T>(params string[] paths)
            where T : ScriptableObject
        {
            return LoadAsset<T>("configs", paths) ?? throw new NullReferenceException($"Can't find config '{nameof(T)}'");
        }

        Sprite IJxResourceLoader.LoadSpriteOrFallback(params string[] spritePaths)
        {
            return LoadAsset<Sprite>("Sprites", spritePaths) ??
                   LoadAsset<Sprite>("Sprites", FallbackIconPath); // fallback
        }

        Sprite IJxResourceLoader.LoadSpriteOrNull(params string[] spritePaths)
        {
            return LoadAsset<Sprite>("Sprites", spritePaths);
        }

        public GameObject LoadPrefab(params string[] prefabPaths)
        {
            var prefab = LoadAsset<GameObject>("Prefabs", prefabPaths);
            Ensure.NotNull(prefab, $"Prefab from path [Prefabs/{prefabPaths.Join("/")}]");

#if UNITY_EDITOR
            var prefabWasActive = prefab.activeSelf;
            prefab.SetActive(false);

            var copy = Object.Instantiate(prefab);

            // restore prefab active state
            prefab.SetActive(prefabWasActive);

            copy.SetActive(false);
            return copy;
#else
            prefab.SetActive(false);
            return prefab;
#endif
        }

        public T? LoadPrefab<T>(params string[] prefabPaths)
            where T : Component
        {
            var prefab = LoadPrefab(prefabPaths);

            if (!prefab.TryGetComponent(out T component))
            {
                _logger.LogError(
                    "Can't get component on prefab",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["PrefabPath"] = Path.Combine(prefabPaths),
                        ["ComponentName"] = nameof(T)
                    }
                );

                return null;
            }

            return component;
        }

        IReadOnlyList<T> IJxResourceLoader.LoadAllPrefabsInFolder<T>(params string[] prefabPaths)
        {
            var path = Path.Combine("Prefabs", Path.Combine(prefabPaths));
            return Resources.LoadAll<T>(path);
        }

        Material IJxResourceLoader.LoadSkin(string characterResourceName, string skinResourceName)
        {
            return LoadAsset<Material>($"CharactersResources/{characterResourceName}/Skins/{skinResourceName}");
        }

        AudioClip IJxResourceLoader.LoadAudio(params string[] audioPaths)
        {
            return LoadAudioInternal(audioPaths);
        }

        public TextAsset LoadText(params string[] paths)
        {
            return LoadAsset<TextAsset>("Texts", paths);
        }

        public Texture2D? LoadTextureOrNull(params string[] paths)
        {
            return LoadAsset<Texture2D>("textures", paths);
        }

        public RenderTexture LoadRenderTexture(params string[] paths)
        {
            return Resources.Load<RenderTexture>(Path.Combine("RenderTextures", Path.Combine(paths)));
        }

        public TMP_FontAsset LoadFontOrNull(params string[] paths)
        {
            return Resources.Load<TMP_FontAsset>(Path.Combine("Fonts", Path.Combine(paths)));
        }

        public GameObject LoadRewardContainerPrefab(params string[] paths)
        {
            var combinedPaths = Path.Combine(paths);

            return LoadAsset<GameObject>(Path.Combine("Prefabs/Screens/Common/RewardsDialog/Rewards/Containers", combinedPaths));
        }

        public TAsset LoadAsset<TAsset>(string prefix, string[] assetPaths)
            where TAsset : Object
        {
            var combinedPaths = Path.Combine(assetPaths);
            return LoadAsset<TAsset>(Path.Combine(prefix, combinedPaths));
        }

        private AudioClip LoadAudioInternal(params string[] audioPaths)
        {
            const string prefix = @"Audio";
            var combinePath = Path.Combine(audioPaths);
            combinePath = Path.Combine(prefix, combinePath);

            return LoadAsset<AudioClip>(combinePath);
        }

        private TAsset LoadAsset<TAsset>(string prefix, string assetPath)
            where TAsset : Object
        {
            return LoadAsset<TAsset>(Path.Combine(prefix, assetPath));
        }

        private void PreloadAsset(string fullPath)
        {
            fullPath = fullPath.Replace('\\', '/');
            if (JxUnityResourceIndex.Instance.Exists(fullPath))
            {
                var asset = Resources.Load(fullPath);

#if UNITY_EDITOR
                if (asset == null)
                {
                    _logger.LogError(
                        "Asset to preload not found",
                        trackingFactory: () => new Dictionary<string, object>
                        {
                            ["Path"] = fullPath
                        }
                    );
                }
#endif
            }
        }

        private TAsset LoadAsset<TAsset>(string fullPath)
            where TAsset : Object
        {
            var asset = default(TAsset);

            if (!string.IsNullOrEmpty(fullPath))
            {
                fullPath = fullPath.Replace('\\', '/');

                if (JxUnityResourceIndex.Instance.Exists(fullPath))
                {
                    asset = Resources.Load<TAsset>(fullPath);
                }
            }

#if UNITY_EDITOR
            if (asset == null)
            {
                _logger.LogError(
                    "Asset not found",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Path"] = fullPath,
                        ["Type"] = typeof(TAsset),
                    }
                );
            }
#endif

            return asset;
        }
    }
}