using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;

namespace Core.Managers.AssetLoader
{
    public interface IJxResourceLoader
    {
        IReadOnlyList<Sprite> LoadSpriteAtlasOrFallback(params string[] paths);
        TAsset LoadAsset<TAsset>(string prefix, string[] assetPaths) where TAsset : Object;
        Material? LoadMaterialOrNull(params string[] materialPaths); 
        T LoadConfigOrThrow<T>(params string[] paths) where T : ScriptableObject; 
        Sprite LoadSpriteOrFallback(params string[] spritePaths);
        Sprite LoadSpriteOrNull(params string[] spritePaths);
        GameObject LoadPrefab(params string[] prefabPaths);
        T? LoadPrefab<T>(params string[] prefabPaths) where T : Component;
        IReadOnlyList<T> LoadAllPrefabsInFolder<T>(params string[] prefabPaths) where T : Object;
        Material LoadSkin(string characterResourceName, string skinResourceName); // todo: replace with with LoadMaterial
        AudioClip LoadAudio(params string[] audioPaths);
        TextAsset LoadText(params string[] paths);
        Texture2D? LoadTextureOrNull(params string[] paths);
        RenderTexture LoadRenderTexture(params string[] paths);
        TMP_FontAsset LoadFontOrNull(params string[] paths);

        GameObject LoadRewardContainerPrefab(params string[] paths);
        
        // GameObject LoadCurrencyRewardPrefab(CurrencyType type);
    }
}