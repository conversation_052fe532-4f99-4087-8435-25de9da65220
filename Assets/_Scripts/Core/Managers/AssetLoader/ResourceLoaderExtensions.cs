using System.IO;
using Api.Client.User.Rewards.Adapters;
using Api.Entities;
using App.Inventory.Item;
using Core.Extensions;
using ExternalServices.Auth;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Share.Character;
using Jx.Utils.Objects;
using LoM.Characters.ClientIntegration.Perks;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using SceneLogics.GameplayScene.Abilities;
using UI.Screens.Common.Reward.Parameters;
using UnityEngine;

namespace Core.Managers.AssetLoader
{
    public static class ResourceLoaderExtensions
    {
        private const string CharacterIconPlacement = "rectangle";
        private const string FeatureAccessIconFolder = "feature-access";
        private const string ChestDirectory = "chests/closed";
        private const string GameState = "match-finish/player-statuses";

        public static Sprite LoadPlayerMatchStateSpriteOrFallback(this IJxResourceLoader resourceLoader, GameplayPlayerState state)
        {
            return resourceLoader.LoadSpriteOrFallback(GameState, state.ToString());
        }
        
        public static Sprite LoadFeatureAccessSpriteOrFallback(this IJxResourceLoader resourceLoader, MonsterLandFeatureAccessType type)
        {
            return resourceLoader.LoadSpriteOrFallback(FeatureAccessIconFolder, type.ToString().ToLowerInvariant());
        }
        
        public static Sprite LoadInventoryItemIcon(this IJxResourceLoader resourceLoader, InventoryItemType type)
        {
            var name = type switch
            {
                InventoryItemType.PotionSpeedUp => "speed-up",
                InventoryItemType.PotionInvisibility => "invisibility",
                InventoryItemType.PotionPower => "power",
                InventoryItemType.PotionHearing => "hearing",
                
                InventoryItemType.TrapExplosion => "explosion",
                InventoryItemType.TrapSlowDown => "slow-down",
                InventoryItemType.TrapDisclosure => "disclosure",
                InventoryItemType.TrapCatch => "catch",
                
                _ => "fallback"
            };

            var folderName = type.IsPotion() ? "potions" : "traps";
            return resourceLoader.LoadSpriteOrFallback("gameplay-icons", "inventory-items", folderName, name);
        }

        public static Sprite LoadPerkRarityChanceSprite(this IJxResourceLoader resourceLoader, PerkRarity rarity)
        {
            var path = Path.Combine("rewards", "chances", "perk");

            return rarity switch
            {
                PerkRarity.Common => resourceLoader.LoadSpriteOrFallback(Path.Combine(path, "common")),
                PerkRarity.Rare => resourceLoader.LoadSpriteOrFallback(Path.Combine(path, "rare")),
                PerkRarity.Epic => resourceLoader.LoadSpriteOrFallback(Path.Combine(path, "epic")),
                PerkRarity.Legendary => resourceLoader.LoadSpriteOrFallback(Path.Combine(path, "legendary")),
                _ => resourceLoader.LoadSpriteOrFallback(Path.Combine(path, "common"))
            };
        }
        
        public static Sprite LoadChestSprite(this IJxResourceLoader resourceLoader, string contentPath)
        {
            var sprite = resourceLoader.LoadSpriteOrNull(ChestDirectory, contentPath);

            if (sprite == null)
            {
                sprite = resourceLoader.LoadSpriteOrFallback(ChestDirectory, "fallback");
            }

            return sprite;
        }

        // todo refactor
        public static Sprite LoadRewardSprite(this IJxResourceLoader resourceLoader, IJxUserReward? reward)
        {
            if (reward == null)
            {
                return resourceLoader.LoadSpriteOrFallback("");
            }
            
            switch (reward)
            {
                case SeasonPassXpUserReward:
                    return resourceLoader.LoadSeasonPassXpSprite();
                case EntityUserReward entity:
                    return resourceLoader.LoadCharacterIcon(CharacterIconPlacement, entity.Entity.GetCharacterIndex());
                case ChestUserReward chest:
                    return string.IsNullOrEmpty(reward.ContentPath) ?
                        resourceLoader.LoadChestSprite(chest.ChestDefinition.ContentPath) : 
                        resourceLoader.LoadSpriteOrFallback(reward.ContentPath);
                default:
                    return string.IsNullOrEmpty(reward.ContentPath) ? 
                        resourceLoader.LoadRewardDefaultSprite(reward) : 
                        resourceLoader.LoadSpriteOrFallback(Path.Join("rewards", reward.ContentPath));
            }
        }

        // todo refactor
        public static Sprite LoadRewardDefaultSprite(this IJxResourceLoader resourceLoader, IJxUserReward reward)
        {
            if (reward == null)
            {
                return resourceLoader.LoadSpriteOrFallback("");
            }

            return resourceLoader.LoadSpriteOrFallback(
                Path.Join("rewards", $"{reward.Kind.ToString().ToLowerInvariant()}-default")
            );
        }

        public static Sprite LoadSeasonPassXpSprite(this IJxResourceLoader loader)
        {
            return loader.LoadSpriteOrFallback("rewards/seasonpassxp-default");
        }

        public static Sprite LoadResourceRewardSprite(this IJxResourceLoader resourceLoader, IResourceRewardParameters reward)
        {
            return reward switch
            {
                GemRewardParameters => resourceLoader.LoadSpriteOrFallback("rewards/gem-big-default"),
                GoldRewardParameters => resourceLoader.LoadSpriteOrFallback("rewards/gold-big-default"),
                _ => resourceLoader.LoadSpriteOrFallback(""),
            };
        }

        public static Sprite LoadRewardScreenBackground(this IJxResourceLoader resourceLoader, IRewardParameters parameters)
        {
            var path = @"rewards/reward-screen/backgrounds/";

            path += parameters switch
            {
                CharacterRewardParameters => "character",
                SkinRewardParameters => "skin",
                _ => "default"
            };
            
            return resourceLoader.LoadSpriteOrFallback(path);
        }

        public static Sprite LoadCharacterIcon(this IJxResourceLoader resourceLoader, string placement, int characterIndex)
        {
            var iconName = CharacterTypeHelper.GetName(characterIndex);
            return resourceLoader.LoadSpriteOrFallback("character-icons", placement, iconName);
        }

        public static Sprite LoadPlayerStateIndicatorIcon(this IJxResourceLoader resourceLoader, string iconName)
        {
            return resourceLoader.LoadSpriteOrFallback("player-state-indicator-icons", iconName);
        }

        public static Sprite LoadInteractionIcon(this IJxResourceLoader resourceLoader, InteractionType interactionType)
        {
            var icon = resourceLoader.LoadSpriteOrFallback("interaction-icons", interactionType.ToString());

            if (icon == null)
            {
                icon = resourceLoader.LoadSpriteOrFallback("interaction-icons", "unknown");
            }

            return icon;
        }

        public static Sprite LoadAuthProviderIcon(this IJxResourceLoader resourceLoader, AuthProviderType providerType)
        {
            var iconName = string.Empty;

            switch (providerType)
            {
                case AuthProviderType.Google:
                    iconName = "Google";
                    break;
                case AuthProviderType.GooglePlayGamesSilent:
                case AuthProviderType.GooglePlayGamesWithUI:
                    iconName = "GooglePlayGames";
                    break;
                default:
                    iconName = "Guest";
                    break;
            }

            return resourceLoader.LoadSpriteOrFallback("auth-providers", iconName);
        }
        
        public static Sprite LoadCharacterAbilityIcon(this IJxResourceLoader resourceLoader, AbilityType abilityType)
        {
            return resourceLoader.LoadSpriteOrFallback("abilities", abilityType.ToString());
        }
    }
}
