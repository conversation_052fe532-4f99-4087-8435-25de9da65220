#if !HEADLESS && !PRODUCTION_BUILD && DEV
using System;
using Core.Managers.AssetLoader;
using Core.UnityUtils.Debug.Spatial.Special;
using UnityEngine;
using UnityEngine.Rendering;
using Object = UnityEngine.Object;

namespace Core.UnityUtils.Debug.Spatial
{
    internal static class JxDebugSpatialDrawer
    {
        public static GameObject DrawSphere()
        {
           return Primitive(PrimitiveType.Sphere)
                   .WithName("JxDebug_Sphere")
                   .WithDefaultGameObject()
                   .WithDefaultMeshRenderer();
        }

        public static GameObject DrawWireCube()
        {
            return new GameObject()
                .WithName("JxDebug_WireCube")
                .WithDefaultGameObject()
                .WithComponent<JxDebugWireCubeDrawer>(drawer => 
                    drawer.Initialize(LoadMaterial())
                );
        }

        public static GameObject DrawText()
        {
            return new GameObject()
                .WithName("JxDebug_Text")
                .WithDefaultGameObject()
                .WithComponent<JxDebugTextDrawer>(drawer => 
                    drawer.Initialize()
                );
        }

        #region BUILDING

        private static GameObject WithName(this GameObject gameObject, string name)
        {
            gameObject.name = name;
            return gameObject;
        }

        private static GameObject WithComponent<T>(this GameObject gameObject, Action<T> setupComponent)
            where T : Component
        {
            setupComponent(gameObject.AddComponent<T>());
            return gameObject;
        }

        private static GameObject WithDefaultGameObject(this GameObject gameObject)
        {
            gameObject.transform.SetParent(null, false);
            return gameObject;
        }

        private static GameObject Primitive(PrimitiveType primitiveType)
        {
            var gameObject = GameObject.CreatePrimitive(primitiveType);
            Object.Destroy(gameObject.gameObject.GetComponent<Collider>());
            return gameObject;
        }

        private static GameObject WithDefaultMeshRenderer(this GameObject gameObject)
        {
            var meshRenderer = gameObject.GetComponent<MeshRenderer>();
            meshRenderer.shadowCastingMode = ShadowCastingMode.Off;
            meshRenderer.lightProbeUsage = LightProbeUsage.Off;
            meshRenderer.reflectionProbeUsage = ReflectionProbeUsage.Off;
            meshRenderer.receiveShadows = false;
            meshRenderer.material = new Material(LoadMaterial());
            return meshRenderer.gameObject;
        }

        private static Material LoadMaterial()
            => JxResourceLoader.Instance.LoadMaterialOrNull("JxDebugDraw") ?? throw new NullReferenceException("Failed to find JxDebugDraw material");

        #endregion
    }
}
#endif