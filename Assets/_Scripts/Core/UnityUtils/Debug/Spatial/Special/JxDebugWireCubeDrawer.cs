#if !HEADLESS && !PRODUCTION_BUILD && DEV
using UnityEngine;

namespace Core.UnityUtils.Debug.Spatial.Special
{
    public class JxDebugWireCubeDrawer : MonoBehaviour, IJxDebugWireCubeDrawer
    {
        private const int VertexCount = 24; // 12 линий, каждая линия - 2 точки
        
        private LineRenderer _lineRenderer = null!;
        private Vector3 _size;
        private Vector3 _parentCenter;
        private bool _initialized;
        private Color _color;
        private float _width;
        
        public Vector3 Size
        {
            get => _size;
            set => _size = new Vector3(Mathf.Abs(value.x), Mathf.Abs(value.y), Mathf.Abs(value.z));
        }

        public Color Color
        {
            get => _color;
            set
            {
                _color = value;
                _lineRenderer.material.color = _color;
            }
        }
        
        public float Width
        {
            get => _width;
            set
            {
                _width = value;
                _lineRenderer.startWidth = _width;
                _lineRenderer.endWidth = _width;
            }
        }
        
        private void Update()
        {
            if (!_initialized) 
                return;
            
            UpdateLinePositions();
        }
        
        public JxDebugWireCubeDrawer Initialize(Material material)
        {
            _size = Vector3.one;
            _lineRenderer = gameObject.AddComponent<LineRenderer>();
            SetupLineRenderer();
            _lineRenderer.positionCount = VertexCount;
            _lineRenderer.material = new Material(material);
            
            _initialized = true;
            return this;
        }

        private void SetupLineRenderer()
        {
            _lineRenderer.startWidth = 0.05f;
            _lineRenderer.endWidth = 0.05f;
            _lineRenderer.useWorldSpace = false;
            _lineRenderer.loop = false;
        }

        private void UpdateLinePositions()
        {
            if (!_initialized)
                return;

            var halfSize = _size * 0.5f;

            // Вершины куба
            var p0 = new Vector3(-halfSize.x, -halfSize.y, -halfSize.z);
            var p1 = new Vector3(halfSize.x, -halfSize.y, -halfSize.z);
            var p2 = new Vector3(halfSize.x, -halfSize.y, halfSize.z);
            var p3 = new Vector3(-halfSize.x, -halfSize.y, halfSize.z);
            var p4 = new Vector3(-halfSize.x, halfSize.y, -halfSize.z);
            var p5 = new Vector3(halfSize.x, halfSize.y, -halfSize.z);
            var p6 = new Vector3(halfSize.x, halfSize.y, halfSize.z);
            var p7 = new Vector3(-halfSize.x, halfSize.y, halfSize.z);

            // Нижняя грань
            _lineRenderer.SetPosition(0, p0);
            _lineRenderer.SetPosition(1, p1);

            _lineRenderer.SetPosition(2, p1);
            _lineRenderer.SetPosition(3, p2);

            _lineRenderer.SetPosition(4, p2);
            _lineRenderer.SetPosition(5, p3);

            _lineRenderer.SetPosition(6, p3);
            _lineRenderer.SetPosition(7, p0);

            // Верхняя грань
            _lineRenderer.SetPosition(8, p4);
            _lineRenderer.SetPosition(9, p5);

            _lineRenderer.SetPosition(10, p5);
            _lineRenderer.SetPosition(11, p6);

            _lineRenderer.SetPosition(12, p6);
            _lineRenderer.SetPosition(13, p7);

            _lineRenderer.SetPosition(14, p7);
            _lineRenderer.SetPosition(15, p4);

            // Вертикальные ребра
            _lineRenderer.SetPosition(16, p0);
            _lineRenderer.SetPosition(17, p4);

            _lineRenderer.SetPosition(18, p1);
            _lineRenderer.SetPosition(19, p5);

            _lineRenderer.SetPosition(20, p2);
            _lineRenderer.SetPosition(21, p6);

            _lineRenderer.SetPosition(22, p3);
            _lineRenderer.SetPosition(23, p7);
        }
    }
}
#endif