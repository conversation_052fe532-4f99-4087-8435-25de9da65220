using UnityEngine;

namespace Core.UnityUtils.TransformSnapshots
{
    public static class TransformSnapshotsExtensions
    {
        public static TransformSnapshot GetSnapshot(this Transform transform)
        {
            return new TransformSnapshot(transform);
        }

        public static LocalTransformSnapshot GetLocalSnapshot(this Transform transform)
        {
            return new LocalTransformSnapshot(transform);
        }

        public static void ApplySnapshot(this Transform transform, TransformSnapshot snapshot)
        {
            transform.position = snapshot.Position;
            transform.rotation = snapshot.Rotation;
        }

        public static void ApplySnapshot(this Transform transform, LocalTransformSnapshot localSnapshot)
        {
            transform.localPosition = localSnapshot.LocalPosition;
            transform.localRotation = localSnapshot.LocalRotation;
        }
    }
}