using Jx.Utils.Logging;
using UnityEngine;

namespace Core.Utils
{
    public class DestroyComponentOnHeadless : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(DestroyComponentOnHeadless));
        
        [SerializeField]
        private Component _component = null!;

        private void Awake()
        {
#if HEADLESS
            if (_component == null)
            {
                _logger.LogError("Component to disable on headless is null");
                return;
            }

            Destroy(_component);
#endif
        }
    }
}