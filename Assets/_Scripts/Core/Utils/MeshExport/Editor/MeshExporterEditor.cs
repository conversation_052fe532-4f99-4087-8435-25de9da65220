using UnityEditor;
using UnityEngine;

namespace MeshExport.Editor
{
    [CustomEditor(typeof(MeshExporter))]
    public class MeshExporterEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var exporter = target as MeshExporter;

            base.OnInspectorGUI();

            if (GUILayout.Button("Export"))
            {
                exporter.Export();
            }
        }
    }
}