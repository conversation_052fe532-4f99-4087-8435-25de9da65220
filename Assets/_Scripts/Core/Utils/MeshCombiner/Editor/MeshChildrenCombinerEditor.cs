using Core.Utils.MeshCombiner;
using UnityEditor;
using UnityEngine;

namespace Core.Utils.MeshCombiner.Editor
{
    [CustomEditor(typeof(MeshChildrenCombiner))]
    public class MeshChildrenCombinerEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            var myTarget = (MeshChildrenCombiner) target;

            if (GUILayout.Button("Combine"))
            {
                myTarget.Combine();
            }
        }
    }
}