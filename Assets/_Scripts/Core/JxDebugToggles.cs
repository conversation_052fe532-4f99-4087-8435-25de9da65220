using UnityEngine;

namespace Core
{
    public static class JxDebugToggles
    {
        private const string PlayerPrefsPrefix = "woodroom-debug-";

        public const string NetworkLogging = "NetworkLogging";
        public const string LongGameplayNetworkTimeout = "LongGameplayNetworkTimeout";
        public const string GameplayLocalhostAsRemote = "GameplayLocalhostAsRemote";
        public const string GameplayNetworkDiagnostics = "GameplayNetworkDiagnostics";
        public const string GameplayNetworkDiagnosticsEveryFrame = "GameplayNetworkDiagnosticsEveryFrame";

        public static bool IsNetworkLoggingEnabled()
        {
#if !UNITY_EDITOR
            return false;
#else
            return IsEnabled(NetworkLogging);
#endif
        }

        public static bool IsLongGameplayNetworkTimeoutEnabled()
        {
#if !UNITY_EDITOR
            return false;
#else
            return IsEnabled(LongGameplayNetworkTimeout);
#endif
        }

        public static bool IsGameplayLocalhostAsRemote()
        {
#if !UNITY_EDITOR
            return false;
#else
            return IsEnabled(GameplayLocalhostAsRemote);
#endif
        }
        
        public static bool GameplayNetworkDiagnosticsEnabled()
        {
#if !UNITY_EDITOR
            return false;
#else
            return IsEnabled(GameplayNetworkDiagnostics);
#endif
        }
        
        public static bool GameplayNetworkDiagnosticsEveryFrameEnabled()
        {
#if !UNITY_EDITOR
            return false;
#else
            return IsEnabled(GameplayNetworkDiagnosticsEveryFrame);
#endif
        }

#if UNITY_EDITOR
        public static string GetOptionKey(string option) => PlayerPrefsPrefix + option;

        public static bool IsEnabled(string option) => PlayerPrefs.GetInt(GetOptionKey(option), 0) == 1;
#endif
    }
}