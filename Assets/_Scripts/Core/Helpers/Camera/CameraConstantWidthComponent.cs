using UnityEngine;

namespace Core.Helpers.Camera
{
    /// <summary>
    /// Keeps constant camera width instead of height, works for both Orthographic & Perspective cameras
    /// Made for tutorial https://youtu.be/0cmxFjP375Y
    /// </summary>
    public class CameraConstantWidthComponent : MonoBehaviour
    {
        [SerializeField]
        private UnityEngine.Camera _camera;
        
        [SerializeField]
        private Vector2 _defaultResolution = new Vector2(1080, 1920);
        
        [SerializeField]
        [Range(0f, 1f)]
        private float _widthOrHeight = 0;

        private float _initialSize;
        private float _targetAspect;

        private float _initialFov;
        private float _horizontalFov = 120f;

        private void Start()
        {
            _initialSize = _camera.orthographicSize;

            _targetAspect = _defaultResolution.x / _defaultResolution.y;

            _initialFov = _camera.fieldOfView;
            _horizontalFov = CalcVerticalFov(_initialFov, 1 / _targetAspect);
        }

        private void Update()
        {
            if (_camera.orthographic)
            {
                var constantWidthSize = _initialSize * (_targetAspect / _camera.aspect);
                _camera.orthographicSize = Mathf.Lerp(constantWidthSize, _initialSize, _widthOrHeight);
            }
            else
            {
                var constantWidthFov = CalcVerticalFov(_horizontalFov, _camera.aspect);
                _camera.fieldOfView = Mathf.Lerp(constantWidthFov, _initialFov, _widthOrHeight);
            }
        }

        private float CalcVerticalFov(float hFovInDeg, float aspectRatio)
        {
            var hFovInRads = hFovInDeg * Mathf.Deg2Rad;

            var vFovInRads = 2 * Mathf.Atan(Mathf.Tan(hFovInRads / 2) / aspectRatio);

            return vFovInRads * Mathf.Rad2Deg;
        }
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            _camera ??= GetComponent<UnityEngine.Camera>();
        }
#endif
    }
}