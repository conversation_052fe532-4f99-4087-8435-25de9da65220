using System;
using Jx.Utils.Observable;
using UnityEngine;

namespace Core.Helpers.Collisions
{
    public class CollisionListener : MonoBehaviour
    {
        #region Trigger

        private readonly JxObservable<TriggerEventInfo> _triggerEnterObservable = new JxObservable<TriggerEventInfo>();
        private readonly JxObservable<TriggerEventInfo> _triggerStayObservable = new JxObservable<TriggerEventInfo>();
        private readonly JxObservable<TriggerEventInfo> _triggerExitObservable = new JxObservable<TriggerEventInfo>();

        public IDisposable SubscribeOnTriggerEnter(Action<TriggerEventInfo> onEnter)
        {
            return _triggerEnterObservable.Subscribe(onEnter);
        }
        
        public IDisposable SubscribeOnTriggerStay(Action<TriggerEventInfo> onStay)
        {
            return _triggerStayObservable.Subscribe(onStay);
        }
        
        public IDisposable SubscribeOnTriggerExit(Action<TriggerEventInfo> onExit)
        {
            return _triggerExitObservable.Subscribe(onExit);
        }

        private void OnTriggerEnter(Collider other)
        {
            _triggerEnterObservable.Invoke(new TriggerEventInfo(gameObject, other));
        }

        private void OnTriggerStay(Collider other)
        {
            _triggerStayObservable.Invoke(new TriggerEventInfo(gameObject, other));
        }

        private void OnTriggerExit(Collider other)
        {
            _triggerExitObservable.Invoke(new TriggerEventInfo(gameObject, other));
        }

        #endregion

        #region Collision

        private readonly JxObservable<CollisionEventInfo> _collisionEnterObservable = new JxObservable<CollisionEventInfo>();
        private readonly JxObservable<CollisionEventInfo> _collisionStayObservable = new JxObservable<CollisionEventInfo>();
        private readonly JxObservable<CollisionEventInfo> _collisionExitObservable = new JxObservable<CollisionEventInfo>();
        
        public IDisposable SubscribeToCollisionEnter(Action<CollisionEventInfo> onEnter)
        {
            return _collisionEnterObservable.Subscribe(onEnter);
        }
        
        public IDisposable SubscribeToCollisionStay(Action<CollisionEventInfo> onStay)
        {
            return _collisionStayObservable.Subscribe(onStay);
        }
        
        public IDisposable SubscribeToCollisionExit(Action<CollisionEventInfo> onExit)
        {
            return _collisionExitObservable.Subscribe(onExit);
        }

        private void OnCollisionEnter(Collision other)
        {
            _collisionEnterObservable.Invoke(new CollisionEventInfo(gameObject, other));
        }

        private void OnCollisionStay(Collision other)
        {
            _collisionStayObservable.Invoke(new CollisionEventInfo(gameObject, other));
        }

        private void OnCollisionExit(Collision other)
        {
            _collisionExitObservable.Invoke(new CollisionEventInfo(gameObject, other));
        }

        #endregion
    }
}