using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Jx.Utils.Collections;
using Jx.Utils.Logging;
using Jx.Utils.Objects;

namespace Core.Helpers.Systems
{
    public abstract class BaseLogicSystem<TParameters> : BaseLogicSystem
    {
        protected TParameters Parameters { get; private set; }
        
        public override void InjectParameters(object parameters)
        {
            Parameters = parameters.Cast<TParameters>();
        }
    }
    
    public abstract class BaseLogicSystem : ILogicSystem
    {
        protected IJxLogger Logger => _logger ??= JxLoggerFactory.CreateLogger(GetType().Name);
        private IJxLogger _logger;
        
        private readonly IList<IDisposable> _disposablesOnExit = new List<IDisposable>();

        public virtual void InjectParameters(object parameters)
        { }

        public async UniTask InitializeAsync()
        {
            await OnInitializeAsync();
        }

        public UniTask ExecuteAsync()
        {
            return OnExecuteAsync();
        }

        public async UniTask DeInitializeAsync()
        {
            _disposablesOnExit.ForEach(d => d.Dispose());
            _disposablesOnExit.Clear();

            await OnDeInitializeAsync();
        }

        protected virtual UniTask OnInitializeAsync()
        {
            return UniTask.CompletedTask;
        }

        protected virtual UniTask OnExecuteAsync() => UniTask.CompletedTask;

        protected virtual UniTask OnDeInitializeAsync()
        {
            return UniTask.CompletedTask;
        }

        protected void DisposeOnExit(IDisposable disposable)
        {
            _disposablesOnExit.Add(disposable);
        }
    }
}