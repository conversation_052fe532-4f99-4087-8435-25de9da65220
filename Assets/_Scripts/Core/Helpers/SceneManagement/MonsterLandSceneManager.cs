using System;
using System.Collections.Generic;
using App.Analytics;
using Core.Helpers.SceneManagement.TransitionDecorators;
using ExternalServices.Telemetry;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils.Logging;
using Mirror;
using UnityEngine.Scripting;

namespace Core.Helpers.SceneManagement
{
    public class MonsterLandSceneManager : JxSceneManager<MonsterLandSceneType>,
                                           IMonsterLandSceneManager
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(MonsterLandSceneManager));
        private static readonly JxSceneConfiguration _fallbackConfiguration = new JxSceneConfiguration("MainMenuScene")
        {
#if !HEADLESS
            FixedUpdateFrequency = 15,
            MaximumParticleFrequency = 15,
#endif
        };
        
        private static readonly IReadOnlyDictionary<MonsterLandSceneType, JxSceneConfiguration> _configurations =
            new Dictionary<MonsterLandSceneType, JxSceneConfiguration>
            {
                [MonsterLandSceneType.Init] = new JxSceneConfiguration("InitScene"),
                [MonsterLandSceneType.MainMenu] = _fallbackConfiguration,
                [MonsterLandSceneType.Tutorial] = new JxSceneConfiguration("GameplayScene_Tutorial"),
                [MonsterLandSceneType.Match] = new JxSceneConfiguration("MatchScene")
                {
#if !HEADLESS
                    FixedUpdateFrequency = (uint)(NetworkServer.sendRate * 1.2f)
#endif
                },
                [MonsterLandSceneType.Reconnect] = new JxSceneConfiguration("ReconnectScene"),
            };
        
        public static IMonsterLandSceneManager Instance { get; private set; } = null!;

        [Preserve]
        public MonsterLandSceneManager(
            IEnumerable<IJxSceneTransitionInterceptor>? sceneTransitionDecorators = null
        )
            : base(sceneTransitionDecorators ?? Array.Empty<IJxSceneTransitionInterceptor>(), GetSceneConfiguration)
        {
            Instance = this;
            AfterSceneChangeEvent += OnAfterSceneChanged;
        }

        private void OnAfterSceneChanged(MonsterLandSceneType sceneType)
        {
            var sceneTrackingName = sceneType.ToString().ToLowerInvariant();

            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.Scene,
                "changed",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.Placement] = sceneTrackingName,
                }
            );

            JxTelemetryIntegration.Instance.TrackAudioVolume(sceneTrackingName);
        }

        public static JxSceneConfiguration GetSceneConfiguration(MonsterLandSceneType sceneType)
        {
            if (_configurations.TryGetValue(sceneType, out var configuration))
                return configuration;
            
            _logger.LogError($"Unknown scene `{sceneType}`");
            return _fallbackConfiguration;
        }
    }
}