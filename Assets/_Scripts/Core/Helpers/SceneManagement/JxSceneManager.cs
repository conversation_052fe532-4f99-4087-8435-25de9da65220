using System;
using System.Collections.Generic;
using Core.Helpers.SceneManagement.TransitionDecorators;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Ensurance;
using Jx.Utils.Extensions;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Scripting;

namespace Core.Helpers.SceneManagement
{
    public class JxSceneManager<TSceneEnum> : IJxSceneManager<TSceneEnum>
        where TSceneEnum : struct
    {
        private readonly IEnumerable<IJxSceneTransitionInterceptor> _sceneTransitionDecorators;
        private readonly Func<TSceneEnum, JxSceneConfiguration> _getConfiguration;
        private readonly IJxLogger _logger;
        private readonly IJxChangeableObject<TSceneEnum, TSceneEnum> _currentScene;

        private object? _transitionParameters;

        [Preserve]
        public JxSceneManager(
            IEnumerable<IJxSceneTransitionInterceptor> sceneTransitionDecorators,
            Func<TSceneEnum, JxSceneConfiguration> getConfiguration
        )
        {
            _sceneTransitionDecorators = sceneTransitionDecorators;
            _getConfiguration = getConfiguration;
            _logger = JxLoggerFactory.CreateLogger(GetType().Name);
            _currentScene = new JxChangeableObject<TSceneEnum, TSceneEnum>();
        }

        public event Action<TSceneEnum>? BeforeSceneChangeEvent;
        public event Action<TSceneEnum>? AfterSceneChangeEvent;
        public IJxChangeableObject<TSceneEnum> CurrentChangeable => _currentScene;
        public TSceneEnum Current { get; private set; }

        public async UniTask TransitAsync(TSceneEnum scene, object parameters)
        {
            await UniTask.SwitchToMainThread();

            BeforeSceneChangeEvent?.Invoke(scene);

            await ExecuteDecoratorsAsync();

            await UniTask.SwitchToMainThread();

            var configuration = _getConfiguration(scene);
            
            var asyncOperation = LoadSceneAsync(configuration);
            await WaitSceneLoadingAsync(asyncOperation);
            
            ApplyConfiguration(configuration);

            _transitionParameters = parameters;

            asyncOperation.allowSceneActivation = true;

            await ExecuteSceneActivationAsync();

            Current = scene;
            _currentScene.Set(scene);
            AfterSceneChangeEvent?.Invoke(scene);
        }

        public bool TryGetTransitionParameters<T>(out T parameters) where T : class => _transitionParameters!.TryCast<T>(out parameters);

        public T GetTransitionParameters<T>() => (T)_transitionParameters!;

        private AsyncOperation LoadSceneAsync(JxSceneConfiguration configuration) => SceneManager.LoadSceneAsync(configuration.Name, LoadSceneMode.Single);

        private async UniTask ExecuteDecoratorsAsync()
        {
            foreach (var sceneTransitionDecorator in _sceneTransitionDecorators)
            {
                try
                {
                    await sceneTransitionDecorator.ExecuteAsync();
                }
                catch (Exception exception)
                {
                    _logger.LogError("Scene transition decorator execution failed", exception);
                }
            }
        }

        private async UniTask ExecuteSceneActivationAsync()
        {
            foreach (var sceneTransitionDecorator in _sceneTransitionDecorators)
            {
                try
                {
                    await sceneTransitionDecorator.ProcessSceneActivationAsync();
                }
                catch (Exception exception)
                {
                    _logger.LogError("Scene transition decorator execution failed", exception);
                }
            }
        }

        private UniTask WaitSceneLoadingAsync(AsyncOperation? asyncOperation) => UniTask.WaitUntil(() => asyncOperation is { progress: >= 0.9f });

        private static void ApplyConfiguration(JxSceneConfiguration configuration)
        {
            Time.fixedDeltaTime = 1f / configuration.FixedUpdateFrequency;
            Time.maximumParticleDeltaTime = 1f / configuration.MaximumParticleFrequency;
            
#if HEADLESS
            Application.runInBackground = true;
            QualitySettings.vSyncCount = 0;
#endif
            Application.targetFrameRate = configuration.TargetFrameRate;
        }
    }
}