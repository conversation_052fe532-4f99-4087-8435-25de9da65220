#if DEV && !HEADLESS
using System;
using System.Collections;
using System.Collections.Generic;
using Api.Client.User;
using App.Offers;
using CodeStage.AdvancedFPSCounter;
using Configs.Game;
using Configs.ServerGameplay;
using Core.Helpers.DebugScreen.SubScreens;
using Core.Helpers.DebugScreen.SubScreens.Audio;
using Core.Helpers.DebugScreen.SubScreens.Bots;
using Core.Helpers.DebugScreen.SubScreens.Disabler;
using Core.Helpers.DebugScreen.SubScreens.Gameplay;
using Core.Helpers.DebugScreen.SubScreens.Gameplay.Section;
using Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.DebugGameplayMenu;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics;
using Core.Helpers.DebugScreen.SubScreens.Meta;
using Core.Helpers.DebugScreen.SubScreens.Log;
using Core.Helpers.DebugScreen.SubScreens.Tutorial;
using Core.Helpers.Navigation;
using Core.Helpers.SceneManagement;
using Core.Loading.Cover;
using Core.UI.Components.Buttons;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Server;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Control;
using Jx.Utils.ApplicationShutdown;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Helpers;
using Jx.Utils.UnityComponentExtensions;
using Managers.Rewards;
using MonsterLand.Meta.Api;
using Savings;
using Tutorial.Runner;
using UI.Screens.TutorialScene.Gameplay;
using Zenject;
#endif

using System.Threading;
using CodeStage.AdvancedFPSCounter.Labels;
using Core.Extensions;
using Core.Helpers.Chests;
using Installers.App;
using Jx.UserRecords;
using Jx.Utils.Objects;
using UnityEngine;
using UnityEngine.UI;

namespace Core.Helpers.DebugScreen
{
    // TODO refactoring's still in progress. Not enough time for
    public class DebugScreenController : MonoBehaviour
#if DEV && !HEADLESS
      , IDebugScreenController
#endif
    {
        [SerializeField]
        private RectTransform _screensContainer = null!;

        [SerializeField]
        private RectTransform _navigationContainer = null!;

        [SerializeField]
        private DebugSelectorButton _goToLogScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToMetaScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToAudioScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToBotsScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToDisablersScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToGameplayScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToTutorialScreenButton = null!;

        [SerializeField]
        private DebugSelectorButton _goToGameplayAnalyticsButton = null!;

        [SerializeField]
        private Button _debugScreenButton = null!;

        [SerializeField]
        private Toggle _enabledOnStartToggle = null!;

        [SerializeField]
        private Button _completeTutorialButton = null!;

#if DEV && !HEADLESS

        public static IDebugScreenController? Instance { get; private set; }

        private bool _enabled;
        private JxDisposableAction? _disposeOnChangeStatus;
        private IDisposable? _sceneChangeSubscription;
        private JxDisposableAction? _activeSceneObjects;
        private CancellationTokenSource? _gameplayScreenInitializationCts;
        private CancellationTokenSource? _mainMenuInitializationCts;

        private IUserContext _userContext = null!;
        private ILocalSave _localSave = null!;
        private IMonsterLandSceneManager _sceneManager = null!;
        private INavigation _defaultNavigation = null!;

        private DebugScreenNavigation _debugNavigation = null!;

        [Inject]
        private void Inject(
            IUserContext userContext,
            ILocalSave localSave,
            IMonsterLandSceneManager sceneManager,
            INavigation navigation
        )
        {
            _debugNavigation = new DebugScreenNavigation(_screensContainer);

            _userContext = userContext;
            _localSave = localSave;
            _sceneManager = sceneManager;
            _defaultNavigation = navigation;
        }

        public DebugScreenNavigation Navigation => _debugNavigation;

        private void Awake()
        {
            Instance = this;
            DontDestroyOnLoad(this);
            Initialize();

            _sceneChangeSubscription = MonsterLandSceneManager.Instance.CurrentChangeable.SubscribeAndFire(OnCurrentSceneChanged);
        }

        private void OnDestroy()
        {
            _disposeOnChangeStatus?.Dispose();
            _sceneChangeSubscription?.Dispose();
            
            _gameplayScreenInitializationCts.CancelAndDispose();
            _mainMenuInitializationCts.CancelAndDispose();
        }

        private void OnCurrentSceneChanged(MonsterLandSceneType scene)
        {
            _gameplayScreenInitializationCts.CancelAndDispose();
            _mainMenuInitializationCts.CancelAndDispose();
            
            _activeSceneObjects?.Dispose();
            _activeSceneObjects  = JxDisposableAction.Build();

            switch (scene)
            {
                case MonsterLandSceneType.MainMenu:
                    _mainMenuInitializationCts = new CancellationTokenSource();
                    InitializeMainMenuAsync(_mainMenuInitializationCts.Token).Forget();
                    break;
                case MonsterLandSceneType.Match:
                    _gameplayScreenInitializationCts = new CancellationTokenSource();
                    InitializeGameplayScreenAsync(_gameplayScreenInitializationCts.Token).Forget();
                    break;
            }
        }

        private void Initialize()
        {
            _disposeOnChangeStatus = JxDisposableAction.Build();
            _debugScreenButton.gameObject.SetActive(false);

            var audioScreen = _debugNavigation.Preload<DebugAudioScreen>();
            _disposeOnChangeStatus.AppendDispose(audioScreen.Initialize());

            _debugNavigation.Preload<DebugLogScreen>();
            _debugNavigation.Preload<DebugDisablerScreen>();

            _disposeOnChangeStatus.AppendDispose(_goToLogScreenButton.OnClick(GoTo<DebugLogScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToDisablersScreenButton.OnClick(GoTo<DebugDisablerScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToAudioScreenButton.OnClick(GoTo<DebugAudioScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToMetaScreenButton.OnClick(GoTo<DebugMetaScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToGameplayScreenButton.OnClick(GoTo<DebugGameplayScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToBotsScreenButton.OnClick(GoTo<DebugBotsScreen>));
            _disposeOnChangeStatus.AppendDispose(_goToTutorialScreenButton.OnClick(GoTo<DebugTutorialScreen>));
#if DEBUG_GAMEPLAY_ANALYTICS
            _disposeOnChangeStatus.AppendDispose(_goToGameplayAnalyticsButton.OnClick(GoTo<DebugGameplayAnalyticsScreen>));
#else
            _goToGameplayAnalyticsButton.gameObject.SetActive(false);
#endif
            _disposeOnChangeStatus.AppendDispose(_completeTutorialButton.OnClick(OnCompleteGameplayTutorialClicked));

            // ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
            if (_localSave != null)
            {
                if (_localSave.Debug.EnableDebugOnStart)
                {
                    _debugScreenButton.gameObject.SetActive(true);
                }

                _enabledOnStartToggle.onValueChanged.AddListener(OnEnableDebugOnStartToggleChange);
            }

            _debugScreenButton.onClick.AddListener(SwitchDebugScreenShowMode);
        }

        private void UpdateView()
        {
            _navigationContainer.gameObject.SetActive(_enabled);
            _screensContainer.gameObject.SetActive(_enabled);
            if (!_enabled)
            {
                return;
            }

            RenderEnabledOnStartToggle();
            SyncButtonState<DebugLogScreen>(_goToLogScreenButton);
            SyncButtonState<DebugAudioScreen>(_goToAudioScreenButton);
            SyncButtonState<DebugDisablerScreen>(_goToDisablersScreenButton);
            SyncButtonState<DebugMetaScreen>(_goToMetaScreenButton);
            SyncButtonState<DebugGameplayScreen>(_goToGameplayScreenButton);
            SyncButtonState<DebugBotsScreen>(_goToBotsScreenButton);
            SyncButtonState<DebugTutorialScreen>(_goToTutorialScreenButton);
#if DEBUG_GAMEPLAY_ANALYTICS
            SyncButtonState<DebugGameplayAnalyticsScreen>(_goToGameplayAnalyticsButton);
#endif
            RenderCompleteTutorialButton();
        }
        
        private void OnCompleteGameplayTutorialClicked()
        {
#if DEV && !PRODUCTION_BUILD
            if (_defaultNavigation.CurrentNode.Value?.Controller is not TutorialGameplayScreenController controller)
                return;
            
            controller.Debug_CompleteTutorial();
#endif
        }

        private void RenderCompleteTutorialButton()
        {
            var can = _sceneManager.CurrentChangeable.Value == MonsterLandSceneType.Tutorial;
            _completeTutorialButton.gameObject.SetActive(can);
        }

        private void SwitchDebugScreenShowMode()
        {
            _enabled = !_enabled;
            UpdateView();
        }

        private void GoTo<TScreen>()
            where TScreen : DebugSubScreen
        {
            if (_debugNavigation.IsOpen<TScreen>())
            {
                _debugNavigation.Close<TScreen>();
            }
            else
            {
                _debugNavigation.GoTo<TScreen>();
            }

            UpdateView();
        }

        private async UniTaskVoid InitializeMainMenuAsync(CancellationToken cancellationToken)
        {
            while (Condition())
                await UniTask.Delay(500, DelayType.Realtime, cancellationToken: cancellationToken);
            
            _activeSceneObjects!
                .AppendDispose(InitializeBotsScreen())
                .AppendDispose(InitializeMetaScreen())
                .AppendDispose(InitializeTutorialScreen());
            
            return;
            
            static bool Condition() => !AppInstaller.CurrentScope.Value.HasBinding<IChestInteraction>();
        }

        private async UniTaskVoid InitializeGameplayScreenAsync(CancellationToken cancellationToken)
        {
            while (Condition())
                await UniTask.Delay(500, DelayType.Realtime, cancellationToken: cancellationToken);

            _activeSceneObjects!.AppendDispose(InitializeGameplayScreen());
#if DEBUG_GAMEPLAY_ANALYTICS
            _activeSceneObjects!.AppendDispose(InitializeGameplayAnalytics());
#endif
            
            return;

            static bool Condition() => !AppInstaller.CurrentScope.Value.HasBinding<ILocalGameplayContext>();
        }

#if DEBUG_GAMEPLAY_ANALYTICS
        private IDisposable InitializeGameplayAnalytics()
        {
            var diScope = AppInstaller.CurrentScope.Value;
            var localContext = diScope.Resolve<ILocalGameplayContext>();
            var manager = diScope.Resolve<IDebugGameplayAnalyticsItemViewManager>();
            
            return InitializeScreen<DebugGameplayAnalyticsScreen>(
                s => s.Initialize(localContext, manager), 
                s => s.Cleanup()
            );
        }
#endif

        private IDisposable InitializeGameplayScreen()
        {
            var diScope = AppInstaller.CurrentScope.Value;

            var localContext = diScope.Resolve<ILocalGameplayContext>();
            var gameplayContextGetter = diScope.Resolve<IGameplayServerContextGetter>();

            var initCancellation = new CancellationTokenSource();
            var initStop = initCancellation.ToDisposable();
            IDisposable? screenInstance = null;
            
            InitAsync(initCancellation.Token).Forget();

            return JxDisposableAction.Build()
                                     .AppendDispose(initStop)
                                     .AppendCallback(() => screenInstance?.Dispose());

            async UniTask InitAsync(CancellationToken cancellationToken)
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    if (ReferenceEquals(localContext.Player, null) || !JxNetworkServer.Instance.IsActive)
                    {
                        await UniTask.Delay(TimeSpan.FromSeconds(1f));
                        continue;
                    }
                    
                    var gameplayContext = gameplayContextGetter.Get(((NetGamePlayer)localContext.Player).MatchId);
                
                    screenInstance = InitializeScreen<DebugGameplayScreen>(
                        initialize: s => s.Initialize(
                            new DebugGameplayMenuContext(
                                gameplayContext,
                                _userContext,
                                _localSave,
                                localContext
                            )
                        ),
                        deinitialize: s => s.Cleanup()
                    );

                    break;
                }
            }
        }

        private IDisposable InitializeTutorialScreen()
        {
            var diScope = AppInstaller.CurrentScope.Value;
            
            return InitializeScreen<DebugTutorialScreen>(
                initialize: s => s.Initialize(
                    _localSave,
                    diScope.Resolve<IGameplayTutorialRunner>(),
                    diScope.Resolve<IUserContext>()
                ),
                deinitialize: s => s.Cleanup()
            );
        }

        private IDisposable InitializeMetaScreen()
        {
            var diScope = AppInstaller.CurrentScope.Value;
            
            return InitializeScreen<DebugMetaScreen>(
                initialize: s => s.Initialize(
                    _userContext,
                    _localSave,
                    diScope.Resolve<IJxMonsterLandMetaApi>(),
                    diScope.Resolve<IGameConfigProvider>(),
                    diScope.Resolve<IJxApplicationShutdownHandler>(),
                    diScope.Resolve<ILoadingCoverController>(),
                    diScope.Resolve<IJxOfferIntegration>(),
                    diScope.Resolve<IRewardPresenter>(),
                    diScope.Resolve<IChestInteraction>(),
                    diScope.Resolve<IJxUserRecordStore>(),
                    diScope
                ),
                deinitialize: s => s.Cleanup()
            );
        }

        public void RegisterDebugComponentDisabler(BaseDebugDisablerComponent component)
        {
            if (!_userContext.IsDebugUser.Value)
                return;

            var screen = _debugNavigation.GetOrNull<DebugDisablerScreen>();

            if (screen.IsNullObj())
                return;

            screen!.RegisterDebugComponentDisabler(component);
        }

        private IDisposable InitializeBotsScreen()
        {
            return InitializeScreen<DebugBotsScreen>(
                initialize: s => s.Initialize(_localSave),
                deinitialize: s => s.Cleanup()
            );
        }

        public void UnRegisterDebugComponentDisabler(BaseDebugDisablerComponent component)
        {
            if (!_userContext.IsDebugUser.Value)
                return;

            var screen = _debugNavigation.GetOrNull<DebugDisablerScreen>();

            if (screen.IsNullObj())
                return;

            screen!.UnRegisterDebugComponentDisabler(component);
        }

        private void OnEnableDebugOnStartToggleChange(bool isToggled)
        {
            _localSave.Change(m => m.Debug.EnableDebugOnStart = isToggled);
        }

        private IDisposable InitializeScreen<TScreen>(Action<TScreen> initialize, Action<TScreen> deinitialize)
            where TScreen : DebugSubScreen
        {
            var screen = _debugNavigation.Preload<TScreen>();
            initialize(screen);

            UpdateView();

            return JxDisposableAction.Build().AppendCallback(
                () =>
                {
                    deinitialize(screen);
                    _debugNavigation.UnLoad<TScreen>();
                    UpdateView();
                }
            );
        }

        private void SyncButtonState<TScreen>(DebugSelectorButton button)
            where TScreen : DebugSubScreen
        {
            button.SetActiveStatus(_debugNavigation.IsOpen<TScreen>());
            button.gameObject.SetActive(_debugNavigation.IsLoaded<TScreen>());
        }

        private void RenderEnabledOnStartToggle()
        {
            _enabledOnStartToggle.gameObject.SetActive(_enabled);
            _enabledOnStartToggle.isOn = _localSave?.Debug.EnableDebugOnStart ?? false;
        }

        public void SwitchDebugButton()
        {
            // var enableDebugButton = !_debugScreenButton.gameObject.activeSelf || _enabled || _localSave?.Debug.EnableDebugOnStart == true;

            SwitchDebugScreenShowMode();
            var enableDebugButton = _enabled;

            _debugScreenButton.gameObject.SetActive(enableDebugButton);
            SetActiveFpsCounter(enableDebugButton);
        }

        private AFPSCounter? _fpsCounter;

        private void SetActiveFpsCounter(bool isActive)
        {
            if (isActive && ReferenceEquals(_fpsCounter, null))
            {
                _fpsCounter = AFPSCounter.AddToScene(keepAlive: true);
                _fpsCounter.fpsCounter.Style = FontStyle.Bold;
                _fpsCounter.deviceInfoCounter.Enabled = false;
                _fpsCounter.fpsCounter.Render = true;

                _fpsCounter.fpsCounter.Anchor = LabelAnchor.UpperCenter;
                _fpsCounter.memoryCounter.Anchor = LabelAnchor.UpperCenter;
                _fpsCounter.deviceInfoCounter.Anchor = LabelAnchor.UpperCenter;
            }

            if (!ReferenceEquals(_fpsCounter, null))
                _fpsCounter.OperationMode = isActive ? OperationMode.Normal : OperationMode.Disabled;
        }
#endif
    }
}