using System;
using System.Text;
using Audio.Debug;
using Core.Extensions;
using Jx.Utils.Helpers;
using TMPro;
using UnityEngine;

namespace Core.Helpers.DebugScreen.SubScreens.Audio
{
    public class DebugAudioScreen : DebugSubScreen
    {
#if DEV
        private const int MaxEventsAtScreen = 34;
        private const int MaxMessageLength = 66;
        
        private readonly StringBuilder _stringBuilder = new StringBuilder(400);

        private static readonly string _warningColorHex = ColorUtility.ToHtmlStringRGBA(new Color(122f, 255f, 202f, 1f).NormalizeRGB());
        private static readonly string _errorColorHex = ColorUtility.ToHtmlStringRGBA(Color.red);
        private static readonly string _listenerColorHer = ColorUtility.ToHtmlStringRGBA(Color.yellow);

        private int _eventCount;
#endif
        
        [SerializeField]
        private TextMeshProUGUI _log = null!;

        public IDisposable Initialize()
        {
#if DEV
            return DebugAudioAnalytics.Instance.SubscribeToLog(OnEventEmit);
#endif
            return JxDisposableAction.Empty;
        }

#if DEV
        private void OnEventEmit(string message)
        {
            if (_eventCount > MaxEventsAtScreen)
            {
                _stringBuilder.Clear();
                _eventCount = 0;
            }

            ++_eventCount;

            if (message.Length > MaxMessageLength)
            {
                message = $"{message[..(MaxMessageLength - 2)]}..";
            }

            if (message.Contains("ERROR", StringComparison.Ordinal))
            {
                message = message.ColoredHtml(_errorColorHex);
            }
            
            if (message.Contains("REM", StringComparison.Ordinal))
            {
                message = message.ColoredHtml(_warningColorHex);
            }

            if (message.Contains("LISTENER", StringComparison.Ordinal))
            {
                message = message.ColoredHtml(_listenerColorHer);
            }
            
            _stringBuilder.AppendLine(message);
            Rebuild();
        }

        private void Rebuild()
        {
            _log.text = _stringBuilder.ToString();
        }
#endif
    }
}