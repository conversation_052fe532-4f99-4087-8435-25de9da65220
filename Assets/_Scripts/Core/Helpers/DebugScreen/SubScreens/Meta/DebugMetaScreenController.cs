using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Stats;
using Api.Client.User.TrophyPath;
using App.Offers;
using Configs.Game;
using Core.Extensions;
using Core.Helpers.Chests;
using Core.Loading.Cover;
using Core.Managers.MapSelection;
using Core.UI.Components.Buttons;
using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using GameplayComponents.Bots;
using GameplayNetworking.Gameplay.Player.Components.PublicClientInfo;
using GameplayNetworking.Share.Character;
using Jx.UserRecords;
using Jx.Utils.ApplicationShutdown;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using Jx.Utils.UnityComponentExtensions;
using LoM.Characters.ClientIntegration;
using LoM.Messaging.ClientIntegrations;
using LoM.Messaging.ClientIntegrations.Stats;
using LoM.Messaging.Requests.Debug;
using Managers.Rewards;
using MonsterLand.Matchmaking;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Matchmaking.Dto.Maps;
using MonsterLand.Meta.Api;
using Savings;
using Savings.Data;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using Zenject;

namespace Core.Helpers.DebugScreen.SubScreens.Meta
{
    public class DebugMetaScreenController : DebugSubScreenController
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(DebugMetaScreenController));

        private readonly DebugMetaScreen _view;
        private readonly IUserContext _userContext;
        private readonly ILocalSave _localSave;
        private readonly IJxMonsterLandMetaApi _clientApi;
        private readonly IGameConfigProvider _gameGameConfigProvider;
        private readonly IJxApplicationShutdownHandler _shutdownHandler;
        private readonly ILoadingCoverController _loadingCoverController;
        private readonly IJxOfferIntegration _offers;
        private readonly IRewardPresenter _rewardPresenter;
        private readonly IChestInteraction _chestInteraction;
        private readonly IJxUserRecordStore _userRecordStore;
        private readonly DiContainer _diScope;

        private JxDisposableAction _disposeOnDetach = null!;

        private IDisposable? _mapSelectSubscription;

        public DebugMetaScreenController(
            DebugMetaScreen view,
            IUserContext userContext,
            ILocalSave localSave,
            IJxMonsterLandMetaApi clientApi,
            IGameConfigProvider gameConfigProvider,
            IJxApplicationShutdownHandler shutdownHandler,
            ILoadingCoverController loadingCoverController,
            IJxOfferIntegration offers,
            IRewardPresenter rewardPresenter,
            IChestInteraction chestInteraction,
            IJxUserRecordStore userRecordStore,
            DiContainer diScope
        )
        {
            _view = view;
            _userContext = userContext;
            _localSave = localSave;
            _clientApi = clientApi;
            _gameGameConfigProvider = gameConfigProvider;
            _shutdownHandler = shutdownHandler;
            _loadingCoverController = loadingCoverController;
            _offers = offers;
            _rewardPresenter = rewardPresenter;
            _chestInteraction = chestInteraction;
            _userRecordStore = userRecordStore;
            _diScope = diScope;
            
            _previousGamemode = _localSave.Value.GameMode;
        }

        public override void OnViewAttached()
        {
            _disposeOnDetach = JxDisposableAction.Build();

            if (!_userContext.IsDebugUser.Value)
            {
                _logger.LogError("Trying to use debug mode wihout debug rights.");
                return;
            }

            _disposeOnDetach.AppendDispose(_view.CompleteMissionsChapterButton.OnClick(OnCompleteMissionsChapterButtonClick));
            _disposeOnDetach.AppendDispose(_view.CompleteOneMissionChapterButton.OnClick(OnCompleteOneMissionButtonClick));

            _disposeOnDetach.AppendDispose(_view.LoadTestButton.OnClick(RunLoadTestAsync));
            _disposeOnDetach.AppendDispose(_view.ShowAllOffersButton.OnClick(ShowAllOffersAsync));
            _disposeOnDetach.AppendDispose(_view.TimeSkipButton.OnClick(SkipTimeAsync));
            _disposeOnDetach.AppendDispose(_view.RefillChestsButton.OnClick(RefillChestsAsync));
            _disposeOnDetach.AppendDispose(_view.GiveMvpButton.OnClick(() => GiveMatchAsync(isMvp: true, isWinner: true)));
            _disposeOnDetach.AppendDispose(_view.WinButton.OnClick(() => GiveMatchAsync(isMvp: false, isWinner: true)));
            _disposeOnDetach.AppendDispose(_view.LoseButton.OnClick(() => GiveMatchAsync(isMvp: false, isWinner: false)));
            _disposeOnDetach.AppendDispose(_view.CompleteAllDailyQuestsButton.OnClick(CompleteAllDailyQuestsAsync));
            _disposeOnDetach.AppendDispose(_view.GiveTokensButton.OnClick(GiveTokensAsync));
            _disposeOnDetach.AppendDispose(_view.GiveFullProgressButton.OnClick(GiveFullProgressAsync));
            _disposeOnDetach.AppendDispose(_view.SetResetGoldenPassButton.OnClick(SetResetGoldenPassAsync));
            _disposeOnDetach.AppendDispose(_clientApi.User.SubscribeAndFire(DebugRerender));
            _disposeOnDetach.AppendDispose(_userContext.Stats.Value.SubscribeAndFireOnMainThread(RenderPlayedMatchCount));
            _disposeOnDetach.AppendDispose(_view.ClaimAllTrophyPathRewardsButton.OnClick(OnClaimAllTrophyPathRewardsAsync));
            _disposeOnDetach.AppendDispose(_view.DeleteAccountButton.OnClick(OnDeleteAccountClickAsync));
            _disposeOnDetach.AppendDispose(_view.ClearLocalDataButton.OnClick(OnClearLocalDataClick));
            _disposeOnDetach.AppendDispose(_view.ClaimCurrentTrophyPathRewardButton.OnClick(ClaimCurrentTrophyPathRewardAsync));
            _disposeOnDetach.AppendDispose(_localSave.SubscribeAndFire(OnLocalSaveChange));
            _disposeOnDetach.AppendDispose(_view.ToggleSeasonPass.OnClick(() => ProcessSimpleActionAsync(DebugSimpleActionType.SeasonPassChangePremium).Forget()));
            _disposeOnDetach.AppendDispose(_view.GiveXpSeasonPass.OnClick(() => ProcessSimpleActionAsync(DebugSimpleActionType.SeasonPassAddXp).Forget()));
            _disposeOnDetach.AppendDispose(_view.ResetSeasonPass.OnClick(() => ProcessSimpleActionAsync(DebugSimpleActionType.SeasonPassReset).Forget()));
            _disposeOnDetach.AppendDispose(_view.GiveFullProgressSeasonPass.OnClick(() => ProcessSimpleActionAsync(DebugSimpleActionType.CompleteSeasonPass).Forget()));
            _disposeOnDetach.AppendDispose(_userContext.SeasonPass.Interaction.IsPremiumUser.SubscribeAndFire(OnSeasonPassPremiumStatusChange));

            _view.MmrInputField.text = _localSave.Debug.Mmr == null ? "default" : ((int)(_localSave.Debug.Mmr)).ToString();
            _view.MmrInputField.onValueChanged.AddListener(OnMmrChanged);
            _disposeOnDetach.AppendDispose(JxDisposableAction.Build().AppendCallback(() => _view.MmrInputField.onValueChanged.RemoveListener(OnMmrChanged)));

            SetupPresetViewAsync().Forget();
            SetupGiveMatchTeamKindDropdown();
            RenderBotDifficulty();
            
#if INSPECT_LOCALIZATION_KEYS
            _view.ShowLocalizationToggle.interactable = true;
            _disposeOnDetach.AppendDispose(_view.ShowLocalizationToggle.onValueChanged.Subscribe(OnShowLocalizationToggle));
#else
            _view.ShowLocalizationToggle.interactable = false;
#endif

            // #if UNITY_EDITOR || UNITY_STANDALONE_WIN
            //             _view.SpawnBotsInBackgroundRectTransform.gameObject.SetActive(true);
            //
            //             _disposeOnDetach.AppendDispose(
            //                 SynchronizeWithToggle(
            //                     _view.SpawnBotsInBackgroundToggle,
            //                     _ => _localSave.Debug.StartBackgroundMatchOnHost,
            //                     (m, v) => m.Debug.StartBackgroundMatchOnHost = v
            //                 )
            //             );
            // #else
            //             _view.SpawnBotsInBackgroundRectTransform.gameObject.SetActive(false);
            // #endif
        }

        private void OnSeasonPassPremiumStatusChange(bool isActive)
        {
            _view.ActivatedSeasonPassContent.gameObject.SetActive(isActive);
            _view.ActivatedSeasonPassText.text = isActive ? "DeActivate\nseason pass premium" : "Activate\nseason pass premium";
        }

        private static void OnShowLocalizationToggle(bool isOn)
        {
            DebugRuntimeVariables.ShowTextLocalization = isOn;
        }

        private void OnLocalSaveChange(ILocalSaveModel _)
        {
            _mapSelectSubscription?.Dispose();
            SetupMapSelectionDropDown();
        }

        public override void OnViewDetached()
        {
            _disposeOnDetach?.Dispose();
            _mapSelectSubscription?.Dispose();
        }

        private async UniTaskVoid ClaimCurrentTrophyPathRewardAsync()
        {
            using (_loadingCoverController.Show())
            {
                var item = _userContext.TrophyPath.Interaction.Items.FirstOrDefault(i => i.Status.Value == TrophyPathItemStatus.RewardReady);

                if (item == null)
                    return;

                await item.ClaimAsync();
                await _chestInteraction.ClaimAllPendingChestRewardsAsync();
                _rewardPresenter.MarkDisplayed();
            }
        }

        private void OnClearLocalDataClick()
        {
            using (_loadingCoverController.Show())
            {
                _localSave.Clear();
            }

            _shutdownHandler.ShutdownAsync().Forget();
        }

        private async UniTaskVoid OnDeleteAccountClickAsync()
        {
            try
            {
                using (_loadingCoverController.Show())
                {
                    var deleted = await _userContext.ResetAsync();

                    if (deleted)
                    {
                        _userRecordStore.ClearAll();
                        _localSave.Clear();
                    }
                }
            }
            catch (Exception exception)
            {
                _logger.LogError("Delete account exception", exception);
            }

            _shutdownHandler.ShutdownAsync().Forget();
        }

        private async UniTaskVoid OnClaimAllTrophyPathRewardsAsync()
        {
            using (_loadingCoverController.Show())
            {
                foreach (var item in _userContext.TrophyPath.Interaction.Items.Where(Filter))
                {
                    if (item == null)
                        continue;

                    await item.ClaimAsync();
                    await _chestInteraction.ClaimAllPendingChestRewardsAsync();
                    _rewardPresenter.MarkDisplayed();

                    var itemLost = _userContext.TrophyPath.Interaction.Items.Count - _userContext.TrophyPath.Interaction.Items.Count(Filter);
                    var progress01 = (float)itemLost / _userContext.TrophyPath.Interaction.Items.Count;
                    _view.SetProgress(progress01, itemLost.ToString());
                }
            }

            return;

            static bool Filter(ITrophyPathItem item) => item.Status.Value.IsClaimable();
        }

        private void RenderPlayedMatchCount(UserStatsClientIntegration stats)
        {
            _view.PlayedMatchCountText.text = $"Matches: {stats.GetPlayedMatchCount()}";
        }

        private void DebugRerender(UserDataClientIntegration _)
        {
            RenderBotDifficulty();

            var isGoldenPassActive = _userContext.Billing.GoldenPassState.IsActive.Value;
            _view.SetResetGoldenPassText.text = isGoldenPassActive ? "DeActivate\n Golden pass" : "Activate\n Golden Pass";
            _view.GoldenPassStyleSetter.SetStyle(isGoldenPassActive ? LoMButtonStyle.Blue : LoMButtonStyle.Green);
        }

        private async UniTaskVoid SetResetGoldenPassAsync()
        {
            var success = _userContext.Billing.GoldenPassState.IsActive.Value
                ? await ProcessSimpleActionAsync(DebugSimpleActionType.ResetGoldenPass)
                : await ProcessSimpleActionAsync(DebugSimpleActionType.GiveGoldenPass);

            ToastManager.Instance.Show(success ? "Success" : "Error");

            await _clientApi.SyncUserAsync();
        }

        private async void RenderBotDifficulty()
        {
            try
            {
                var escapeeDifficultyName = "unknown";
                var catcherDifficultyName = "unknown";

                var gameplayConfigContainer = await _gameGameConfigProvider.Gameplay.GetAsync(JxMonsterLandGameModeKind.Turbo);
                var gameplayConfig = gameplayConfigContainer.Value;
                if (gameplayConfig != null)
                {
                    var escapeeMmr = MmrCalculator.Instance.Get(_clientApi.User.Value, _localSave.Value.SelectedEscapeeCharacterIndex);
                    escapeeDifficultyName =
                        gameplayConfig.Bots.GetDifficultyProfile(JxMatchmakingTeamKind.Escapee, escapeeMmr, JxMatchmakingTeamKind.Escapee, 0).Name +
                        $"|{escapeeMmr}";

                    var catcherMmr = MmrCalculator.Instance.Get(_clientApi.User.Value, _localSave.Value.SelectedCatcherCharacterIndex);
                    catcherDifficultyName =
                        gameplayConfig.Bots.GetDifficultyProfile(JxMatchmakingTeamKind.Catcher, catcherMmr, JxMatchmakingTeamKind.Escapee, 0).Name +
                        $"|{catcherMmr}";
                }

                _view.EscapeeDifficulty.text = $"Escapee bot difficulty: {escapeeDifficultyName}";
                _view.CatcherDifficulty.text = $"Catcher bot difficulty: {catcherDifficultyName}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
        }

        private void OnCompleteMissionsChapterButtonClick()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.CompleteCurrentChapterMissions).Forget();
        }

        private void OnCompleteOneMissionButtonClick()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.CompleteCurrentMission).Forget();
        }

        private UniTaskVoid RefillChestsAsync()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.RefillChests).Forget();
            return default;
        }

        private async UniTaskVoid ResetProgressOnlyAsync()
        {
            var success = await ProcessSimpleActionAsync(DebugSimpleActionType.ResetProgressOnly);
            if (success)
            {
                _localSave.Clear();
                _shutdownHandler.ShutdownAsync().Forget();
            }
        }

        private async UniTaskVoid GiveMatchAsync(bool isMvp, bool isWinner)
        {
            var team = _view.GiveMatchTeamDropdown.value == 0 ? TeamKind.Catcher : TeamKind.Escapee;
            var characterIndex = team == TeamKind.Escapee ? _localSave.Value.SelectedEscapeeCharacterIndex : _localSave.Value.SelectedCatcherCharacterIndex;

            using (_loadingCoverController.Show())
            {
                var success = await _userContext.Debug.GiveMatchAsync(team, characterIndex, isWinner, isMvp);
                ToastManager.Instance.Show(success ? "Success" : "Error");
            }

            RenderBotDifficulty();
        }

        private UniTaskVoid GiveFullProgressAsync()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.UpgradeProgress).Forget();
            return default;
        }

        private UniTaskVoid GiveTokensAsync()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.GiveTokens).Forget();
            return default;
        }

        private UniTaskVoid CompleteAllDailyQuestsAsync()
        {
            ProcessSimpleActionAsync(DebugSimpleActionType.CompleteAllDailyQuests).Forget();
            return default;
        }

        private void OnMmrChanged(string value)
        {
            if (int.TryParse(value, out var mmr))
            {
                _localSave.Change((m) => m.Debug.Mmr = mmr > 0 ? mmr : null);
            }
        }

        private async UniTaskVoid SkipTimeAsync()
        {
            if (int.TryParse(_view.TimeSkipInputField.text, out var minutes))
            {
                using (_loadingCoverController.Show())
                {
                    var success = await _userContext.Debug.SkipTimeAsync(TimeSpan.FromMinutes(minutes));
                    var postfix = success ? "success" : "fail";
                    // debug show, no provider
                    ToastManager.Instance.Show($"Time skipping {postfix}");
                }
            }
            else
            {
                ToastManager.Instance.Show("Specify skip time in minutes as integer value");
            }
        }

        private readonly JxAtomicFlag _loadTestIsRunning = new JxAtomicFlag(false);

        private async UniTaskVoid RunLoadTestAsync()
        {
            if (!_loadTestIsRunning.TrySet())
                return;

            using (_loadingCoverController.Show())
            {
                try
                {
                    var stopwatch = new Stopwatch();
                    stopwatch.Start();

                    var (success, requestCount) = await _userContext.Debug.RunLoadTestAsync();
                    stopwatch.Stop();

                    if (success)
                        ToastManager.Instance.Show($"SUCCESS[requests:{requestCount} duration:{(int)stopwatch.Elapsed.TotalSeconds}s]", toastDuration: 40f);
                    else
                        ToastManager.Instance.Show($"FAIL[requests:{requestCount} duration:{(int)stopwatch.Elapsed.TotalSeconds}s]", toastDuration: 40f);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex);
                }
                finally
                {
                    _loadTestIsRunning.TryReset();
                }
            }
        }

        private UniTaskVoid ShowAllOffersAsync()
        {
            _offers.DebugShowAllFullScreenOneByOneAsync(_diScope).Forget();
            return default;
        }

        private async UniTask<bool> ProcessSimpleActionAsync(DebugSimpleActionType actionType)
        {
            using (_loadingCoverController.Show())
            {
                var success = await _userContext.Debug.ProcessSimpleActionAsync(actionType);
                ToastManager.Instance.Show(success ? "Success" : "Error");

                return success;
            }
        }

        #region Give Match

        private bool _giveMatchDropdownInitialized;

        private void SetupGiveMatchTeamKindDropdown()
        {
            if (_giveMatchDropdownInitialized)
            {
                return;
            }

            _view.GiveMatchTeamDropdown.options = new List<TMP_Dropdown.OptionData>
            {
                new TMP_Dropdown.OptionData
                {
                    text = TeamKind.Catcher.ToString(),
                },
                new TMP_Dropdown.OptionData
                {
                    text = TeamKind.Escapee.ToString(),
                },
            };

            _view.GiveMatchTeamDropdown.value = 0;

            _giveMatchDropdownInitialized = true;
        }

        #endregion

        #region MAP SELECTION

        private static readonly IReadOnlyDictionary<JxMonsterLandGameModeKind, JxMonsterLandMap[]> _mapsByGameMode
            = new Dictionary<JxMonsterLandGameModeKind, JxMonsterLandMap[]>()
            {
                [JxMonsterLandGameModeKind.Classic] = new JxMonsterLandMap[]
                {
                    JxMonsterLandMap.BigDarkIsland,
                    JxMonsterLandMap.BigWinterIsland,
                },
                [JxMonsterLandGameModeKind.Turbo] = new JxMonsterLandMap[]
                {
                    JxMonsterLandMap.SmallDarkIsland1,
                    JxMonsterLandMap.SmallDarkIsland2,
                    JxMonsterLandMap.SmallWinterIsland1,
                    JxMonsterLandMap.SmallWinterIsland2,
                }
            };

        private JxMonsterLandGameModeKind _previousGamemode;

        private void SetupMapSelectionDropDown()
        {
            var gameMode = _localSave.Value.GameMode;
            if (_previousGamemode != gameMode)
                _localSave.Debug.Map = null;

            _previousGamemode = gameMode;
            if (!_mapsByGameMode.TryGetValue(gameMode, out var maps))
            {
                _view.GameModeName.text = "FAILED to load gamemode maps";
                _view.GameModeName.color = Color.red;
                return;
            }

            var totalMapsCount = _mapsByGameMode.Values.Sum(v => v.Length);
            _view.GameModeName.text = $"{gameMode} GameMode\n Maps: ({maps.Length}/{totalMapsCount})";
            _view.GameModeName.color = Color.white;

            var mapNameByType = maps
                .ToDictionary(t => t, t => t.ToString());

            var options = new List<TMP_Dropdown.OptionData> { new("NULL") };
            options.AddRange(mapNameByType.Select(a => new TMP_Dropdown.OptionData(a.Value)));

            _view.MapSelectionDropdown.options = options;

            _mapSelectSubscription = SubscribeOnMapSelectedDropDownValueChange(_ => ChangeMap());

            var savedMap = _localSave.Debug.Map.HasValue && mapNameByType.TryGetValue(_localSave.Debug.Map.Value, out var mapName) ? mapName : null;

            if (string.IsNullOrEmpty(savedMap))
            {
                _view.MapSelectionDropdown.value = 0;
                return;
            }

            var savedOptionData = _view.MapSelectionDropdown.options.FirstOrDefault(o => o.text == savedMap);
            if (savedOptionData == null)
                return;

            _view.MapSelectionDropdown.value = _view.MapSelectionDropdown.options.IndexOf(savedOptionData);

            return;

            void ChangeMap()
            {
                if (_view.MapSelectionDropdown.value == 0)
                {
                    _localSave.Change(s => s.DebugOptions.Map = null);
                    return;
                }

                var selectedMapName = _view.MapSelectionDropdown.options.ElementAtOrDefault(_view.MapSelectionDropdown.value)?.text ?? string.Empty;
                var mapType = mapNameByType.FirstOrDefault(a => a.Value == selectedMapName).Key;

                _localSave.Change(s => s.DebugOptions.Map = mapType);
            }
        }

        private IDisposable SubscribeOnMapSelectedDropDownValueChange(UnityAction<int> callback)
        {
            _view.MapSelectionDropdown.onValueChanged.AddListener(callback);

            return JxDisposableAction.Build().AppendCallback(() => _view.MapSelectionDropdown.onValueChanged.RemoveListener(callback));
        }

        #endregion

        private async UniTaskVoid SetupPresetViewAsync()
        {
            var presetList = await _userContext.Preset.LoadPresetListAsync();

            _view.PresetDropdown.options = presetList.Select(pn => new TMP_Dropdown.OptionData(pn)).ToList();
            _view.PresetDropdown.value = presetList.ToList().IndexOf(_userContext.Preset.Value.Value.Name);

            _disposeOnDetach.AppendDispose(_view.ApplyPresetButton.OnClick(ChangePresetAsync));

            async UniTaskVoid ChangePresetAsync()
            {
                var selectedPreset = _view.PresetDropdown.options[_view.PresetDropdown.value].text;
                var success = await _userContext.Preset.ChangePresetAsync(selectedPreset);

                if (!success)
                {
                    _logger.LogError("Preset change failed");
                    return;
                }

                _shutdownHandler.ShutdownAsync().Forget();
            }
        }
    }
}