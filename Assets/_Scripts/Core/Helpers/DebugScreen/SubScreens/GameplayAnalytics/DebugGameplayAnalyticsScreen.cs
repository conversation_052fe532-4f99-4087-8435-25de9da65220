using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics.Buffs;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics.InteractionStats;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics.PlayerStats;
using GameplayNetworking.Gameplay.Local;
using UnityEngine;

namespace Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics
{
    public class DebugGameplayAnalyticsScreen : DebugSubScreen
    {
        [field: SerializeField]
        public DebugPlayerStatsGameplayAnalyticsContainer PlayerStats { get; private set; } = null!;
        
        [field: SerializeField]
        public DebugInteractionStatsGameplayAnalyticsContainer InteractionStats { get; private set; } = null!;

        [field: SerializeField]
        public DebugBuffsGameplayAnalyticsContainer BuffsStats { get; private set; } = null!;

        private DebugGameplayAnalyticsScreenController? _controller;

#if DEBUG_GAMEPLAY_ANALYTICS
         protected override void OnEnable()
        {
            base.OnEnable();
            _controller?.OnViewAttached();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            _controller?.OnViewDetached();
        }

        public void Initialize(ILocalGameplayContext localGameplayContext,
                               IDebugGameplayAnalyticsItemViewManager manager)
        {
            _controller = new DebugGameplayAnalyticsScreenController(this, localGameplayContext, manager);
            
            _controller.OnViewDetached();
            _controller.OnViewAttached();
        }
        
        public void Cleanup()
        {
            _controller?.OnViewDetached();
            _controller?.DeInitialize();
            _controller = null;
        }
#endif
    }
}