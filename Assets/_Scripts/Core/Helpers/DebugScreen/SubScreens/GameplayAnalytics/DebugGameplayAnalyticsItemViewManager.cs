#if DEBUG_GAMEPLAY_ANALYTICS
using System;
using Core.Managers.AssetLoader;
using Core.Utils.Pooling;
using Jx.Utils.Coroutines;
using UnityEngine;
using UnityEngine.Scripting;
using Object = UnityEngine.Object;

namespace Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics
{
    [Preserve]
    public class DebugGameplayAnalyticsItemViewManager : IDebugGameplayAnalyticsItemViewManager
    {
        private const string Path = "Debug/SubScreens/GameplayAnalytics/AnalyticsItem";

        private DebugGameplayAnalyticsItemView _prefab = null!;

        [Preserve]
        public DebugGameplayAnalyticsItemViewManager()
        {
        }

        public DebugGameplayAnalyticsItemView Get(RectTransform container, DebugGameplayAnalyticsItem item)
        {
            if (_prefab == null || _prefab.IsDestroyed())
                _prefab = JxResourceLoader.Instance.LoadPrefab<DebugGameplayAnalyticsItemView>(Path) ?? throw new NullReferenceException();
            
            var view = Object.Instantiate(_prefab, container, false);
            view.Setup(item);
            view.gameObject.SetActive(true);
            return view;
        }

        public void Release(DebugGameplayAnalyticsItemView view)
        {
            if (view.IsDestroyed())
                return;
            
            view.Cleanup();
            Object.Destroy(view.gameObject);
        }
    }
}
#endif