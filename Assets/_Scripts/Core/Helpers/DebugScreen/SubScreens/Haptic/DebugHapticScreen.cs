using Core.UI.Components.Buttons;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Core.Helpers.DebugScreen.SubScreens.Haptic
{
    public class DebugHapticScreen : DebugSubScreen
    {
        [field: SerializeField]
        public RectTransform MainContent { get; private set; } = null!;
        
        [field: SerializeField]
        public RectTransform NotAvailableContent { get; private set; } = null!;

        [field: SerializeField]
        public TextMeshProUGUI NotAvailableText { get; private set; } = null!;
        
        [field: SerializeField]
        public ButtonComponent PlayButton { get; private set; } = null!;
        
        [field: SerializeField]
        public ButtonComponent StopButton { get; private set; } = null!;
        
        [field: SerializeField]
        public TMP_Dropdown Dropdown { get; private set; } = null!;
        
        [field: SerializeField]
        public Slider AmplitudeSlider { get; private set; } = null!;
        
        [field: SerializeField]
        public Slider FrequencySlider { get; private set; } = null!;
        
        [field: SerializeField]
        public TMP_InputField DurationInputField { get; private set; } = null!;
        
        [field: SerializeField]
        public ButtonComponent ManualPlayButton { get; private set; } = null!;
        
        private DebugHapticScreenController? _controller;
        
        protected override void OnEnable()
        {
            base.OnEnable();
            
            _controller?.OnViewAttached();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            _controller?.OnViewDetached();
        }

        public void Initialize()
        {
            _controller = new DebugHapticScreenController(
                this
            );
        }
        
        public void Cleanup()
        {
            _controller?.OnViewDetached();
            _controller = null;
        }
    }
}