using System.Collections.Generic;
using System.Linq;
using Core.Haptic;
using Jx.Utils.Helpers;
using Jx.Utils.UnityComponentExtensions;
using Lofelt.NiceVibrations;
using TMPro;

namespace Core.Helpers.DebugScreen.SubScreens.Haptic
{
    public class DebugHapticScreenController
    {
        private readonly DebugHapticScreen _view;

        private string[] _allPatterns = null!;

        private JxDisposableAction _disposeOnDetach = null!;

        public DebugHapticScreenController(
            DebugHapticScreen view)
        {
            _view = view;
        }
        
        public void OnViewAttached()
        {
            _disposeOnDetach = JxDisposableAction.Build();
            
            var isAvailable = JxHapticManager.Instance.IsSupportedOnDevice;
            _view.MainContent.gameObject.SetActive(isAvailable);
            _view.NotAvailableContent.gameObject.SetActive(!isAvailable);

            if (!JxHapticManager.Instance.IsSupportedOnDevice)
            {
                _view.NotAvailableText.text = ":(\nUNAVAILABLE on your device!";
                return;
            }
            
            _allPatterns = EnumerateAllPatterns().ToArray();
            SetupDropdown();
            _disposeOnDetach.AppendDispose(_view.PlayButton.OnClick(OnPlayClick, preventMultiClick: true));
            _disposeOnDetach.AppendDispose(_view.StopButton.OnClick(OnStopClick));
            _disposeOnDetach.AppendDispose(_view.ManualPlayButton.OnClick(OnManualPlayButtonClick));
        }

        public void OnViewDetached()
        {
            _disposeOnDetach?.Dispose();
        }

        private void OnPlayClick()
        {
            var haptic = _allPatterns[_view.Dropdown.value];
            JxHapticManager.Instance.Play(haptic);
        }
        
        private static void OnStopClick()
        {
            JxHapticManager.Instance.Stop();
        }
        
        private void SetupDropdown()
        {
            _view.Dropdown.options = _allPatterns.Select(p => new TMP_Dropdown.OptionData(p)).ToList();
        }
        
        private void OnManualPlayButtonClick()
        {
            var amplitude = _view.AmplitudeSlider.value;
            var frequency = _view.FrequencySlider.value;
            var duration = int.Parse(_view.DurationInputField.text);
            HapticPatterns.PlayConstant(amplitude, frequency, duration);
        }

        private static IEnumerable<string> EnumerateAllPatterns()
        {
            return Haptics.Instance.Enumerate();
        }
    }
}