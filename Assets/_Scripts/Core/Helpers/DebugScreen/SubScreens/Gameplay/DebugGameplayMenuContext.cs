using Api.Client.User;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using Savings;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.DebugGameplayMenu
{
    public class DebugGameplayMenuContext
    {
        public DebugGameplayMenuContext(
            IGameplayServerContext gameplayContext, 
            IUserContext userContext, 
            ILocalSave localSave,
            ILocalGameplayContext localGameplayContext
        )
        {
            GameplayContext = gameplayContext;
            UserContext = userContext;
            LocalSave = localSave;
            LocalGameplayContext = localGameplayContext;
        }

        public IGameplayServerContext GameplayContext { get; }
        public IUserContext UserContext { get; }
        public ILocalSave LocalSave { get; }
        public ILocalGameplayContext LocalGameplayContext { get; }
    }
}