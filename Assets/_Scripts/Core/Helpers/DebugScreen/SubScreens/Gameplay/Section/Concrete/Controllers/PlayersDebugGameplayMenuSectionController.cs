using System.Linq;
using GameplayComponents.Helpers;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Data;
using SceneLogics.GameplayScene.Abilities;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Controllers
{
    public class PlayersDebugGameplayMenuSectionController : DebugGameplayMenuSectionController
    {
        public void SendRandom()
        {
            var player = ServerGameplayContext.Spawned.Players.Where(IsNotHost).GetNearestToPosition(Player.NetTransform.Position);
            if (player == null)
                return;
            
            var emoji = player.BaseSyncData.EmojiSlotsReadOnly.FirstOrDefault();
            if (emoji == null)
                return;
            
            player.Emoji.Send(emoji);
        }
        
        public void ActiveAbilityNearPlayer(bool isEscapee)
        {
            NetGamePlayer? target = isEscapee ?
                ServerGameplayContext.Spawned.Escapees.Where(IsNotHost).GetNearestToPosition(Player.NetTransform.Position) :
                ServerGameplayContext.Spawned.Catchers.FirstOrDefault();
            
            if (target.IsNullObj())
                return;
            
            var controller = target!.Ability.Controller;

            if (!target.BaseSyncData.Ability.Value.IsAvailable())
                return;
            
            var ownerPosition = Player.NetTransform.Position;
            var targetEscapeePosition = target!.NetTransform.Position;

            if (target.BaseSyncData.Ability.Value.InputType == AbilityInputType.Button)
            {
                Activate(AbilityParametersFactory.CreateNoTarget());
            }
            else
            {
                Activate(AbilityParametersFactory.CreatePointTarget(targetEscapeePosition, ownerPosition));
            }
            
            return;

            void Activate(GenericAbilityInputParameters parameters) => controller!.TryProcessAsync(parameters, target!.MatchContext.CancellationToken);
        }
        
        public void TeleportNearEscapeeHere()
        {
            var player = GetNearestToHost(ServerGameplayContext.Spawned.Escapees.Where(IsNotHost));

            if (player == null)
            {
                return;
            }

            var myPlayerTransform = Player.NetTransform;
            var teleportPosition = myPlayerTransform.Position + myPlayerTransform.Forward * 2f;
            
            player.NetTransform.ServerTeleport(teleportPosition, Quaternion.identity);
        }
        
        public void TeleportAllEscapeesHere()
        {
            TeleportPlayersToLocalPlayer<NetEscapeePlayer>();
        }

        public void CageAllEscapees()
        {
            CageEscapees(ServerGameplayContext.Spawned.Escapees.Where(IsNotHost));
        }

        public void ReleaseFromCageAllEscapees()
        {
            ReleaseFromCageEscapees(ServerGameplayContext.Spawned.Escapees.Where(IsNotHost));
        }

        public void TeleportCatcherHere()
        {
            InvokeIfEscapee(_ => TeleportPlayersToLocalPlayer<NetCatcherPlayer>());
        }
        
        public void ReleaseFromCageNearestEscapee()
        {
            var player = GetNearestToHost(ServerGameplayContext.Spawned.Escapees
                .Where(a => IsNotHost(a) && a.BaseSyncData.Action.Value == ActionType.InCage));
            
            if (player == null)
                return;
            
            ReleaseFromCageEscapees(new []{player});
        }

        public void ChangeHealthNear(bool increase)
        {
            var player = GetNearestToHost(ServerGameplayContext.Spawned.Escapees.Where(IsNotHost));
            
            if (increase)
                player.Health.Increase();
            else
                player.Health.Decrease();
        }
    }
}