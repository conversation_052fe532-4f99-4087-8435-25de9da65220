using System.Collections.Generic;
using System.Linq;
using Core.Helpers.DebugScreen.SubScreens.Gameplay.Section;
using GameplayNetworking.Gameplay.Player.Components.Score;
using Jx.Utils.Extensions;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Controllers
{
    public class ScoresDebugGameplayMenuSection : DebugGameplayMenuSection<ScoresDebugGameplayMenuSectionController>
    {
        public override string SectionName => "Add Score";
        
        public override IEnumerable<IDebugOptionParameters> EnumerateOptions()
        {
            foreach (var score in EnumerateAvailableScores())
            {
                yield return new DebugOptionParameters(score.ToString(), () => Controller.AddInstantScore(score));
            }
        }

        private IEnumerable<ScoreSource> EnumerateAvailableScores()
        {
            return JxEnumExtensions.GetEnumValues<ScoreSource>()
                .Where(s => s.IsInstant());
        }
    }
}