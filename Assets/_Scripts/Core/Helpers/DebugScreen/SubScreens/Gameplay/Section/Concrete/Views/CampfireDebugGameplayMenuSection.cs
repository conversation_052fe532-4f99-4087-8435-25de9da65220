using System.Collections.Generic;
using Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Controllers;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Views
{
    public class CampfireDebugGameplayMenuSection : DebugGameplayMenuSection<CampfireDebugGameplayMenuSectionController>
    {
        public override string SectionName => "Campfire";
        
        public override IEnumerable<IDebugOptionParameters> EnumerateOptions()
        {
            yield return new DebugOptionParameters("Teleport to near", Controller.TeleportToNear);
            yield return new DebugOptionParameters("Light near", Controller.LightNear);
            yield return new DebugOptionParameters("+25% near", () => Controller.AddNearProgress(0.25f));
            yield return new DebugOptionParameters("Light all", Controller.LightAll);
            yield return new DebugOptionParameters("Put out near", Controller.PutOutNear);
            yield return new DebugOptionParameters("Put out all", Controller.PutOutAll);
        }
    }
}