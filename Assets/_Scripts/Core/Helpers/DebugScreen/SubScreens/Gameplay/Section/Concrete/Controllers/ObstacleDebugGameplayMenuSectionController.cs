using System.Linq;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.DestroyObstacle;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.PutObstacle;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Controllers
{
    public class ObstacleDebugGameplayMenuSectionController : DebugGameplayMenuSectionController
    {
        public void TeleportToNear()
        {
            TeleportPlayers(new[] { Player }, GetNearestToHost(ServerGameplayContext.Spawned.Obstacles).transform.position);
        }

        public void PutNear()
        {
            GetNearestToHostInteraction<PutObstacleInteraction>().Debug_Complete(Player);
        }

        public void PutAll()
        {
            foreach (var interaction in ServerGameplayContext.Spawned.Interactions.All.OfType<PutObstacleInteraction>())
                interaction.Debug_Complete(Player);
        }

        public void DestroyNear()
        {
            GetNearestToHostInteraction<DestroyObstacleInteraction>().Debug_Complete(Player);
        }

        public void DestroyAll()
        {
            foreach (var interaction in ServerGameplayContext.Spawned.Interactions.All.OfType<DestroyObstacleInteraction>())
                interaction.Debug_Complete(Player);
        }
    }
}