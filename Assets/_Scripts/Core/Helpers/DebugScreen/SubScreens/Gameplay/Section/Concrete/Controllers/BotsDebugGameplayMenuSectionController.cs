using System.Linq;
using GameplayComponents.Bots;
using GameplayComponents.Helpers;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;

namespace Core.Helpers.DebugScreen.SubScreens.Gameplay.Section.Concrete.Controllers
{
    public class BotsDebugGameplayMenuSectionController : DebugGameplayMenuSectionController
    {
        public void SetEnabledNearEscapeeBehaviour(bool enabled)
        {
            var escapee = ServerGameplayContext.Spawned.Escapees.Where(IsNotHost).GetNearestToPosition(Player.NetTransform.Position);
            
            if (escapee == null)
                return;

            if (escapee.TryGetComponent<IBotBehaviour>(out var behaviour))
            {
                behaviour.SetActive(enabled);
            }
        }
        
        public void SetEnabledEscapeesBehaviour(bool enabled)
        {
            SetEnabledBotsBehaviour<NetEscapeePlayer>(enabled);
        }

        public void SetEnabledCatchersBehaviour(bool enabled)
        {
            SetEnabledBotsBehaviour<NetCatcherPlayer>(enabled);
        }
    }
}