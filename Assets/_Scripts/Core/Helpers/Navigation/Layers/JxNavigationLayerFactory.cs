using System.Collections.Generic;
using Core.Helpers.DebugScreen.SubScreens.Gameplay.Section;
using Core.UI.Components;
using UnityEngine;
using UnityEngine.UI;

namespace Core.Helpers.Navigation.Layers
{
    public class JxNavigationLayerFactory
    {
        private readonly IReadOnlyDictionary<NavigationLayer, LayerSettings> _settings;

        public JxNavigationLayerFactory()
        {
            _settings = new Dictionary<NavigationLayer, LayerSettings>
            {
                [NavigationLayer.Main] = new LayerSettings("main-canvas", 0),
                [NavigationLayer.Dialog] = new LayerSettings("dialog-canvas", 1),
                [NavigationLayer.Tutorial] = new LayerSettings("tutorial-canvas", 2),
                [NavigationLayer.Overlay] = new LayerSettings("overlay-canvas", 4),
                [NavigationLayer.Loading] = new LayerSettings("loading-canvas", 1000),
            };
        }

        public Canvas Create(NavigationLayer layer, Transform parent)
        {
            var settings = _settings[layer];

            var gameObject = new GameObject(settings.Name)
            {
                transform =
                {
                    parent = parent,
                    localPosition = Vector3.zero,
                    localRotation = Quaternion.identity,
                    localScale = Vector3.one,
                },
            };

            var canvas = AddCanvas(gameObject, settings);
            AddCanvasScaler(gameObject);
            AddGraphicRaycaster(gameObject);
            AddLaptopScaler(gameObject);
            AddDebug(gameObject);

            return canvas;
        }

        private static void AddDebug(GameObject gameObject)
        {
#if DEV && !HEADLESS
            gameObject.AddComponent<DebugObjectDisablerComponent>().SetName(gameObject.name);
#endif
        }

        private static void AddLaptopScaler(GameObject gameObject) => gameObject.AddComponent<CanvasLaptopScalerComponent>();

        private static void AddCanvasScaler(GameObject gameObject)
        {
            var canvasScaler = gameObject.AddComponent<CanvasScaler>();
            canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            canvasScaler.referenceResolution = new Vector2(1920f, 1080f);
            canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            canvasScaler.matchWidthOrHeight = 1f;
            canvasScaler.referencePixelsPerUnit = 100f;
        }

        private static void AddGraphicRaycaster(GameObject gameObject)
        {
            var graphicRaycaster = gameObject.AddComponent<GraphicRaycaster>();
            graphicRaycaster.ignoreReversedGraphics = true;
            graphicRaycaster.blockingObjects = GraphicRaycaster.BlockingObjects.None;
            graphicRaycaster.blockingMask = LayerMask.GetMask("Default", "UI");
        }

        private static Canvas AddCanvas(GameObject gameObject, LayerSettings settings)
        {
            var canvas = gameObject.AddComponent<Canvas>();

            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = settings.SortingOrder;
            canvas.pixelPerfect = false;
            canvas.targetDisplay = 0;
            canvas.additionalShaderChannels = AdditionalCanvasShaderChannels.TexCoord1 | AdditionalCanvasShaderChannels.TexCoord2;

            return canvas;
        }

        #region CLASSES

        private class LayerSettings
        {
            public LayerSettings(string name, int sortingOrder)
            {
                Name = name;
                SortingOrder = sortingOrder;
            }

            public string Name { get; }
            public int SortingOrder { get; }
        }

        #endregion
    }
}