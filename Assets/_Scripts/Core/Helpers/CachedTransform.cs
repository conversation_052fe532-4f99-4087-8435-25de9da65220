using DG.Tweening;
using UnityEngine;

namespace Core.Helpers
{
    public class CachedTransform
    {
        private readonly Vector3 _position;
        private readonly Quaternion _rotation;

        public CachedTransform(Transform transformToCache)
        {
            _position = transformToCache.position;
            _rotation = transformToCache.rotation;
        }

        public Tween DoCopyTransform(Transform transformToUpdate, float animationSec = 0f)
        {
            if (animationSec == 0f)
            {
                transformToUpdate.position = _position;
                transformToUpdate.rotation = _rotation;
            } else
            {
                var sequence = DOTween.Sequence()
                        .Join(transformToUpdate.DOMove(_position, animationSec))
                        .Join(transformToUpdate.DORotateQuaternion(_rotation, animationSec));

                return sequence;
            }

            return null;
        }
    }
}