using Api.Discovery;
using Core.UI.Components.Text;
using Jx.Utils.Logging;
using Jx.Utils.Templating;
using Jx.Utils.UnityContext;
using UnityEngine;

namespace Core.UI.Components.AppVersion
{
    public class AppVersionLabelComponent : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(AppVersionLabelComponent));

        [SerializeField]
        private JxTextComponent _text = null!;

        private IJxUnityContext _unityContext = null!;

        public void Initialize(IJxUnityContext unityContext) => _unityContext = unityContext;

        public void Render()
        {
            if (_unityContext == null)
            {
                gameObject.SetActive(false);
                _logger.LogError($"Not initialized");
                return;
            }

            gameObject.SetActive(true);

            // localize hint, example: App Version: {VERSION}.{SERVER}
            _text.SetLocalizationKey("AppVersion");
            _text.SetTextInterpolationContext(
                new JxStringTemplateParameters()
                    .Set("VERSION", _unityContext.ApplicationInfo.Version)
                    .Set(
                        "SERVER",
                        $"{MonsterLandDiscovery.GetMnemonicStack(MonsterLandDiscovery.Stack)}-{MonsterLandDiscovery.MatchmakingScope[0]}{MonsterLandDiscovery.MatchmakingScope.Substring(MonsterLandDiscovery.MatchmakingScope.Length - 3)}"
                    )
            );
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            _text ??= GetComponent<JxTextComponent>();
        }
#endif
    }
}