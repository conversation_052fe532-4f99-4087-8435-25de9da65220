using UnityEngine.Experimental.Rendering;

namespace Core.UI.Components.RenderTexture.Factory
{
    public class RenderTextureQuality
    {
        public static RenderTextureQuality HalfHD { get; } = new (256, 256);
        public static RenderTextureQuality HD { get; } = new (512, 512);
        public static RenderTextureQuality FullHD { get; } = new (1024, 1024);
        public static RenderTextureQuality UltraHD { get; } = new (2048, 2048);
        
        private RenderTextureQuality(
            int width, 
            int height,
            GraphicsFormat colorFormat = GraphicsFormat.R8G8B8A8_UNorm, 
            GraphicsFormat depthStencilFormat = GraphicsFormat.D24_UNorm_S8_UInt)
        {
            Width = width;
            Height = height;
            ColorFormat = colorFormat;
            DepthStencilFormat = depthStencilFormat;
        }

        public int Width { get; }
        public int Height { get; }
        public GraphicsFormat ColorFormat { get; }
        public GraphicsFormat DepthStencilFormat { get; }
    }
}