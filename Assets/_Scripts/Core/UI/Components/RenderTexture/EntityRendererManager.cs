using System;
using System.Threading;
using Configs.EntityRenderer;
using Core.Extensions;
using Core.Helpers.SceneManagement;
using Core.UI.Components.RenderTexture.Factory;
using Core.UI.Components.RenderTexture.Renderers;
using GameplayComponents.CharacterPresentation;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using UnityEngine;
using UnityEngine.Scripting;

namespace Core.UI.Components.RenderTexture
{
    [Preserve]
    public class EntityRendererManager : IEntityRendererManager,
                                         IDisposable
    {
        private const int MaxCountUsedTextures = 12;
        private const float SpawnOffsetX = 100f;
        private const int MaxEntityIndex = 10000;

        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(EntityRendererManager));

        private readonly IRenderTextureFactory _renderTextureFactory;
        private readonly EntityRenderer.Factory _entityRendererFactory;
        private readonly IMonsterLandSceneManager _sceneManager;
        private readonly IEntityRendererRepository _entityRendererRepository;

        private JxDisposableAction _renderers = null!;
        private int _countUsedRenderTextures;
        private int _entityIndex;

        [Preserve]
        protected EntityRendererManager(
            IRenderTextureFactory renderTextureFactory,
            EntityRenderer.Factory entityRendererFactory,
            IMonsterLandSceneManager sceneManager,
            IEntityRendererRepository entityRendererRepository
        )
        {
            _entityRendererFactory = entityRendererFactory;
            _renderTextureFactory = renderTextureFactory;
            _sceneManager = sceneManager;
            _entityRendererRepository = entityRendererRepository;

            _sceneManager.BeforeSceneChangeEvent += OnSceneChange;
        }

        public void Dispose()
        {
            _renderers?.Dispose();

            _sceneManager.BeforeSceneChangeEvent -= OnSceneChange;
        }

        public EntityRendererHandler GetCharacterOrThrow(
            CharacterViewIdentifier identifier, 
            EntityRendererPlacement placement, 
            RenderTextureQuality quality)
        {
            _renderers ??= JxDisposableAction.Build();
            
            if (_countUsedRenderTextures > MaxCountUsedTextures)
                throw new OverflowException($"Renderer count max '{MaxCountUsedTextures}' reached. Do you want to increase max size?");

            Interlocked.Increment(ref _entityIndex);
            Interlocked.Increment(ref _countUsedRenderTextures);
                
            var renderTexture = _renderTextureFactory.Create(quality);
            var parameters = new EntityRendererParameters(identifier, renderTexture, _entityRendererRepository, placement, () => DisposeTextureOrThrow(renderTexture));
            var spawnPosition = GetNextRendererPosition();
            var renderer = _entityRendererFactory.Create(spawnPosition, parameters);

            _renderers.AppendDispose(renderer);

            return new EntityRendererHandler(renderer, renderTexture);
        }
        
        private void DisposeTextureOrThrow(UnityEngine.RenderTexture renderTexture)
        {
            Interlocked.Decrement(ref _countUsedRenderTextures);
            
            try
            {
                // here raw image has been already cleaned up own texture, so we can discard it safely -> otherwise exception
                if (renderTexture != null)
                    renderTexture.DiscardAndRelease();
            }
            catch (Exception exception)
            {
                _logger.LogError(exception);
            }
        }

        private void OnSceneChange(MonsterLandSceneType _)
        {
            // ok if some is being already disposed
            _renderers?.Dispose();
            _renderers = JxDisposableAction.Build();
            
            Interlocked.Exchange(ref _countUsedRenderTextures, 0);
            Interlocked.Exchange(ref _entityIndex, 0);
        }
        
        // todo: instead of movement make camera render only single entity! To architecturally avoid entity overlapping.
        private Vector3 GetNextRendererPosition() => new (SpawnOffsetX * (_entityIndex % MaxEntityIndex), 0f, 0f);
    }
}