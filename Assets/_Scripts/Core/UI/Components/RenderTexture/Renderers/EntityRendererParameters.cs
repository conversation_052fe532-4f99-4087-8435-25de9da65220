using System;
using Configs.EntityRenderer;
using GameplayComponents.CharacterPresentation;

namespace Core.UI.Components.RenderTexture.Renderers
{
    public class EntityRendererParameters
    {
        public EntityRendererParameters(
            CharacterViewIdentifier characterIdentifier,
            UnityEngine.RenderTexture renderTexture,
            IEntityRendererRepository repository,
            EntityRendererPlacement placement,
            Action onDispose
        )
        {
            CharacterIdentifier = characterIdentifier;
            RenderTexture = renderTexture;
            Repository = repository;
            Placement = placement;
            OnDispose = onDispose;
        }
        
        public CharacterViewIdentifier CharacterIdentifier { get; }
        public UnityEngine.RenderTexture RenderTexture { get; }
        public IEntityRendererRepository Repository { get; }
        public EntityRendererPlacement Placement { get; }
        public Action OnDispose { get; }
    }
}