using System.Collections.Generic;
using Jx.Utils.Logging;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Core.UI.Components
{
    public class TextMeshProLink : TextMeshProUGUI, IPointerClickHandler
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(TextMeshProLink));
        
        public void OnPointerClick(PointerEventData eventData)
        {
            // First, get the index of the link clicked. Each of the links in the text has its own index.
            var linkIndex = TMP_TextUtilities.FindIntersectingLink(this, Input.mousePosition, null);

            if (linkIndex == -1)
            {
                return;
            }
		
            // As the order of the links can vary easily (e.g. because of multi-language support),
            // you need to get the ID assigned to the links instead of using the index as a base for our decisions.
            // you need the LinkInfo array from the textInfo member of the TextMesh Pro object for that.
            var linkId = textInfo.linkInfo[linkIndex].GetLinkID();

            _logger.LogDebug("URL clicked", trackingFactory: () => new Dictionary<string, object>
            {
                ["Link"] = linkId,
            });

            // Let's see that web page!
            Application.OpenURL(linkId);
        }
    }
}