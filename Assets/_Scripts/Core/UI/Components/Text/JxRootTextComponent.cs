using System.Collections.Generic;
using App.Localization.Components.GlobalLocalization;
using Jx.Utils.Logging;

namespace Core.UI.Components.Text
{
    public class JxRootTextComponent : JxTextComponent
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxRootTextComponent));
        
        private const string FolderFormat = "{0}.{1}";
        
        public override bool IsRoot => true;

        public void SetGlobalLocalization(LocalizationFolderType folder, string subLocalizationKey)
        {
            if (Validate(folder, subLocalizationKey))
            {
                _logger.LogError("Invalid folder or key", trackingFactory: () => new Dictionary<string, object>()
                {
                    ["Folder"] = folder,
                    ["SubLocalizationKey"] = subLocalizationKey
                });
                
                DisableLocalization();
                return;
            }
            
            UpdateLocalization(folder, subLocalizationKey);
        }

        private void UpdateLocalization(LocalizationFolderType folder, string subLocalizationKey)
        {
            EnableLocalization();
            SetLocalizationKey(string.Format(FolderFormat, folder.ToString(), subLocalizationKey));
        }

        private static bool Validate(LocalizationFolderType folder, string subLocalizationKey)
        {
            return folder == LocalizationFolderType.None || string.IsNullOrWhiteSpace(subLocalizationKey);
        }
    }
}