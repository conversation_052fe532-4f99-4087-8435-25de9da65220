using Core.Inspector.ConditionalFieldInspector;
using Core.Managers.AssetLoader;
using Core.UI.Components.Effects;
using UnityEngine;
using UnityEngine.UI;

namespace Core.UI.Components.Buttons.Behaviours
{
    public class ButtonStyleSetterComponent : MonoBehaviour
    {
        [SerializeField]
        private Image _image;

        [SerializeField]
        private LoMButtonStyle _defaultStyle = LoMButtonStyle.Blue;

        [SerializeField]
        private LoMButtonStyle _style;

        [SerializeField]
        private bool _syncGlossStyle = false;

        [SerializeField]
        [Tooltip("Can be null")]
        [ConditionalField(nameof(_syncGlossStyle))]
        private GlossEffectComponent? _glossEffect;

        public void OverrideImage(Image image)
        {
            _image = image;
        }

        public void SetStyle(LoMButtonStyle style)
        {
            _style = style;
            Rebuild();
        }
        
        private void Rebuild()
        {
            if (_image != null)
            {
                var styleSprite = FindStyleSprite(_style);
                if (styleSprite != null)
                {
                    _image.sprite = styleSprite;
                }
                else
                {
                    _image.sprite = FindStyleSprite(_defaultStyle);
                }

                if (_syncGlossStyle && _glossEffect != null)
                {
                    _glossEffect.ApplyStyle(GetMappedGlossStyleToButtonStyle(_style));
                }
            }
        }

        private GlossStyleType GetMappedGlossStyleToButtonStyle(LoMButtonStyle style)
        {
            return style switch
            {
                LoMButtonStyle.None => GlossStyleType.White,
                LoMButtonStyle.Blue => GlossStyleType.Blue,
                LoMButtonStyle.Dark => GlossStyleType.White,
                LoMButtonStyle.Gold => GlossStyleType.Yellow,
                LoMButtonStyle.Gray => GlossStyleType.White,
                LoMButtonStyle.Green => GlossStyleType.Green,
                LoMButtonStyle.Orange => GlossStyleType.Yellow,
                LoMButtonStyle.Red => GlossStyleType.Red,
                LoMButtonStyle.Purple => GlossStyleType.Purple,
                _ => GlossStyleType.White
            };
        }
        
        private Sprite? FindStyleSprite(LoMButtonStyle style) 
        {
            if (style == LoMButtonStyle.None)
            {
                return null;
            }
            
            var styleName = style.ToString().ToLowerInvariant();

#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                return Resources.Load<Sprite>($"Sprites/button-styles/{styleName}-button-style");
            }
#endif
            return JxResourceLoader.Instance?.LoadSpriteOrFallback($"button-styles/{styleName}-button-style");
        }
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            _image ??= GetComponent<Image>();
            Rebuild();
        }
#endif
    }
}