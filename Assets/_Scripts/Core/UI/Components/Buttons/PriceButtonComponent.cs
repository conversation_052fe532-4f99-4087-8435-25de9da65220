using System;
using Api.Client.User;
using Api.Client.User.Billing;
using Api.Client.User.Billing.Products;
using Api.Client.User.Prices;
using Api.Client.User.Prices.Adapters;
using App.Billing;
using Core.Helpers.Navigation;
using Core.Inspector.ConditionalFieldInspector;
using Core.UI.Components.Buttons.Behaviours;
using Core.UI.Prices;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Observable;
using Managers.Rewards;
using UI.Screens.Common.GoldenPass;
using UnityEngine;
using UnityEngine.Serialization;
using Zenject;

namespace Core.UI.Components.Buttons
{
    [RequireComponent(typeof(ButtonStyleSetterComponent))]
    public class PriceButtonComponent : ButtonComponent
    {
        [FormerlySerializedAs("_priceTagRendererComponent")]
        [SerializeField]
        private PriceTagRendererComponent _priceRenderer = null!;

        [SerializeField]
        private bool _grayedOnNotAvailable = false;

        [SerializeField]
        [ConditionalField(nameof(_grayedOnNotAvailable), true)]
        private Sprite? _disabledButtonSprite;

        private readonly JxObservable<PriceUsageHandler> _successObservable = new JxObservable<PriceUsageHandler>();

        private ButtonStyleSetterComponent? _styleSetter;

        private IDisposable? _progressSubscription;
        private IJxUserPrice? _renderedPrice;
        private IJxUserProduct? _renderedProduct;
        private string? _confirmationKey;
        private bool _purchaseOnClick;

        private IUserContext _userContext = null!;
        private IBillingInteraction _billingInteraction = null!;
        private IRewardPresenter _rewardPresenter = null!;
        private INavigation _navigation = null!;

        private ButtonStyleSetterComponent StyleSetter
        {
            get
            {
                _styleSetter ??= this.GetComponentOrThrow<ButtonStyleSetterComponent>();
                return _styleSetter;
            }
        }

        [Inject]
        private void Inject(
            IUserContext userContext,
            IBillingInteraction billingInteraction,
            IRewardPresenter rewardPresenter,
            INavigation navigation
        )
        {
            _userContext = userContext;
            _billingInteraction = billingInteraction;
            _rewardPresenter = rewardPresenter;
            _navigation = navigation;
        }

        protected override void OnEnable()
        {
            onClick.AddListener(OnClicked);
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            onClick.RemoveListener(OnClicked);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            Cleanup();
        }
        
        public void SetupFromProduct(IJxUserProduct product, string? confirmationKey) 
        {
            SetupFromPrice(product.Price, confirmationKey);

            _renderedProduct = product;
        }

        public void SetupFromPrice(IJxUserPrice price, string? confirmationKey)
        {
            if (!RenderOnly(price))
            {
                return;
            }

            _confirmationKey = confirmationKey;
            _purchaseOnClick = true;
        }

        public bool RenderOnly(IJxUserPrice price)
        {
            Cleanup();

            if (_priceRenderer.CheckPriceSupported(price))
            {
                gameObject.SetActive(true);

                _priceRenderer.Render(price);
                SetBackground(price);

                _renderedPrice = price;
                _progressSubscription = _userContext.Progress.SubscribeAndFire(OnProgressModelChanged);
                return true;
            }

            gameObject.SetActive(false);
            return false;
        }

        public IDisposable SubscribeOnSuccess(Func<PriceUsageHandler, UniTaskVoid> action)
        {
            return _successObservable.Subscribe(h => action.Invoke(h).Forget());
        }

        private void Cleanup()
        {
            _progressSubscription?.Dispose();
            _renderedPrice = null;
            _renderedProduct = null;
            _purchaseOnClick = false;
        }

        private void SetBackground(IJxUserPrice price)
        {
            var styleSetter = StyleSetter;
            if (styleSetter == null)
            {
                return;
            }

            styleSetter.OverrideImage(image);

            var style = price switch
            {
                MoneyUserPrice => LoMButtonStyle.Green,
                GemUserPrice => LoMButtonStyle.Purple,
                GoldenPassUserPrice => LoMButtonStyle.Gold,
                SubscriptionUserPrice => LoMButtonStyle.Green,
                _ => LoMButtonStyle.Gold
            };

            styleSetter.SetStyle(style);
        }

        private void OnClicked()
        {
            if (_renderedPrice != null && _renderedPrice is GoldenPassUserPrice)
            {
                _navigation.Navigate<GoldenPassScreen>();
                return;
            }
            
            if (!_purchaseOnClick)
            {
                return;
            }

            IJxUserPrice? renderedPrice = null;
            
            var renderedProduct = _renderedProduct;
            if (renderedProduct != null)
            {
                renderedPrice = renderedProduct.Price;
                _billingInteraction.ConfirmProductAsync(renderedProduct, _confirmationKey)
                                   .ContinueWith(OnStatusReceived);
            }

            if (renderedPrice == null)
            {
                renderedPrice = _renderedPrice;
                if (renderedPrice != null)
                {
                    _billingInteraction.ConfirmPriceAsync(renderedPrice, _confirmationKey)
                                       .ContinueWith(OnStatusReceived);
                }
            }

            void OnStatusReceived(PurchaseStatus status)
            {
                if (status == PurchaseStatus.Success)
                {
                    _successObservable.Invoke(_rewardPresenter.DisplayPriceUsage(renderedPrice));
                }
                else
                {
                    _billingInteraction.HandlePurchaseStatusAsync(status, renderedPrice).Forget();
                }
            }
        }

        private void OnProgressModelChanged()
        {
            if (!_grayedOnNotAvailable)
            {
                return;
            }

            var renderedPriceTag = _renderedPrice;
            if (renderedPriceTag == null)
            {
                return;
            }

            interactable = _userContext.Billing.IsPriceAvailable(renderedPriceTag, instantly: true);
            if (!interactable && _disabledButtonSprite != null)
            {
                image.sprite = _disabledButtonSprite;
            }
            else
            {
                SetBackground(renderedPriceTag);
            }
        }

#if UNITY_EDITOR
        protected override void OnValidate()
        {
            base.OnValidate();

            _priceRenderer ??= GetComponentInChildren<PriceTagRendererComponent>();
        }
#endif
    }
}