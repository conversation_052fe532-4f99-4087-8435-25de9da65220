using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Core.Helpers.SceneManagement;
using Jx.Utils.Helpers;
using Jx.Utils.Singleton;
using Jx.Utils.Templating;
using UnityEngine;

namespace Core.UI.Toasts
{
    public class ToastManager : JxSingletonAppObject<ToastManager>
    {
        private const ToastAlignment DefaultAlignment = ToastAlignment.MiddleTop;

        [SerializeField]
        private GameObject _toastPrefab = null!;

        [SerializeField]
        private float _toastsFadeInDurationSec = 0.1f;

        [SerializeField]
        private float _toastsFadeOutDurationSec = 0.1f;

        [SerializeField]
        private RectTransform _container = null!;

        private readonly ISet<IToastRequest> _pendingToastRequests = new HashSet<IToastRequest>();
        private readonly IDictionary<ToastAlignment, IToast> _toastByAlignment = new Dictionary<ToastAlignment, IToast>();
        private readonly ISet<IToastRequest> _requestsMarkedOnDeletion = new HashSet<IToastRequest>();

        private int _ignoreRequestCount;

        protected new void Awake()
        {
            base.Awake();
            MonsterLandSceneManager.Instance.BeforeSceneChangeEvent += OnBeforeSceneChange;
        }

        private void OnDestroy()
        {
            MonsterLandSceneManager.Instance.BeforeSceneChangeEvent -= OnBeforeSceneChange;
        }

        private void OnEnable() => StartCoroutine(ToastCheckRoutine());

        public void Show(string id, float toastDuration = 2f, JxStringTemplateParameters? template = null, ToastAlignment alignment = ToastAlignment.Default)
        {
            if (_ignoreRequestCount > 0)
                return;

            if (alignment == ToastAlignment.Default)
                alignment = DefaultAlignment;

            _pendingToastRequests.Add(new ToastRequest(id, alignment, toastDuration, MonsterLandSceneManager.Instance.Current, template));
        }

        public void Show(string id, TimeSpan duration, JxStringTemplateParameters? template = null, ToastAlignment alignment = ToastAlignment.Default) =>
            Show(id, (float)duration.TotalSeconds, template, alignment);

        public IDisposable Ignore()
        {
            Interlocked.Increment(ref _ignoreRequestCount);

            _pendingToastRequests.Clear();

            HideActiveToasts();

            return JxDisposableAction.Build()
                                     .AppendCallback(() => Interlocked.Decrement(ref _ignoreRequestCount));
        }

        private void HideActiveToasts()
        {
            foreach (var view in EnumerateViews())
            {
                if (view.IsActive())
                    view.Hide(force: true);
            }
        }

        private IEnumerator ToastCheckRoutine()
        {
            var delay = new WaitForSeconds(0.1f);

            while (true)
            {
                if (_pendingToastRequests.Count > 0)
                {
                    foreach (var toastRequest in _pendingToastRequests)
                    {
                        var toast = GetView(toastRequest.Alignment);
                        if (toast.IsActive())
                            toast.Hide(force: true);

                        _requestsMarkedOnDeletion.Add(toastRequest);
                        toast.Show(toastRequest);
                    }

                    foreach (var request in _requestsMarkedOnDeletion)
                        _pendingToastRequests.Remove(request);

                    _requestsMarkedOnDeletion.Clear();
                }

                yield return delay;
            }
        }

        private void OnBeforeSceneChange(MonsterLandSceneType scene) => HideSceneDependentToasts(scene);

        private void HideSceneDependentToasts(MonsterLandSceneType nextScene)
        {
            foreach (var toast in EnumerateViews())
            {
                if (toast?.Request == null)
                    continue;

                if (toast.IsSceneDependent && toast.Request.ActualScene != nextScene)
                    toast.Hide(force: true);
            }
        }

        private IToast GetView(ToastAlignment alignment)
        {
            if (_toastByAlignment.TryGetValue(alignment, out var view))
                return view;

            view = CreateView();
            _toastByAlignment[alignment] = view;

            return view;
        }

        private IEnumerable<IToast> EnumerateViews() => _toastByAlignment.Values;

        private IToast CreateView()
        {
            var view = Instantiate(_toastPrefab, _container, worldPositionStays: false).GetComponent<IToast>();
            view.Initialize(_toastsFadeInDurationSec, _toastsFadeOutDurationSec);

            return view;
        }
    }
}