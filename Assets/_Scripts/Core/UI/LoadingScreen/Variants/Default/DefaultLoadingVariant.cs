using Configs.LoadingHints.Providers;
using Core.UI.LoadingScreen.Components.Card;
using Core.UI.LoadingScreen.Parameters;
using Core.UIReusable.Loading;
using UnityEngine;
using UnityEngine.UI;

namespace Core.UI.LoadingScreen.Variants.Default
{
    public class DefaultLoadingVariant : LoadingVariant
    {
        [SerializeField]
        private CanvasGroup _progressBarContent = null!;

        [SerializeField]
        private RectTransform _loadingCogs = null!;

        [SerializeField]
        private Image _overlapImage = null!;

        [SerializeField]
        private GameplayLoadingCardsContainer _cardsContainer = null!;

        [SerializeField]
        private LoadingBar _loadingBar = null!;

        [SerializeField]
        private LoadingMessage _loadingMessage = null!;

        public override void SetProgress(float progress01)
        {
            _loadingBar.SetProgress(progress01);
        }

        protected override void OnShow(ILoadingScreenParameters parameters)
        {
            var castedParameters = parameters as DefaultLoadingScreenParameters;
            if (castedParameters == null)
                return;
            
            var hintsEnabled = parameters.HintProvider != null;
            
            _loadingCogs.gameObject.SetActive(!hintsEnabled);
            _loadingBar.gameObject.SetActive(hintsEnabled);
            _loadingMessage.gameObject.SetActive(false);
            _progressBarContent.gameObject.SetActive(hintsEnabled);
            _overlapImage.gameObject.SetActive(hintsEnabled && parameters.HintProvider is IGameplayLoadingScreenHintProvider);
            
            if (parameters.HintProvider is IGameplayLoadingScreenHintProvider gameplayProvider)
            {
                _cardsContainer.Render(gameplayProvider, castedParameters.CardGroupIndex);
            }
        }

        protected override void OnHide()
        {
            _overlapImage.gameObject.SetActive(false);
            
            _loadingBar.Clear();
            _cardsContainer.Clear();
        }
    }
}