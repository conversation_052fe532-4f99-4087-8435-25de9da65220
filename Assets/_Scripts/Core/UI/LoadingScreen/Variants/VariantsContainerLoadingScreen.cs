using System;
using Core.UI.LoadingScreen.Parameters;
using Core.UI.LoadingScreen.Variants.Default;
using Core.UI.LoadingScreen.Variants.Match;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace Core.UI.LoadingScreen.Variants
{
    public class VariantsContainerLoadingScreen : MonoBehaviour
    {
        [SerializeField]
        private DefaultLoadingVariant _default = null!;

        [SerializeField]
        private MatchLoadingVariant _match = null!;

        private LoadingVariant? _current;

        private void OnDestroy()
        {
            _current = null;
            Destroy(_default.gameObject);
            Destroy(_match.gameObject);
        }

        public void SetProgress(float progress01)
        {
            _current?.SetProgress(progress01);
        }
        
        public void Cleanup()
        {
            _current?.Hide();
            _current = null;
        }

        public void ShowSuited(ILoadingScreenParameters parameters)
        {
            switch (parameters)
            {
                case DefaultLoadingScreenParameters:
                    ShowInternal(_default, parameters);
                    break;
                case MatchLoadingScreenParameters:
                    ShowInternal(_match, parameters);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(parameters));
            }
        }
        
        private void ShowInternal(LoadingVariant variant, ILoadingScreenParameters parameters)
        {
            if (ReferenceEquals(_current, variant))
                return;
            
            if (this.IsDestroyed() || variant.IsDestroyed())
                return;
            
            _current?.Hide();
            _current = variant;
            _current.Show(parameters);
        }
    }
}