using System;
using System.Collections.Generic;
using System.Linq;
using Core.Attribution;
using Core.Helpers;
using DG.Tweening;
using Jx.Utils.Logging;
using PooledScrollList.View;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Layouts.PoolingScroll
{
    [RequireComponent(typeof(ScrollRect))]
    public class PoolingScrollComponent : MonoBehaviour
    {
        [SerializeField]
        private ScrollRect _scrollRect = null!;
        
        [SerializeField]
        private RectTransform _content = null!;
        
        [SerializeField]
        private RectTransform _viewport = null!;

        [Space(15)]
        [Header("Settings")]
        [SerializeField]
        [JxColor(127f, 127f, 0f)]
        private bool _horizontal;
        
        [SerializeField]
        private bool _flexible = false;

        [SerializeField]
        [Min(0)]
        private int _minSiblingIndex = 0;
        
        [SerializeField]
        [Min(0)]
        private int _extraCount = 4;

        [SerializeField]
        [Range(-1f, 1f)]
        private float _scrollToItemViewportNormalizedOffset;
        
        [SerializeField]
        [Range(-1f, 1f)]
        private float _scrollToItemItemNormalizedOffset;

        [SerializeField]
        private Paddings _paddings = null!;
        
        [Space(20)]
#if UNITY_EDITOR
        [SerializeField]
        [Range(0f, 1f)]
        private float _scrollValue01;
#endif
        [Header("Info")]
        [SerializeField]
        [JxReadonly(false)]
        private int _totalDataCount;
        
        [SerializeField]
        [JxReadonly(false)]
        private int _spawnedItemCount;
        
        [SerializeField]
        [JxReadonly(false)]
        private Vector2 _itemSize; 
        
        [SerializeField]
        [Tooltip("Calculated from item own size + spacing")]
        [JxReadonly(false)]
        private Vector2 _actualItemSize;
        
        [SerializeField]
        [JxReadonly(false)]
        private Vector2 _viewportSize;
        
        [field: SerializeField]
        [field: JxReadonly(false)]
        [field: Tooltip("Actual content size, including expanding from element's actual sizes")]
        private Vector2 _contentSizeDelta;

        private readonly List<PoolingScrollItem> _items = new List<PoolingScrollItem>();

        private PoolingScrollItem _itemPrefab = null!;
        private Func<RectTransform, PoolingScrollItem> _createItem = null!;
        private Action<PoolingScrollItem> _destroyItem = null!;
        private IReadOnlyList<object> _data = null!;
        
        private bool _isInitialized = false;
        private bool _isCurrentlyScrollingForward;
        private int? _firstVisibleDataIndex = 0;

        protected IJxLogger Logger { get; private set; } = null!;
        
        public float ScrollValue01 => _horizontal ? _scrollRect.horizontalNormalizedPosition : _scrollRect.verticalNormalizedPosition;
        public ScrollRect.ScrollRectEvent OnValueChanged => _scrollRect.onValueChanged;
        public IReadOnlyList<PoolingScrollItem> Items => _items;

        private void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        private void OnEnable()
        {
            if (_isInitialized)
            {
                UpdateVisibleItems();
            }
            
            _scrollRect.onValueChanged.AddListener(OnScrollChanged);
        }

        private void OnDisable()
        {
            _scrollRect.onValueChanged.RemoveListener(OnScrollChanged);
        }

        public void Initialize(
            PoolingScrollItem itemPrefab,
            Func<RectTransform, PoolingScrollItem> createItem,
            Action<PoolingScrollItem> destroyItem,
            IReadOnlyList<object> data
        )
        {
            _itemPrefab = itemPrefab;
            _createItem = createItem;
            _destroyItem = destroyItem;
            _data = data;
            _totalDataCount = data.Count;
            
            InitializeSettings();
            
            if (isActiveAndEnabled)
            {
                UpdateVisibleItems();
            }
            
            _isInitialized = true;
        }

        public void DeInitialize()
        {
            if (_items.Any())
            {
                foreach (var item in _items)
                {
                    _destroyItem(item);
                }
            
                _items.Clear();
            }
            
            _itemPrefab = null;
            _createItem = null;
            _destroyItem = null;
            _data = null;
            _firstVisibleDataIndex = null;

            _isInitialized = false;
        }

        public void SetIsBlocked(bool isBlocked)
        {
            if (isBlocked)
            {
                _scrollRect.vertical = false;
                _scrollRect.horizontal = false;
            }
            else
            {
                _scrollRect.vertical = !_horizontal;
                _scrollRect.horizontal = _horizontal;
            }
        }

        public float GetItemScrollValue(int index)
        {
            var itemWidthOffset = _itemSize * _scrollToItemItemNormalizedOffset;
            var viewportWidthOffset = _viewportSize * _scrollToItemViewportNormalizedOffset;
            var itemAnchoredPosition = GetItemAnchoredPosition(index) - itemWidthOffset - viewportWidthOffset;
            
            return _horizontal ? 
                Mathf.Clamp(itemAnchoredPosition.x, 0f, _contentSizeDelta.x) / _contentSizeDelta.x : 
                1 - Mathf.Clamp(-itemAnchoredPosition.y, 0f, _contentSizeDelta.y) / _contentSizeDelta.y;
        }

        public void SetValue01Instantly(float value01)
        {
            value01 = Mathf.Clamp01(value01);
            if (_horizontal)
            {
                _scrollRect.horizontalNormalizedPosition = value01;
            }
            else
            {
                _scrollRect.verticalNormalizedPosition = value01;
            }

            OnScrollChanged(default);
            UpdateVisibleItems();
        }

        public Tween DoSetValue01(float value01, float durationInSeconds)
        {
            value01 = Mathf.Clamp01(value01);
            
            return _horizontal ?
                _scrollRect.DOHorizontalNormalizedPos(value01, durationInSeconds, false) : 
                _scrollRect.DOVerticalNormalizedPos(value01, durationInSeconds, false);
        }

        public ItemVisibilityStatus GetDataVisibilityStatus(int dataIndex)
        {
            var itemPosition = GetItemAnchoredPosition(dataIndex);
            var itemSize = ExtractWorkingItemActualSize();
            var itemStartOffset = _horizontal ? itemPosition.x : itemPosition.y;
            var itemEndOffset = _horizontal ? itemPosition.x + itemSize : itemPosition.y + itemSize;
            var halfViewportSize = _viewportSize / 2f;
            var contentAnchoredPosition =  _horizontal ? -_content.localPosition.x : -_content.localPosition.y;
            var viewportStart = _horizontal ? contentAnchoredPosition - halfViewportSize.x : contentAnchoredPosition - halfViewportSize.y;
            var viewportEnd = _horizontal ? contentAnchoredPosition + halfViewportSize.x : contentAnchoredPosition + halfViewportSize.y;
            
            if (itemEndOffset <= viewportStart)
                return ItemVisibilityStatus.InvisibleOnLeft;
            
            if (itemStartOffset >= viewportEnd)
                return ItemVisibilityStatus.InvisibleOnRight;
            
            return ItemVisibilityStatus.Visible;
        }

        private void InitializeSettings()
        {
            _itemSize = _itemPrefab.RectTransform.sizeDelta;
            _actualItemSize = GetActualItemSize();
            _viewportSize = _viewport.rect.size;

            _spawnedItemCount = GetItemCountToSpawn();
            SetContentParameters();

            for (var i = 0; i < _spawnedItemCount; ++i)
            {
                var item = _createItem(_content);
                _items.Add(item);
                item.PreserveAnchors();

                if (_flexible)
                {
                    var sizeDelta = _horizontal ? 
                            new Vector2(item.RectTransform.sizeDelta.x, _content.rect.height) :
                            new Vector2(_content.rect.width, item.RectTransform.sizeDelta.y);

                    item.RectTransform.sizeDelta = sizeDelta;
                }
                
                item.gameObject.SetActive(true);
            }
            
            OnScrollChanged(default);
        }

        private Vector2 GetContentSizeDelta()
        {
            // we decrease by viewport size, because first visible items don't change the content size (which are within the viewport);
            // we decrease by item spacing here, because we don't consider the last element spacing, like in layout groups
            if (_horizontal)
            {
                return new Vector2(
                    x: _totalDataCount * _actualItemSize.x + _paddings.Left + _paddings.Right - _viewportSize.x - _paddings.Spacing,
                    y: 0f);
            }
            
            return new Vector2(
                x:  0f,
                y: (_totalDataCount * _actualItemSize.y + _paddings.Top + _paddings.Bottom - _viewportSize.y - _paddings.Spacing)
            );
        }

        private Vector2 GetItemAnchoredPosition(int index)
        {
            var workingItemSize = ExtractWorkingItemActualSize();
            
            // the content size has already been increased by right or bottom padding;
            // we need to consider start padding only
            var horizontalPadding = _paddings.Left;
            var verticalPadding = -_paddings.Top;
            
            // but if an axis isn't working one, then we use `(startPadding - endPadding)` formula, but the paddings do the same reversed
            var x = _horizontal ? horizontalPadding + index * workingItemSize : horizontalPadding - _paddings.Right;
            var y = _horizontal ? verticalPadding + _paddings.Bottom : verticalPadding - index * workingItemSize;

            return new Vector2(x, y);
        }

        private void OnScrollChanged(Vector2 _)
        {
            var scrollOffset = _horizontal ? _content.localPosition.x : _content.localPosition.y;
            scrollOffset += _horizontal ? _viewportSize.x / 2f : -_viewportSize.y / 2f;

            var extraItemsOffset = Mathf.Ceil((float)_extraCount / 2f);
            scrollOffset += _horizontal ? extraItemsOffset * _actualItemSize.x : -extraItemsOffset * _actualItemSize.y;

            var approximatedIndex = _horizontal ? scrollOffset / _actualItemSize.x : scrollOffset / _actualItemSize.y;

            var newFirstVisibleIndex = Mathf.FloorToInt(_horizontal ? -approximatedIndex : approximatedIndex);
            newFirstVisibleIndex = Mathf.Clamp(newFirstVisibleIndex, 0, _totalDataCount - 1);

            _isCurrentlyScrollingForward = newFirstVisibleIndex > _firstVisibleDataIndex;

            if (newFirstVisibleIndex != _firstVisibleDataIndex)
            {
                _firstVisibleDataIndex = newFirstVisibleIndex;
                UpdateVisibleItems();
            }
        }

        private void UpdateVisibleItems()
        {
            if (_items.Count == 0 || !_firstVisibleDataIndex.HasValue) 
                return;
            
            if (_isCurrentlyScrollingForward)
            {
                MoveFirstItemToEnd();
            }
            else
            {
                MoveLastItemToStart();
            }
            
            for (var i = 0; i < _items.Count; ++i)
            {
                var item = _items[i];
                
                var dataIndex = _firstVisibleDataIndex.Value + i;
                if (dataIndex >= _totalDataCount)
                {
                    item.gameObject.SetActive(false);
                    continue;
                }

                item.gameObject.SetActive(true);
                item.RectTransform.anchoredPosition = GetItemAnchoredPosition(dataIndex);
                item.RectTransform.SetSiblingIndex(_minSiblingIndex + i);
                
                if (item.DataIndex != dataIndex)
                {
                    item.SetData(dataIndex, _data.ElementAtOrDefault(dataIndex));
                }
            }
        }

        private void MoveFirstItemToEnd()
        {
            var targetItem = _items.FirstOrDefault();
            
            if (targetItem.IsNullObj())
                return;
            
            _items.RemoveAt(0);
            _items.Add(targetItem);
        }

        private void MoveLastItemToStart()
        {
            var targetItem = _items.LastOrDefault();
            
            if (targetItem.IsNullObj())
                return;
            
            _items.RemoveAt(_items.Count - 1);
            _items.Insert(0, targetItem);
        }

        private int GetItemCountToSpawn() => Mathf.Min(GetViewportMaxElementCount() + _extraCount, _totalDataCount);
        private int GetViewportMaxElementCount() => Mathf.CeilToInt(ExtractWorkingViewportSize() / ExtractWorkingItemActualSize());
        private float ExtractWorkingViewportSize() => _horizontal ? _viewportSize.x : _viewportSize.y;
        private float ExtractWorkingItemActualSize() => _horizontal ? _actualItemSize.x : _actualItemSize.y;
        private Vector2 GetActualItemSize() => new Vector2(_itemSize.x + _paddings.Spacing, _itemSize.y + _paddings.Spacing);

        private void SetContentParameters()
        {
            _contentSizeDelta = GetContentSizeDelta();
            _content.sizeDelta = _contentSizeDelta;
            _content.pivot = _horizontal ? new Vector2(0f, 0.5f) : new Vector2(0.5f, 1f);
            _content.anchorMin = Vector2.zero;
            _content.anchorMax = Vector2.one;
            _content.anchoredPosition = _horizontal ? new Vector2(_content.anchoredPosition.x, 0f) : new Vector2(0f, _content.anchoredPosition.y);
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            _scrollRect ??= GetComponent<ScrollRect>();
            if (_scrollRect == null)
                return;
            
            _viewport ??= _scrollRect.viewport;
            _content ??= _scrollRect.content;
            SetContentParameters();

            if (Application.isPlaying)
                return;
            
            _scrollRect.horizontal = _horizontal;
            _scrollRect.vertical = !_horizontal;
            
            if (_horizontal)
            {
                _scrollRect.horizontalNormalizedPosition = _scrollValue01;
            }
            else
            {
                _scrollRect.verticalNormalizedPosition = _scrollValue01;
            }

            if (!_items.IsNullObj())
            {
                OnScrollChanged(_scrollRect.normalizedPosition);
            }
        }
#endif

        #region CLASSES

        [Serializable]
        public class Paddings
        {
            [field: SerializeField]
            public float Left { get; set;} = 0f;
            
            [field: SerializeField]
            public float Right { get; set;} = 0f;
            
            [field: SerializeField]
            public float Top { get; set;} = 0f;
            
            [field: SerializeField]
            public float Bottom { get; set;} = 0f;
            
            [field: SerializeField]
            public float Spacing { get; set;} = 0f;
        }
        
        public enum ItemVisibilityStatus
        {
            Visible = 0,
            InvisibleOnLeft = 1,
            InvisibleOnRight = 2,
        }

        #endregion
    }
}
