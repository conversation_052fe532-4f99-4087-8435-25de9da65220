using UnityEngine;

namespace PooledScrollList.View
{
    public class PoolingScrollItem : MonoBehaviour
    {
        private static readonly Vector2 _anchorPoint = new Vector2(0f, 1f);
        
        [field: SerializeField]
        public RectTransform RectTransform { get; private set; } = null!;
        
        public int? DataIndex { get; private set; } = null;
        public object? Data { get; private set; }
        
        /// <summary>
        /// A pooling scroll puts any class with data into this method.
        /// In inheritors of this class, you must to cast to the desired class type.
        /// Validate if the data is null or not the desired class.
        /// </summary>
        public virtual void SetData(int index, object? data)
        {
            DataIndex = index;
            Data = data;
        }
        
        // do not change the code
        // it's needed to clarify the movement starting point of an element withing a pooling scroll's content
        // as well as in default layout group
        public void PreserveAnchors()
        {
            RectTransform.anchorMin = _anchorPoint;
            RectTransform.anchorMax = _anchorPoint;
            RectTransform.pivot = _anchorPoint;
        }

#if UNITY_EDITOR
        protected virtual void OnValidate()
        {
            RectTransform ??= GetComponent<RectTransform>();
            PreserveAnchors();
        }
#endif
    }
}