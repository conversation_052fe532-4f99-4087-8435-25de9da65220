#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using Core.Editor.SourceGeneration;
using Core.Editor.SourceGeneration.Writing;

namespace Core.Attribution.Editor
{
    public class AttributeRegistrationProviderGenerator : SourceGenerator
    {
        private const string GeneratedNamespace = "Generated";
        private const string GeneratedName = "AttributeRegistrationProviderGenerated";
        
        protected override void Generate(ISourceWriter sourceWriter)
        {
            var source = $@"
using System;
using System.Collections.Generic;
using System.Reflection;
using Core.Attribution;
using UnityEngine;
using UnityEngine.Scripting;

namespace {GeneratedNamespace}
{{
    [Preserve]
    public class {GeneratedName} : {nameof(IAttributeRegistrationProvider)}
    {{
        private static readonly IDictionary<Type, IReadOnlyList<IAttributeRegistration<{nameof(AutoRegisterAttribute)}>>> _cache = new Dictionary<Type, IReadOnlyList<IAttributeRegistration<{nameof(AutoRegisterAttribute)}>>>() {{
            {
				string.Join(",\n", EnumerateAllRegistrations().GroupBy(r => r.Attribute.GetType())
					.Select(g => $@"
                    [typeof({g.Key.FullName})] = new [] {{
                      {string.Join(",\n", EnumerateAllRegistrations()
	                      .Where(r => g.Key.IsInstanceOfType(r.Attribute))
	                      .Select(r => $@"
                            new AttributeRegistration<{g.Key.FullName}>() {{
                              Attribute = typeof({r.Type.FullName}).GetCustomAttribute<{r.Attribute.GetType().FullName}>(true),
                              Type = typeof({r.Type.FullName})  
                            }}
                       "))}  
                    }}
                "))
			}
        }};

        public IReadOnlyList<IAttributeRegistration<T>> GetRegistrations<T>()
            where T : {nameof(AutoRegisterAttribute)} {{
            if(_cache.TryGetValue(typeof(T), out var result)) {{
                return (IReadOnlyList<AttributeRegistration<T>>) result;
            }}

            return Array.Empty<IAttributeRegistration<T>>();
        }}

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        [Preserve]
        private static void OnLoad() {{
            #if !UNITY_EDITOR
            {nameof(AttributeRegistrationProvider)}.{nameof(AttributeRegistrationProvider.Instance)} = new {GeneratedName}();
            #endif
        }}
    }}
}}";

			sourceWriter.Write($"{GeneratedName}.cs", source);
        }
        
        private static IEnumerable<IAttributeRegistration<AutoRegisterAttribute>> EnumerateAllRegistrations()
        {
            return AttributeRegistrationProvider.Instance.GetRegistrations<AutoRegisterAttribute>();
        }

        private static IEnumerable<Type> EnumerateAllTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies().SelectMany(a => a.GetTypes());
        }
    }
}
#endif