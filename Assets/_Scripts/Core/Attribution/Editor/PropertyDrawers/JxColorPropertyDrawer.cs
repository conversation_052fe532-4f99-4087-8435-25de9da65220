using UnityEditor;
using UnityEngine;

namespace Core.Attribution.Editor.PropertyDrawers
{
    [CustomPropertyDrawer(typeof(JxColorAttribute))]
    public class JxColorPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var coloredField = (JxColorAttribute)attribute;
            var originalColor = GUI.color;
            
            GUI.color = coloredField.Color;
            EditorGUI.LabelField(position, label);
            GUI.color = originalColor;
            EditorGUI.PropertyField(position, property, true);
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUI.GetPropertyHeight(property, label, true);
        }
    }
}