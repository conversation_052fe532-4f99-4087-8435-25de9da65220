using System;
using System.Threading;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;

namespace Core.Extensions
{
    public static class CancellationTokenSourceExtensions
    {
        public static void CancelAndDispose(this CancellationTokenSource? cancellationTokenSource)
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.TryCancel();
                cancellationTokenSource.Dispose();
            }
        }
        
        public static CancellationTokenSource LinkedTo(this CancellationTokenSource cancellationTokenSource, CancellationToken cancellationTokenToLink)
        {
            return CancellationTokenSource.CreateLinkedTokenSource(cancellationTokenSource.Token, cancellationTokenToLink);
        }

        public static IDisposable ToDisposable(this CancellationTokenSource? cancellationTokenSource) => JxDisposableAction.Build().AppendCallback(() => cancellationTokenSource?.CancelAndDispose());
    }
}