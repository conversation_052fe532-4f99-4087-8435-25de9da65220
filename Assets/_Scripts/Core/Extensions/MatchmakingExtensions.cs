using System;
using System.Linq;
using Core.Helpers;
using MonsterLand.Matchmaking;

namespace Core.Extensions
{
    public static class MatchmakingExtensions
    {
        public static bool IsFriendInLobbyNow(this IJxMatchmakingIntegration matchmaking, string friendId)
        {
            var players = matchmaking.ActiveConnection.Value?.Lobby.State.Value?.Players.Value;
            if (players.IsNullOrEmpty())
                return false;

            return players!.Any(p => IsTarget(p.Info.Identifier.PublicId));
            
            bool IsTarget(string playerId) => string.Equals(playerId, friendId, StringComparison.Ordinal);
        }
    }
}