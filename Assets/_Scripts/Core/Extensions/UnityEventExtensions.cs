using System;
using Jx.Utils.Helpers;
using UnityEngine.Events;

namespace Core.Extensions
{
    public static class UnityEventExtensions
    {
        public static void AddListenerOnce<T>(this UnityEvent<T> @event, UnityAction<T> action)
        {
            @event.AddListener(Action);
            
            void Action(T a)
            {
                action(a);
                @event.RemoveListener(Action);
            }
        }
        
        public static void AddListenerOnce(this UnityEvent @event, UnityAction action)
        {
            @event.AddListener(Action);
            
            void Action()
            {
                action();
                @event.RemoveListener(Action);
            }
        }
        
        public static IDisposable Subscribe<T>(this UnityEvent<T> unityEvent, UnityAction<T> callback)
        {
            unityEvent.AddListener(callback);

            return JxDisposableAction.Build().AppendCallback(() => unityEvent.RemoveListener(callback));
        }

        public static IDisposable Subscribe(this UnityEvent unityEvent, UnityAction callback)
        {
            unityEvent.AddListener(callback);

            return JxDisposableAction.Build().AppendCallback(() => unityEvent.RemoveListener(callback));
        }
    }
}