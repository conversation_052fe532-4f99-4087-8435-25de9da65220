using System;
using System.Collections.Generic;
using System.Linq;
using Jx.Utils.Collections;
using Jx.Utils.Logging;

namespace Core.DataStructures.Pool
{
    public class JxMultiPool<TTarget, TIdentifier> : IJxMultiPool<TTarget, TIdentifier>
        where TTarget : class, IJxPoolable
        where TIdentifier : struct, IEquatable<TIdentifier>
    {
        private static readonly IJxLogger _logger = 
            JxLoggerFactory.CreateLogger($"{nameof(TTarget)}MultiPool");
        
        private readonly IDictionary<TIdentifier, IList<TTarget>> _offerViewsByDesignIdentifier =
            new Dictionary<TIdentifier, IList<TTarget>>();

        public JxMultiPool(int maxPoolSize)
        {
            MaxSize = maxPoolSize;
        }

        public int MaxSize { get; }

        public void Add(TIdentifier identifier, TTarget target)
        {
            if (IsPoolOverflowed())
            {
                _logger.LogError(
                    $"Pool overflow (during add new item in the pool)",
                    trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["MaxPoolSize"] = MaxSize,
                        ["CurrentTotalOfferCount"] = GetCurrentPoolSize()
                    }
                );
                
                return;
            }

            if (_offerViewsByDesignIdentifier.TryGetValue(identifier, out var offerViewsList))
            {
                offerViewsList.Add(target);
            }
            else
            {
                _offerViewsByDesignIdentifier[identifier] = new List<TTarget>() { target };
            }
        }

        public void Remove(TIdentifier identifier, TTarget target)
        {
            if (_offerViewsByDesignIdentifier.TryGetValue(identifier, out var offerViewsList))
            {
                offerViewsList.Remove(target);
            }
        }

        public bool TryGetReleased(TIdentifier identifier, out TTarget target)
        {
            target = null;
            
            if (_offerViewsByDesignIdentifier.TryGetValue(identifier,
                    out var offerViews))
            {
                target = offerViews.FirstOrDefault(o => o.IsReleased);
            }

            return target != null;
        }
        
        public int GetCurrentPoolSize()
        {
            return _offerViewsByDesignIdentifier.Sum(a => a.Value.Count);
        }

        public int GetReleasedCount()
        {
            return _offerViewsByDesignIdentifier.Sum(a => a.Value.Count(b => b.IsReleased));
        }
        
        public int GetNotReleaseCount()
        {
            return _offerViewsByDesignIdentifier.Sum(a => a.Value.Count(b => !b.IsReleased));
        }
        
        public bool IsPoolOverflowed()
        {
            // TODO TEST IT! Crash-test!
            return GetNotReleaseCount() >= MaxSize;
        }

        public void Clear()
        {
            _offerViewsByDesignIdentifier.ForEach(pair => pair.Value.ForEach(view => view.Dispose()));
            _offerViewsByDesignIdentifier.Clear();
        }
    }
}