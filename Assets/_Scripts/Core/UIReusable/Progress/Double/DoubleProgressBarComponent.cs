using Jx.Utils.Logging;
using UnityEngine;

namespace Core.UIReusable.Progress.Double
{
    public class DoubleProgressBarComponent : MonoBehaviour
    {
        [field: SerializeField]
        public FillDoubleProgressBarComponent FirstProgress { get; private set; } = null!;

        [field: SerializeField]
        public FillDoubleProgressBarComponent SecondProgress { get; private set; } = null!;
        
        protected IJxLogger Logger { get; private set; }

        private void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }
    }
}