using System;
using System.Linq;
using Api.Client.User.TrophyPath;
using Core.Extensions;
using Core.UIReusable.Progress;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace UIReusable.ProfileExperience
{
    public class ProfileExperienceProgressBar : ProgressBar
    {
        [SerializeField]
        private ProfileLevelView _leftIndicator = null!;

        [SerializeField]
        private ProfileLevelView _rightIndicator = null!;
        
        private ITrophyPathInteraction _interaction = null!;

        private IDisposable? _stateSubscription;
        
        private void OnEnable()
        {
            if (_interaction != null)
            {
                Setup();
            }
        }

        private void OnDisable()
        {
            Cleanup();
        }

        public void Initialize(ITrophyPathInteraction interaction)
        {
            _interaction = interaction;
            _leftIndicator.Initialize(interaction);
            _rightIndicator.Initialize(interaction);

            if (isActiveAndEnabled)
            {
                Setup();
            }
        }

        private void Setup()
        {
            Cleanup();
            _stateSubscription = _interaction.State.SubscribeAndFire(Render);
        }

        private void Cleanup()
        {
            _stateSubscription?.Dispose();
        }

        private void Render()
        {
            var state = _interaction.State.Value;
            var leftLevel = state.LastPassedLevelItem;
            var rightLevel = state.NextLevelItem;

            _leftIndicator.Render(leftLevel.Level!.Value);
            _rightIndicator.Render(rightLevel.Level!.Value);
            
            ProgressText.Text = $"{state.GetCurrentLocalTrophy()}/{state.GetNextLevelLocalThreshold()}";
            
            SetFillAmountInstantly(JxMathf.NormalizedInInterval(
                state.GetPassedLevelLocalThreshold(),
                state.GetNextLevelLocalThreshold(), 
                state.GetCurrentLocalTrophy()
                )
            );
        }
    }
}