using System;
using System.Linq;
using Core.Haptic;
using Core.Helpers;
using UnityEngine;

namespace Core.UIReusable.Timer.Behaviours
{
    public class UiHapticTimerBehaviour : UIDurationTimeBehaviour
    {
        [SerializeField]
        private Range[]? _ranges;

        private Range? _lastRange;
        private bool _disabled;
        
        private void Awake()
        {
            _disabled = _ranges.IsNullOrEmpty();
        }
        
        public override void HandleRemainingUpdate(TimeSpan remainingTime)
        {
            if (_disabled)
            {
                return;
            }
            
            if (_lastRange == null || !_lastRange.Contains(remainingTime))
            {
                _lastRange = _ranges.FirstOrDefault(r => r.Contains(remainingTime));
            }

            if (_lastRange != null)
            {
                JxHapticManager.Instance.Play(Haptics.Instance.TimerCountdown);
            }
        }
        
        #region CLASSES

        [Serializable]
        private class Range
        {
            [SerializeField]
            [Min(0f)]
            private int _fromSeconds = 0;
            
            [SerializeField]
            [Min(0f)]
            private int _toSeconds = 1;

            public bool Contains(TimeSpan time)
            {
                var seconds = (int)time.TotalSeconds;
                return _fromSeconds <= seconds && seconds <= _toSeconds;
            }
        }

        #endregion
    }
}