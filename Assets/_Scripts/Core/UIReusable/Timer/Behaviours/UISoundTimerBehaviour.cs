using System;
using System.Linq;
using Audio.Manager;
using Audio.Provider.Sounds;
using Core.Helpers;
using UnityEngine;

namespace Core.UIReusable.Timer.Behaviours
{
    public class UISoundTimerBehaviour : UIDurationTimeBehaviour
    {
        [SerializeField]
        private Range[]? _ranges;

        private Range? _lastRange;
        private bool _disabled;

        private void Awake()
        {
            _disabled = _ranges.IsNullOrEmpty();
        }

        public override void HandleRemainingUpdate(TimeSpan remainingTime)
        {
            if (_disabled)
            {
                return;
            }
            
            if (_lastRange == null || !_lastRange.Contains(remainingTime))
            {
                _lastRange = _ranges.FirstOrDefault(r => r.Contains(remainingTime));
            }

            if (_lastRange != null)
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(new SoundEventIdentifier(_lastRange.SoundId));
            }
        }
        
        #region CLASSES

        [Serializable]
        private class Range
        {
            [SerializeField]
            [Min(0f)]
            private int _fromSeconds = 0;
            
            [SerializeField]
            [Min(0f)]
            private int _toSeconds = 1;

            [field: SerializeField]
            public string SoundId { get; private set; }

            public bool Contains(TimeSpan time)
            {
                var seconds = (int)time.TotalSeconds;
                return _fromSeconds <= seconds && seconds <= _toSeconds;
            }
        }

        #endregion
    }
}