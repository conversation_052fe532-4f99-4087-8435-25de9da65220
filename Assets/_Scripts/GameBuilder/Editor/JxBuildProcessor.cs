using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using GameBuilder.Editor.Processing;
using GameBuilder.Editor.Profiles;
using Jx.Utils.Extensions;
using UnityEditor;
using UnityEditor.Build.Reporting;

namespace GameBuilder.Editor
{
    public static class JxBuildProcessor
    {
        [MenuItem("Woodroom/Build/Android", priority = 30)]
        private static void BuildAndroid() => Build(JxBuildProfileRepository.Android);
        
        [MenuItem("Woodroom/Build/Linux HEADLESS", priority = 50)]
        private static void BuildLinuxServerOnly() => Build(JxBuildProfileRepository.LinuxHeadless);
        
        [MenuItem("Woodroom/Build/Linux HEADLESS BACKGROUND BOTS", priority = 100)]
        private static void BuildLinuxServerOnlyBackgroundBots() => Build(JxBuildProfileRepository.LinuxHeadlessBackgroundBots);
        
        [MenuItem("Woodroom/Build/Restore HEADLESS", priority = 350)]
        private static void RestoreHeadless() => HeadlessOptimizeBuildProcessor.RestoreOptimizations(JxBuildProfileRepository.LinuxHeadless);
        
        [MenuItem("Woodroom/Build/Optimize HEADLESS", priority = 351)]
        private static void OptimizeHeadless() => HeadlessOptimizeBuildProcessor.OptimizeHeadless(JxBuildProfileRepository.LinuxHeadless);
        
        [MenuItem("Woodroom/Build/Clear preferences", priority = int.MaxValue)]
        private static void CleaPreferences() => JxBuildPreferences.Instance.Clear();

        private static async void Build(IJxBuildProfile profile)
        {
            BuildResult result;
            var exception = default(Exception);
            
            try
            {
                result = await BuildProfileAsync(profile);
            }
            catch (Exception ex)
            {
                exception = ex;
                result = BuildResult.Failed;
            }
            
            if (exception != null)
                Debug.LogError(exception);
            
            EditorUtility.ClearProgressBar();
            EditorUtility.DisplayDialog(
                "Build result",
                result == BuildResult.Succeeded ? "Completed" : $"Non success result {result}\n{exception?.Message}",
                "Ok"
            );
        }

        private static async Task<BuildResult> BuildProfileAsync(IJxBuildProfile profile)
        {
            if (EditorUserBuildSettings.activeBuildTarget != profile.Target.ToUnityTarget())
                throw new InvalidOperationException(
                    $"Switch to required target.\nRequired: {profile.Target.ToUnityTarget()}\nSet: {EditorUserBuildSettings.activeBuildTarget}"
                );
            
            if (PlayerSettings.GetScriptingBackend(profile.TargetGroup) != ScriptingImplementation.IL2CPP)
                throw new InvalidOperationException("Il2cpp scripting backed required");
            
            var outputPath = JxBuildProcessorHelper.GetOutputPath(profile.Target);

            if (profile.Processors != null)
            {
                foreach (var processor in profile.Processors)
                    await processor.PreProcessAsync(profile);
            }

            var buildOptions = new BuildPlayerOptions
            {
                locationPathName = Path.Combine(outputPath, profile.OutputFileName),
                scenes = profile.Scenes.ToArray(),
                target = profile.Target.ToUnityTarget(),
                options = profile.BuildOptions,
                extraScriptingDefines = profile.ExtraDefines.ToArray(),
                subtarget = (int)profile.Subtarget,
                targetGroup = profile.TargetGroup,
            };
            
            var report = BuildPipeline.BuildPlayer(buildOptions);
            
            var summary = report.summary;

            if (summary.result == BuildResult.Succeeded)
            {
                Debug.Log($"{profile.Name} build succeeded: " + (summary.totalSize / 1024) + " kb" + $" `{outputPath}`");
                
                if (profile.Processors != null)
                {
                    foreach (var processor in profile.Processors)
                        await processor.PostProcessAsync(outputPath, profile);
                }
            }

            if (summary.result == BuildResult.Failed)
                Debug.Log($"{profile.Name} build failed");

            return summary.result;
        }
    }
}