using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Api.Client.User;
using Api.Client.User.Prices;
using Api.Client.User.Prices.Adapters;
using Configs.Entities;
using Core.Helpers.Navigation;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Jx.Utils;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using LoM.Messaging.Model.Progress;
using LoM.Rewards.ClientIntegration;
using LoM.Rewards.Integration.History;
using UI.Screens.Common.Reward;
using UI.Screens.Common.Reward.Parameters;
using UI.Screens.Common.Reward.Providers;
using UI.Screens.MainMenuScene.RewardReplacement;
using UnityEngine.Scripting;

namespace Managers.Rewards
{
    [Preserve]
    public class RewardPresenter : IRewardPresenter, IDisposable, IJxInitializer
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(RewardPresenter));
        private static readonly ISet<RewardSource> _nonFullScreenRewardSources = new HashSet<RewardSource>
        {
            RewardSource.Match,
            RewardSource.Mission,
        };

        private readonly IJxChangeableObject<ResourceProgress, ResourceProgress> _approvedProgress;
        private readonly IJxChangeableObject<ResourceProgress, ResourceProgress> _displayedProgress;
        private readonly JxAtomicFlag _initialized = false;

        private readonly IUserContext _userContext;
        private readonly INavigation _navigation;

        private IDisposable _userProgressSubscription;
        private long _lastShownRewardTimestamp;
        private UniTaskCompletionSource<bool> _presentingResult;

        [Preserve]
        public RewardPresenter(
            IUserContext userContext,
            INavigation navigation
        )
        {
            _userContext = userContext;
            _navigation = navigation;

            _approvedProgress = new JxChangeableObject<ResourceProgress, ResourceProgress>(
                new ResourceProgress(_userContext.Progress.Value?.Gold ?? 0, _userContext.Progress.Value?.Gems ?? 0)
            );
            _displayedProgress = new JxChangeableObject<ResourceProgress, ResourceProgress>(_approvedProgress.Value);

            MarkDisplayed();
        }

        public IJxInitializer Initializer => this;
        public IJxChangeableObject<ResourceProgress> ApprovedProgress => _approvedProgress;
        public IJxChangeableObject<ResourceProgress> DisplayedProgress => _displayedProgress;

        public bool TryGetDisplayedApprovedDiff(out ResourceProgress diff)
        {
            var displayed = _displayedProgress.Value;
            var approved = _approvedProgress.Value;
            diff = default;

            if (!displayed.Equals(approved))
            {
                diff = new ResourceProgress(approved.Gold - displayed.Gold, approved.Gems - displayed.Gems);
            }

            return diff != null;
        }

        public void MarkDisplayed()
        {
            var displayed = _displayedProgress.Value;
            var approved = _approvedProgress.Value;

            if (!displayed.Equals(approved))
            {
                _displayedProgress.Set(approved);
            }
        }

        public PriceUsageHandler DisplayPriceUsage(IJxUserPrice price)
        {
            var priceUsedResourceProgress = default(ResourceProgress);

            switch (price)
            {
                case GemUserPrice gem:
                    priceUsedResourceProgress = new ResourceProgress(_displayedProgress.Value.Gold, _displayedProgress.Value.Gems - gem.Value);
                    break;
                case GoldUserPrice gold:
                    priceUsedResourceProgress = new ResourceProgress(_displayedProgress.Value.Gold - gold.Value, _displayedProgress.Value.Gems);
                    break;
            }

            var oldDisplayedProgress = _displayedProgress.Value;
            var oldApprovedProgress = _approvedProgress.Value;

            if (priceUsedResourceProgress != null)
            {
                _displayedProgress.Set(priceUsedResourceProgress);
                _approvedProgress.Set(priceUsedResourceProgress);

                return new PriceUsageHandler(
                    () =>
                    {
                        _displayedProgress.Set(oldDisplayedProgress);
                        _approvedProgress.Set(oldApprovedProgress);
                    }
                );
            }

            return PriceUsageHandler.Empty;
        }

        public async UniTask<bool> PresentPendingAsync()
        {
            await TryPresentPendingCompensationsAsync();
            return await PresentPendingRewardsInternalAsync();
        }

        private async UniTask TryPresentPendingCompensationsAsync()
        {
            if (!_userContext.Compensation.TryGetPendingCompensations(out var compensations))
                return;
            
            foreach (var compensation in compensations)
            {
                await _navigation.NavigateAndWaitAsync<CompensationRewardScreen>(new CompensationRewardScreenParameters(compensation));
            }
        }

        public UniTask LoadAsync(CancellationToken cancellationToken = default)
        {
            return UniTask.CompletedTask;
        }

        public UniTask InitializeAsync(CancellationToken cancellationToken = default)
        {
            return UniTask.CompletedTask;
        }

        public UniTask ConfigureAsync(CancellationToken cancellationToken = default)
        {
            _userProgressSubscription = _userContext.Progress.SubscribeAndFire(OnProgressUpdated);
            return UniTask.CompletedTask;
        }

        public UniTask OnConfiguredAsync(CancellationToken cancellationToken = default)
        {
            return UniTask.CompletedTask;
        }

        public UniTask ResetAsync(CancellationToken cancellationToken = default)
        {
            return UniTask.CompletedTask;
        }

        private async UniTask<bool> PresentPendingRewardsInternalAsync()
        {
            if (!(_userContext.Rewards.History?.Value?.Records.Count > 0))
                return false;

            var result = new UniTaskCompletionSource<bool>();

            if (Interlocked.CompareExchange(ref _presentingResult, result, null) != null)
            {
                return await _presentingResult.Task;
            }

            var displayed = false;

            try
            {
                foreach (var historyRecord in _userContext.Rewards.History.Value.Records)
                {
                    if (historyRecord == null)
                    {
                        continue;
                    }
                    
                    if (_lastShownRewardTimestamp < historyRecord.Timestamp)
                    {
                        _lastShownRewardTimestamp = historyRecord.Timestamp;

                        if (!_nonFullScreenRewardSources.Contains(historyRecord.Source))
                        {
                            var parameters = CreateParameters(historyRecord);
                            if (parameters.Count > 0)
                            {
                                await DisplayRewardAsync(parameters);
                                displayed = true;
                            }
                        }

                        _approvedProgress.Set(new ResourceProgress(_userContext.Progress.Value?.Gold ?? 0, _userContext.Progress.Value?.Gems ?? 0));
                    }
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(exception.Message, exception);
            }
            finally
            {
                result.TrySetResult(displayed);
                Interlocked.CompareExchange(ref _presentingResult, null, result);
            }

            return displayed;
        }

        private void OnProgressUpdated(UserProgressModel progress)
        {
            if (progress != null && _initialized.TrySet())
            {
                _lastShownRewardTimestamp = _userContext.Rewards.History.Value?.Records?.LastOrDefault()?.Timestamp ?? 0;
                _approvedProgress.Set(CreateSynchronizedResourceProgress(progress));

                MarkDisplayed();

                _userProgressSubscription?.Dispose();
                _userProgressSubscription = null;
            }
            else
            {
                var newProgress = CreateSynchronizedResourceProgress(progress);

                // если обновился прогресс, но при это не обновилась история наград(купили что-то, за что не дают награду)
                // то рендерим изменения
                // иначе всё обновится после показа истории наград
                if (!newProgress.Equals(_approvedProgress.Value) &&
                    _lastShownRewardTimestamp == (_userContext.Rewards.History.Value?.Records?.LastOrDefault()?.Timestamp ?? 0))
                {
                    _approvedProgress.Set(newProgress);
                }
            }
        }

        private async UniTask DisplayRewardAsync(IReadOnlyList<IRewardParameters> parameters)
        {
            var tokenOnly = parameters.All(p => p is CharacterTokenRewardParameters || p is PerkRewardParameters);
            
            await _navigation.NavigateAndWaitAsync<RewardScreen>(
                new GivenRewardScreenParameterProvider(
                    new RewardScreenParameters(parameters)
                    {
                        ShowTotalSummary = parameters.Count > 1 && !tokenOnly,
                    }
                )
            );
        }

        private IReadOnlyList<IRewardParameters> CreateParameters(UserRewardHistoryRecordClientIntegration historyRecord)
        {
            var parameters = new List<IRewardParameters>();

            if (historyRecord.Gems > 0)
            {
                parameters.Add(new GemRewardParameters(historyRecord.Gems));
            }

            if (historyRecord.Gold > 0)
            {
                parameters.Add(new GoldRewardParameters(historyRecord.Gold));
            }

            if (historyRecord.SeasonPassXp > 0)
            {
                parameters.Add(new SeasonPassXpRewardParameters(historyRecord.SeasonPassXp));
            }

            if (historyRecord.Entities?.Count > 0)
            {
                foreach (var entityId in historyRecord.Entities)
                {
                    if (EntityRepository.Instance.TryGetRewardParameters(entityId, out var entityReward))
                    {
                        parameters.Add(entityReward);
                    }
                }
            }

            if (historyRecord.CharacterToken != null)
            {
                if (RewardPresenterHelper.AddCharacterTokenRewardParameters(
                        historyRecord.CharacterToken.CharacterIndex,
                        historyRecord.CharacterToken.Tokens,
                        _userContext,
                        synchronized: true,
                        out var characterTokenParameters
                    ))
                {
                    parameters.Add(characterTokenParameters);
                }
            }

            if (historyRecord.Perks != null)
            {
                if (RewardPresenterHelper.TryConvertPerkReward(
                        historyRecord.Perks,
                        _userContext,
                        synchronized: true,
                        out var perkRewards
                    ))
                {
                    foreach (var perkReward in perkRewards)
                    {
                        parameters.Add(perkReward);
                    }
                }
            }

            return parameters;
        }

        void IDisposable.Dispose()
        {
            _userProgressSubscription?.Dispose();
        }

        private static ResourceProgress CreateSynchronizedResourceProgress([CanBeNull] UserProgressModel progress)
        {
            return new ResourceProgress(progress?.Gold ?? 0, progress?.Gems ?? 0);
        }
    }
}