using Configs.Perks.Model.Settings.Concrete.Escapee.Unique.Golem;

namespace Configs.Perks.Display.Factories.Escapees.Unique.Golem
{
    /*
     * Один удар от ловца Голем с этим перком держит.
     * Прогрессия будет через уменьшение кулдауна.
     */
    public class BlockHitGolemUniquePerkDisplayFactory : PerkContentFactory<BlockHitGolemUniquePerkSettings>
    {
        protected override string CreateName()
        {
            return "Stone Shield";
        }

        protected override string CreateDescription(BlockHitGolemUniquePerkSettings settings)
        {
            return "Receiving one hit without damage during shield activation time";
        }

        protected override string LocalizeParameter(BlockHitGolemUniquePerkSettings settings)
        {
            return $"{settings.CooldownInSec}s";
        }
    }
}