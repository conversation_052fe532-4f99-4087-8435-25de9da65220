using Configs.Perks.Model.Settings.Concrete.Catcher;

namespace Configs.Perks.Display.Factories.Catchers
{
    /*
     * Ловец быстрее в себя приходит после стана или нокдауна
     */
    public class DurabilityCatcherPerkDisplayFactory : PerkContentFactory<DurabilityCatcherPerkSettings>
    {
        protected override string CreateName()
        {
            return "Persistence";
        }

        protected override string CreateDescription(DurabilityCatcherPerkSettings settings)
        {
            return "Faster recovery after a knockout";
        }

        protected override string LocalizeParameter(DurabilityCatcherPerkSettings settings)
        {
            return $"{settings.StunDurationReductionMod * 100}%";
        }
    }
}