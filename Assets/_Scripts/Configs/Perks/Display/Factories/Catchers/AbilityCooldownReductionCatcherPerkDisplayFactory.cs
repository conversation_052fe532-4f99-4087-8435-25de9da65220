using Configs.Perks.Model.Settings.Concrete.Catcher;

namespace Configs.Perks.Display.Factories.Catchers
{
    public class AbilityCooldownReductionCatcherPerkDisplayFactory : PerkContentFactory<AbilityCooldownReductionCatcherPerkSettings>
    {
        protected override string CreateName()
        {
            return "Recovery";
        }

        protected override string CreateDescription(AbilityCooldownReductionCatcherPerkSettings settings)
        {
            return "Faster main skill recovery";
        }

        protected override string LocalizeParameter(AbilityCooldownReductionCatcherPerkSettings settings)
        {
            return $"{settings.AbilityCooldownModifier * 100}%";
        }
    }
}