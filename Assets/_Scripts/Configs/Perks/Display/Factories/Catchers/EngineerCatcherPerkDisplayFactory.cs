using Configs.Perks.Model.Settings.Concrete.Catcher;

namespace Configs.Perks.Display.Factories.Catchers
{
    /*
     * Ловец получает предупреждение о розжиге костра до его окончания при X процентов прогресса розжига
     */
    public class EngineerCatcherPerkDisplayFactory : PerkContentFactory<EngineerCatcherPerkSettings>
    {
        protected override string CreateName()
        {
            return "Firefighter";
        }

        protected override string CreateDescription(EngineerCatcherPerkSettings settings)
        {
            return "Receiving a warning about lighting a fire before it is finished";
        }

        protected override string LocalizeParameter(EngineerCatcherPerkSettings settings)
        {
            return $"{settings.IndicationProgressThreshold01 * 100}%";
        }
    }
}