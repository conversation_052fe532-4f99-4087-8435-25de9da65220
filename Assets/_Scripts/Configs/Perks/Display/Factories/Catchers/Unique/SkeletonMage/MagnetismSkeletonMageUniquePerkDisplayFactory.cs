using Configs.Perks.Model.Settings.Concrete.Catcher.Unique.SkeletonMage;

namespace Configs.Perks.Display.Factories.Catchers.Unique.SkeletonMage
{
    /*
     * Огненный шар притягивает к себе убегающих, находящихся в радиусе действия шара, с силой X ед.
     */
    public class MagnetismSkeletonMageUniquePerkDisplayFactory : PerkContentFactory<MagnetismSkeletonMageUniquePerkSettings>
    {
        protected override string CreateName()
        {
            return "Magnetism";
        }

        protected override string CreateDescription(MagnetismSkeletonMageUniquePerkSettings settings)
        {
            return "Fireball attracts all of those who fall within the radius of the ball";
        }

        protected override string LocalizeParameter(MagnetismSkeletonMageUniquePerkSettings settings)
        {
            return $"{settings.MagnetRadius}m";
        }
    }
}