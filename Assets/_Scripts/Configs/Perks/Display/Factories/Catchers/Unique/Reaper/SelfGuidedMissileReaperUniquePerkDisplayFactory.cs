using Configs.Perks.Model.Settings.Concrete.Catcher.Unique.Reaper;

namespace Configs.Perks.Display.Factories.Catchers.Unique.Reaper
{
    /*
     * Самонаводящийся крюк c углом поворота крюка в X градусов
     */
    public class SelfGuidedMissileReaperUniquePerkDisplayFactory : PerkContentFactory<SelfGuidedMissileReaperUniquePerkSettings>
    {
        protected override string CreateName()
        {
            return "Auto Hook";
        }

        protected override string CreateDescription(SelfGuidedMissileReaperUniquePerkSettings settings)
        {
            return "Self-directing hook with a rotation speed from the set trajectory";
        }

        protected override string LocalizeParameter(SelfGuidedMissileReaperUniquePerkSettings settings)
        {
            return $"{settings.RotationSpeed}m/s";
        }
    }
}