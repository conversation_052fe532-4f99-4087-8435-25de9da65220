using Configs.Perks.Model.Settings.Concrete.Catcher.Unique.SkeletonGiant;

namespace Configs.Perks.Display.Factories.Catchers.Unique.SkeletonGiant
{
    /*
     * Скелет начинает перепрыгивать сквозь активированные препятствия и уменьшает кулдаун своего умения на X сек. Перк имеет кулдаун N сек.
     */
    public class ObstacleIgnoranceSkeletonGiantUniquePerkDisplayFactory : PerkContentFactory<ObstacleIgnoranceSkeletonGiantUniquePerkSettings>
    {
        protected override string CreateName()
        {
            return "Unhindered Jump";
        }

        protected override string CreateDescription(ObstacleIgnoranceSkeletonGiantUniquePerkSettings settings)
        {
            return "Jumping through the activated obstacles and faster main skill recovery";
        }

        protected override string LocalizeParameter(ObstacleIgnoranceSkeletonGiantUniquePerkSettings settings)
        {
            return $"{settings.AbilityCooldownReductionInSeconds}s";
        }
    }
}