using System.Collections.Generic;
using UnityEngine;

namespace Configs.Perks.Display
{
    internal class PerkDisplayInfo : IPerkDisplayInfo
    {
        private readonly PerkLazyContent _content;

        public PerkDisplayInfo(PerkLazyContent content)
        {
            _content = content;
        }

        public Sprite Icon => _content.Icon;
        public Sprite Background => _content.Background;
        public string Name { get; set; }
        public string Description { get; set; }
        public IReadOnlyList<string> UpgradeParameters { get; set; }
    }
}