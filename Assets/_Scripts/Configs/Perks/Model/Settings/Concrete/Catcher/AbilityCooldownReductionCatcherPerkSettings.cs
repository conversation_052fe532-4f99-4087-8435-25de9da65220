using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Catcher
{
    [Serializable]
    public class AbilityCooldownReductionCatcherPerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("cooldownModifier")]
        [field: SerializeField]
        [JsonProperty("AbilityCooldownModifier")]
        public float AbilityCooldownModifier { get; private set; }
    }
}