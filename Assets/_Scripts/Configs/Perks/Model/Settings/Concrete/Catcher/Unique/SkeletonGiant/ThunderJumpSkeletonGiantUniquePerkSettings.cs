using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Catcher.Unique.SkeletonGiant
{
    [Serializable]
    public class ThunderJumpSkeletonGiantUniquePerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("muteDurationInSeconds")]
        [field: SerializeField]
        [JsonProperty("MuteDurationInSeconds")]
        public float MuteDurationInSeconds { get; private set; }
    }
}