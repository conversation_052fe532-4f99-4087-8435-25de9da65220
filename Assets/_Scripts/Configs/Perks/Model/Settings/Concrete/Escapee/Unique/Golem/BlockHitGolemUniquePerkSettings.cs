using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Escapee.Unique.Golem
{
    [Serializable]
    public class BlockHitGolemUniquePerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("shieldHealth")]
        [field: SerializeField]
        [JsonProperty("ShieldHealth")]
        public int ShieldHealth { get; private set; }
    }
}