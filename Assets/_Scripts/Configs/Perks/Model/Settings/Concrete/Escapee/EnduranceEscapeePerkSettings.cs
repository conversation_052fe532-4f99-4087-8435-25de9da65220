using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Escapee
{
    [Serializable]
    public class EnduranceEscapeePerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("movementSpeedReductionMod")]
        [field: SerializeField]
        [JsonProperty("MovementSpeedReductionMod")]
        public float MovementSpeedReductionMod { get; private set; }
    }
}