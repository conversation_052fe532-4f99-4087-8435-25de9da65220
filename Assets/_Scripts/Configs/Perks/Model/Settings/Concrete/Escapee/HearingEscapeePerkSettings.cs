using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Escapee
{
    [Serializable]
    public class HearingEscapeePerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("additionalEarRadius")]
        [field: SerializeField]
        [JsonProperty("AdditionalEarRadius")]
        public float AdditionalEarRadius { get; private set; }
    }
}