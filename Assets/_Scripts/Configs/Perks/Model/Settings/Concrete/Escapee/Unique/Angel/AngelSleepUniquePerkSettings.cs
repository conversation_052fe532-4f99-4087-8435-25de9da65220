using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Escapee.Unique.Angel
{
    [Serializable]
    public class AngelSleepUniquePerkSettings : PerkSettings
    {
        [field: FormerlySerializedAs("stunDurationInSeconds")]
        [field: SerializeField]
        [JsonProperty("StunDurationInSeconds")]
        public float StunDurationInSeconds { get; private set; }
    }
}