using System;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.Perks.Model.Settings.Concrete.Escapee
{
    [Serializable]
    public class SurvivorEscapeePerkSettings : PerkSettings
    { 
        [field: FormerlySerializedAs("ignoreminigamefailprobability01")]
        [field: SerializeField]
        [JsonProperty("IgnoreMinigameFailProbability01")]
        public float IgnoreMinigameFailProbability01 { get; private set; }
    }
}