using System;
using System.Collections.Generic;
using System.Linq;
using App.Configs.LoadingHints.Hints;
using Jx.Utils.Logging;
using Savings;
using UnityEngine.Scripting;

namespace Configs.LoadingHints.Providers
{
    [Preserve]
    public abstract class LoadingScreenHintProvider : ILoadingScreenHintProvider
    {
        [Preserve]
        protected LoadingScreenHintProvider(ILocalSave localSave)
        {
            LocalSave = localSave;
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }
        
        protected IJxLogger Logger { get; }
        protected ILocalSave LocalSave { get; }
        protected abstract IReadOnlyList<LoadingHint> Hints { get; }
        
        public LoadingHint? FindByIndex(int index)
        {
            return Hints.ElementAtOrDefault(index);
        }

        public LoadingHint? GetNext()
        {
            try
            {
                var hintIndex = GetHintIndexToShow();
                var hint = Hints[hintIndex % Hints.Count];
                
                MarkHintShown(hintIndex);
                
                return hint;
            }
            catch (Exception exception)
            {
                Logger.LogError($"Get next loading hint caused exception", exception);
                return null;
            }
        }

        protected abstract void MarkHintShown(int index);

        protected abstract int GetHintIndexToShow();
    }
}