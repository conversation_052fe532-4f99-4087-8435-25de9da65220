using System.Collections.Generic;
using Configs.Tutorial.Dialogs.Blocks;
using UI.Screens.Common.ScreenPositions;

namespace Configs.Tutorial.Dialogs.MainMenu
{
    public class ChestUnlockScreenTutorialDialogConfig : ScreenTutorialDialogConfig
    {
        public ChestUnlockScreenTutorialDialogConfig()
        {
            TapFilledChestSlot = new ScreenTutorialTipsConfigBlocks()
            {
                EscapeeCloudBlocks = new List<EscapeeCloudTutorialConfigBlock>()
                {
                    new EscapeeCloudTutorialConfigBlock()
                    {
                        Id = GenerateId("TapFilled"),
                        OverridePosition = ScreenPositionType.UpperRightCorner
                    }
                }
            };
            
            TapStartUnlockSlot = new ScreenTutorialTipsConfigBlocks()
            {
                EscapeeCloudBlocks = new List<EscapeeCloudTutorialConfigBlock>()
                {
                    new EscapeeCloudTutorialConfigBlock()
                    {
                        Id = GenerateId("TapStartUnlock"),
                        OverridePosition = ScreenPositionType.UpperRightCorner
                    }
                }
            };
            
            TapInstantUnlockChest = new ScreenTutorialTipsConfigBlocks()
            {
                EscapeeCloudBlocks = new List<EscapeeCloudTutorialConfigBlock>()
                {
                    new EscapeeCloudTutorialConfigBlock()
                    {
                        Id = GenerateId("TapInstantUnlock"),
                        OverridePosition = ScreenPositionType.UpperRightCorner
                    }
                }
            };
        }
        
        public ScreenTutorialTipsConfigBlocks TapFilledChestSlot { get; }
        
        public ScreenTutorialTipsConfigBlocks TapStartUnlockSlot { get; }
        
        public ScreenTutorialTipsConfigBlocks TapInstantUnlockChest { get; }
        public override string BlocksPrefix => "ChestUnlock";
    }
}