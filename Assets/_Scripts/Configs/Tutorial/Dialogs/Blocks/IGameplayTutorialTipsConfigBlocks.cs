using System.Collections.Generic;

namespace Configs.Tutorial.Dialogs.Blocks
{
    public interface IGameplayTutorialTipsConfigBlocks : ITutorialTipsConfigBlocks
    {
        TaskTutorialConfigBlock Task { get; }
        List<DialogTutorialConfigBlock> MessagesBlocks { get; }
        List<EscapeeDialogTutorialConfigBlock> EscapeeDialogsBlock { get; }
        List<TemporaryEscapeeDialogTutorialConfigBlock> TemporaryEscapeeDialogsBlock { get; }
        List<CatherDialogTutorialConfigBlock> CatcherDialogsBlock { get; }
        List<TemporaryCatcherDialogTutorialConfigBlock> TemporaryCatcherDialogsBlock { get; }
    }
}