using System;
using System.Collections.Generic;
using UnityEngine;

namespace Configs.Tutorial.Dialogs.Blocks
{
    [Serializable]
    public class GameplayTutorialTipsConfigBlocks : TutorialTipsConfigBlocks, IGameplayTutorialTipsConfigBlocks
    {
        [field: SerializeField]
        public TaskTutorialConfigBlock Task { get; private set; }
        
        [field: Tooltip("Message is a popup without character's icon. Game waits until it's accepted.")]
        [field: SerializeField]
        public List<DialogTutorialConfigBlock> MessagesBlocks { get; private set; }

        [field: SerializeField]
        public List<EscapeeDialogTutorialConfigBlock> EscapeeDialogsBlock { get; private set; }
        
        [field: SerializeField]
        public List<TemporaryEscapeeDialogTutorialConfigBlock> TemporaryEscapeeDialogsBlock { get; private set; }
        
        [field: SerializeField]
        public List<CatherDialogTutorialConfigBlock> CatcherDialogsBlock { get; private set; }
        
        [field: Serial<PERSON>Field]
        public List<TemporaryCatcherDialogTutorialConfigBlock> TemporaryCatcherDialogsBlock { get; private set; }
    }
}