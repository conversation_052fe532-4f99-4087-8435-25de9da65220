using System;
using Configs.ServerGameplay.Presentation.MiniGames.Concrete;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.ServerGameplay.Config.MiniGames.Concrete
{
    [Serializable]
    public class CageMiniGameConfigDto : MiniGameConfigDto<CageMiniGameConfigPresentation>
    {
        [SerializeField] 
        [JsonProperty("minSuccessZoneSize01")]
        [FormerlySerializedAs("minSuccessZoneSize01")]
        private float _minSuccessZoneSize01;
        
        [SerializeField] 
        [JsonProperty("maxSuccessZoneSize01")]
        [FormerlySerializedAs("maxSuccessZoneSize01")]
        private float _maxSuccessZoneSize01;
        
        [SerializeField] 
        [JsonProperty("minSuccessZoneOffset01")]
        [FormerlySerializedAs("minSuccessZoneOffset01")]
        private float _minSuccessZoneOffset01;
        
        [Serialize<PERSON>ield] 
        [JsonProperty("maxSuccessZoneOffset01")]
        [FormerlySerializedAs("maxSuccessZoneOffset01")]
        private float _maxSuccessZoneOffset01;
        
        [SerializeField] 
        [JsonProperty("minPerfectSuccessZoneSize01")]
        [FormerlySerializedAs("minPerfectSuccessZoneSize01")]
        private float _minPerfectSuccessZoneSize01;
        
        [SerializeField] 
        [JsonProperty("maxPerfectSuccessZoneSize01")]
        [FormerlySerializedAs("maxPerfectSuccessZoneSize01")]
        private float _maxPerfectSuccessZoneSize01;
        
        [SerializeField] 
        [JsonProperty("minPerfectSuccessZoneOffset01")]
        [FormerlySerializedAs("minPerfectSuccessZoneOffset01")]
        private float _minPerfectSuccessZoneOffset01;
                
        [SerializeField] 
        [JsonProperty("maxPerfectSuccessZoneOffset01")]
        [FormerlySerializedAs("maxPerfectSuccessZoneOffset01")]
        private float _maxPerfectSuccessZoneOffset01;
        
        [SerializeField] 
        [JsonProperty("acceleration")]
        [FormerlySerializedAs("acceleration")]
        private float _acceleration;
        
        [SerializeField] 
        [JsonProperty("speed")]
        [FormerlySerializedAs("speed")]
        private float _speed;
        
        [SerializeField] 
        [JsonProperty("announceDurationInMills")]
        [FormerlySerializedAs("announceDurationInMills")]
        private int _announceDurationInMills;
        
        [SerializeField] 
        [JsonProperty("decreaseSuccessZoneFactor01")]
        [FormerlySerializedAs("decreaseSuccessZoneFactor01")]
        private float _decreaseSuccessZoneFactor01;
        
        [SerializeField] 
        [JsonProperty("decreasePerfectSuccessZoneFactor01")]
        [FormerlySerializedAs("decreasePerfectSuccessZoneFactor01")]
        private float _decreasePerfectSuccessZoneFactor01;
        
        [SerializeField] 
        [JsonProperty("lockCount")]
        [FormerlySerializedAs("lockCount")]
        private sbyte _lockCount;
        
        [SerializeField] 
        [JsonProperty("successUnlockCount")]
        [FormerlySerializedAs("successUnlockCount")]
        private sbyte _successUnlockCount;
        
        [SerializeField] 
        [JsonProperty("perfectSuccessUnlockCount")]
        [FormerlySerializedAs("perfectSuccessUnlockCount")]
        private sbyte _perfectSuccessUnlockCount;
        
        [SerializeField] 
        [JsonProperty("missCountToLose")]
        [FormerlySerializedAs("missCountToLose")]
        private sbyte _missCountToLose;
        
        [SerializeField] 
        [JsonProperty("missDeathIncrease01")]
        [FormerlySerializedAs("missDeathIncrease01")]
        private float _missDeathIncrease01;
        
        protected override void Populate(CageMiniGameConfigPresentation config)
        {
            base.Populate(config);
            config.Populate(
                speed: _speed,
                announceDurationInMills: _announceDurationInMills,
                acceleration: _acceleration,
                minSuccessZoneSize01: _minSuccessZoneSize01,
                maxSuccessZoneSize01: _maxSuccessZoneSize01,
                minSuccessZoneOffset01: _minSuccessZoneOffset01,
                maxSuccessZoneOffset01: _maxSuccessZoneOffset01,
                minPerfectSuccessZoneOffset01: _minPerfectSuccessZoneOffset01,
                maxPerfectSuccessZoneOffset01: _maxPerfectSuccessZoneOffset01,
                minPerfectSuccessZoneSize01: _minPerfectSuccessZoneSize01,
                maxPerfectSuccessZoneSize01: _maxPerfectSuccessZoneSize01,
                decreaseSuccessZoneFactor01: _decreaseSuccessZoneFactor01,
                decreasePerfectSuccessZoneFactor01: _decreasePerfectSuccessZoneFactor01,
                missCountToLose: _missCountToLose,
                missDeathIncrease01: _missDeathIncrease01,
                lockCount: _lockCount,
                successUnlockCount: _successUnlockCount,
                perfectSuccessUnlockCount: _perfectSuccessUnlockCount
            );
        }
    }
}