using System;
using Configs.ServerGameplay.Presentation.Buffs;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Buffs
{
    [Serializable]
    public class TemporaryBuffConfig<TConfig> : BaseBuffConfig<TConfig>
        where TConfig : TemporaryBuffConfigPresentation, new()
    {
        [SerializeField]
        [JsonProperty("duration")]
        private int _durationInMills;
        
        protected override void Populate(TConfig config)
        {
            config.Duration = TimeSpan.FromMilliseconds(_durationInMills);
        }
    }
}