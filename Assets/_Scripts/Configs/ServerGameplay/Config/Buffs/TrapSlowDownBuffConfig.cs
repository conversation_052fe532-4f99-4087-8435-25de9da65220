using System;
using Configs.ServerGameplay.Presentation.Buffs;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Buffs
{
    [Serializable]
    public class TrapSlowDownBuffConfig : TemporaryBuffConfig<TrapSlowDownBuffConfigPresentation>
    {
        [SerializeField]
        [JsonProperty("movementMultiplier")]
        private float _movementMultiplier = 1f;
        
        protected override void Populate(TrapSlowDownBuffConfigPresentation config)
        {
            base.Populate(config);
            
            config.MovementMultiplier = _movementMultiplier;
        }
    }
}