using System;
using Configs.ServerGameplay.Presentation.Buffs;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Buffs
{
    [Serializable]
    public class EchoSpeedBuffConfig : TemporaryBuffConfig<EchoSpeedUpBuffConfigPresentation>
    {
        [SerializeField]
        [JsonProperty]
        private float _movementSpeedMultiplier;

        protected override void Populate(EchoSpeedUpBuffConfigPresentation config)
        {
            base.Populate(config);
            
            config.MovementSpeedBuffMultiplier = _movementSpeedMultiplier;
        }
    }
}