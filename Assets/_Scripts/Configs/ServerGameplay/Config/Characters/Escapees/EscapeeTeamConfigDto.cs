using Configs.ServerGameplay.Presentation.Character.Escapee;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Characters.Escapees
{
    [JsonObject(MemberSerialization.OptIn)]
    public class EscapeeTeamConfigDto : BaseTeamConfigDto<EscapeeTeamConfigPresentation>
    {
        [JsonProperty("rab")]
        private EscapeeCharacterConfigDto? _rab;

        [JsonProperty("sup")]
        private EscapeeCharacterConfigDto? _sup;

        [JsonProperty("spidi")]
        private EscapeeCharacterConfigDto? _spidi;

        [JsonProperty("golem")]
        private EscapeeCharacterConfigDto? _golem;

        [JsonProperty("pango")]
        private EscapeeCharacterConfigDto? _pango;

        [JsonProperty("chamie")]
        private EscapeeCharacterConfigDto? _chamie;

        [JsonProperty("gor")]
        private EscapeeCharacterConfigDto? _gor;

        [JsonProperty("fay")]
        private EscapeeCharacterConfigDto? _fay;

        [JsonProperty("electra")]
        private EscapeeCharacterConfigDto? _electra;

        [JsonProperty("marty")]
        private EscapeeCharacterConfigDto? _marty;

        [JsonProperty("robby")]
        private EscapeeCharacterConfigDto? _robby;

        [JsonProperty("lucky")]
        private EscapeeCharacterConfigDto? _lucky;

        [JsonProperty("liz")]
        private EscapeeCharacterConfigDto? _liz;

        [JsonProperty("trickie")]
        private EscapeeCharacterConfigDto? _trickie;

        [JsonConstructor]
        public EscapeeTeamConfigDto(
            EscapeeCharacterConfigDto? rab,
            EscapeeCharacterConfigDto? sup,
            EscapeeCharacterConfigDto? spidi,
            EscapeeCharacterConfigDto? golem,
            EscapeeCharacterConfigDto? pango,
            EscapeeCharacterConfigDto? chamie,
            EscapeeCharacterConfigDto? gor,
            EscapeeCharacterConfigDto? fay,
            EscapeeCharacterConfigDto? electra,
            EscapeeCharacterConfigDto? marty,
            EscapeeCharacterConfigDto? robby,
            EscapeeCharacterConfigDto? lucky,
            EscapeeCharacterConfigDto? liz,
            EscapeeCharacterConfigDto? trickie,
            float movementSpeed,
            float rotationSpeed,
            CameraConfigDto camera
        )
            : base(movementSpeed, rotationSpeed, camera)
        {
            _rab = rab;
            _sup = sup;
            _spidi = spidi;
            _golem = golem;
            _pango = pango;
            _chamie = chamie;
            _gor = gor;
            _fay = fay;
            _electra = electra;
            _marty = marty;
            _robby = robby;
            _lucky = lucky;
            _liz = liz;
            _trickie = trickie;
        }

        protected override void Populate(EscapeeTeamConfigPresentation config)
        {
            base.Populate(config);

            config.Populate(
                rab: _rab ?? EscapeeCharacterConfigDto.Default,
                sup: _sup ?? EscapeeCharacterConfigDto.Default,
                spidi: _spidi ?? EscapeeCharacterConfigDto.Default,
                golem: _golem ?? EscapeeCharacterConfigDto.Default,
                pango: _pango ?? EscapeeCharacterConfigDto.Default,
                chamie: _chamie ?? EscapeeCharacterConfigDto.Default,
                gor: _gor ?? EscapeeCharacterConfigDto.Default,
                fay: _fay ?? EscapeeCharacterConfigDto.Default,
                electra: _electra ?? EscapeeCharacterConfigDto.Default,
                marty: _marty ?? EscapeeCharacterConfigDto.Default,
                robby: _robby ?? EscapeeCharacterConfigDto.Default,
                lucky: _lucky ?? EscapeeCharacterConfigDto.Default,
                liz: _liz ?? EscapeeCharacterConfigDto.Default,
                trickie: _trickie ?? EscapeeCharacterConfigDto.Default
            );
        }
    }
}