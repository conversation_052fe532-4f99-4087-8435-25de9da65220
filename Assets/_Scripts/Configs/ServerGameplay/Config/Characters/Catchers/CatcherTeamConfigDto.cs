using Configs.ServerGameplay.Presentation.Character.Catcher;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Characters.Catchers
{
    [JsonObject(MemberSerialization.OptIn)]
    public class CatcherTeamConfigDto : BaseTeamConfigDto<CatcherTeamConfigPresentation>
    {
        [JsonProperty("Goliath")]
        public CatcherCharacterConfigDto _goliath;

        [JsonProperty("Echo")]
        public CatcherCharacterConfigDto _echo;

        [JsonProperty("Wiz")]
        public CatcherCharacterConfigDto _wiz;

        [JsonProperty("ripp")]
        public CatcherCharacterConfigDto _ripp;

        [JsonProperty("spec")]
        public CatcherCharacterConfigDto _spec;

        [JsonProperty("witch")]
        public CatcherCharacterConfigDto _witch;

        [Json<PERSON>roperty("phantom")]
        public CatcherCharacterConfigDto _phantom;

        [Json<PERSON>roperty("attackingmovementspeed")]
        public float _attackingMovementSpeed;

        [JsonConstructor]
        public CatcherTeamConfigDto(
            CatcherCharacterConfigDto? goliath,
            CatcherCharacterConfigDto? echo,
            CatcherCharacterConfigDto? wiz,
            CatcherCharacterConfigDto? ripp,
            CatcherCharacterConfigDto? spec,
            CatcherCharacterConfigDto? witch,
            CatcherCharacterConfigDto? phantom,
            float movementSpeed,
            float rotationSpeed,
            CameraConfigDto camera,
            float attackingMovementSpeed
        )
            : base(movementSpeed, rotationSpeed, camera)
        {
            _goliath = goliath ?? CatcherCharacterConfigDto.Default;
            _echo = echo ?? CatcherCharacterConfigDto.Default;
            _wiz = wiz ?? CatcherCharacterConfigDto.Default;
            _ripp = ripp ?? CatcherCharacterConfigDto.Default;
            _spec = spec ?? CatcherCharacterConfigDto.Default;
            _witch = witch ?? CatcherCharacterConfigDto.Default;
            _phantom = phantom ?? CatcherCharacterConfigDto.Default;
            _attackingMovementSpeed = attackingMovementSpeed > 0 ? attackingMovementSpeed : 0;
        }

        protected override void Populate(CatcherTeamConfigPresentation config)
        {
            base.Populate(config);

            config.Populate(
                goliath: _goliath,
                echo: _echo,
                wiz: _wiz,
                ripp: _ripp,
                spec: _spec,
                witch: _witch,
                phantom: _phantom,
                _attackingMovementSpeed
            );
        }
    }
}