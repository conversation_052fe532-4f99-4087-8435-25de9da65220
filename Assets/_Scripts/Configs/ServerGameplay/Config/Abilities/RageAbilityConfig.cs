using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Abilities
{
    [Serializable]
    public class RageAbilityConfig : BaseAbilityConfig<RageAbilityConfigPresentation>
    {
        [SerializeField]
        [JsonProperty]
        private float _movementSpeedBuffMultiplier;

        protected override void Populate(RageAbilityConfigPresentation config)
        {
            base.Populate(config);
            
            config.MovementSpeedBuffMultiplier = _movementSpeedBuffMultiplier;
        }
    }
}