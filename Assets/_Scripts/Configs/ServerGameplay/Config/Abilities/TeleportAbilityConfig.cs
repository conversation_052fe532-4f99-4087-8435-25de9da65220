using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.ServerGameplay.Config.Abilities
{
    [Serializable]
    public class TeleportAbilityConfig : BaseAbilityConfig<TeleportAbilityConfigPresentation>
    {
        [SerializeField]
        [FormerlySerializedAs("distance")]
        [JsonProperty("distance")]
        private float _distance;

        protected override void Populate(TeleportAbilityConfigPresentation config)
        {
            base.Populate(config);

            config.Distance = _distance;
        }
    }
}