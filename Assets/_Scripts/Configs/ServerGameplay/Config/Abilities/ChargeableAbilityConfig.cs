using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Abilities
{
    [Serializable]
    public class ChargeableAbilityConfig<TPresentation> : BaseAbilityConfig<TPresentation>
        where TPresentation : ChargeableAbilityConfigPresentation, new()
    {
        [SerializeField]
        [JsonProperty("chargesCount")]
        public int _chargesCount;
        
        [SerializeField]
        [JsonProperty("chargeReloadDurationInMillis")]
        public int _changesReloadDurationInMillis;

        protected override void Populate(TPresentation config)
        {
            base.Populate(config);
            config.ChargeReloadDuration = TimeSpan.FromMilliseconds(_changesReloadDurationInMillis);
            config.MaxCharges = (byte)_chargesCount;
        }
    }
}