using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Abilities
{
    [Serializable]
    public class FollowProjectileAbilityConfig<TPresentation> : ProjectileAbilityConfig<TPresentation>
        where TPresentation : FollowProjectileAbilityConfigPresentation, new()
    {
        [SerializeField]
        [JsonProperty]
        private float _sqrMinDistanceToFollow;
        
        [SerializeField]
        [JsonProperty]
        private float _angleToTargetToFollow;
        
        [SerializeField]
        [JsonProperty]
        private float _rotationSpeed;

        protected override void Populate(TPresentation config)
        {
            base.Populate(config);

            config.SqrMinDistanceToFollow = Mathf.Max(_sqrMinDistanceToFollow, 0f);
            config.AngleToTargetToFollow = _angleToTargetToFollow;
            config.RotationSpeed = Mathf.Max(_rotationSpeed, 0f);
        }
    }
    
    [Serializable]
    public class FollowProjectileAbilityConfig : FollowProjectileAbilityConfig<FollowProjectileAbilityConfigPresentation>
    {}
}