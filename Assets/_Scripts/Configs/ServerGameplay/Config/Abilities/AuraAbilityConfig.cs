using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Abilities
{
    [Serializable]
    public class AuraAbilityConfig<TPresentation> : BaseAbilityConfig<TPresentation>
        where TPresentation : AuraAbilityConfigPresentation, new()
    {
        [SerializeField]
        [JsonProperty("radius")]
        private float _radius;

        protected override void Populate(TPresentation config)
        {
            base.Populate(config);

            config.Radius = _radius;
        }
    }
}