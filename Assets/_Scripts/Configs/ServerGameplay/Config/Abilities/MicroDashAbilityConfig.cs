using Configs.ServerGameplay.Presentation.Abilities;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Abilities
{
    public class MicroDashAbilityConfig : ChargeableAbilityConfig<MicroDashAbilityConfigPresentation>
    {
        [JsonProperty("dashSpeed")]
        private float _dashSpeed;

        protected override void Populate(MicroDashAbilityConfigPresentation config)
        {
            base.Populate(config);
            config.DashSpeed = Mathf.Max(0f, _dashSpeed);
        }
    }
}