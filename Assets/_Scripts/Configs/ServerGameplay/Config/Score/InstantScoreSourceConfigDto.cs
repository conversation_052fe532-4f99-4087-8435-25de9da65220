using System;
using Configs.ServerGameplay.Presentation.Score;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Score
{
    [Serializable]
    public class InstantScoreSourceConfigDto : AbstractConfigItem<InstantScoreSourceConfig>
    {
        [SerializeField]
        [JsonProperty("score")]
        private int _score = 1;
        
        protected override void Populate(InstantScoreSourceConfig config)
        {
            config.Score = _score;
        }
    }
}