using System;
using Configs.ServerGameplay.Config.Abilities;
using Configs.ServerGameplay.Presentation.Abilities;
using Jx.Utils.Extensions;
using Jx.Utils.Objects;
using Newtonsoft.Json;
using SceneLogics.GameplayScene.Abilities;
using UnityEngine;
using UnityEngine.Serialization;

namespace Configs.ServerGameplay.Config
{
    [Serializable]
    public class AbilitiesConfig
    {
        [SerializeField]
        [JsonProperty("rage")]
        private RageAbilityConfig _rageAbilityConfig;

        [SerializeField]
        [JsonProperty("fireball")]
        private ProjectileAbilityConfig _fireballAbilityConfig;

        [SerializeField]
        [JsonProperty("magnetFireball")]
        private MagnetFireballAbilityConfig _magnetFireballAbilityConfig;

        [SerializeField]
        [JsonProperty("hook")]
        private ProjectileAbilityConfig _hookAbilityConfig;

        [SerializeField]
        [JsonProperty("autoHook")]
        private FollowProjectileAbilityConfig _autoHookAbilityConfig;

        [SerializeField]
        [JsonProperty("throwRock")]
        private ThrowRockAbilityConfig _throwRockAbilityConfig;

        [SerializeField]
        [JsonProperty("roll")]
        private RollAbilityConfig _rollAbilityConfig;

        [SerializeField]
        [JsonProperty("echoLocation")]
        private BaseAbilityConfig _echoLocationConfig;

        [SerializeField]
        [JsonProperty("mimicry")]
        private MimicryEscapeeAbilityConfig _mimicryToEscapeeConfig;

        [SerializeField]
        [JsonProperty("angelHeal")]
        private AngelHealAbilityConfig _angelHealAbilityConfig;

        [SerializeField]
        [JsonProperty("jumpShake")]
        private JumpShakeAbilityConfig _jumpShakeAbilityConfig;

        [SerializeField]
        [JsonProperty("spiderWeb")]
        private ProjectileAbilityConfig _spiderWebAbilityConfig;

        [SerializeField]
        [JsonProperty("teleport")]
        private TeleportAbilityConfig _teleportAbilityConfig;

        [SerializeField]
        [JsonProperty("invisibility")]
        private ChamieInvisibilityAbilityConfig _invisibilityAbilityConfig;

        [SerializeField]
        [JsonProperty("knockingdash")]
        private KnockingDashAbilityConfig _knockingDashAbilityConfig;

        [SerializeField]
        [JsonProperty("fayWall")]
        private AirWallAbilityConfig _airWallAbilityConfig;

        [SerializeField]
        [JsonProperty("timelaps")]
        private TimeLapsAbilityConfig _timeLapsAbilityConfig;

        [SerializeField]
        [JsonProperty("electraField")]
        private ElectraFieldAbilityConfig _electraFieldAbilityConfig;

        [SerializeField]
        [JsonProperty("luckyBug")]
        private LuckyBugAbilityConfig _luckyBugAbilityConfig;

        [SerializeField]
        [JsonProperty("smokeCloud")]
        private SmokeCloudAbilityConfig _smokeCloudAbilityConfig;

        [SerializeField]
        [JsonProperty("fear")]
        private FearAbilityConfig _fearAbilityConfig;

        [SerializeField]
        [JsonProperty("teleportationInMark")]
        private TeleportationInMarkAbilityConfig _teleportationInMarkAbilityConfig;

        [SerializeField]
        [JsonProperty("regeneration")]
        private LazyRegenerationAbilityConfig _regenerationAbilityConfig;

        [SerializeField]
        [JsonProperty("microDash")]
        private MicroDashAbilityConfig _microDashAbilityConfig;

        public TConfigPresentation GetAbilityConfig<TConfigPresentation>(AbilityType abilityType)
        {
            BaseAbilityConfigPresentation configPresentation;
            
            switch (abilityType)
            {
                case AbilityType.Fireball:
                    //improved
                    configPresentation = _fireballAbilityConfig.ConvertToPresentation();
                    //configPresentation = _magnetFireballAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Hook:
                    //improved
                    configPresentation = _hookAbilityConfig.ConvertToPresentation();
                    //configPresentation = _autoHookAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Rage:
                    configPresentation = _rageAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Roll:
                    configPresentation = _rollAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.EchoLocation:
                    configPresentation = _echoLocationConfig.ConvertToPresentation();
                    break;
                case AbilityType.EscapeeMimicry:
                    configPresentation = _mimicryToEscapeeConfig.ConvertToPresentation();
                    break;
                case AbilityType.AngelHelp:
                    configPresentation = _angelHealAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.ThrowRock:
                    configPresentation = _throwRockAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.JumpShake:
                    configPresentation = _jumpShakeAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.SpiderWeb:
                    configPresentation = _spiderWebAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Teleport:
                    configPresentation = _teleportAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Invisibility:
                    configPresentation = _invisibilityAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.KnockingDash:
                    configPresentation = _knockingDashAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.FayWall:
                    configPresentation = _airWallAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.TimeLaps:
                    configPresentation = _timeLapsAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.ElectraField:
                    configPresentation = _electraFieldAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.LuckyBag:
                    configPresentation = _luckyBugAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.SmokeCloud:
                    configPresentation = _smokeCloudAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.Fear:
                    configPresentation = _fearAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.TeleportInMark:
                    configPresentation = _teleportationInMarkAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.LazyRegeneration:
                    configPresentation = _regenerationAbilityConfig.ConvertToPresentation();
                    break;
                case AbilityType.MicroDash:
                    configPresentation = _microDashAbilityConfig.ConvertToPresentation();
                    break;
                default:
                    throw new ArgumentException($"Unknown ability type: [{abilityType}]");
            }
            
            return configPresentation.Cast<TConfigPresentation>();
        }

        public static AbilitiesConfig BuildSample()
        {
            return new AbilitiesConfig
            {
                _rageAbilityConfig = new RageAbilityConfig(),
                _fireballAbilityConfig = new ProjectileAbilityConfig(),
                _magnetFireballAbilityConfig = new MagnetFireballAbilityConfig(),
                _hookAbilityConfig = new ProjectileAbilityConfig(),
                _autoHookAbilityConfig = new FollowProjectileAbilityConfig(),
                _throwRockAbilityConfig = new ThrowRockAbilityConfig(),
                _rollAbilityConfig = new RollAbilityConfig(),
                _echoLocationConfig = new BaseAbilityConfig(),
                _mimicryToEscapeeConfig = new MimicryEscapeeAbilityConfig(),
                _angelHealAbilityConfig = new AngelHealAbilityConfig(),
                _jumpShakeAbilityConfig = new JumpShakeAbilityConfig(),
                _spiderWebAbilityConfig = new ProjectileAbilityConfig(),
                _teleportAbilityConfig = new TeleportAbilityConfig(),
                _invisibilityAbilityConfig = new ChamieInvisibilityAbilityConfig(),
                _knockingDashAbilityConfig = new KnockingDashAbilityConfig(),
                _airWallAbilityConfig = new AirWallAbilityConfig(),
                _timeLapsAbilityConfig = new TimeLapsAbilityConfig(),
                _electraFieldAbilityConfig = new ElectraFieldAbilityConfig(),
                _luckyBugAbilityConfig = new LuckyBugAbilityConfig(),
                _fearAbilityConfig = new FearAbilityConfig(),
                _teleportationInMarkAbilityConfig = new TeleportationInMarkAbilityConfig(),
                _regenerationAbilityConfig = new LazyRegenerationAbilityConfig(),
                _microDashAbilityConfig = new MicroDashAbilityConfig(),
            };
        }
    }
}