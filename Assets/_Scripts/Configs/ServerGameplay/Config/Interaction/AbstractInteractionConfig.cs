using System;
using Configs.ServerGameplay.Presentation.Interaction;
using Core.Inspector.ConditionalFieldInspector;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Interaction
{
    [Serializable]
    public class AbstractInteractionConfig<TPresentation> : AbstractConfigItem<TPresentation>
        where TPresentation : InteractionDataConfigPresentation, new()
    {
        [SerializeField]
        [JsonProperty("_radius")]
        private float _radius = 5f;

        [SerializeField]
        [JsonProperty("_angleInDeg")]
        private float _angleInDeg = 360f;

        [SerializeField]
        [JsonProperty("_durationInMills")]
        private int _durationInMills = 5_000;

        [SerializeField]
        [JsonProperty("_resetProgressOnStop")]
        private bool _resetProgressOnStop = false;

        [ConditionalField(nameof(_resetProgressOnStop), false)]
        [SerializeField]
        [JsonProperty("_maxConcurrentUsages")]
        public int _maxConcurrentUsages = 1;

        [SerializeField]
        [JsonProperty("_progressBuffDurationInMilliseconds")]
        protected int _progressBuffDurationInMilliseconds = 3_000;
        
        [SerializeField]
        [JsonProperty("_progressDeBuffDurationInMilliseconds")]
        protected int _progressDeBuffDurationInMilliseconds = 3_000;
        
        [SerializeField]
        [JsonProperty("_progressBuffModifier")]
        protected float _progressBuffModifier = 1.5f;
        
        [SerializeField]
        [JsonProperty("_progressDeBuffModifier")]
        protected float _progressDeBuffModifier = -0.75f;

        [SerializeField]
        [JsonProperty("_efficiencyLossPerCharacter01")]
        protected float _efficiencyLossPerCharacter01 = 0f;

        protected override void Populate(TPresentation config)
        {
            config.SqrRadius = Mathf.Pow(Mathf.Max(0, _radius), 2);
            config.AngleInDeg = Mathf.Max(0, _angleInDeg);
            config.Duration = TimeSpan.FromMilliseconds(Mathf.Max(0, _durationInMills));
            config.ResetProgressOnStop = _resetProgressOnStop;
            config.MaxConcurrentUsages = _resetProgressOnStop ? 1 : _maxConcurrentUsages;
            
            FillBuffsConfig(config);
        }
        
        protected virtual void FillBuffsConfig(TPresentation configPresentation)
        {
            configPresentation.ProgressBuffDuration = TimeSpan.FromMilliseconds(_progressBuffDurationInMilliseconds);
            configPresentation.ProgressDeBuffDuration = TimeSpan.FromMilliseconds(_progressDeBuffDurationInMilliseconds);
            configPresentation.ProgressBuffModifier = _progressBuffModifier;
            configPresentation.ProgressDeBuffModifier = _progressDeBuffModifier;
            configPresentation.EfficiencyLossPerCharacter01 = _efficiencyLossPerCharacter01;
        }
    }
}