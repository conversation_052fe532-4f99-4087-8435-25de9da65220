using System;
using Configs.ServerGameplay.Presentation.Interaction;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Interaction
{
    [Serializable]
    public class StagedInteractionDataConfig : AbstractInteractionConfig<StagedInteractionDataConfigPresentation>
    {
        [JsonProperty("stageCount")]
        [SerializeField]
        private int _stageCount;

        protected override void Populate(StagedInteractionDataConfigPresentation presentation)
        {
            base.Populate(presentation);
            presentation.StageCount = _stageCount > 0 ? _stageCount : 1;
        }
    }
}