using System;
using System.Collections.Generic;
using Configs.ServerGameplay.Config.Interaction;
using Configs.ServerGameplay.Presentation.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using Jx.Utils.Objects;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config
{
    [Serializable]
    public class InteractionsConfig
    {
        [SerializeField]
        [JsonProperty("None")]
        private InteractionDataConfig _none = null!;
        
        [SerializeField]
        [JsonProperty("LightCampfire")]
        private StagedInteractionDataConfig _lightCampfire = null!;
        
        [SerializeField]
        [JsonProperty("PutOutCampfire")]
        private InteractionDataConfig _putOutCampfire = null!;
        
        [SerializeField]
        [JsonProperty("TeleportEscapeeInCage")]
        private InteractionDataConfig _teleportEscapeeInCage = null!;
        
        [SerializeField]
        [JsonProperty("HealEscapeeTeammate")]
        private InteractionDataConfig _healEscapeeTeammate = null!;
        
        [SerializeField]
        [JsonProperty("ReleaseEscapeeFromCage")]
        private InteractionDataConfig _releaseEscapeeFromCage = null!;
        
        [SerializeField]
        [JsonProperty("OpenGate")]
        private InteractionDataConfig _openGate = null!;
        
        [SerializeField]
        [JsonProperty("PutObstacle")]
        private InteractionDataConfig _putObstacle = null!;
        
        [SerializeField]
        [JsonProperty("DestroyObstacle")]
        private InteractionDataConfig _destroyObstacle = null!;
        
        [SerializeField]
        [JsonProperty("SelfHealing")]
        private InteractionDataConfig _selfHealing = null!;
        
        [SerializeField]
        [JsonProperty("OpenChest")]
        private InteractionDataConfig _openChest = null!;
        
        [SerializeField]
        [JsonProperty("Teleport")]
        private InteractionDataConfig _teleport = null!;

        private readonly IDictionary<int, InteractionDataConfigPresentation> _presentationByInteractionType =
            new Dictionary<int, InteractionDataConfigPresentation>();

        public TConfigPresentation GetInteractionDataConfigForType<TConfigPresentation>(InteractionType interactionType)
            where TConfigPresentation : InteractionDataConfigPresentation
        {
            if (!_presentationByInteractionType.TryGetValue((int)interactionType, out var presentation))
            {
                presentation = interactionType switch
                {
                    InteractionType.None => _none.ConvertToPresentation(),
                    InteractionType.LightCampfire => _lightCampfire.ConvertToPresentation(),
                    InteractionType.PutOutCampfire => _putOutCampfire.ConvertToPresentation(),
                    InteractionType.TeleportEscapeeInCage => _teleportEscapeeInCage.ConvertToPresentation(),
                    InteractionType.HealEscapeeTeammate => _healEscapeeTeammate.ConvertToPresentation(),
                    InteractionType.ReleaseEscapeeFromCage => _releaseEscapeeFromCage.ConvertToPresentation(),
                    InteractionType.OpenGate => _openGate.ConvertToPresentation(),
                    InteractionType.PutObstacle => _putObstacle.ConvertToPresentation(),
                    InteractionType.DestroyObstacle => _destroyObstacle.ConvertToPresentation(),
                    InteractionType.SelfHealing => _selfHealing.ConvertToPresentation(),
                    InteractionType.Teleport => _teleport.ConvertToPresentation(),
                    InteractionType.OpenChest => _openChest.ConvertToPresentation(),
                    
                    _ => throw new ArgumentOutOfRangeException(nameof(interactionType), interactionType, null),
                };

                _presentationByInteractionType.Add((int)interactionType, presentation);
            }
            
            return presentation.Cast<TConfigPresentation>();
        }
    }
}