using Configs.ServerGameplay.Presentation.Traps;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Traps
{
    [JsonObject(MemberSerialization.OptIn)]
    public class ExplosionTrapConfigDto : TrapConfigDto<ExplosionTrapConfigPresentation>
    {
        [JsonConstructor]
        public ExplosionTrapConfigDto(
            float radius,
            int setDurationInMills,
            int activationDelayInMills,
            int damage) 
            : base(radius, setDurationInMills, activationDelayInMills)
        {
            _damage = damage;
        }
        
        [JsonProperty("damage")]
        private int _damage;

        protected override void Populate(ExplosionTrapConfigPresentation config)
        {
            base.Populate(config);
            config.Populate(_damage);
        }
    }
}