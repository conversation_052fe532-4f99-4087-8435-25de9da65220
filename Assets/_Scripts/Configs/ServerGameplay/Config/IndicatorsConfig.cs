using System;
using System.Collections.Generic;
using Configs.ServerGameplay.Config.Indicator;
using Core.Helpers.Serializable;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using Newtonsoft.Json;
using SceneLogics.GameplayScene.Components.Indicators;
using UnityEngine;

namespace Configs.ServerGameplay.Config
{
    [Serializable]
    public class IndicatorsConfig
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(IndicatorsConfig));
        
        [SerializeField] 
        [JsonProperty("concrete")]
        private IDictionary<IndicationType, IndicatorDataConfig> _indicatorsDataConfig;

        public IDictionary<IndicationType, IndicatorDataConfig> GetIndicatorConfigPresentation()
        {
            if (_indicatorsDataConfig != null)
            {
                return _indicatorsDataConfig;
            }

            _logger.LogError("The dictionary is missing");
            return null;

        }

        public static IndicatorsConfig BuildSample()
        {
            var interactionDataConfigByType = new SerializableDictionary<IndicationType, IndicatorDataConfig>();

            foreach (var interactionType in Enum.GetValues(typeof(IndicationType)).Cast<IndicationType[]>())
            {
                interactionDataConfigByType.Add(interactionType, new IndicatorDataConfig());
            }

            return new IndicatorsConfig
            {
                _indicatorsDataConfig = interactionDataConfigByType,
            };
        }
    }
}