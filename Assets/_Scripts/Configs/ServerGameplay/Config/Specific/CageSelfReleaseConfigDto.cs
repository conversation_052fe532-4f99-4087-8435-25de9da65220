using System;
using Configs.ServerGameplay.Presentation.Specific;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Specific
{
    [Serializable]
    public class CageSelfReleaseConfigDto : AbstractConfigItem<CageSelfReleaseConfigPresentation>
    {
        [SerializeField]
        [JsonProperty("keyCount")]
        private sbyte _keyCount;
        
        protected override void Populate(CageSelfReleaseConfigPresentation config)
        {
            config.Populate(_keyCount);
        }
    }
}