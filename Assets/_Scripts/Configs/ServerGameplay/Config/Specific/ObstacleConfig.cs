using System;
using Configs.ServerGameplay.Presentation.Specific;
using Newtonsoft.Json;
using UnityEngine;

namespace Configs.ServerGameplay.Config.Specific
{
    [Serializable]
    public class ObstacleConfig : AbstractConfigItem<ObstacleConfigPresentation>
    {
        [SerializeField]
        [JsonProperty("stunDuration")]
        private int _stunDurationInMills;

        [SerializeField]
        [JsonProperty("stunRadius")]
        private float _stunRadius;

        protected override void Populate(ObstacleConfigPresentation config)
        {
            config.CatcherStunDuration = TimeSpan.FromMilliseconds(_stunDurationInMills);
            config.SqrStunRadius = Mathf.Pow(Mathf.Max(0, _stunRadius), 2);
        }
    }
}