using Configs.ServerGameplay.Presentation.Upgrades;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Upgrades
{
    [JsonObject(MemberSerialization.OptIn)]
    public class InteractionCharacterUpgradeConfigDto : AbstractConfigItem<InteractionCharacterUpgradeConfigPresentation>
    {
        [JsonConstructor]
        public InteractionCharacterUpgradeConfigDto(
            float progressMultiplier)
        {
            _progressMultiplier = progressMultiplier;
        }

        [JsonProperty("progressMultiplier")]
        private float _progressMultiplier;
        
        protected override void Populate(InteractionCharacterUpgradeConfigPresentation config)
        {
            config.Populate(_progressMultiplier);
        }
    }
}