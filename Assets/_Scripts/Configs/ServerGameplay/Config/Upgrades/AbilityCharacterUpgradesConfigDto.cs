using Configs.ServerGameplay.Config.Upgrades.Ability.Concrete;
using Configs.ServerGameplay.Presentation.Upgrades.Ability;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Upgrades
{
    [JsonObject(MemberSerialization.OptIn)]
    public class AbilityCharacterUpgradesConfigDto : AbstractConfigItem<AbilityCharacterUpgradesConfigPresentation>
    {
        public AbilityCharacterUpgradesConfigDto(
            InvisibilityAbilityUpgradeConfigDto invisibility,
            EchoLocationAbilityUpgradeConfigDto echoLocation,
            ElectraFieldAbilityUpgradeConfigDto electraField,
            FayWallAbilityUpgradeConfigDto fayWall,
            ThrowRockAbilityUpgradeConfigDto throwRock,
            KnockingDashAbilityUpgradeConfigDto knockingDash,
            LazyRegenerationAbilityUpgradeConfigDto lazyRegeneration,
            LuckyBugAbilityUpgradeConfigDto luckyBag,
            SmokeCloudAbilityUpgradeConfigDto smokeCloud,
            TeleportAbilityUpgradeConfigDto teleport,
            RollAbilityUpgradeConfigDto roll,
            TimeLapsAbilityUpgradeConfigDto timeLaps,
            SpiderWebAbilityUpgradeConfigDto spiderWeb,
            AngelHelpAbilityUpgradeConfigDto angelHelp,
            MicroDashAbilityUpgradeConfigDto microDash,
            JumpShakeAbilityUpgradeConfigDto jumpShake,
            FearAbilityUpgradeConfigDto fear,
            HookAbilityUpgradeConfigDto hook,
            EscapeeMimicryAbilityUpgradeConfigDto escapeeMimicry,
            TeleportInMarkAbilityUpgradeConfigDto teleportationInMark,
            FireballAbilityUpgradeConfigDto fireball
        )
        {
            _invisibility = invisibility;
            _echoLocation = echoLocation;
            _electraField = electraField;
            _fayWall = fayWall;
            _throwRock = throwRock;
            _knockingDash = knockingDash;
            _lazyRegeneration = lazyRegeneration;
            _luckyBag = luckyBag;
            _smokeCloud = smokeCloud;
            _teleport = teleport;
            _roll = roll;
            _timeLaps = timeLaps;
            _spiderWeb = spiderWeb;
            _angelHelp = angelHelp;
            _microDash = microDash;
            _jumpShake = jumpShake;
            _fear = fear;
            _hook = hook;
            _escapeeMimicry = escapeeMimicry;
            _teleportationInMark = teleportationInMark;
            _fireball = fireball;
        }

        // escapees
        [JsonProperty("invisibility")]
        private InvisibilityAbilityUpgradeConfigDto _invisibility;
        
        [JsonProperty("echoLocation")]
        private EchoLocationAbilityUpgradeConfigDto _echoLocation;
        
        [JsonProperty("electraField")]
        private ElectraFieldAbilityUpgradeConfigDto _electraField;
        
        [JsonProperty("fayWall")]
        private FayWallAbilityUpgradeConfigDto _fayWall;
        
        [JsonProperty("throwRock")]
        private ThrowRockAbilityUpgradeConfigDto _throwRock;
        
        [JsonProperty("knockingDash")]
        private KnockingDashAbilityUpgradeConfigDto _knockingDash;
        
        [JsonProperty("lazyRegeneration")]
        private LazyRegenerationAbilityUpgradeConfigDto _lazyRegeneration;
        
        [JsonProperty("luckyBag")]
        private LuckyBugAbilityUpgradeConfigDto _luckyBag;
        
        [JsonProperty("smokeCloud")]
        private SmokeCloudAbilityUpgradeConfigDto _smokeCloud;
        
        [JsonProperty("teleport")]
        private TeleportAbilityUpgradeConfigDto _teleport;
        
        [JsonProperty("roll")]
        private RollAbilityUpgradeConfigDto _roll;
        
        [JsonProperty("timeLaps")]
        private TimeLapsAbilityUpgradeConfigDto _timeLaps;
        
        [JsonProperty("spiderWeb")]
        private SpiderWebAbilityUpgradeConfigDto _spiderWeb;

        [JsonProperty("angelHelp")]
        private AngelHelpAbilityUpgradeConfigDto _angelHelp;

        [JsonProperty("microDash")]
        private MicroDashAbilityUpgradeConfigDto _microDash;
        
        // catchers
        [JsonProperty("jumpShake")]
        private JumpShakeAbilityUpgradeConfigDto _jumpShake;
        
        [JsonProperty("fear")]
        private FearAbilityUpgradeConfigDto _fear;
        
        [JsonProperty("hook")]
        private HookAbilityUpgradeConfigDto _hook;
        
        [JsonProperty("escapeeMimicry")]
        private EscapeeMimicryAbilityUpgradeConfigDto _escapeeMimicry;
        
        [JsonProperty("teleportationInMark")]
        private TeleportInMarkAbilityUpgradeConfigDto _teleportationInMark;

        [JsonProperty("fireball")]
        private FireballAbilityUpgradeConfigDto _fireball;

        protected override void Populate(AbilityCharacterUpgradesConfigPresentation config)
        {
            config.Populate(
                _invisibility,
                _echoLocation,
                _electraField,
                _fayWall,
                _throwRock,
                _knockingDash,
                _lazyRegeneration,
                _luckyBag,
                _smokeCloud,
                _teleport,
                _roll,
                _timeLaps,
                _spiderWeb,
                _angelHelp,
                _microDash,
                _jumpShake,
                _fear,
                _hook,
                _escapeeMimicry,
                _teleportationInMark,
                _fireball
            );
        }
    }
}