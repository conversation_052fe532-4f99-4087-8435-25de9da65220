using Configs.ServerGameplay.Presentation.Upgrades.Concrete;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Upgrades.Ability.Concrete
{
    [JsonObject(MemberSerialization.OptIn)]
    public class HookAbilityUpgradeConfigDto : AbstractConfigItem<HookAbilityUpgradeConfigPresentation>,
                                               ICharacterUpgradeConfigDto
    {
        [JsonConstructor]
        public HookAbilityUpgradeConfigDto(float cooldownMultiplier, float projectileMovementSpeedMultiplier)
        {
            _cooldownMultiplier = cooldownMultiplier;
            _projectileMovementSpeedMultiplier = projectileMovementSpeedMultiplier;
        }

        [JsonProperty(UpgradeConfigDtoJsonNames.CooldownMultiplier)]
        private float _cooldownMultiplier;

        [JsonProperty(UpgradeConfigDtoJsonNames.ProjectileMovementSpeedMultiplier)]
        private float _projectileMovementSpeedMultiplier;
        
        protected override void Populate(HookAbilityUpgradeConfigPresentation config)
        {
            config.Populate(cooldownMultiplier: _cooldownMultiplier, projectileMovementSpeedMultiplier: _projectileMovementSpeedMultiplier);
        }
    }
}