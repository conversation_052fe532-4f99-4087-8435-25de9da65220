using Configs.ServerGameplay.Presentation.Upgrades.Concrete;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Upgrades.Ability.Concrete
{
    [JsonObject(MemberSerialization.OptIn)]
    public class JumpShakeAbilityUpgradeConfigDto : AbstractConfigItem<JumpShakeAbilityUpgradeConfigPresentation>,
                                                    ICharacterUpgradeConfigDto
    {
        [JsonConstructor]
        public JumpShakeAbilityUpgradeConfigDto(float shakeRadiusMultiplier, float debuffDurationMultiplier)
        {
            _shakeRadiusMultiplier = shakeRadiusMultiplier;
            _debuffDurationMultiplier = debuffDurationMultiplier;
        }

        [JsonProperty("shakeRadiusMultiplier")]
        private float _shakeRadiusMultiplier;
        
        [JsonProperty("debuffDurationMultiplier")]
        private float _debuffDurationMultiplier;

        protected override void Populate(JumpShakeAbilityUpgradeConfigPresentation config)
        {
            config.Populate(shakeRadiusMultiplier: _shakeRadiusMultiplier, debuffDurationMultiplier: _debuffDurationMultiplier);
        }
    }
}