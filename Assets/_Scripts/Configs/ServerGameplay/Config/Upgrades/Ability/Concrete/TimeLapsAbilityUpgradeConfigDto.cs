using Configs.ServerGameplay.Presentation.Upgrades.Concrete;
using Newtonsoft.Json;

namespace Configs.ServerGameplay.Config.Upgrades.Ability.Concrete
{
    [JsonObject(MemberSerialization.OptIn)]
    public class TimeLapsAbilityUpgradeConfigDto : AbstractConfigItem<TimeLapsAbilityUpgradeConfigPresentation>,
                                                   ICharacterUpgradeConfigDto
    {
        [JsonConstructor]
        public TimeLapsAbilityUpgradeConfigDto(float cooldownMultiplier, float markLifespanMultiplier)
        {
            _cooldownMultiplier = cooldownMultiplier;
            _markLifespanMultiplier = markLifespanMultiplier;
        }

        [JsonProperty(UpgradeConfigDtoJsonNames.CooldownMultiplier)]
        private float _cooldownMultiplier;
        
        [JsonProperty("markLifespanMultiplier")]
        private float _markLifespanMultiplier;
        
        protected override void Populate(TimeLapsAbilityUpgradeConfigPresentation config)
        {
            config.Populate(cooldownMultiplier: _cooldownMultiplier, markLifespawnMultiplier: _markLifespanMultiplier);
        }
    }
}