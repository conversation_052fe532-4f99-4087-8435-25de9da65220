using System;
using System.Collections.Generic;
using System.Linq;

namespace Configs.ServerGameplay.Presentation.DifficultyGameplayOverrides
{
    public class DifficultyGameplayOverrideParametersPresentation
    {
        public IReadOnlyList<DifficultyGameplayOverrideItemPresentation> Items { get; set; } = Array.Empty<DifficultyGameplayOverrideItemPresentation>();

        public DifficultyGameplayOverrideItemPresentation? FindOverride(int mmr)
        {
#if HEADLESS
            return null;
#else
            return Items.FirstOrDefault(item => mmr >= item.MinMmr && mmr <= item.MaxMmr);
#endif
        }
    }
}