using System;
using GameplayNetworking.Gameplay.Components.Server.Interaction;

namespace Configs.ServerGameplay.Presentation.Character
{
    public class InteractionMultiplierConfigPresentation
    {
        private float _lightCampfire;
        private float _putOutCampfire;
        private float _teleportInCage;
        private float _healEscapeeTeammate;
        private float _releaseEscapeeFromCage;
        private float _openGate;
        private float _putObstacle;
        private float _destroyObstacle;
        private float _selfHealing;
        
        public void Populate(
            float lightCampfireMultiplier,
            float putOutCampfireMultiplier,
            float teleportInCageMultiplier,
            float healEscapeeTeammateMultiplier,
            float releaseEscapeeFromCageMultiplier,
            float openGateMultiplier,
            float putObstacleMultiplier,
            float destroyObstacleMultiplier,
            float selfHealingMultiplier)
        {
            _lightCampfire = lightCampfireMultiplier;
            _putOutCampfire = putOutCampfireMultiplier;
            _teleportInCage = teleportInCageMultiplier;
            _healEscapeeTeammate = healEscapeeTeammateMultiplier;
            _releaseEscapeeFromCage = releaseEscapeeFromCageMultiplier;
            _openGate = openGateMultiplier;
            _destroyObstacle = destroyObstacleMultiplier;
            _selfHealing = selfHealingMultiplier;
            _putObstacle = putObstacleMultiplier;
        }

        public float Get(InteractionType interactionType)
        {
            return interactionType switch
            {
                InteractionType.None => 0,
                InteractionType.LightCampfire => _lightCampfire,
                InteractionType.PutOutCampfire => _putOutCampfire,
                InteractionType.TeleportEscapeeInCage => _teleportInCage,
                InteractionType.HealEscapeeTeammate => _healEscapeeTeammate,
                InteractionType.ReleaseEscapeeFromCage => _releaseEscapeeFromCage,
                InteractionType.OpenGate => _openGate,
                InteractionType.PutObstacle => _putObstacle,
                InteractionType.DestroyObstacle => _destroyObstacle,
                InteractionType.SelfHealing => _selfHealing,
                InteractionType.Teleport => _teleportInCage,
                InteractionType.OpenChest => 1,
                _ => throw new ArgumentOutOfRangeException(nameof(interactionType), interactionType, null)
            };
        }
    }
}