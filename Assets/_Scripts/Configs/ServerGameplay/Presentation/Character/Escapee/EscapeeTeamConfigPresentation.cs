using System.Collections.Generic;
using Configs.ServerGameplay.Config.Characters.Escapees;
using GameplayNetworking.Share.Character;

namespace Configs.ServerGameplay.Presentation.Character.Escapee
{
    public class EscapeeTeamConfigPresentation : BaseTeamConfigPresentation
    {
        private IReadOnlyDictionary<EscapeeType, EscapeeCharacterConfigPresentation> _presentationByEscapeeType = null!;

        public void Populate(
            EscapeeCharacterConfigDto rab, 
            EscapeeCharacterConfigDto sup, 
            EscapeeCharacterConfigDto spidi, 
            EscapeeCharacterConfigDto golem, 
            EscapeeCharacterConfigDto pango, 
            EscapeeCharacterConfigDto chamie,
            EscapeeCharacterConfigDto gor, 
            EscapeeCharacterConfigDto fay, 
            EscapeeCharacterConfigDto electra,
            EscapeeCharacterConfigDto marty, 
            EscapeeCharacterConfigDto robby, 
            EscapeeCharacterConfigDto lucky,
            EscapeeCharacterConfigDto liz,
            EscapeeCharacterConfigDto trickie)
        {
            _presentationByEscapeeType = new Dictionary<EscapeeType, EscapeeCharacterConfigPresentation>()
            {
                [EscapeeType.Rab] = rab.ConvertToPresentation(),
                [EscapeeType.Sup] = sup.ConvertToPresentation(),
                [EscapeeType.Spidi] = spidi.ConvertToPresentation(),
                [EscapeeType.Golem] = golem.ConvertToPresentation(),
                [EscapeeType.Pango] = pango.ConvertToPresentation(),
                [EscapeeType.Chamie] = chamie.ConvertToPresentation(),
                [EscapeeType.Gor] = gor.ConvertToPresentation(),
                [EscapeeType.Electra] = electra.ConvertToPresentation(),
                [EscapeeType.Marty] = marty.ConvertToPresentation(),
                [EscapeeType.Robby] = robby.ConvertToPresentation(),
                [EscapeeType.Fay] = fay.ConvertToPresentation(),
                [EscapeeType.Lucky] = lucky.ConvertToPresentation(),
                [EscapeeType.Liz] = liz.ConvertToPresentation(),
                [EscapeeType.Trickie] = trickie.ConvertToPresentation()
            };
        }

        public EscapeeCharacterConfigPresentation Get(EscapeeType escapeeType)
        {
            return _presentationByEscapeeType[escapeeType];
        }
    }
}