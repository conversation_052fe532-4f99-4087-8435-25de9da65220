using System.Collections.Generic;
using Configs.ServerGameplay.Config.Characters.Catchers;
using GameplayNetworking.Share.Character;

namespace Configs.ServerGameplay.Presentation.Character.Catcher
{
    public class CatcherTeamConfigPresentation : BaseTeamConfigPresentation
    {
        private IReadOnlyDictionary<CatcherType, CatcherCharacterConfigPresentation> _catchers = null!;
        
        public float AttackingMovementSpeed { get; private set; }

        public void Populate(
            CatcherCharacterConfigDto goliath,
            CatcherCharacterConfigDto echo,
            CatcherCharacterConfigDto wiz,
            CatcherCharacterConfigDto ripp,
            CatcherCharacterConfigDto spec,
            Catch<PERSON><PERSON>haracterConfigDto witch,
            CatcherCharacterConfigDto phantom,
            float attackingMovementSpeed
        )
        {
            _catchers = new Dictionary<CatcherType, CatcherCharacterConfigPresentation>()
            {
                [CatcherType.Goliath] = goliath.ConvertToPresentation(),
                [CatcherType.Echo] = echo.ConvertToPresentation(),
                [CatcherType.Wiz] = wiz.ConvertToPresentation(),
                [CatcherType.Ripp] = ripp.ConvertToPresentation(),
                [CatcherType.Spec] = spec.ConvertToPresentation(),
                [CatcherType.Witch] = witch.ConvertToPresentation(),
                [CatcherType.Phantom] = phantom.ConvertToPresentation(),
            };
            AttackingMovementSpeed = attackingMovementSpeed;
        }

        public CatcherCharacterConfigPresentation Get(CatcherType catcherType)
        {
            return _catchers[catcherType];
        }
    }
}