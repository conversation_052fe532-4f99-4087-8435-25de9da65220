using System;

namespace Configs.ServerGameplay.Presentation.Interaction
{
    public interface IInteractionDataConfigPresentation
    {
        public float SqrRadius { get; }

        public float AngleInDeg { get; }

        public TimeSpan Duration { get; }

        public bool ResetProgressOnStop { get; }

        public int MaxConcurrentUsages { get; }
        
        public TimeSpan ProgressBuffDuration { get; }
        public TimeSpan ProgressDeBuffDuration { get; }
        public float ProgressBuffModifier { get; }
        public float ProgressDeBuffModifier { get; }
    }
}