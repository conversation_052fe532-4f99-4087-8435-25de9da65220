using System;

namespace Configs.ServerGameplay.Presentation.Interaction
{
    public class InteractionDataConfigPresentation : IInteractionDataConfigPresentation
    {
        public float SqrRadius { get; set; }

        public float AngleInDeg { get; set; }

        public TimeSpan Duration { get; set; }
        public bool ResetProgressOnStop { get; set; }
        public int MaxConcurrentUsages { get; set; }
        public TimeSpan ProgressBuffDuration { get; set; }
        public TimeSpan ProgressDeBuffDuration { get; set; }
        public float ProgressBuffModifier { get; set; }
        public float ProgressDeBuffModifier { get; set; }
        public float EfficiencyLossPerCharacter01 { get; set; }
    }
}