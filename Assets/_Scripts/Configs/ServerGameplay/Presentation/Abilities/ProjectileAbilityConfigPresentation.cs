namespace Configs.ServerGameplay.Presentation.Abilities
{
    public class ProjectileAbilityConfigPresentation : BaseAbilityConfigPresentation
    {
        public float ProjectileMaxLiveDurationInSec { get; set; }
        public float ProjectileMovementSpeed { get; set; }
        public float ProjectileInteractionSqrRadius { get; set; }
        public float DelayBeforeProjectileDestroyingInSec { get; set; }
        public float SphereCastRadius { get; set; }
    }
}