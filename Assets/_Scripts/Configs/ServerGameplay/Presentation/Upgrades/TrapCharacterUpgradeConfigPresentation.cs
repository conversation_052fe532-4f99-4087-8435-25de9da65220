namespace Configs.ServerGameplay.Presentation.Upgrades
{
    public class TrapCharacterUpgradeConfigPresentation : ICharacterUpgradeConfigPresentation
    {
        public float DurationMultiplier { get; private set; }
        public float RadiusMultiplier { get; private set; }
        
        public void Populate(
            float durationMultiplier, 
            float radiusMultiplier)
        {
            DurationMultiplier = durationMultiplier;
            RadiusMultiplier = radiusMultiplier;
        }
    }
}