namespace Configs.ServerGameplay.Presentation.Upgrades.Concrete
{
    public class FayWallAbilityUpgradeConfigPresentation : ICharacterUpgradeConfigPresentation
    {
        public float CooldownMultiplier { get; private set; }
        public float ObjectLengthMultiplier { get; private set; }
        
        public void Populate(
            float cooldownMultiplier,
            float objectLengthMultiplier)
        {
            CooldownMultiplier = cooldownMultiplier;
            ObjectLengthMultiplier = objectLengthMultiplier;
        }
    }
}