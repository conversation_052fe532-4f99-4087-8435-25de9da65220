namespace Configs.ServerGameplay.Presentation.Upgrades.Concrete
{
    public class SmokeCloudAbilityUpgradeConfigPresentation : ICharacterUpgradeConfigPresentation
    {
        public float CooldownMultiplier { get; private set; }
        public float ObjectLifetimeMultiplier { get; private set; }

        public void Populate(float cooldownMultiplier, float objectLifetimeInMillsMultiplier)
        {
            CooldownMultiplier = cooldownMultiplier;
            ObjectLifetimeMultiplier = objectLifetimeInMillsMultiplier;
        }
    }
}