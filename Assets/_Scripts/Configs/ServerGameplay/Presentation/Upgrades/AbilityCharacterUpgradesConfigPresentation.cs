using System;
using System.Collections.Generic;
using System.Linq;
using Configs.ServerGameplay.Config.Upgrades.Ability.Concrete;
using Jx.Utils.Objects;

namespace Configs.ServerGameplay.Presentation.Upgrades.Ability
{
    public class AbilityCharacterUpgradesConfigPresentation : ICharacterUpgradeConfigPresentation
    {
        private IReadOnlyDictionary<Type, ICharacterUpgradeConfigPresentation> _presentations = null!;

        public void Populate(
            InvisibilityAbilityUpgradeConfigDto invisibility,
            EchoLocationAbilityUpgradeConfigDto echoLocation,
            ElectraFieldAbilityUpgradeConfigDto electraField,
            FayWallAbilityUpgradeConfigDto fayWall,
            ThrowRockAbilityUpgradeConfigDto throwRock,
            KnockingDashAbilityUpgradeConfigDto knockingDash,
            LazyRegenerationAbilityUpgradeConfigDto lazyRegeneration,
            LuckyBugAbilityUpgradeConfigDto luckyBag,
            SmokeCloudAbilityUpgradeConfigDto smokeCloud,
            TeleportAbilityUpgradeConfigDto teleport,
            RollAbilityUpgradeConfigDto roll,
            TimeLapsAbilityUpgradeConfigDto timeLaps,
            SpiderWebAbilityUpgradeConfigDto spiderWeb,
            AngelHelpAbilityUpgradeConfigDto angelHelp,
            MicroDashAbilityUpgradeConfigDto microDash,
            JumpShakeAbilityUpgradeConfigDto jumpShake,
            FearAbilityUpgradeConfigDto fear,
            HookAbilityUpgradeConfigDto hook,
            EscapeeMimicryAbilityUpgradeConfigDto escapeeMimicry,
            TeleportInMarkAbilityUpgradeConfigDto teleportInMark,
            FireballAbilityUpgradeConfigDto fireball)
        {
            _presentations = new List<ICharacterUpgradeConfigPresentation>
            {
                invisibility.ConvertToPresentation(),
                echoLocation.ConvertToPresentation(),
                electraField.ConvertToPresentation(),
                fayWall.ConvertToPresentation(),
                throwRock.ConvertToPresentation(),
                knockingDash.ConvertToPresentation(),
                lazyRegeneration.ConvertToPresentation(),
                luckyBag.ConvertToPresentation(),
                smokeCloud.ConvertToPresentation(),
                roll.ConvertToPresentation(),
                timeLaps.ConvertToPresentation(),
                spiderWeb.ConvertToPresentation(),
                angelHelp.ConvertToPresentation(),
                microDash.ConvertToPresentation(),
                jumpShake.ConvertToPresentation(),
                fear.ConvertToPresentation(),
                hook.ConvertToPresentation(),
                escapeeMimicry.ConvertToPresentation(),
                teleport.ConvertToPresentation(),
                teleportInMark.ConvertToPresentation(),
                fireball.ConvertToPresentation()
            }.ToDictionary(k => k.GetType(), v => v);
        }

        public T GetOrThrow<T>() where T : class, ICharacterUpgradeConfigPresentation
        {
            if (!_presentations.TryGetValue(typeof(T), out var presentation))
                throw new NullReferenceException($"Unknown ability upgrade config type: {nameof(T)}");
            
            return presentation.Cast<T>();
        }
    }
}