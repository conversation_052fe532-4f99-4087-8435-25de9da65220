using System;
using System.Collections.Generic;
using Configs.ServerGameplay.Config.CatcherRage;

namespace Configs.ServerGameplay.Presentation.CatcherRage
{
    public class CatcherRageConfig
    {
        private const int DefaultScore = 0;

        private IReadOnlyDictionary<CatcherRageSource, int> _sources = null!;
        private float _matchTimeDelay;
        
        public TimeSpan MatchTimeDelay { get; private set; }
        public int MaxRageCount { get; private set; }

        public void Populate(
            IReadOnlyDictionary<CatcherRageSource, int> sources,
            float matchTimeDelayInSec,
            int maxRageCount)
        {
            _sources = sources;
            MatchTimeDelay = TimeSpan.FromSeconds(matchTimeDelayInSec);
            MaxRageCount = maxRageCount;
        }

        public int GetSourceScore(CatcherRageSource source)
        {
            return _sources.GetValueOrDefault(source, DefaultScore);
        }
    }
}