using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Configs.ServerGameplay.Presentation.Buffs;
using Configs.ServerGameplay.Presentation.CatcherRage;
using Configs.ServerGameplay.Presentation.Character;
using Configs.ServerGameplay.Presentation.DifficultyGameplayOverrides;
using Configs.ServerGameplay.Presentation.Indicator;
using Configs.ServerGameplay.Presentation.Interaction;
using Configs.ServerGameplay.Presentation.Items;
using Configs.ServerGameplay.Presentation.MiniGames;
using Configs.ServerGameplay.Presentation.Potions;
using Configs.ServerGameplay.Presentation.Specific;
using Configs.ServerGameplay.Presentation.Traps;
using Configs.ServerGameplay.Presentation.Upgrades;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Buffs.Config;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots;
using SceneLogics.GameplayScene.Abilities;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using SceneLogics.GameplayScene.Components.Indicators;
using SceneLogics.GameplayScene.Items;

namespace Configs.ServerGameplay
{
    public interface IServerGameplayConfig
    {
        BotsConfigurationDto Bots { get; }
        PotionsConfigPresentation Potions { get; }
        TrapsConfigPresentation TrapsConfig { get; }
        CharactersConfigPresentation Characters { get; }
        CharacterUpgradesConfigPresentation Upgrades { get; }
        
        bool IsValid();

        GhostCharacterConfigPresentation GetGhostCharacterConfigPresentation();
        T GetCharacterConfigPresentation<T>(int concreteCharacterIndex) where T : BaseCharacterConfigPresentation;
        T GetInteractionConfig<T>(InteractionType interactionType) where T : InteractionDataConfigPresentation;
        T GetAbilityConfigPresentation<T>(AbilityType abilityType) where T : BaseAbilityConfigPresentation;
        ObstacleConfigPresentation GetObstacleConfig();
        TeleportConfigPresentation GetTeleportConfig();
        ScoreSourceConfig GetScoreSourceConfig();
        MedkitConfigPresentation GetMedkitConfig();
        DifficultyGameplayOverrideParametersPresentation? FindDifficultyGameplayOverride();
        CatcherRageConfig GetCatcherRageConfig();

        TimeSpan StartGameDelay { get; }
        TimeSpan MatchDuration { get; }
        TimeSpan AdditionalDurationAfterGateReady { get; }
        T GetItemConfig<T>(ItemType itemType) where T : BaseItemConfigPresentation;
        T GetBuffConfig<T>(BuffType buffType) where T : BuffConfigPresentation;
        IndicatorDataConfigPresentation GetIndicationConfig(IndicationType indicationType);
        T GetMiniGameConfig<T>(MiniGameType type) where T : MiniGameConfigPresentation;
        CageSelfReleaseConfigPresentation GetCageSelfReleaseConfig();
        
        JxBuffConfigDto Buff { get; }
    }
}