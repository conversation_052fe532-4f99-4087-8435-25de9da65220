using Api.Client.User;
using Api.Client.User.Billing;
using Api.Client.User.Billing.Products;
using Api.Client.User.Progress;
using Api.Entities.Characters;
using Api.Entities.Skins;
using Configs.Entities.Rarity;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Share.Character;

namespace Api.Entities
{
    public static class GameEntityExtensions
    {
        public static CharacterType GetCharacterType(this IGameEntity entity)
        {
            return CharacterTypeHelper.GetCharacterType(entity.GetCharacterIndex());
        }
        
        public static EntityRarityType GetRarity(this IGameEntity entity, IEntityRarityProvider provider)
        {
            return provider.GetEntityRarity(entity.Id);
        }
        
        public static int GetCharacterIndex(this IGameEntity entity)
        {
            return entity switch
            {
                CharacterGameEntity characterGameEntity => characterGameEntity.Index,
                SkinGameEntity skin => skin.Character.Index,
                _ => 0
            };
        }
        
        public static CharacterViewIdentifier ToViewIdentifier(
            this CharacterGameEntity characterEntity
        )
        {
            return new CharacterViewIdentifier
            {
                Index = characterEntity.Index,
                SkinEntityId = characterEntity.DefaultSkin.Id
            };
        }

        public static CharacterViewIdentifier ToViewIdentifier(
            this SkinGameEntity skinEntity
        )
        {
            return new CharacterViewIdentifier
            {
                Index = skinEntity.Character.Index,
                SkinEntityId = skinEntity.Id,
            };
        }
    }
}