namespace Configs.Entities.Dto
{
    public static class EntityIdsStorage
    {
        #region CATCHERS

        // Goliath
        public const string GoliathCharacter = "SkeletonGiant_Character";
        public const string GoliathSkinDefault = "SkeletonGiant_Skin_Default";
        public const string GoliathSkinKing = "SkeletonGiant_Skin_King";
        public const string GoliathSkinFireBiker = "SkeletonGiant_Skin_Fire_Biker";
        public const string GoliathSkinMinecraft = "SkeletonGiant_Skin_Minecraft";
        public const string GoliathSkinIce = "SkeletonGiant_Skin_Ice";
        
        // Wiz
        public const string WizCharacter = "SkeletonMage_Character";
        public const string WizSkinDefault = "SkeletonMage_Skin_Default";
        public const string WizSkinMonarch = "SkeletonMage_Skin_Monarch";
        
        // Echo
        public const string EchoCharacter = "BatLord_Character";
        public const string EchoSkinDefault = "BatLord_Skin_Default";
        public const string EchoSkinGold = "BatLord_Skin_Gold";
        
        // Ripp
        public const string RippCharacter = "Reaper_Character";
        public const string RippSkinDefault = "Reaper_Skin_Default";
        public const string RippSkinProtector = "Reaper_Skin_Protector";
        
        // Spec
        public const string SpecCharacter = "DeathMage_Character";
        public const string SpecSkinDefault = "DeathMage_Skin_Default";
        public const string SpecSkinGold = "DeathMage_Skin_Gold";
        public const string SpecSkinNaruto = "DeathMage_Skin_Naruto";
        public const string SpecSkinVenom = "DeathMage_Skin_Venom";
        
        // Phantom
        public const string PhantomCharacter = "Phantom_Character";
        public const string PhantomSkinDefault = "Phantom_Skin_Default";
        public const string PhantomSkinGhostPirate = "Phantom_Skin_Ghost_Pirate";
        
        // Witch
        public const string WitchCharacter = "Witch_Character";
        public const string WitchSkinDefault = "Witch_Skin_Default";

        #endregion

        #region ESCAPEES
        
        // Golem
        public const string GolemCharacter = "GolemEarth_Character";
        public const string GolemSkinDefault = "GolemEarth_Skin_Default";
        public const string GolemSkinQuarterback = "GolemEarth_Skin_Quarterback";
        public const string GolemSkinBodyguard = "GolemEarth_Skin_Bodyguard";
        public const string GolemSkinGreenbarbarian = "GolemEarth_Skin_GreenBarbarian";

        // Rab
        public const string RabCharacter = "Rabbit_Character";
        public const string RabSkinDefault = "Rabbit_Skin_Default";
        public const string RabSkinBiker = "Rabbit_Skin_Biker";
        public const string RabSkinFirestarter = "Rabbit_Skin_Firestarter";
        
        // Spidi
        public const string SpidiCharacter = "Spider_Character";
        public const string SpidiSkinDefault = "Spider_Skin_Default";
        public const string SpidiSkinAristocrat = "Spider_Skin_Aristocrat";
        
        // Sup
        public const string SupCharacter = "Angel_Character";
        public const string SupSkinDefault = "Angel_Skin_Default";
        public const string SupSkinNurse = "Angel_Skin_Nurse";
        public const string SupSkinInferno = "Angel_Skin_Inferno";
        
        // Chamie
        public const string ChamieCharacter = "Chamie_Character";
        public const string ChamieSkinDefault = "Chamie_Skin_Default";
        public const string ChamieSkinStealthMaster = "Chamie_Skin_StealthMaster";
        
        // Gor
        public const string GorCharacter = "Gor_Character";
        public const string GorSkinDefault = "Gor_Skin_Default";
        public const string GorSkinChampion = "Gor_Skin_Champion";
        
        // Fay
        public const string FayCharacter = "Fay_Character";
        public const string FaySkinDefault = "Fay_Skin_Default";
        public const string FaySkinLadybug = "Fay_Skin_Ladybug";
        
        // Pango
        public const string PangoCharacter = "Pango_Character";
        public const string PangoSkinDefault = "Pango_Skin_Default";
        public const string PangoSkinBreakingBad = "Pango_Skin_BreakingBad";
        public const string PangoSkinGirl = "Pango_Skin_Girl";
        public const string PangoSkinWizard = "Pango_Skin_Wizard";
        public const string PangoSkinSuit = "Pango_Skin_Suit";
        public const string PangoSkinBrainmaniac = "Pango_Skin_Brainmaniac";
        
        // Electra
        public const string ElectraCharacter = "Electra_Character";
        public const string ElectraSkinDefault = "Electra_Skin_Default";
        public const string ElectraSkinEmo = "Electra_Skin_Emo";
        public const string ElectraSkinStudent = "Electra_Skin_Student";
        
        // Lucky
        public const string LuckyCharacter = "Lucky_Character";
        public const string LuckySkinDefault = "Lucky_Skin_Default";
        public const string LuckySkinPussInBoots = "Lucky_Skin_PussInBoots";
        public const string LuckySkinGirl = "Lucky_Skin_Girl";
        
        // Robby
        public const string RobbyCharacter = "Robby_Character";
        public const string RobbySkinDefault = "Robby_Skin_Default";
        public const string RobbySkinRasta = "Robby_Skin_Rasta";
        public const string RobbySkinTeddy = "Robby_Skin_Teddy";
        
        // Marty
        public const string MartyCharacter = "Marty_Character";
        public const string MartySkinDefault = "Marty_Skin_Default";
        public const string MartySkinNinja = "Marty_Skin_Ninja";
        public const string MartySkinSensei = "Marty_Skin_Sensei";
        
        // Liz
        public const string LizCharacter = "Liz_Character";
        public const string LizSkinDefault = "Liz_Skin_Default";
        
        // Trickie
        public const string TrickieCharacter = "Trickie_Character";
        public const string TrickieSkinDefault = "Trickie_Skin_Default";
        public const string TrickieSkinPirate = "Trickie_Skin_Pirate";
        public const string TrickieSkinHood = "Trickie_Skin_Hood";

        #endregion
    }
}