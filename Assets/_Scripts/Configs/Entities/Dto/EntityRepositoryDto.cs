using System.Collections.Generic;
using System.Linq;
using Api.Entities;
using Api.Entities.Characters;
using Api.Entities.Skins;
using GameplayNetworking.Share.Character;
using Jx.Utils.Collections;
using SceneLogics.GameplayScene.Abilities;

namespace Configs.Entities.Dto
{
    public class EntityRepositoryDto
    {
        public const EscapeeType DefaultEscapee = EscapeeType.Golem;
        public const CatcherType DefaultCatcher = CatcherType.Goliath;
        
        private readonly CharacterGameEntity[] _escapees;
        private readonly CharacterGameEntity[] _catchers;
        private readonly CharacterGameEntity[] _allCharacters;
        private readonly IDictionary<int, CharacterGameEntity> _characterByIndex;
        private readonly IDictionary<string, IGameEntity> _entityById;
        
        public EntityRepositoryDto()
        {
            _catchers = EnumerateCatchers().ToArray();
            _escapees = EnumerateEscapees().ToArray();
            _allCharacters = _escapees.Union(_catchers).ToArray();
            _characterByIndex = new Dictionary<int, CharacterGameEntity>();

            foreach (var escapee in _escapees)
                _characterByIndex[escapee.Index] = escapee;
            foreach (var catcher in _catchers)
                _characterByIndex[catcher.Index] = catcher;

            _entityById = new Dictionary<string, IGameEntity>();
            foreach (var character in _characterByIndex.Values)
            {
                _entityById[character.Id] = character;
                foreach (var skin in character.Skins)
                {
                    _entityById[skin.Id] = skin;
                }
            }
        }

        public IReadOnlyList<CharacterGameEntity> GetEscapees() => _escapees;
        public IReadOnlyList<CharacterGameEntity> GetCatchers() => _catchers;
        public IReadOnlyList<CharacterGameEntity> GetAllCharacters() => _allCharacters;

        public CharacterGameEntity? FindCharacter(int index)
        {
            return _characterByIndex.GetOrDefault(index);
        }
        
        public IGameEntity? FindEntity(string id)
        {
            return _entityById.GetOrDefault(id);
        }

        private static IEnumerable<CharacterGameEntity> EnumerateEscapees()
        {
            // Golem
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.GolemCharacter, 
                contentPath: "golem", 
                index: EscapeeType.Golem.ToIndex(), 
                ability: AbilityType.ThrowRock,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.GolemSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.GolemSkinQuarterback, "quarterback"),
                    new SkinGameEntity(EntityIdsStorage.GolemSkinBodyguard, "bodyguard"),
                    new SkinGameEntity(EntityIdsStorage.GolemSkinGreenbarbarian, "greenbarbarian")
                }
            );
            
            // Rab
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.RabCharacter, 
                contentPath: "rab", 
                index: EscapeeType.Rab.ToIndex(), 
                ability: AbilityType.Roll,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.RabSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.RabSkinBiker, "biker"),
                    new SkinGameEntity(EntityIdsStorage.RabSkinFirestarter, "firestarter")
                }
            );
            
            // Spidi
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.SpidiCharacter, 
                contentPath: "spidi", 
                index: EscapeeType.Spidi.ToIndex(), 
                ability: AbilityType.SpiderWeb,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.SpidiSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.SpidiSkinAristocrat, "aristocrat")
                }
            );
            
            // Sup
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.SupCharacter, 
                contentPath: "sup", 
                index: EscapeeType.Sup.ToIndex(), 
                ability: AbilityType.AngelHelp,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.SupSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.SupSkinNurse, "nurse"),
                    new SkinGameEntity(EntityIdsStorage.SupSkinInferno, "inferno")
                }
            );
            
            // Chamie
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.ChamieCharacter, 
                contentPath: "chamie", 
                index: EscapeeType.Chamie.ToIndex(), 
                ability: AbilityType.Invisibility,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.ChamieSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.ChamieSkinStealthMaster, "stealthmaster")
                }
            );
            
            // Gor
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.GorCharacter, 
                contentPath: "Gor", 
                index: EscapeeType.Gor.ToIndex(), 
                ability: AbilityType.KnockingDash,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.GorSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.GorSkinChampion, "champion")
                }
            );
            
            // Fay
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.FayCharacter, 
                contentPath: "Fay", 
                index: EscapeeType.Fay.ToIndex(), 
                ability: AbilityType.FayWall,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.FaySkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.FaySkinLadybug, "ladybug")
                }
            );
            
            // Pango
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.PangoCharacter, 
                contentPath: "Pango", 
                index: EscapeeType.Pango.ToIndex(), 
                ability: AbilityType.Teleport,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.PangoSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.PangoSkinBreakingBad, "breakingbad"),
                    new SkinGameEntity(EntityIdsStorage.PangoSkinGirl, "girl"),
                    new SkinGameEntity(EntityIdsStorage.PangoSkinWizard, "wizard"),
                    new SkinGameEntity(EntityIdsStorage.PangoSkinSuit, "suit"),
                    new SkinGameEntity(EntityIdsStorage.PangoSkinBrainmaniac, "brainmaniac")
                }
            );
            
            // Electra
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.ElectraCharacter, 
                contentPath: "Electra", 
                index: EscapeeType.Electra.ToIndex(), 
                ability: AbilityType.ElectraField,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.ElectraSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.ElectraSkinEmo, "emo"),
                    new SkinGameEntity(EntityIdsStorage.ElectraSkinStudent, "student")
                }
            );
            
            // Lucky
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.LuckyCharacter, 
                contentPath: "Lucky", 
                index: EscapeeType.Lucky.ToIndex(), 
                ability: AbilityType.LuckyBag,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.LuckySkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.LuckySkinPussInBoots, "pussinboots"),
                    new SkinGameEntity(EntityIdsStorage.LuckySkinGirl, "girl")
                }
            );
            
            // Robby
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.RobbyCharacter, 
                contentPath: "Robby", 
                index: EscapeeType.Robby.ToIndex(), 
                ability: AbilityType.TimeLaps,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.RobbySkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.RobbySkinRasta, "rasta"),
                    new SkinGameEntity(EntityIdsStorage.RobbySkinTeddy, "teddy")
                }
            );
            
            // Marty
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.MartyCharacter, 
                contentPath: "Marty", 
                index: EscapeeType.Marty.ToIndex(), 
                ability: AbilityType.SmokeCloud,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.MartySkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.MartySkinNinja, "ninja"),
                    new SkinGameEntity(EntityIdsStorage.MartySkinSensei, "sensei")
                }
            );
            
            // Liz
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.LizCharacter, 
                contentPath: "Liz", 
                index: EscapeeType.Liz.ToIndex(), 
                ability: AbilityType.LazyRegeneration,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.LizSkinDefault, SkinGameEntity.DefaultContentPath)
                }
            );
            
            // Trickie
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.TrickieCharacter, 
                contentPath: "Trickie", 
                index: EscapeeType.Trickie.ToIndex(), 
                ability: AbilityType.MicroDash,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.TrickieSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.TrickieSkinPirate, "pirate"),
                    new SkinGameEntity(EntityIdsStorage.TrickieSkinHood, "hood")
                }
            );
        }

        private static IEnumerable<CharacterGameEntity> EnumerateCatchers()
        {
            // Goliath
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.GoliathCharacter, 
                contentPath: "goliath", 
                index: CatcherType.Goliath.ToIndex(), 
                ability: AbilityType.JumpShake,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.GoliathSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.GoliathSkinKing, "fallenking"),
                    new SkinGameEntity(EntityIdsStorage.GoliathSkinFireBiker, "firebiker"),
                    new SkinGameEntity(EntityIdsStorage.GoliathSkinMinecraft, "minecraft"),
                    new SkinGameEntity(EntityIdsStorage.GoliathSkinIce, "ice")
                }
            );
            
            // Wiz
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.WizCharacter, 
                contentPath: "wiz", 
                index: CatcherType.Wiz.ToIndex(), 
                ability: AbilityType.Fireball,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.WizSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.WizSkinMonarch, "lordoffire")
                }
            );
            
            // Echo
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.EchoCharacter, 
                contentPath: "echo", 
                index: CatcherType.Echo.ToIndex(), 
                ability: AbilityType.EchoLocation,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.EchoSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.EchoSkinGold, "dracula")
                }
            );
            
            // Ripp
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.RippCharacter, 
                contentPath: "ripp", 
                index: CatcherType.Ripp.ToIndex(), 
                ability: AbilityType.Hook,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.RippSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.RippSkinProtector, "horrormask")
                }
            );
            
            // Spec
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.SpecCharacter, 
                contentPath: "spec", 
                index: CatcherType.Spec.ToIndex(), 
                ability: AbilityType.EscapeeMimicry,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.SpecSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.SpecSkinGold, "manyfaced"),
                    new SkinGameEntity(EntityIdsStorage.SpecSkinNaruto, "naruto"),
                    new SkinGameEntity(EntityIdsStorage.SpecSkinVenom, "venom")
                }
            );
            
            // Phantom
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.PhantomCharacter, 
                contentPath: "Phantom", 
                index: CatcherType.Phantom.ToIndex(), 
                ability: AbilityType.Fear,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.PhantomSkinDefault, SkinGameEntity.DefaultContentPath),
                    new SkinGameEntity(EntityIdsStorage.PhantomSkinGhostPirate, "ghostpirate")
                }
            );
            
            // Witch
            yield return new CharacterGameEntity(
                enabled: true, 
                id: EntityIdsStorage.WitchCharacter, 
                contentPath: "Witch", 
                index: CatcherType.Witch.ToIndex(), 
                ability: AbilityType.TeleportInMark,
                skins: new []
                {
                    new SkinGameEntity(EntityIdsStorage.WitchSkinDefault, SkinGameEntity.DefaultContentPath)
                }
            );
        }
    }
}