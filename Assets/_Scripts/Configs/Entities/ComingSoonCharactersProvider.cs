using System.Collections.Generic;
using Configs.Entities.Rarity;
using GameplayNetworking.Share.Character;

namespace Configs.Entities
{
    public class ComingSoonCharactersProvider
    {
        public static ComingSoonCharactersProvider Instance { get; } = new();
        
        private static readonly IReadOnlyList<ComingSoonCharacterInfo> _escapees = new List<ComingSoonCharacterInfo>()
        {
            new (EscapeeType.Ent.ToIndex(), EntityRarityType.Epic),
            new (EscapeeType.Ivy.ToIndex(), EntityRarityType.Rare),
        };
        
        private static readonly IReadOnlyList<ComingSoonCharacterInfo> _catchers = new List<ComingSoonCharacterInfo>()
        {
            new (CatcherType.Zombie.ToIndex(), EntityRarityType.Rare),
            new (CatcherType.Hunter.ToIndex(), EntityRarityType.Rare),
        };
        
        public IReadOnlyList<ComingSoonCharacterInfo> GetEscapees() => _escapees;
        public IReadOnlyList<ComingSoonCharacterInfo> GetCatchers() => _catchers;
    }
}