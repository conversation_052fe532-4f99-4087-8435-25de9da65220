using System.Collections.Generic;
using Configs.Entities.Dto;
using Jx.Utils.Logging;

namespace Configs.Entities.Rarity
{
    public class EntityRarityConfigDto
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(EntityRarityConfigDto));
        
        private static readonly IReadOnlyDictionary<string, EntityRarityType> _rarityByEntityId = new Dictionary<string, EntityRarityType>()
        {
            // Golem
            [EntityIdsStorage.GolemCharacter] = EntityRarityType.Common,
            [EntityIdsStorage.GolemSkinGreenbarbarian] = EntityRarityType.Epic,
            [EntityIdsStorage.GolemSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.GolemSkinBodyguard] = EntityRarityType.Epic,
            [EntityIdsStorage.GolemSkinQuarterback] = EntityRarityType.Common,

            // Goliath
            [EntityIdsStorage.GoliathCharacter] = EntityRarityType.Common,
            [EntityIdsStorage.GoliathSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.GoliathSkinKing] = EntityRarityType.Common,
            [EntityIdsStorage.GoliathSkinFireBiker] = EntityRarityType.Legendary,
            [EntityIdsStorage.GoliathSkinMinecraft] = EntityRarityType.Rare,
            [EntityIdsStorage.GoliathSkinIce] = EntityRarityType.Epic,

            // Wiz
            [EntityIdsStorage.WizCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.WizSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.WizSkinMonarch] = EntityRarityType.Epic,

            // Echo
            [EntityIdsStorage.EchoCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.EchoSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.EchoSkinGold] = EntityRarityType.Common,

            // Ripp
            [EntityIdsStorage.RippCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.RippSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.RippSkinProtector] = EntityRarityType.Rare,

            // Spec
            [EntityIdsStorage.SpecCharacter] = EntityRarityType.Common,
            [EntityIdsStorage.SpecSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.SpecSkinGold] = EntityRarityType.Rare,
            [EntityIdsStorage.SpecSkinNaruto] = EntityRarityType.Epic,
            [EntityIdsStorage.SpecSkinVenom] = EntityRarityType.Epic,

            // Phantom
            [EntityIdsStorage.PhantomCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.PhantomSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.PhantomSkinGhostPirate] = EntityRarityType.Epic,

            // Witch
            [EntityIdsStorage.WitchCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.WitchSkinDefault] = EntityRarityType.Common,

            // Rab
            [EntityIdsStorage.RabCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.RabSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.RabSkinBiker] = EntityRarityType.Rare,
            [EntityIdsStorage.RabSkinFirestarter] = EntityRarityType.Common,

            // Spidi
            [EntityIdsStorage.SpidiCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.SpidiSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.SpidiSkinAristocrat] = EntityRarityType.Common,

            // Sup
            [EntityIdsStorage.SupCharacter] = EntityRarityType.Common,
            [EntityIdsStorage.SupSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.SupSkinNurse] = EntityRarityType.Common,
            [EntityIdsStorage.SupSkinInferno] = EntityRarityType.Epic,

            // Chamie
            [EntityIdsStorage.ChamieCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.ChamieSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.ChamieSkinStealthMaster] = EntityRarityType.Common,

            // Gor
            [EntityIdsStorage.GorCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.GorSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.GorSkinChampion] = EntityRarityType.Common,

            // Fay
            [EntityIdsStorage.FayCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.FaySkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.FaySkinLadybug] = EntityRarityType.Common,

            // Pango
            [EntityIdsStorage.PangoCharacter] = EntityRarityType.Common,
            [EntityIdsStorage.PangoSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.PangoSkinBreakingBad] = EntityRarityType.Epic,
            [EntityIdsStorage.PangoSkinGirl] = EntityRarityType.Rare,
            [EntityIdsStorage.PangoSkinWizard] = EntityRarityType.Epic,
            [EntityIdsStorage.PangoSkinSuit] = EntityRarityType.Epic,
            [EntityIdsStorage.PangoSkinBrainmaniac] = EntityRarityType.Common,

            // Electra
            [EntityIdsStorage.ElectraCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.ElectraSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.ElectraSkinEmo] = EntityRarityType.Epic,
            [EntityIdsStorage.ElectraSkinStudent] = EntityRarityType.Epic,

            // Lucky
            [EntityIdsStorage.LuckyCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.LuckySkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.LuckySkinPussInBoots] = EntityRarityType.Epic,
            [EntityIdsStorage.LuckySkinGirl] = EntityRarityType.Rare,

            // Robby
            [EntityIdsStorage.RobbyCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.RobbySkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.RobbySkinRasta] = EntityRarityType.Epic,
            [EntityIdsStorage.RobbySkinTeddy] = EntityRarityType.Epic,

            // Marty
            [EntityIdsStorage.MartyCharacter] = EntityRarityType.Rare,
            [EntityIdsStorage.MartySkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.MartySkinNinja] = EntityRarityType.Epic,
            [EntityIdsStorage.MartySkinSensei] = EntityRarityType.Epic,

            // Liz
            [EntityIdsStorage.LizCharacter] = EntityRarityType.Epic,
            [EntityIdsStorage.LizSkinDefault] = EntityRarityType.Common,

            // Trickie
            [EntityIdsStorage.TrickieCharacter] = EntityRarityType.Legendary,
            [EntityIdsStorage.TrickieSkinDefault] = EntityRarityType.Common,
            [EntityIdsStorage.TrickieSkinPirate] = EntityRarityType.Epic,
            [EntityIdsStorage.TrickieSkinHood] = EntityRarityType.Epic
        };
        
        public EntityRarityType Get(string entityId)
        {
            if (_rarityByEntityId.TryGetValue(entityId, out var rarity))
                return rarity;
            
            _logger.LogError($"Failed to find rarity for entity '{entityId}'. Common rarity is used.");
            return EntityRarityType.Common;
        }
    }
}