using System;

namespace Configs.Game
{
    public sealed class JxGameConfigIdentifier : IEquatable<JxGameConfigIdentifier>
    {
        public JxGameConfigIdentifier(string type, string preset)
        {
            Type = type;
            Preset = preset;
        }

        public string Type { get; }
        public string Preset { get; }

        public string Name => $"{Type}_{Preset}";
        
        public bool Equals(JxGameConfigIdentifier? other)
        {
            if (ReferenceEquals(null, other))
                return false;
            if (ReferenceEquals(this, other))
                return true;
            return Type == other.Type && Preset == other.Preset;
        }

        public override bool Equals(object? obj) => ReferenceEquals(this, obj) || obj is JxGameConfigIdentifier other && Equals(other);

        public override int GetHashCode() => HashCode.Combine(Type, Preset);

        public static bool operator ==(JxGameConfigIdentifier? left, JxGameConfigIdentifier? right) => Equals(left, right);

        public static bool operator !=(JxGameConfigIdentifier? left, JxGameConfigIdentifier? right) => !Equals(left, right);
    }
}