using System;
using System.Collections.Generic;
using System.Threading;
using BestHTTP;
using Configs.Game.Sections;
using Cysharp.Threading.Tasks;
using Jx.ApiGateway.Web.Http;
using Jx.ApiGateway.Web.Http.Responses;
using Jx.ApiGateway.Web.UrlSwitching;
using Jx.Utils.Threading;
using UnityEngine.Scripting;

namespace Configs.Game
{
    public class GameConfigProvider : IGameConfigProvider, IDisposable
    {
        private readonly JxGameConfigProviderOptions _options;
        private readonly JxHttpAsyncClient _httpClient;
        
        private readonly GameplayConfigSection _gameplay;

        private JxTimer? _reloadTimer;

        [Preserve]
        public GameConfigProvider(JxGameConfigProviderOptions? options = null)
        {
            _options = options ?? JxGameConfigProviderOptions.Default;
            _httpClient = new JxHttpAsyncClient();
            
            _gameplay = new GameplayConfigSection();
        }

        public IGameplayConfigSection Gameplay => _gameplay;

        public UniTask InitializeAsync(CancellationToken cancellationToken) => UniTask.RunOnThreadPool(LoadEmbeddedAsync, cancellationToken: cancellationToken);

        public void SynchronizeInBackground(CancellationToken cancellationToken) => UniTask.RunOnThreadPool(SynchronizeAsync, cancellationToken: cancellationToken).Forget();
        
        public void StartReloadingInBackground(TimeSpan delay, CancellationToken cancellationToken, Func<UniTask> onReloadAsync)
        {
            _reloadTimer?.Dispose();
            _reloadTimer = new JxTimer(
                $"{nameof(GameConfigProvider)}",
                async ct =>
                {
                    await SynchronizeAsync();
                    await onReloadAsync();
                },
                () => delay,
                delay
            );
        }

        public void Dispose() => _reloadTimer?.Dispose();

        private async UniTask LoadEmbeddedAsync()
        {
            foreach (var section in EnumerateSections())
                await section.LoadEmbeddedAsync();
        }

        private async UniTask SynchronizeAsync()
        {
            Func<JxGameConfigIdentifier, UniTask<string?>> loadRawFunc = LoadRemoteAsync;
            
            foreach (var section in EnumerateSections())
                await section.SynchronizeAsync(loadRawFunc);

            return;

            async UniTask<string?> LoadRemoteAsync(JxGameConfigIdentifier identifier)
            {
                var uri = _options.UriFactory.Create(identifier.Type, identifier.Preset);

                var sender = new JxHttpRequestSender<string>(
                    HTTPMethods.Get,
                    _httpClient,
                    new JxNonSwitchableUrl(uri),
                    new JxStringHttpResponseParser(),
                    null!
                );

                using (var response = await sender.SendAsync(default, null))
                {
                    if (response.StatusCode != 200)
                        return null;

                    var rawConfig = response.Read().Parse(default);
                    return rawConfig;
                }
            }
        }

        private IEnumerable<IJxGameConfigInternalSection> EnumerateSections()
        {
            yield return _gameplay;
        }

#if UNITY_EDITOR

        public IEnumerable<JxGameConfigIdentifier> EnumerateEmbeddedIdentifiers()
        {
            foreach (var section in EnumerateSections())
            foreach (var identifier in section.EditorEnumerateEmbedded())
                yield return identifier;
        }
#endif
    }
}