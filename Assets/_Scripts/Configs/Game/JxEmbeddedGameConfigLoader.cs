using System;
using Cysharp.Threading.Tasks;
using Jx.Utils.Files.Embedded;
using Jx.Utils.Logging;
using Jx.Utils.Serialization.Binary;

namespace Configs.Game
{
    public class JxEmbeddedGameConfigLoader
    {
        public const string EmbeddedDirectory = "configs";
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxEmbeddedGameConfigLoader));
        
        public static JxEmbeddedGameConfigLoader Instance { get; } = new JxEmbeddedGameConfigLoader();
        
        private JxEmbeddedGameConfigLoader() { }

        public async UniTask<TModel?> LoadAsync<TModel>(JxGameConfigIdentifier identifier, IJxBinarySerializer<TModel> serializer)
            where TModel : class
        {
            try
            {
                var reader = new JxUnityEmbeddedStreamingAssetsFileReader(EmbeddedDirectory);
                return await reader.ReadAsync(identifier.Name, serializer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }

            return null;
        }
    }
}