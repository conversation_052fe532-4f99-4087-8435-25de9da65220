using System;
using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;
using Jx.ApiGateway.Web.Http;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Serialization.Binary;
using Jx.Utils.Serialization.Json;

namespace Configs.Game
{
    public abstract class JxGameConfigSection<TInterface, TModel> : IGameConfigSection<TInterface>, IJxGameConfigInternalSection
        where TInterface : class
        where TModel : class, TInterface
    {
        private readonly IJxBinarySerializer<TModel> _serializer;
        private readonly JxGameConfigStorage<TInterface, TModel> _storage;

        protected JxGameConfigSection()
        {
            _serializer = new JxJsonBinarySerializer<TModel>(JxNewtonsoftJsonSerializer.Default, Encoding.UTF8);
            _storage = new JxGameConfigStorage<TInterface, TModel>();
        }

        public async UniTask LoadEmbeddedAsync()
        {
            foreach (var identifier in EnumerateEmbedded())
            {
                var config = await JxEmbeddedGameConfigLoader.Instance.LoadAsync(identifier, _serializer);
                if (config == null)
                    continue;

                _storage.Change(identifier.Name, config);
            }
        }

        public async UniTask SynchronizeAsync(Func<JxGameConfigIdentifier, UniTask<string?>> loadRawAsync)
        {
            foreach (var identifier in EnumerateEmbedded())
            {
                var raw = await loadRawAsync(identifier);
                if (string.IsNullOrEmpty(raw))
                    continue;
                
                var remoteConfig = JxNewtonsoftJsonSerializer.Default.Deserialize<TModel>(raw);
                if (remoteConfig != null)
                    _storage.Change(identifier.Name, remoteConfig);
            }
        }

#if UNITY_EDITOR
        public IEnumerable<JxGameConfigIdentifier> EditorEnumerateEmbedded() => EnumerateEmbedded();
#endif

        protected UniTask<IJxChangeableObject<TInterface?>> GetAsync(JxGameConfigIdentifier identifier) => UniTask.FromResult(_storage.Get(identifier.Name));

        protected abstract IEnumerable<JxGameConfigIdentifier> EnumerateEmbedded();
    }
}