using System;
using System.Threading;
using Configs.Game.Sections;
using Cysharp.Threading.Tasks;

namespace Configs.Game
{
    public interface IGameConfigProvider
    {
        IGameplayConfigSection Gameplay { get; }
        
        UniTask InitializeAsync(CancellationToken cancellationToken);
        
        void SynchronizeInBackground(CancellationToken cancellationToken);
        void StartReloadingInBackground(TimeSpan delay, CancellationToken cancellationToken, Func<UniTask> onReloadAsync);
    }
}