using System;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using Jx.Utils.Logging;
using Tutorial.Executables.Groups;
using Tutorial.Executables.Nodes.Shared;
using Tutorial.Schemas;
using UI.Screens.MainMenuScene.Characters;
using UI.Screens.MainMenuScene.Characters.Upgrade.CharacterUpgrade;

namespace Tutorial.Executables.Nodes.MainMenu.CharactersScreen
{
    public class WaitCharacterInCollectionSelectedTutorialNode : AbstractTutorialExecutable
    {
        private readonly int _characterIndex;
        private readonly ITutorialCharactersScreenController _screenController;
        private readonly TutorialExecutableFactory _executableFactory;

        public WaitCharacterInCollectionSelectedTutorialNode(
            int characterIndex, 
            ITutorialCharactersScreenController screenController,
            TutorialExecutableFactory executableFactory)
        {
            _characterIndex = characterIndex;
            _screenController = screenController;
            _executableFactory = executableFactory;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            var view = _screenController.Collection.Container.Views
                .FirstOrDefault(v => v.Parameters.Interaction.CharacterIndex == _characterIndex);
            if (view == null)
            {
                Logger.LogError($"Not found target character '{_characterIndex}'");
                return;
            }
            
            var waitUpgradesPage = new WaitChangeableObjectChangeTutorialNode<Type>(_screenController.CurrentSubScreenType, typeof(CharacterUpgradeSubScreen));

            var exec = new TutorialSequenceGroup()
                .With(
                    _executableFactory
                        .BuildButtonAccentPointer(view.AccentInfo)
                        .SupplyAwaitWith(waitUpgradesPage)
                );

            using (_screenController.Collection.Container.DisableScroll())
            {
                await exec.ExecuteAsync(cancellationToken);
            }
            
            await WaitSuppliersAsync(cancellationToken);
        }
    }
}