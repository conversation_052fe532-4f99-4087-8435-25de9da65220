using System.Threading;
using Cysharp.Threading.Tasks;

namespace Tutorial.Executables.Nodes.Shared
{
    public class WaitFrameTutorialNode : AbstractTutorialExecutable
    {
        public WaitFrameTutorialNode()
        {
        }
        
        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            await UniTask.DelayFrame(2, cancellationToken: cancellationToken);
            await WaitSuppliersAsync(cancellationToken);
        }
    }
}