using System.Threading;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using Zenject.Internal;

namespace Tutorial.Executables.Nodes.Shared
{
    [Preserve]
    public class SetZeroTimeScaleTutorialNode : AbstractTutorialExecutable
    {
        [Preserve]
        public SetZeroTimeScaleTutorialNode()
        { }
        
        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            using (JxUnityExtensions.SetZeroTimeScale(approximately: false))
            {
                await WaitSuppliersAsync(cancellationToken);
            }
        }
    }
}