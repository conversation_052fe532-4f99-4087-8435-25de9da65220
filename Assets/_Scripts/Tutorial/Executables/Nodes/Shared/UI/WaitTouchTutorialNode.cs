using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Shared.UI
{
    public class WaitTouchTutorialNode : AbstractTutorialExecutable
    {
        public WaitTouchTutorialNode()
        { }
        
        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            await UniTask.WaitUntil(() => Input.GetMouseButton(0), cancellationToken: cancellationToken);
            
            await WaitSuppliersAsync(cancellationToken);
        }
    }
}