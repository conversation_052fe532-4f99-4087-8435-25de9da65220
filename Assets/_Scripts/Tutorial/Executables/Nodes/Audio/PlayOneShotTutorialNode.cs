using System.Threading;
using Audio.Manager;
using Audio.Provider.Sounds;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Audio
{
    public class PlayOneShotTutorialNode : AbstractTutorialExecutable
    {
        private readonly SoundEventIdentifier _sound;
        private readonly Transform? _transform;
        private readonly Vector3? _position;

        public PlayOneShotTutorialNode(
            SoundEventIdentifier sound,
            Transform target)
        {
            _sound = sound;
            _transform = target;
        }
        
        public PlayOneShotTutorialNode(
            SoundEventIdentifier sound,
            Vector3 position)
        {
            _sound = sound;
            _position = position;
        }
        
        public PlayOneShotTutorialNode(
            SoundEventIdentifier sound)
        {
            _sound = sound;
            _transform = null;
            _position = null;
        }
        
        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            if (_position.HasValue)
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(_sound, _position.Value);
            }
            else if (_transform != null)
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(_sound, _transform);
            }
            else
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(_sound);
            }
            
            await WaitSuppliersAsync(cancellationToken);
        }
    }
}