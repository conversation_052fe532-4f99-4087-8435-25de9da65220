using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Gameplay.Map
{
    public class DestinationPositionTutorialNode : AbstractDestinationTutorialNode
    {
        private readonly Transform _originTransform;
        private readonly Vector3 _targetPosition;

        public DestinationPositionTutorialNode(Transform originTransform, Vector3 targetPosition, float targetDistance = 2f) : base(targetDistance)
        {
            _originTransform = originTransform;
            _targetPosition = targetPosition;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            await UniTask.WaitUntil(() => IsDestinationReached(_originTransform.position, _targetPosition), 
                cancellationToken: cancellationToken);

            await WaitSuppliersAsync(cancellationToken);
        }
    }
}