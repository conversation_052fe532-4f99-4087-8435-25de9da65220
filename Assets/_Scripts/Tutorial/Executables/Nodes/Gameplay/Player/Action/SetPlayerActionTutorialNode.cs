using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;

namespace Tutorial.Executables.Nodes.Gameplay.Player.Action
{
    public class SetPlayerActionTutorialNode : AbstractTutorialExecutable
    {
        private readonly NetGamePlayer _player;
        private readonly ActionType _type;

        public SetPlayerActionTutorialNode(NetGamePlayer player, ActionType type)
        {
            _player = player;
            _type = type;
        }
        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            _player.Action.SetNew(_type);

            await WaitSuppliersAsync(cancellationToken);
        }
    }
}