using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;

namespace Tutorial.Executables.Nodes.Gameplay.Player.Ability
{
    public class WaitForUsedAbilityTutorialNode : AbstractTutorialExecutable
    {
        private readonly NetGamePlayer _player;

        public WaitForUsedAbilityTutorialNode(NetGamePlayer player)
        {
            _player = player;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            await UniTask.WaitUntil(() => _player.BaseSyncData.Action.Value == ActionType.AbilityInProgress,
                cancellationToken: cancellationToken);
            
            await WaitSuppliersAsync(cancellationToken);
        }
    }
}