using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Logging;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Gameplay.Player.Ability
{
    public class UseAbilityTutorialNode : AbstractTutorialExecutable
    {
        private readonly NetGamePlayer _player;
        private readonly Transform _targetTransform;

        public UseAbilityTutorialNode(NetGamePlayer player, Transform targetTransform)
        {
            _player = player;
            _targetTransform = targetTransform;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            if (_player.IsBot())
            {
                Logger.LogError($"Bot can't use this node.");
                return;
            }
            
            await _player.Ability.Controller.TryProcessAsync(
                new GenericAbilityInputParameters(_player.transform.position, _targetTransform.position), cancellationToken);

            await WaitSuppliersAsync(cancellationToken);
        }
    }
}