using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Collections;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Gameplay.Player.Movement.NavMeshAgent
{
    public class MoveThroughRandomPointTutorialNode : MovePlayerTutorialNode
    {
        private readonly IEnumerable<Transform> _movePointsTransforms;
        
        public MoveThroughRandomPointTutorialNode(NetGamePlayer player, IEnumerable<Transform> movePointsTransforms, float? stoppingDistance = null) : base(player, default, stoppingDistance)
        {
            _movePointsTransforms = movePointsTransforms;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            UpdateTargetPosition(_movePointsTransforms.RandomOrDefault().position);
            await base.ExecuteAsync(cancellationToken);
        }
    }
}