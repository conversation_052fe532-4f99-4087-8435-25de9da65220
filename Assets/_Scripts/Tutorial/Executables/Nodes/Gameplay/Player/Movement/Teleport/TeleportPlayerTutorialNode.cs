using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Core.Helpers.NavMesh;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace Tutorial.Executables.Nodes.Gameplay.Player.Movement.Teleport
{
    public class TeleportPlayerTutorialNode : AbstractTutorialExecutable
    {
        private readonly Vector3 _teleportPosition;
        private readonly IEnumerable<KeyValuePair<NetGamePlayer, JxNavMeshAgentComponent>> _agentByPlayer;
        private readonly bool _enableAgentOnTeleported;

        public TeleportPlayerTutorialNode(Vector3 position, NetGamePlayer player, bool enableAgentOnTeleported) 
            : this(position, new []{player}, enableAgentOnTeleported)
        {
        }

        public TeleportPlayerTutorialNode(Vector3 position, IEnumerable<NetGamePlayer> players, bool enableAgentOnTeleported)
        {
            position.y = 0f;
            _teleportPosition = position;
            _agentByPlayer = players.Select(p =>
                new KeyValuePair<NetGamePlayer, JxNavMeshAgentComponent>(p, p.GetComponentOrThrow<JxNavMeshAgentComponent>()));
            
            _enableAgentOnTeleported = enableAgentOnTeleported;
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken)
        {
            foreach (var pair in _agentByPlayer)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                var player = pair.Key;
                var agent = pair.Value;

                agent.enabled = false;
                player.NetTransform.ServerTeleport(_teleportPosition, Quaternion.identity);
                agent.enabled = _enableAgentOnTeleported;
            }

            await WaitSuppliersAsync(cancellationToken);
        }
    }
}