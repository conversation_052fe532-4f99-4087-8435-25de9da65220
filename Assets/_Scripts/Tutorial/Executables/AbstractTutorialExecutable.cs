using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Jx.Utils.Logging;

namespace Tutorial.Executables
{
    public abstract class AbstractTutorialExecutable
    {
        public static readonly AbstractTutorialExecutable Empty = new EmptyTutorialExecutable();
        
        protected IReadOnlyList<AbstractTutorialExecutable> _suppliedExecutables = new List<AbstractTutorialExecutable>();

        protected AbstractTutorialExecutable()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }
        
        protected IJxLogger Logger { get; }

        public void Supply(IReadOnlyList<AbstractTutorialExecutable> executable)
        {
            _suppliedExecutables = executable;
        }
        
        public abstract UniTask ExecuteAsync(CancellationToken cancellationToken);

        protected async UniTask WaitSuppliersAsync(CancellationToken cancellationToken)
        {
            if (_suppliedExecutables.Count > 0)
            {
                foreach (var executable in _suppliedExecutables)
                    await executable.ExecuteAsync(cancellationToken);
            }
        }
    }
}