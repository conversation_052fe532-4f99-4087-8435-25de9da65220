using Jx.Utils.Collections;
using UI.Screens.Common.ScreenPositions;
using UnityEngine;

namespace Tutorial.Tips
{
    public class RotatorTutorialTipComponent : MonoBehaviour
    {
        [SerializeField]
        private RectTransform _rotateTarget;

        [SerializeField]
        private RectTransform[] _invariantTransforms;

        public void Rotate(ScreenPositionType positionType)
        {
            switch (positionType)
            {
                case ScreenPositionType.LowerLeftCorner:
                case ScreenPositionType.LowerCenterSide:
                case ScreenPositionType.MiddleLeftSide:
                case ScreenPositionType.UpperLeftCorner:
                case ScreenPositionType.UpperCenter:
                case ScreenPositionType.MiddleCenter:
                    _rotateTarget.rotation = Quaternion.Euler(Vector3.zero);
                    break;
                case ScreenPositionType.LowerRightCorner:
                case ScreenPositionType.MiddleRightSide:
                case ScreenPositionType.UpperRightCorner:
                    _rotateTarget.rotation = Quaternion.Euler(180f, 0f, 180f);
                    break;
            }
            
            _invariantTransforms.ForEach(t => t.transform.rotation = Quaternion.identity);
        }
    }
}