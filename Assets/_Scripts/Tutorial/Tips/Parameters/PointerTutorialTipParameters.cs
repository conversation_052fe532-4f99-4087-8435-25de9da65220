using System.Collections.Generic;
using UnityEngine;

namespace Tutorial.Tips.Parameters
{
    public class PointerTutorialTipParameters : ITutorialTipParameters
    {
        public PointerTutorialTipParameters(RectTransform targetRectTransform, IReadOnlyList<PointerTutorialTipAnimationType> animationTypes)
        {
            TargetRectTransform = targetRectTransform;
            AnimationTypes = animationTypes;
        }

        public RectTransform TargetRectTransform { get; }
        public IReadOnlyList<PointerTutorialTipAnimationType> AnimationTypes { get; }
    }
}