using System;
using Tutorial.Schemas;

namespace Tutorial
{
    public class TutorialException : Exception
    {
        public TutorialException(TutorialType type, string message) : base(WrapMessage(type, message))
        {
        }
        
        public TutorialException(TutorialType type, string message, Exception inner) : base(WrapMessage(type, message), inner)
        {
        }

        private static string WrapMessage(TutorialType type, string message)
        {
            return $"Tutor: '{type}' failed with: {message}";
        }
    }
}