using System.Collections.Generic;
using System.Linq;
using Core.Extensions;
using Tutorial.Tasks.Task;
using Tutorial.Tasks.Task.Parameters;
using Tutorial.Tasks.Task.Styles;
using UnityEngine;

namespace Tutorial.Tasks
{
    public class TutorialTasksContainer : MonoBehaviour
    {
        [SerializeField, Min(1)]
        private int _countTasksShowing = 3;

        [SerializeField]
        private RectTransform _tasksContainerRectTransform;
        
        private TutorialTask.Factory _tasksFactory = null!;
        private readonly IList<TutorialTask> _tasks = new List<TutorialTask>();
        private int _lastTaskIndex = 1;
        
        public void Initialize(
            TutorialTask.Factory tasksFactory)
        {
            _tasksFactory = tasksFactory;
        }

        public void Setup()
        {
            CreateTasks();
        }

        public void Cleanup()
        {
            _tasks.DisposeAndClear();
            _lastTaskIndex = 1;
        }

        public void Enqueue(TaskParameters parameters)
        {
            ShiftTasks();

            var task = _tasks.Last();
            task.SetVisible(true);
            task.UpdateParameters(_lastTaskIndex++, parameters);
        }

        private void CreateTasks()
        {
            for (var i = 0; i < _countTasksShowing; i++)
            {
                var task = _tasksFactory.Create(_tasksContainerRectTransform);
                _tasks.Add(task);
                task.SetVisible(false);
                task.SetType(TutorialTaskType.Passed);
            }
            
            _tasks.Last().SetType(TutorialTaskType.Current);
        }

        private void ShiftTasks()
        {
            var tasksCountWithoutLast = _tasks.Count - 1;

            for (var i = 0; i < tasksCountWithoutLast; i++)
            {
                if (_tasks[i + 1].Parameters == null)
                    continue;
                
                _tasks[i].SetVisible(true);
                _tasks[i].UpdateParameters(_tasks[i + 1].Index, _tasks[i + 1].Parameters);
            }
        }
    }
}