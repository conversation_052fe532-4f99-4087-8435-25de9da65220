namespace Tutorial.Schemas
{
    public enum TutorialType
    {
        // renaming not allowed, used on the server
        
        None = 0,
        GameplayEscapee = 1, 
        GameplayCatcher = 2,
        FirstMatch = 3,
        PerkSelect = 4,
        PerkUpgrade = 5,
        QuestClaimReward = 6,
        ChestOpen = 7,
        PerkSlotUnlock = 8,
        QuestSlotUnlock = 9,
        ChestInstantUnlock = 10,
        ChestOpenForced = 11,
        MissionClaimReward = 12,
        //PlayForced = 13,
        TrophyPathFirstReward = 14,
        CatcherFeatureAccessClaim = 15,
        TasksFeatureAccessClaim = 16,
    }
}