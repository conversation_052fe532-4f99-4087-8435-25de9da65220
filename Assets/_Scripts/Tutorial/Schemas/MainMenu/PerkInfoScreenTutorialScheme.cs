using System;
using System.Collections.Generic;
using Api.Client.User;
using Configs.Tutorial.Dialogs.MainMenu;
using Jx.Telemetry;
using Jx.Utils.Logging;
using Tutorial.Contexts.MainMenu;
using Tutorial.Executables;
using Tutorial.Executables.Groups;
using Tutorial.Executables.Nodes.Shared.UI;
using Tutorial.Managers;
using UnityEngine.Scripting;

namespace Tutorial.Schemas.MainMenu
{
    [Preserve]
    public class PerkInfoScreenTutorialScheme : ScreenTutorialScheme<PerkInfoScreenTutorialContext>
    {
        [Preserve]
        public PerkInfoScreenTutorialScheme(
            ITutorialTipsManager tutorialTipsManager,
            IUserContext userContext,
            TutorialExecutableFactory executableFactory,
            ScreenTutorialDialogsConfig dialogsConfig
        )
            : base(tutorialTipsManager, userContext, executableFactory, dialogsConfig)
        {
            ExecutableByType = new Dictionary<TutorialType, Func<AbstractTutorialExecutable>>()
            {
                [TutorialType.PerkUpgrade] = GetPerkUpgrade
            };
        }

        protected override IReadOnlyDictionary<TutorialType, Func<AbstractTutorialExecutable>> ExecutableByType { get; }

        protected override bool IsLastInChain()
        {
            return true;
        }

        private AbstractTutorialExecutable GetPerkUpgrade()
        {
            var config = DialogsConfig.PerkUpgrade;

            var tapUpgradeButtonBlock = new TutorialSequenceGroup()
               .With(
                    ExecutableFactory.BuildPointer(Context.UpgradeButtonAccentInfo.FingerPivot)
                                     .SupplyAwaitWith(new WaitButtonClickedTutorialNode(Context.UpgradeButton))
                );

            return new TutorialSequenceGroup()
               .With(GetConfiguredBlock(config.TapUpgradeButton, tapUpgradeButtonBlock, "tapupgradeperk"));
        }
    }
}