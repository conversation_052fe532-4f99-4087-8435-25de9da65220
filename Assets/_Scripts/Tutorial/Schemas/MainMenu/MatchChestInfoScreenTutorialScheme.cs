using System;
using System.Collections.Generic;
using Api.Client.User;
using Configs.Tutorial.Dialogs.MainMenu;
using Core.Extensions;
using Jx.Telemetry;
using Tutorial.Contants;
using Tutorial.Contexts.MainMenu;
using Tutorial.Executables;
using Tutorial.Executables.Groups;
using Tutorial.Executables.Nodes.Shared;
using Tutorial.Executables.Nodes.Shared.UI;
using Tutorial.Managers;
using Tutorial.Schemas.Parameters;
using Tutorial.Tips;
using UnityEngine.Scripting;

namespace Tutorial.Schemas.MainMenu
{
    [Preserve]
    public class MatchChestInfoScreenTutorialScheme : ScreenTutorialScheme<MatchChestInfoScreenTutorialContext, MatchChestInfoScreenTutorialSchemeParameters>
    {
        [Preserve]
        public MatchChestInfoScreenTutorialScheme(
            ITutorialTipsManager tutorialTipsManager,
            IUserContext userContext,
            TutorialExecutableFactory executableFactory,
            ScreenTutorialDialogsConfig dialogsConfig
        )
            : base(
                tutorialTipsManager,
                userContext,
                executableFactory,
                dialogsConfig
            )
        {
            ExecutableByType = new Dictionary<TutorialType, Func<AbstractTutorialExecutable>>()
            {
                [TutorialType.ChestOpen] = GetChestOpen,
                [TutorialType.ChestInstantUnlock] = GetChestInstantUnlock,
                [TutorialType.ChestOpenForced] = GetChestOpenForced
            };
        }

        protected override IReadOnlyDictionary<TutorialType, Func<AbstractTutorialExecutable>> ExecutableByType { get; }

        protected override bool IsLastInChain()
        {
            return true;
        }

        protected override bool IsSuited()
        {
            return TutorialType switch
            {
                TutorialType.ChestOpen => Parameters.ChestType == ScreenTutorialConstants.TutorialChestType,
                TutorialType.ChestInstantUnlock => !Parameters.IsReadyToOpen.Value &&
                                                   UserContext.Billing.IsPriceAvailable(Parameters.Price, true) &&
                                                   Parameters.ChestType == ScreenTutorialConstants.TutorialChestType,
                TutorialType.ChestOpenForced => true
            };
        }

        private bool CanPurchaseInstantUnlock()
        {
            return Parameters.Price != null && UserContext.Billing.IsPriceAvailable(Parameters.Price);
        }

        private AbstractTutorialExecutable GetChestOpenForced()
        {
            var tapUnlockButtonBlock = new TutorialSequenceGroup()
               .With(
                    ExecutableFactory.BuildPointer(Context.StartUnlockButtonFingerPivot)
                                     .SupplyAwaitWith(new WaitButtonClickedTutorialNode(Context.StartUnlockButton))
                );

            return new TutorialSequenceGroup()
               .With(tapUnlockButtonBlock);
        }

        private AbstractTutorialExecutable GetChestInstantUnlock()
        {
            if (!CanPurchaseInstantUnlock() || Parameters.IsReadyToOpen.Value)
            {
                return AbstractTutorialExecutable.Empty;
            }

            var tapInstantUnlockButtonBlock = new TutorialSequenceGroup()
                                             .With(
                                                  ExecutableFactory.BuildButtonAccentPointer(
                                                      Context.InstantUnlockButton.RectTransform(),
                                                      Context.InstantUnlockButtonFingerPivot,
                                                      PointerTutorialTipAnimationsFactory.BuildDefaultFinger()
                                                  )
                                              )
                                             .SupplyAwaitWith(
                                                  new AnyTutorialGroup()
                                                     .With(new WaitButtonClickedTutorialNode(Context.InstantUnlockButton))
                                                     .With(new WaitConditionTutorialNode(() => Parameters.IsReadyToOpen.Value))
                                              );

            return new TutorialSequenceGroup()
               .With(GetConfiguredBlock(DialogsConfig.ChestUnlock.TapInstantUnlockChest, tapInstantUnlockButtonBlock, "tapstartinstantunlockchest"));
        }

        private AbstractTutorialExecutable GetChestOpen()
        {
            var tapUnlockButtonBlock = new TutorialSequenceGroup()
               .With(
                    ExecutableFactory.BuildButtonAccentPointer(
                                          Context.StartUnlockButton.RectTransform(),
                                          Context.StartUnlockButtonFingerPivot,
                                          PointerTutorialTipAnimationsFactory.BuildDefaultFinger()
                                      )
                                     .SupplyAwaitWith(new WaitButtonClickedTutorialNode(Context.StartUnlockButton))
                );

            return new TutorialSequenceGroup()
               .With(GetConfiguredBlock(DialogsConfig.ChestUnlock.TapStartUnlockSlot, tapUnlockButtonBlock, "tapstartunlockchest"));
        }
    }
}