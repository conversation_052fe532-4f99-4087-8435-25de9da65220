using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.LightCampfire;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using SceneLogics.GameplayScene.Components.Indicators;
using Tutorial.Executables;
using Tutorial.Executables.Groups;
using Tutorial.Executables.Nodes.Gameplay.Interaction;
using Tutorial.Executables.Nodes.Gameplay.Map;
using Tutorial.Executables.Nodes.Gameplay.Player.Interaction;
using Tutorial.Executables.Nodes.Gameplay.Player.Movement;
using Tutorial.Executables.Nodes.Gameplay.UI;
using Tutorial.Managers;
using Tutorial.Visualizers;
using UnityEngine;
using UnityEngine.Scripting;

namespace Tutorial.Schemas
{
    [Preserve]
    public class GameplayTutorialExecutableFactory : TutorialExecutableFactory
    {
        private readonly IIndicatorsController _indicatorsController;
        private readonly TutorialShine.Factory _destinationVisualizerFactory;

        [Preserve]
        public GameplayTutorialExecutableFactory(ITutorialTipsManager tipsManager,
                                                 IIndicatorsController indicatorsController,
                                                 TutorialShine.Factory destinationVisualizerFactory) 
            : base(tipsManager)
        {
            _indicatorsController = indicatorsController;
            _destinationVisualizerFactory = destinationVisualizerFactory;
        }
        
        public AbstractTutorialExecutable BuildCampfireProgress(
            LightCampfireInteraction lightCampfireInteraction, float progress01)
        {
            return new SetInteractionProgressAwaitlessTutorialNode<NetEscapeePlayer, NetCampfire>(lightCampfireInteraction, progress01);
        }
        
        public AbstractTutorialExecutable BuildIndicator(Transform targetTransform, IndicationType indicationType)
        {
            return new ShowIndicatorTutorialNode(
                targetTransform,
                _indicatorsController,
                indicationType
            );
        }
        
        public AbstractTutorialExecutable BuildInteractUntilProgress(
            NetGamePlayer player,
            AbstractInteraction targetInteraction, 
            int targetInteractionProgress0100 = 100)
        {
            return new TutorialSequenceGroup()
                .With(new UntilTutorialSequenceGroup()
                    .Until(new WaitInteractProgressTutorialNode(targetInteraction, targetInteractionProgress0100))
                    .With(new InteractTutorialNode(player, targetInteraction, new InteractionRequest(true))))
                .With(new InteractTutorialNode(player, targetInteraction, new InteractionRequest(false)));
        }
        
        public AbstractTutorialExecutable BuildDestination(Transform targetTransform)
        {
            return new VisualizeDestinationTransformTutorialNode(_destinationVisualizerFactory, targetTransform);
        }

        public AbstractTutorialExecutable BuildToggleIndicator(
            AbstractTutorialExecutable untilCondition,
            Transform originTransform,
            Transform targetTransform,
            IndicationType indicationType,
            float radius = 2f
        )
        {
            return new UntilTutorialSequenceGroup()
                .Until(untilCondition)
                .With(new WaitPlayerMovedOutsideRadiusTutorialNode(originTransform, targetTransform, radius))
                .With(new TutorialParallelGroup()
                    .With(new ShowIndicatorTutorialNode(targetTransform, _indicatorsController, indicationType))
                    .SupplyAwaitWith(new DestinationTransformTutorialNode(originTransform, targetTransform, radius)));
        }

        public AbstractTutorialExecutable BuildToggleIndicatorWithVisualization(
            AbstractTutorialExecutable untilCondition,
            Transform originTransform,
            Transform targetTransform,
            IndicationType indicationType,
            float radius = 2f
        )
        {
            return new UntilTutorialSequenceGroup()
                .Until(untilCondition)
                .With(new WaitPlayerMovedOutsideRadiusTutorialNode(originTransform, targetTransform, radius))
                .With(
                    new TutorialParallelGroup()
                        .With(new ShowIndicatorTutorialNode(targetTransform, _indicatorsController, indicationType))
                        .With(BuildDestination(targetTransform))
                        .SupplyAwaitWith(new DestinationTransformTutorialNode(originTransform, targetTransform, radius))
                );
        }

        public AbstractTutorialExecutable BuildIndicatingDestination(
            Transform originTransform,
            Transform targetDestination,
            IndicationType indicationType,
            float targetDistance = 2f
        )
        {
            return new TutorialParallelGroup()
                .With(BuildDestination(targetDestination))
                .With(BuildIndicator(targetDestination, indicationType))
                .SupplyAwaitWith(new DestinationTransformTutorialNode(originTransform, targetDestination, targetDistance));
        }
    }
}