using System.Threading;
using Cysharp.Threading.Tasks;
using Tutorial.Contexts;
using Tutorial.Schemas.Parameters;

namespace Tutorial.Schemas
{
    public interface ITutorialScheme
    {
        TutorialType TutorialType { get; }
        
        bool IsTypeSupported(TutorialType type);
        bool IsContextSupported(ITutorialContext context);
        
        UniTask<bool> ExecuteAsync(TutorialType type, ITutorialContext tutorialContext, CancellationToken cancellationToken, ITutorialSchemeParameters parameters);
    }
}