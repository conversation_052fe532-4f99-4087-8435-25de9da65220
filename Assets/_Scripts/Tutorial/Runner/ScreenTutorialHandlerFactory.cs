using System;
using Core.Helpers.Navigation.Controller;
using Core.Helpers.Navigation.View;
using Core.Helpers.SceneManagement;
using Tutorial.Contexts;
using Tutorial.Managers;
using Tutorial.Providers;
using Tutorial.Providers.Handlers;
using UnityEngine.Scripting;

namespace Tutorial.Runner
{
    [Preserve]
    public class ScreenTutorialHandlerFactory : IScreenTutorialHandlerFactory
    {
        private readonly ITutorialManager _tutorialManager;
        private readonly IScreenTutorialContextFactory _screenContextFactory;
        private readonly IScreenTutorialProvider _screenTutorialProvider;
        private readonly IMonsterLandSceneManager _sceneManager;
        
        [Preserve]
        public ScreenTutorialHandlerFactory(ITutorialManager tutorialManager, 
                                    IScreenTutorialContextFactory screenContextFactory,
                                    IScreenTutorialProvider screenTutorialProvider,
                                    IMonsterLandSceneManager sceneManager)
        {
            _tutorialManager = tutorialManager;
            _screenContextFactory = screenContextFactory;
            _screenTutorialProvider = screenTutorialProvider;
            _sceneManager = sceneManager;
        }

        public IScreenTutorialHandler Create<TScreen, TController>(TScreen screen, TController controller) 
            where TScreen : BaseNavigationScreen
            where TController : BaseNavigationScreenController<TScreen>
        {
            var context = _screenContextFactory.Build(screen, controller) ?? throw new Exception($"Failed to create screen context '{nameof(TScreen)}'");
            return new ScreenTutorialHandler(context, _screenTutorialProvider, _tutorialManager, _sceneManager);
        }
    }
}