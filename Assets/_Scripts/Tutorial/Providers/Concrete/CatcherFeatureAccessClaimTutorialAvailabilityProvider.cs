using Api.Client.User.TrophyPath;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using Tutorial.Schemas;

namespace Tutorial.Providers.Concrete
{
    public class CatcherFeatureAccessClaimTutorialAvailabilityProvider : ScreenTutorialAvailabilityProvider
    {
        public CatcherFeatureAccessClaimTutorialAvailabilityProvider(ScreenTutorialAvailabilityProviderContext context) : base(context)
        {
        }

        public override TutorialType Type { get; } = TutorialType.CatcherFeatureAccessClaim;
        
        protected override bool IsTutorialUnderLaunchConditions()
        {
            return AreSchemasPassed(TutorialType.TrophyPathFirstReward) &&
                   Context.UserContext.FeatureAccess.IsUnlocked(MonsterLandFeatureAccessType.TrophyPath) &&
                   Context.UserContext.TrophyPath.Interaction.FindFeatureAccessItem(MonsterLandFeatureAccessType.Catcher)?.Status.Value.IsClaimable() == true;
        }
    }
}