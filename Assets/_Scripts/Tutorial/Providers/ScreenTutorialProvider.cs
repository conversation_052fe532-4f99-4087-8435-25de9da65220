using System;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Upgrades.Interactions;
using Core.Helpers.Navigation;
using Savings;
using Tutorial.Contexts;
using Tutorial.Managers;
using Tutorial.Schemas;
using UnityEngine.Scripting;

namespace Tutorial.Providers
{
    [Preserve]
    public class ScreenTutorialProvider : IScreenTutorialProvider
    {
        private readonly ILocalSave _localSave;
        private readonly IUserContext _userContext;
        private readonly INavigation _navigation;
        private readonly IScreenTutorialAvailabilityManager _availabilityManager;

        [Preserve]
        public ScreenTutorialProvider(
            IUserContext userContext,
            ITutorialManager tutorialManager,
            ILocalSave localSave,
            IRuntimeTutorialChainsProgress chainsProgress,
            INavigation navigation,
            IScreenTutorialAvailabilityManager availabilityManager
        )
        {
            _localSave = localSave;
            _userContext = userContext;
            _navigation = navigation;
            _availabilityManager = availabilityManager;
        }

        public bool CanForcedBeExecuted<TContext>(TContext context, TutorialType type)
            where TContext : ITutorialContext
        {
            if (!IsScreenShowing(context) || CheckDebugSkipTutorial())
            {
                return false;
            }

            return _availabilityManager.Providers.Any(p => p.Type == type && p.Forced && p.IsAvailableToPass(context));
        }

        public bool TryGetAvailableForContext<TContext>(TContext context, out TutorialType type)
            where TContext : ITutorialContext
        {
            if (!IsScreenShowing(context) || CheckDebugSkipTutorial())
            {
                type = TutorialType.None;
                return false;
            }

            var availableTutorial = _availabilityManager.Providers.FirstOrDefault(p => !p.Forced && p.IsAvailableToPass(context));

            type = availableTutorial?.Type ?? TutorialType.None;
            return availableTutorial != default;
        }

        private bool IsScreenShowing<TContext>(TContext context)
            where TContext : ITutorialContext
        {
            return context.ScreenType == _navigation.CurrentNode.Value?.View.GetType();
        }

        private bool CheckDebugSkipTutorial()
        {
            return _userContext.IsDebugUser.Value && _localSave.Debug.DisableMenuTutorials;
        }
    }
}