using Api.Client.User;
using Api.Client.User.Stats;
using App.Friends;
using App.Gdpr;
using Core.Helpers.SceneManagement;
using Jx.Telemetry.Actions;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Sessions;
using LoM.Messaging.ClientIntegrations.Stats;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using Savings;
using UnityEngine.Scripting;

namespace App.Analytics
{
    public class TelemetryGlobalPopulatorApp : IJxTelemetryGlobalActionPopulator
    {
        private readonly ILocalSave _localSave;
        private readonly IUserContext _userContext;
        private readonly IFriendPresenter _friendPresenter;
        private readonly IJxGdprIntegration _gdpr;
        private readonly IJxChangeableObject<IJxUserSession> _session;

        [Preserve]
        public TelemetryGlobalPopulatorApp(
            ILocalSave localSave,
            IUserContext userContext,
            IMonsterLandSceneManager sceneManager,
            IFriendPresenter friendPresenter,
            IJxGdprIntegration gdpr,
            IJxChangeableObject<IJxUserSession> session
        )
        {
            _localSave = localSave;
            _userContext = userContext;
            _friendPresenter = friendPresenter;
            _gdpr = gdpr;
            _session = session;
        }

        public void Populate(IJxTelemetryActionBuilder actionBuilder)
        {
            actionBuilder.SetParameters("firstsession", _session.Value.IsFirst ? "1" : "0");
            
            if (!_gdpr.ConsentAccepted)
            {
                return;
            }
            
            actionBuilder.SetParameters("age", _localSave.Value.Age.ToString());

            if (!_userContext.IsInitialized)
            {
                return;
            }
            
            actionBuilder.SetParameters("multiplayerhas", _friendPresenter.MultiplayerExistsOnTrophyPath() ? "1" : "0");
            actionBuilder.SetParameters("multiplayerunlocked", _userContext.FeatureAccess.GetListener(MonsterLandFeatureAccessType.Multiplayer).Value ? "1" : "0");
            actionBuilder.SetParameters("launchnum", _localSave.Value.SessionNumber.ToString());
            actionBuilder.SetParameters("launchday", _localSave.Value.LaunchDayCount.ToString());

            var stats = _userContext.Stats.Value.Value;
            if (stats != null)
            {
                actionBuilder.SetParameters("matches", stats.GetPlayedMatchCount().ToString());
                
                PopulateWinVector(actionBuilder, stats);
            }

            var seasonPassInteraction = _userContext.SeasonPass.Interaction;
            if (seasonPassInteraction != null)
            {
                actionBuilder.SetParameters("sp_premium", seasonPassInteraction.IsPremiumUser.Value ? "1" : "0");
                actionBuilder.SetParameters("sp_progress", seasonPassInteraction.Progress.Value.ToString() ?? "0");
                actionBuilder.SetParameters("sp_available", seasonPassInteraction.IsAvailable.Value ? "1" : "0");
            }
        }

        private void PopulateWinVector(IJxTelemetryActionBuilder actionBuilder, UserStatsClientIntegration stats)
        {
            actionBuilder.SetParameters("winvector", LimitWinVector(stats.Global.WinVector));
            actionBuilder.SetParameters("escapee_winvector", LimitWinVector(stats.Escapee.WinVector));
            actionBuilder.SetParameters("catcher_winvector", LimitWinVector(stats.Catcher.WinVector));
            
            return;

            string LimitWinVector(string? val)
            {
                if (val?.Length > 30)
                {
                    return val.Substring(0, 30);
                }

                return val ?? string.Empty;
            }
        }
    }
}