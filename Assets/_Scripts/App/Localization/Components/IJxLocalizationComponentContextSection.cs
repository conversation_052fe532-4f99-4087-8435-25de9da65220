using App.Components.Contexts;
using Jx.Utils.ChangeableObjects;

namespace App.Localization.Components
{
    public interface IJxLocalizationComponentContextSection : IJxComponentContextSection,
                                                              IJxChangeableObject<IJxLocalizationContext, JxLocalizationContextModel>
    {
        string Key { get; }

        string GetText(string defaultText);

        string GetTextOptional();

        IJxLocalizationKey CreateSubKey(string key);
    }
}