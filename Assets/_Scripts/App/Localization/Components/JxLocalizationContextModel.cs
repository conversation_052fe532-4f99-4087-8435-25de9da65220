using App.Components.Contexts;

namespace App.Localization.Components
{
    public class JxLocalizationContextModel : IJxComponentContextProperty<IJxLocalizationContext, JxLocalizationContextModel>, IJxLocalizationContext, IJxLocalizationKeyOverride
    {
        public JxLocalizationContextModel()
        {
        }

        public JxLocalizationContextModel(IJxLocalizationContext other)
        {
            if (other != null)
            {
                IsRoot = other.IsRoot;
                Key = other.Key;
                DefaultText = other.DefaultText;

                if (other is IJxLocalizationKeyOverride keyOverride)
                {
                    KeyOverrideName = keyOverride.OverrideName;
                }
            }
        }

        public bool IsRoot { get; set; }

        public string Key { get; set; }

        public string DefaultText { get; set; }

        public string KeyOverrideName { get; set; }

        string IJxLocalizationKeyOverride.OverrideName => KeyOverrideName;

        public JxLocalizationContextModel Merge(IJxLocalizationContext other)
        {
            return new JxLocalizationContextModel(this);
        }

        public JxLocalizationContextModel Clone()
        {
            return new JxLocalizationContextModel(this);
        }
    }
}