using System;
using System.Collections;
using System.Collections.Generic;
using BestHTTP;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using Jx.Utils.Collections;
using Jx.Utils.Coroutines;
using Jx.Utils.Logging;
using Jx.Utils.MainThread;
using Jx.Utils.Threading;
using UnityEngine.Scripting;

namespace App.Localization.Usage
{
    internal class JxLocalizationUsageStatsSender
    {
        private const string ServiceUrlFormat = "http://localization.edtechdev.net/localization/createkey?key={0}";

        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxLocalizationUsageStatsSender));
        private static readonly TimeSpan _sendPeriod = TimeSpan.FromSeconds(15);

        private JxLocalizationIntegration _localization = null!;
#if UNITY_EDITOR
        private readonly JxAtomicFlag _sendingMissingKeys;
        private readonly JxConcurrentHashSet<string> _sentMissingKeys;
#endif

        public static JxLocalizationUsageStatsSender Instance { get; } = new JxLocalizationUsageStatsSender();

        private JxLocalizationUsageStatsSender()
        {
#if UNITY_EDITOR
            _sendingMissingKeys = new JxAtomicFlag();
            _sentMissingKeys = new JxConcurrentHashSet<string>();
#endif
        }

        public void Initialize()
        {
            _localization = (JxLocalizationIntegration)JxLocalizationIntegration.Instance;
            JxMainThread.Instance.InvokeEveryNSeconds(Process, (float)_sendPeriod.TotalSeconds);
        }

        private void Process()
        {
            Send();
        }

        private async void Send()
        {
            foreach (var repository in _localization.EnumerateRepositories())
            {
                var usageStats = repository.PopUsageStats();
                if (usageStats.Count <= 0)
                    continue;

                _logger.LogError(
                    "Usage report",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Language"] = repository.Language,
                        ["Version"] = repository.Version,
                        ["Stats"] = string.Join('\n', usageStats),
                    }
                );

#if UNITY_EDITOR
                if (_sendingMissingKeys.TrySet())
                {
                    foreach (var stat in usageStats)
                    {
                        if (stat.NotFound > 0 && !_sentMissingKeys.Contains(stat.Key))
                        {
                            _sentMissingKeys.Add(stat.Key);
                            await SendMissingKeyAsync(stat.Key);
                        }
                    }

                    _sendingMissingKeys.TryReset();
                    // repository.SaveEmbeddedMissingKeys(usageStats);
                }
#endif
            }
        }

#if UNITY_EDITOR
        private async UniTask SendMissingKeyAsync(string key)
        {
            var uri = new Uri(string.Format(ServiceUrlFormat, key));

            var request = new HTTPRequest(uri);

            try
            {
                await request.GetAsStringAsync();
                _logger.LogEditorOnlyError($"Missing key sent [{key}]");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
        }
#endif
    }
}