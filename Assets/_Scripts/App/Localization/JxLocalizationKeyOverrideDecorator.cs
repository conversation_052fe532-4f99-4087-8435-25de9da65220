using System;

namespace App.Localization
{
    internal class JxLocalizationKeyOverrideDecorator : IJxLocalizationKey
    {
        private readonly IJxLocalizationKey _key;
        private readonly IJxLocalizationKey _overrideKey;

        public JxLocalizationKeyOverrideDecorator(IJxLocalizationKey key, IJxLocalizationKey overrideKey)
        {
            _key = key;
            _overrideKey = overrideKey;
        }

        public string Key => _key.Key;

        public IJxLocalizationKey CreateSubKey(string key)
        {
            return new JxLocalizationKeyOverrideDecorator(
                _key.CreateSubKey(key),
                _overrideKey.CreateSubKey(key)
            );
        }

        public IDisposable Subscribe(Action action)
        {
            return _key.Subscribe(action);
        }

        public bool TryGetText(string defaultText, out string text)
        {
            if (TryGetOverrideText(out text))
            {
                return true;
            }

            return _key.TryGetText(defaultText, out text);
        }

        public bool TryGetTextOptional(out string text)
        {
            if (TryGetOverrideText(out text))
            {
                return true;
            }

            return _key.TryGetTextOptional(out text);
        }

        private bool TryGetOverrideText(out string text)
        {
            return _overrideKey.TryGetTextOptional(out text);
        }
    }
}