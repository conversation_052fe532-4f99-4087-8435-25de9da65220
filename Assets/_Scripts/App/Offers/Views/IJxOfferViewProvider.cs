using System.Diagnostics.CodeAnalysis;
using UI.Screens.MainMenuScene.Offer.Components.View.Base;

namespace App.Offers.Views
{
    internal interface IJxOfferViewProvider
    {
        bool TryGet(
            JxPromoOfferType type,
            IJxOffer offer,
            bool isFullScreen,
            bool isOpeningByClick,
            [MaybeNullWhen(false)] out IOfferView view,
            bool preloading = false
        );
    }
}