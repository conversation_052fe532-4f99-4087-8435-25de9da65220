using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.Offers.Profile.ActivationTriggers;
using UnityEngine.Scripting;
using Zenject;

namespace App.Offers.ActivationTriggers
{
    internal class JxOfferActivationTriggerFactoryProxy : IJxOfferActivationTriggerFactoryProxy
    {
        private readonly DiContainer _diScope;
        
        [Preserve]
        public JxOfferActivationTriggerFactoryProxy(
            DiContainer diScope
        )
        {
            _diScope = diScope;
        }

        public bool TryCreate(IOfferActivationTriggerClientIntegration triggerIntegration, out IJxOfferActivationTrigger trigger)
        {
            var integrationType = triggerIntegration?.GetType();
            if (integrationType != null)
            {
                var factory = _diScope.TryResolveId<IJxOfferActivationTriggerFactory>(integrationType);
                if (factory != null)
                {
                    trigger = factory.Create(triggerIntegration);
                    return true;
                }
            }

            trigger = default;
            return false;
        }
    }
}