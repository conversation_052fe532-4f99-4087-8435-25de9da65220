using Api.Client.User;
using Jx.Utils.ChangeableObjects;

namespace App.Offers.ActivationTriggers.Concrete
{
    public class GoldenPassOfferActivationTrigger : IJxOfferActivationTrigger
    {
        private readonly IJxConvertedChangeableObject<JxOfferTriggerStatus> _status;
    
        public GoldenPassOfferActivationTrigger(
            IUserContext userContext
        )
        {
            _status = userContext.Billing.GoldenPassState.IsActive.CreateConverted(goldenPassIsActive =>
            {
                if (!goldenPassIsActive)
                {
                    return JxOfferTriggerStatus.Fired;
                }

                return JxOfferTriggerStatus.Pending;
            });
        }

        public IJxChangeableObject<JxOfferTriggerStatus> Status => _status;

        public void Dispose()
        {
            _status.Dispose();
        }
    }
}