using Jx.Utils.Encryption;

namespace App.Offers
{
    public sealed class JxOfferIntegrationOptions
    {
        public JxOfferIntegrationOptions(
            IJxEncryptionAlgorithm encryptionAlgorithm,
            int preloadViewSize
        )
        {
            EncryptionAlgorithm = encryptionAlgorithm;
            PreloadViewSize = preloadViewSize;
        }
        
        public IJxEncryptionAlgorithm EncryptionAlgorithm { get; }
        public int PreloadViewSize { get; }
    }
}