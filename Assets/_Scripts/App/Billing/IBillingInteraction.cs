using System;
using Api.Client.User.Billing;
using Api.Client.User.Billing.Products;
using Api.Client.User.Prices;
using Cysharp.Threading.Tasks;

namespace App.Billing
{
    public interface IBillingInteraction
    {
        UniTask<PurchaseStatus> ActivateAsync(
            IJxUserPrice price,
            Func<UniTask<PurchaseStatus>> onSuccessfullyConfirmedAsync,
            string confirmationKey,
            Func<UniTask>? onRewardShownAsync = null
        );

        UniTask<PurchaseStatus> ConfirmPriceAsync(IJxUserPrice price, string? confirmationKey);
        UniTask<PurchaseStatus> ConfirmProductAsync(IJxUserProduct product, string? confirmationKey);

        UniTask HandlePurchaseStatusAsync(PurchaseStatus status, IJxUserPrice price);
    }
}