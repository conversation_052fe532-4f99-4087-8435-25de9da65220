namespace App.Inventory.Item
{
    public enum InventoryItemType : byte
    {
        // do not change int values, serialized
        // do not change names, used in localization as keys
        
        None = 0,

        #region POTIONS

        // получение ускорения
        PotionSpeedUp = 1,
        // невидимость
        PotionInvisibility = 2,
        // нельзя застанить, замедлить, нанести урон
        PotionPower = 3,
        // показывается местоположение ловца
        PotionHearing = 4,

        #endregion

        #region TRAPS
        
        // взрыв, урон в радиусе
        TrapExplosion = 100,
        // замедление в радиусе
        TrapSlowDown = 101,
        // показывает местоположение убегающих, попавших в ловушку
        TrapDisclosure = 102,
        // блокирует убегающего, его способности кроме зелей
        TrapCatch = 103,

        #endregion
    }
}