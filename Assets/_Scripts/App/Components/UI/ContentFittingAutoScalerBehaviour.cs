using System;
using Core.Extensions;
using UnityEngine;

namespace App.Components.UI
{
    public class ContentFittingAutoScalerBehaviour : MonoBehaviour
    {
        [SerializeField]
        private ContentFitMode _fitMode;

        [SerializeField]
        private bool _listenSelf = false;

        private IDisposable _dimensionChangeSubscription;
        private IDisposable _selfDimensionChangeSubscription;

        public void SetFitMode(ContentFitMode fitMode)
        {
            _fitMode = fitMode;

            Rescale();
        }

        private void OnEnable()
        {
            OnParentChanged();
        }

        private void OnTransformParentChanged()
        {
            OnParentChanged();
        }

        private void OnDisable()
        {
            if (_dimensionChangeSubscription != null)
            {
                _dimensionChangeSubscription.Dispose();
                _dimensionChangeSubscription = null;
            }

            if (_selfDimensionChangeSubscription != null)
            {
                _selfDimensionChangeSubscription.Dispose();
                _selfDimensionChangeSubscription = null;
            }
        }

        private void OnParentChanged()
        {
            if (transform.parent == null)
            {
                return;
            }
            
            _dimensionChangeSubscription?.Dispose();
            _selfDimensionChangeSubscription?.Dispose();

            _dimensionChangeSubscription = transform.parent.gameObject.SubscribeOnDimensionChange(Rescale);
            
            if (_listenSelf)
            {
                _selfDimensionChangeSubscription = gameObject.SubscribeOnDimensionChange(Rescale);
            }

            Rescale();
        }

        private void Rescale()
        {
            var parent = transform.parent as RectTransform;
            if (parent != null)
            {
                var rectTransform = (RectTransform)transform;
                var sourceWidth = rectTransform.rect.width;
                var sourceHeight = rectTransform.rect.height;

                var parentWidth = parent.rect.width;
                var parentHeight = parent.rect.height;

                if (parentWidth > 0f && parentHeight > 0f && sourceWidth > 0f && sourceHeight > 0f)
                {
                    var scale = 1f;

                    var parentAspectRatio = parentWidth / parentHeight;
                    var sourceAspectRatio = sourceWidth / sourceHeight;

                    var widthRatio = parentWidth / sourceWidth;
                    var heightRatio = parentHeight / sourceHeight;

                    if (_fitMode == ContentFitMode.Contain)
                    {
                        scale = parentAspectRatio < sourceAspectRatio ? widthRatio : heightRatio;
                    }
                    else
                    {
                        scale = parentAspectRatio > sourceAspectRatio ? widthRatio : heightRatio;
                    }

                    transform.localScale = Vector3.one * scale;
                }
            }
        }

#if UNITY_EDITOR
        protected virtual void OnValidate()
        {
            Rescale();
        }
#endif

        #region CLASSES

        public enum ContentFitMode
        {
            /// <summary>
            /// Контент пытается влезть в контейнер по какой-то из сторон
            /// </summary>
            Contain,
            /// <summary>
            /// Контент перекрывает контейнер(может выйти за размеры контейнера)
            /// </summary>
            Cover,
        }

        #endregion
    }
}