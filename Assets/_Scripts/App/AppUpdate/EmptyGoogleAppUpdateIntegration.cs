using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine.Scripting;

namespace App.AppUpdate
{
    public class EmptyJxGoogleAppUpdateIntegration : IJxGoogleAppUpdateIntegration
    {
        [Preserve]
        public EmptyJxGoogleAppUpdateIntegration()
        { }
        
        public UniTask<AppUpdateStatus> TryUpdateImmediatelyIfAvailableAsync(CancellationToken cancellationToken)
        {
            return UniTask.FromResult(AppUpdateStatus.NotRequired);
        }

        public JxGoogleAppUpdateTask CreateTask(CancellationToken cancellationToken) => JxGoogleAppUpdateTask.Empty;
    }
}