using System;
using System.Collections.Generic;
using App.Analytics;
using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using ExternalServices.Telemetry;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.UserRelationship;

namespace App.Friends
{
    public static class FriendsFacade
    {
        public static async UniTask<bool> InviteFriendByFriendIdAsync(
            IJxUserRelationshipIntegration relationship,
            string? friendId,
            string placement
        )
        {
            friendId = friendId?.ToUpperInvariant();
            if (string.IsNullOrEmpty(friendId))
            {
                ToastManager.Instance.Show("FriendInviteSendError");
                TrackFriendInvite(placement, "sendempty");
                return false;
            }

            var mineFriendId = await GetMineFriendIdAsync(relationship);

            if (string.Equals(mineFriendId, friendId, StringComparison.Ordinal))
            {
                ToastManager.Instance.Show("FriendInviteToMyself");
                TrackFriendInvite(placement, "sendself");
                return false;
            }

            var success = await InviteFriendAsync(relationship, r => r.Friends.SendInvitationByFriendIdAsync(friendId));
            TrackFriendInvite(placement, success ? "sendok" : "sendfail");
            return success;
        }

        public static async UniTask<bool> InviteFriendByPublicIdAsync(
            IJxUserRelationshipIntegration relationship,
            string? publicId,
            string placement
        )
        {
            if (string.IsNullOrEmpty(publicId))
            {
                ToastManager.Instance.Show("FriendInviteSendError");
                TrackFriendInvite(placement, "sendempty");
                return false;
            }

            var success = await InviteFriendAsync(relationship, r => r.Friends.SendInvitationByPublicIdAsync(publicId));
            TrackFriendInvite(placement, success ? "sendok" : "sendfail");
            return success;
        }

        public static UniTask<string?> GetMineFriendIdAsync(IJxUserRelationshipIntegration relationship)
        {
            var ignoreFriendIdTtl = false;
#if UNITY_EDITOR
            ignoreFriendIdTtl = true;
#endif
            return relationship.Friends.GenerateFriendIdAsync(ignoreFriendIdTtl);
        }

        private static async UniTask<bool> InviteFriendAsync(
            IJxUserRelationshipIntegration relationship,
            Func<IJxUserRelationshipIntegration, UniTask<bool>> resultProvider
        )
        {
            var success = await resultProvider(relationship);
            if (success)
                ToastManager.Instance.Show("FriendInviteSend");
            // else handled by FriendsNonSuccessResponseListener

            return success;
        }

        private static void TrackFriendInvite(string placement, string status)
        {
            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.Friends,
                "invite",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.Placement] = placement,
                    [TelemetryEventParameters.Status] = status,
                }
            );
        }
    }
}