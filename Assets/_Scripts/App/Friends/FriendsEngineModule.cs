using System;
using Jx.UserRelationship;
using Jx.UserRelationship.Friends;
using UnityEngine.Scripting;
using Zenject;

namespace App.Friends
{
    public class FriendsEngineModule : JxUserRelationshipEngineModule
    {
        [Preserve]
        public FriendsEngineModule() { }

        public override void RegisterComponents(DiContainer container)
        {
            base.RegisterComponents(container);

            container.Bind<IJxFriendInGameFriendInviteListener>()
                .To<NewFriendInviteListener>()
                .AsSingle();

            container.Bind<IFriendPresenter>()
                .To<FriendPresenter>()
                .AsSingle();

            container.Bind<IJxUserRelationshipNonSuccessResponseListener>()
                .To<FriendsNonSuccessResponseListener>()
                .AsSingle();

            container.Bind(typeof(IFriendsInvitePresenter), typeof(IDisposable))
                .To<FriendsInvitePresenter>()
                .<PERSON><PERSON><PERSON><PERSON>();
        }

        protected override JxUserRelationshipIntegrationOptions CreateOptions() => new();
    }
}