using System;
using Jx.Utils.Collections;
using Jx.Utils.Objects;

namespace Graph
{
    public static class GraphWalker
    {
        public static void VisitChildrenRecursively<TNode>(IGraphNode node, Action<TNode> visitAction) where TNode : IGraphNode
        {
            if (node.Children?.Count > 0) 
            {
                node.Children.ForEach(child =>
                {
                    visitAction(child.Cast<TNode>());
                    VisitChildrenRecursively(child, visitAction);
                });
            }
        }
    }
}