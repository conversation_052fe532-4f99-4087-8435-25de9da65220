using System;
using Configs.Entities;
using Configs.Game;
using Configs.NetGameObjects;
using Configs.Particles;
using Configs.Perks;
using Configs.Perks.Loaders;
using Configs.Phrase;
using Configs.ServerGameplay;
using Core.Helpers.DebugScreen;
using Core.Utils;
using ExternalServices.Integrations.Telemetry;
using ExternalServices.Telemetry;
using GameplayComponents.Bots.Constraints.Catcher.Parameters;
using GameplayComponents.Bots.Constraints.Escapee.Parameters;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Di;
using Jx.Utils.Di.Module;
using Jx.Utils.Serialization.Json;
using Savings;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;

namespace Installers.App
{
    public class AppSharedEngineModule : IJxDiModule
    {
#if UNITY_EDITOR
        private static bool _registeredJsonInheritance;
#endif

        private readonly AppInstallerContext _context;

        [Preserve]
        public AppSharedEngineModule(AppInstallerContext context)
        {
            _context = context;
        }

        public void RegisterComponents(DiContainer container)
        {
            RegisterJsonInheritance();

            container.RegisterModule<CoreEngineModule>();
            container.RegisterModule<TelemetryEngineModule>();
            container.RegisterModule<LoggingEngineModule>();

#if !HEADLESS && DEV
             container.Bind(typeof(IJxDebugIntegration), typeof(IDisposable))
                      .To<JxDebugIntegration>()
                      .AsSingle();
#endif

            InstallConfigs(container);

            container.Bind<IPerkIntegration>()
                     .To<PerkIntegration>()
                     .AsSingle();

            container.Bind<ILocalSave>()
                     .To<LocalSave>()
                     .AsSingle();
        }

        private void InstallConfigs(DiContainer container)
        {
            container.Bind(
                          typeof(IJxChangeableObject<IServerGameplayConfig>),
                          typeof(IJxChangeableObject<IServerGameplayConfig, IServerGameplayConfig>)
                      )
                     .FromInstance(new JxChangeableObject<IServerGameplayConfig, IServerGameplayConfig>())
                     .AsSingle();

            container.Bind<IGameConfigProvider>()
                     .To<GameConfigProvider>()
                     .AsSingle();

            container.Bind<INetGameObjectsConfig>()
                     .FromInstance(Resources.Load<NetGameObjectsConfig>("configs/net-gameplay-objects"))
                     .AsSingle();

            container.Bind<IParticlesConfig>()
                     .FromInstance(Resources.Load<ParticlesConfig>("configs/particle-config"))
                     .AsSingle();

            container.Bind<IPhraseConfig>()
                     .FromInstance(Resources.Load<PhraseConfig>("configs/phrase-config"))
                     .AsSingle();

            container.Bind<IPerkConfigLoader>()
                     .To<HttpJsonPerkConfigLoader>()
                     .AsSingle();
        }

        public static void RegisterJsonInheritance()
        {
#if UNITY_EDITOR
            if (_registeredJsonInheritance)
                return;

            _registeredJsonInheritance = true;
#endif

            // don't remove, trigger static constructor
            JxJsonInheritConverterRegistrar.Register(typeof(StartHuntingAfterGateReadyCatcherConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(StartHuntingOnCampfiresCatcherBotConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(StopHuntingOnLastPlayerCatcherConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(StopHuntingOnPlayerInCageCatcherConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(EnemyRadiusInteractionEscapeeConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(StartEscapingCatcherConstraintParameters));
            JxJsonInheritConverterRegistrar.Register(typeof(ForceAttackCatcherConstraintParameters));
        }
    }
}