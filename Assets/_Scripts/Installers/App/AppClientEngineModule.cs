#if !HEADLESS
using System;
using Api.Client.User;
using Api.Client.User.Billing.GoldenPass;
using Api.Client.User.CharacterPath;
using Api.Client.User.Missions.Definitions;
using Api.Client.User.Prices;
using Api.Client.User.Upgrades.Interactions.Perks.Loader;
using Api.Matchmaking;
using App.Analytics;
using App.AppUpdate;
using App.Billing;
using App.ComingSoon;
using App.Components.Contexts;
using App.Friends;
using App.Gdpr;
using App.Leaderboards;
using App.Localization;
using App.Offers;
using App.Profile;
using App.ReviewManager;
using App.ReviewManager.Reporting;
using App.TermsAndCondition;
using Audio;
using Configs.Audio;
using Configs.Dialogs;
using Configs.Entities.Rarity;
using Configs.EntityRenderer;
using Configs.LoadingHints;
using Configs.Perks;
using Core.Helpers.Navigation;
using Core.Helpers.SceneManagement.TransitionDecorators;
using Core.Helpers.ScreenInteraction;
using Core.Loading.Cover;
using Core.Managers.AssetLoader;
using Core.Managers.MapSelection;
using Core.Reconnection;
using Core.UI.Components.RenderTexture;
using Core.UI.Components.RenderTexture.Factory;
using Core.UI.Components.RenderTexture.Renderers;
using Core.UI.LoadingScreen;
using ExternalServices.Advertisement;
using ExternalServices.Auth;
using ExternalServices.Auth.Manager;
using ExternalServices.InApp;
using ExternalServices.Integrations.Firebase;
using ExternalServices.Integrations.GooglePlayGames;
using ExternalServices.Integrations.UnityService.Ads;
using ExternalServices.Integrations.UnityService.InApp;
using Jx.ApiGateway;
using Jx.Unity.NativePermissions;
using Jx.Unity.Notifications;
using Jx.User.Reporting.Api;
using Jx.UserRecords;
using Jx.Utils.Di;
using Jx.Utils.Di.Module;
using Managers.Gameplay.Runner;
using Managers.Rewards;
using Modules.Attribution;
using MonsterLand.Matchmaking.Providers;
using Tutorial;
using UI.Components.Reward;
using UI.Screens.Common.ChangeLanguage.Components;
using UI.Screens.MainMenuScene.Quests.Components.DailyQuests;
using UI.Screens.MainMenuScene.UserProfile;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;
using Object = UnityEngine.Object;

namespace Installers.App
{
    public class AppClientEngineModule : IJxDiModule
    {
        private readonly AppInstallerContext _context;
        private readonly AudioRepository _audioRepository;
        private readonly AudioBanksRepository _audioBanksRepository;

        [Preserve]
        public AppClientEngineModule(
            AppInstallerContext context,
            AudioRepository audioRepository,
            AudioBanksRepository audioBanksRepository
        )
        {
            _context = context;
            _audioRepository = audioRepository;
            _audioBanksRepository = audioBanksRepository;
        }

        public void RegisterComponents(DiContainer container)
        {
            container.RegisterModule<TutorialEngineModule>();
            container.RegisterModule(new AudioEngineModule(_audioRepository, _audioBanksRepository));

            container.RegisterModule<NavigationEngineModule>();
            container.RegisterModule<GoogleAppUpdateEngineModule>();
            container.RegisterModule(new OfferEngineModule(_context.EncryptionAlgorithm));
            container.RegisterModule(new JxUserRecordEngineModule(new JxUserRecordStoreOptions { EncryptionAlgorithm = _context.EncryptionAlgorithm }));
            container.RegisterModule(new JxComponentEngineModule());
            container.RegisterModule(new JxLocalizationEngineModule<JxEmptyLocalizationEventListener>());
            container.RegisterModule(new JxNativePermissionEngineModule());
            container.RegisterModule(new JxNotificationEngineModule());
            container.RegisterModule(new JxReviewManagerEngineModule(_context.EncryptionAlgorithm));
            container.RegisterModule(new JxAttributionEngineModule());
            container.RegisterModule<UserReportingEngineModule>();
            container.RegisterModule<FriendsEngineModule>();
            container.RegisterModule<LeaderboardEngineModule>();
            container.RegisterModule<MatchmakingEngineModule>();

            container
                .Bind<IEntityRarityProvider>()
                .To<EntityRarityProvider>()
                .AsSingle();

            container
                .Bind<IProfilePresenter>()
                .To<ProfilePresenter>()
                .AsSingle();

            container.Bind<IJxSceneTransitionInterceptor>()
                .To<LoadingScreenSingleton.SceneTransitionInterceptor>()
                .AsSingle();

            container.Bind<IPerkDisplayRepository>()
                .To<PerkContentRepository>()
                .AsSingle();

            container.Bind<IPerkContentLoader>()
                .To<PerkContentLoader>()
                .AsSingle();

            container.Bind<ILoadingHintRepository>()
                .To<LoadingHintRepository>()
                .AsSingle();

            container.Bind<ILocalhostGameplayRunner>()
                .To<LocalhostGameplayRunner>()
                .AsSingle();

            InstallServices(container);
            InstallLocal(container);

            container.RegisterBuildCallback(scope => { HandleResourceLoadedInstantiated(JxResourceLoader.Instance, container); });
        }

        private void InstallLocal(DiContainer container)
        {
            container.Bind<IGameplayReconnectManager>()
                .To<GameplayReconnectManager>()
                .AsSingle();

            container.Bind<IQuestSlotContextFactory>()
                .To<QuestSlotContextFactory>()
                .AsSingle();

            container.Bind<ILocalMapProvider>()
                .To<LocalMapProvider>()
                .AsSingle();

            container.Bind<IScreenInteractionHandler>()
                .To<ScreenInteractionHandler>()
                .AsSingle();

            container.Bind<IBillingInteraction>()
                .To<BillingInteraction>()
                .AsSingle();

            container.Bind(typeof(IRewardPresenter), typeof(IDisposable))
                .To<RewardPresenter>()
                .AsSingle();

            container.BindFactory<LoadingCover, LoadingCover.Factory>()
                .FromMethod(
                    diScope =>
                        diScope
                            .InstantiatePrefab(JxResourceLoader.Instance.LoadPrefab("Loading/LoadingCover"))
                            .GetComponent<LoadingCover>()
                );

            container.Bind(typeof(ILoadingCoverController), typeof(IDisposable))
                .To<LoadingCoverController>()
                .AsSingle();

            container.Bind<IExternalInfoStorage>()
                .To<HardcodedExternalInfoStorage>()
                .AsSingle();

            container.Bind(
                    typeof(IUserContext),
                    typeof(IPurchaseHandler),
                    typeof(IJxUserReportingPublicUserIdProvider),
                    typeof(IJxMatchmakingPublicIdProvider),
                    typeof(IDisposable)
                )
                .To<UserContext>()
                .AsSingle();

            container.Bind<ILoadingScreenManager>()
                .To<LoadingScreenManager>()
                .AsSingle();

            container.Bind<IUserMissionDefinitionParser>()
                .To<UserMissionDefinitionParser>()
                .AsSingle();

            container.Bind<IRewardViewFactory>()
                .To<RewardViewFactory>()
                .AsSingle();

            container.Bind(typeof(IRewardViewManager))
                .To<RewardViewManager>()
                .AsSingle();

            container.Bind(typeof(IDialogScreenConfig))
                .FromInstance(Resources.Load<DialogScreenConfig>("configs/dialogs-config"))
                .AsSingle();

            container.Bind<IComingSoonAlert>()
                .To<ComingSoonAlert>()
                .AsSingle();

            container.Bind<ICharacterPathProvider>()
                .To<CharacterPathProvider>()
                .AsSingle();
        }

        private void InstallServices(DiContainer container)
        {
            container.Bind(typeof(IAuthIntegration), typeof(IJxApiGatewayUserAccessTokenProvider))
                .To<LoMFirebaseAuthIntegration>()
                .AsSingle();

            container.Bind<IGooglePlayGamesPlatform>()
#if !UNITY_EDITOR && UNITY_ANDROID
                     .To<GooglePlayGamesPlatform>()
#else
                .To<EmptyGooglePlayGamesPlatform>()
#endif
                .AsSingle();

            container.Bind<IJxGdprIntegration>()
                .To<JxGdprIntegration>()
                .AsSingle();

            container.Bind<INavigationInterceptor>()
                .To<AnalyticsNavigationInterceptor>()
                .AsSingle();

            container.Bind<IAuthManager>()
                .To<AuthManager>()
                .AsSingle();

            container.Bind<IPurchasingProductsProvider>()
                .To<PurchasingProductsProvider>()
                .AsSingle();

            container.Bind<IBillingIntegration>()
                .To<UnityBillingIntegration>()
                .AsSingle();

            container.Bind(typeof(IGoldenPassServerSynchronizer), typeof(IDisposable))
                .To<GoldenPassServerSynchronizer>()
                .AsSingle();

            container.Bind<IJxPriceResolver>()
                .To<PriceResolver>()
                .AsSingle();

            container.Bind<IAdvertisementIntegration>()
                .To<UnityAdvertisementIntegration>()
                .AsSingle();
        }

        private void HandleResourceLoadedInstantiated(IJxResourceLoader resourceLoader, DiContainer container)
        {
            var changeLanguageButtonPrefab = resourceLoader.LoadPrefab(
                "Screens",
                "Common",
                "ChangeLanguage",
                "Components",
                "ChangeLanguageButton"
            );

            Object.DontDestroyOnLoad(changeLanguageButtonPrefab);

            container
                .BindFactory<ChangeLanguageButtonParameters, ChangeLanguageButton, ChangeLanguageButton.Factory>()
                .FromMonoPoolableMemoryPool(
                    pool =>
                        pool.WithInitialSize(0)
                            .FromComponentInNewPrefab(changeLanguageButtonPrefab)
                );

            InstallEntityRenderer(resourceLoader, container);
        }

        private static void InstallEntityRenderer(IJxResourceLoader resourceLoader, DiContainer container)
        {
            var entityRendererPrefab = resourceLoader.LoadPrefab("EntityRenderers", "EntityRenderer");
            Object.DontDestroyOnLoad(entityRendererPrefab);

            container.BindFactory<Vector3, EntityRendererParameters, EntityRenderer, EntityRenderer.Factory>()
                .FromMonoPoolableMemoryPool(
                    pool => pool
                        .WithInitialSize(1)
                        .FromComponentInNewPrefab(entityRendererPrefab)
                );

            container.Bind<IRenderTextureFactory>()
                .To<RenderTextureFactory>()
                .AsSingle();

            container
                .Bind(typeof(IEntityRendererRepository), typeof(IEntityRendererRepositoryInitializer))
                .FromInstance(Resources.Load<EntityRendererRepository>("configs/entity-renderer-repository"))
                .AsSingle();

            container.Bind(typeof(IEntityRendererManager), typeof(IDisposable))
                .To<EntityRendererManager>()
                .AsSingle();
        }
    }
}
#endif