using System.Collections.Generic;
using Core.Extensions;
using Core.Managers.AssetLoader;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Player.Catcher.Components.RageProgressBar
{
    public class RageProgressBarComponent : MonoBehaviour
    {
        private const string PathToIcon = "gameplay-icons/catcher-rage-progress";
        private const string FilledPathToIcon = "filled";
        private const string UnfilledPathToIcon = "unfilled";

        private static readonly IReadOnlyDictionary<int, Color> _colorPerRage = new Dictionary<int, Color>()
        {
            { 0, ColorExtensions.GetColorFrom0255(132f, 129f, 144f,255f) },
            { 1, ColorExtensions.GetColorFrom0255(255f, 202f, 0f, 255f) },
            { 2, ColorExtensions.GetColorFrom0255(255f, 125f, 0f, 255f) },
            { 3, ColorExtensions.GetColorFrom0255(254f, 36f, 36f, 255f) },
            { 4, ColorExtensions.GetColorFrom0255(255f, 42f, 226f, 255f) },
            { 5, ColorExtensions.GetColorFrom0255(255f, 255f, 255f, 255f) }
        };

        [SerializeField]
        private SpriteRenderer[] _rageProgressBars = null!;

        public IReadOnlyList<SpriteRenderer> SpriteRenderers => _rageProgressBars;

        public void SyncRage(int rage)
        {
            rage = Mathf.Clamp(rage, 0, 5);
            for (var i = 0; i < _rageProgressBars.Length; ++i)
            {
                var isFilled = rage > i;
                _rageProgressBars[i].sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(PathToIcon, isFilled ? FilledPathToIcon : UnfilledPathToIcon);
            }

            if (_colorPerRage.TryGetValue(rage, out var currentRageColor))
                foreach (var bar in _rageProgressBars)
                    bar.color = currentRageColor;
        }
    }
}