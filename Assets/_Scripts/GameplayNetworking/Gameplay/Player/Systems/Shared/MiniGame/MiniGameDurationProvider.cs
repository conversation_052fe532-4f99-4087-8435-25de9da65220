using System;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.MiniGames.Concrete;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Utils.TimeSpanUtils;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame.DurationGenerator
{
    public class MiniGameDurationProvider
    {
        private static readonly TimeSpan _pingDuration = TimeSpan.FromSeconds(0.3f);

        private readonly CampfireMiniGameConfigPresentation _campfireConfig;
        private readonly CageMiniGameConfigPresentation _cageConfig;

        public MiniGameDurationProvider(IServerGameplayConfig serverGameplayConfig)
        {
            _campfireConfig = serverGameplayConfig.GetMiniGameConfig<CampfireMiniGameConfigPresentation>(MiniGameType.Campfire);
            _cageConfig = serverGameplayConfig.GetMiniGameConfig<CageMiniGameConfigPresentation>(MiniGameType.Cage);
        }

        public TimeSpan GetMaxDuration(MiniGameType miniGame)
        {
            var duration = miniGame switch
            {
                MiniGameType.None => TimeSpan.Zero,
                MiniGameType.Campfire => GetMaxCampfire(),
                MiniGameType.Cage => GetMaxCage(),
                _ => throw new ArgumentOutOfRangeException(nameof(miniGame), miniGame, null)
            };

            return duration;
        }

        public TimeSpan GetPingDuration() => _pingDuration;

        private TimeSpan GetMaxCampfire() =>
            _campfireConfig.AnnounceDuration + JxTimeSpanExtensions.Random(_campfireConfig.Duration.Min, _campfireConfig.Duration.Max);

        private TimeSpan GetMaxCage() =>
            (_cageConfig.AnnounceDuration + _cageConfig.Duration.Max + _cageConfig.Delay.Max) * _cageConfig.LockCount;
    }
}