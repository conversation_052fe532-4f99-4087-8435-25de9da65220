using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using GameplayNetworking.Gameplay.Player.Systems;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame.BotProcessors;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame.DurationGenerator;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Helpers;
using Mirror;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame
{
    public class MiniGamePlayerSystem : GameplayPlayerSystem
    {
        #region SERVER

        private readonly JxChangeableObject<GenericMiniGameResult, GenericMiniGameResult> _clientResponse = new();
        
        private MiniGameDurationProvider _durationProvider = null!;
        private MiniGameBotProcessorFactory _botProcessorFactory = null!;

        private ServerMiniGameHandler? _lastHandler;
        private Modifiers? _modifiers;

        protected override void OnServerStart()
        {
            base.OnServerStart();
            
            _modifiers = new Modifiers();

            SyncData.MiniGame.SetFromServer(NetPlayerMiniGameDto.Default);
            _clientResponse.Set(GenericMiniGameResult.None);
            
            _botProcessorFactory = new(Player, GlobalConfig);
            _durationProvider = new(GlobalConfig);

            Player.BaseSyncData.On_Cmd_SetMiniGameResult(OnMiniGameResult);
        }

        protected override void OnServerStop()
        {
            base.OnServerStop();

            _lastHandler?.Dispose();
            _modifiers = null;
        }

        [Server]
        public IServerMiniGameHandler GetHandler(MiniGameType type)
        {
            _lastHandler?.Dispose();
            return _lastHandler = new ServerMiniGameHandler(LaunchAsyncInternal, type);
        }

        [Server]
        public IDisposable AddResultModifier(MiniGameType type, Func<GenericMiniGameResult, GenericMiniGameResult> modify)
        {
            _modifiers!.Add(type, modify);
            return JxDisposableAction.Build().AppendCallback(() => _modifiers?.Remove(type));
        }

        [Server]
        private async UniTask<GenericMiniGameResult> LaunchAsyncInternal(MiniGameType type, CancellationToken ct)
        {
            if (ct.IsCancellationRequested)
                return GenericMiniGameResult.None;
            
            SyncData.MiniGame.SetFromServer(SyncData.MiniGame.Value.SetType(type));

            var maxDuration = _durationProvider.GetMaxDuration(type);

            var result = Player.IsBot() ? await LaunchForBotAsync(type, maxDuration, ct) : await LaunchForPlayerAsync(maxDuration, ct);

            HandleResult(result);
            return result;
        }

        private async UniTask<GenericMiniGameResult> LaunchForBotAsync(MiniGameType type, TimeSpan maxDuration, CancellationToken ct)
        {
            var (isCancelled, result) = await _botProcessorFactory.PlayAsync(type, ct)
                                                                  .TimeoutWithoutException(maxDuration);

            if (ct.IsCancellationRequested)
                return GenericMiniGameResult.None;

            return isCancelled ? GenericMiniGameResult.Ignore : result;
        }

        private async UniTask<GenericMiniGameResult> LaunchForPlayerAsync(TimeSpan maxDuration, CancellationToken ct)
        {
            var ping = _durationProvider.GetPingDuration();
            var responseTask = _clientResponse.AwaitChange(cancellationToken: ct).AsUniTask();
            var durationTask = UniTask.Delay(maxDuration + ping, cancellationToken: ct);
            var tasks = await UniTask.WhenAny(responseTask, durationTask).SuppressCancellationThrow();

            if (tasks.IsCanceled)
                return GenericMiniGameResult.None;

            return tasks.Result.hasResultLeft ? tasks.Result.result : GenericMiniGameResult.Ignore;
        }

        [Server]
        private void OnMiniGameResult(GenericMiniGameResult result)
        {
            if (!SyncData.MiniGame.Value.IsRunning || _lastHandler == null)
                return;

            _clientResponse.Set(result);
        }

        [Server]
        private void HandleResult(GenericMiniGameResult result)
        {
            if (!SyncData.MiniGame.Value.IsRunning)
                return;

            if (_modifiers!.Any())
                result = _modifiers.Apply(SyncData.MiniGame.Value.Type, result);
            
            SyncData.MiniGame.SetFromServer(SyncData.MiniGame.Value.SetResult(result));

            _lastHandler?.Dispose();
            _clientResponse.SetIfDistinct(GenericMiniGameResult.None);
        }

        #region CLASSES

        private class Modifiers
        {
            private readonly IDictionary<MiniGameType, MiniGameResultModifier> _modifierByType;

            public Modifiers()
            {
                _modifierByType = new Dictionary<MiniGameType, MiniGameResultModifier>();
            }

            public bool Any() => _modifierByType.Count > 0;

            public void Add(MiniGameType type, Func<GenericMiniGameResult, GenericMiniGameResult> modify)
            {
                if (_modifierByType.TryGetValue(type, out var modifier))
                {
                    modifier.Add(modify);
                }
                else
                {
                    var mod = new MiniGameResultModifier();
                    mod.Add(modify);
                    _modifierByType.Add(type, mod);
                }
            }

            public void Remove(MiniGameType type) => _modifierByType.Remove(type);

            public GenericMiniGameResult Apply(MiniGameType type, GenericMiniGameResult result)
            {
                if (!_modifierByType.TryGetValue(type, out var modifier))
                    return result;
                
                return modifier.Apply(result);
            }
        }

        private class MiniGameResultModifier
        {
            private readonly IList<Func<GenericMiniGameResult, GenericMiniGameResult>> _modifiers;

            public MiniGameResultModifier()
            {
                _modifiers = new List<Func<GenericMiniGameResult, GenericMiniGameResult>>();
            }

            public void Add(Func<GenericMiniGameResult, GenericMiniGameResult> modify)
            {
                _modifiers.Add(modify);
            }

            public GenericMiniGameResult Apply(GenericMiniGameResult result)
            {
                foreach (var modify in _modifiers)
                    result = modify(result);
                
                return result;
            }
        }

        #endregion

        #endregion
    }
}