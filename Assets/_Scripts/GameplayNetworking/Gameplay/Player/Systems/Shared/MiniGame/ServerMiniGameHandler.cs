using System;
using System.Threading;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame
{
    public class ServerMiniGameHandler : IServerMiniGameHandler
    {
        private readonly CancellationTokenSource _cts;
        private readonly MiniGameType _miniGameType;
        private readonly Func<MiniGameType, CancellationToken, UniTask<GenericMiniGameResult>> _playAsync;

        public ServerMiniGameHandler(
            Func<MiniGameType, CancellationToken, UniTask<GenericMiniGameResult>> playAsync,
            MiniGameType miniGameType
        )
        {
            _cts = new CancellationTokenSource();
            _playAsync = playAsync;
            _miniGameType = miniGameType;
        }

        public UniTask<GenericMiniGameResult> PlayAsync() => _playAsync(_miniGameType, _cts.Token);

        public void Dispose()
        {
            if (_cts.IsCancellationRequested)
                return;

            _cts.CancelAndDispose();
        }
    }
}