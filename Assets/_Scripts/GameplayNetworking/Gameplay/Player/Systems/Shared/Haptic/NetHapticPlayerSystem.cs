using GameplayNetworking.Gameplay.Player.Systems;
using GameplayNetworking.Manager.Network.Server;
using Jx.Utils.Observable;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting
{
    public class NetHapticPlayerSystem : GameplayPlayerSystem
    {
        #region SHARED

        public void Request(string patternId)
        {
            // exclude bots
            if (JxNetworkServer.Instance.IsActive && Player.IsBot())
                return;

            var request = new GameplayHapticRequest(patternId);

            // show locally
            if (JxNetworkClient.Instance.IsActive)
            {
                _onClientRequested.Invoke(request);
                return;
            }
            
            SyncData.TargetRpc_RequestHaptic(request);
        }

        #endregion

        #region CLIENT

        private readonly JxObservable<GameplayHapticRequest> _onClientRequested = new JxObservable<GameplayHapticRequest>();
        public IJxObservable<GameplayHapticRequest> OnClientRequested => _onClientRequested;

        protected override void OnClientStart()
        {
            base.OnClientStart();
            
            SyncData.On_TargetRpc_RequestHaptic(_onClientRequested.Invoke);
        }

        #endregion
    }
}