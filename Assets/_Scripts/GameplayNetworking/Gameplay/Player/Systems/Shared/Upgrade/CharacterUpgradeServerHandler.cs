using System;
using Configs.ServerGameplay.Presentation.Upgrades;
using Jx.Utils.Logging;
using Jx.Utils.Objects;

namespace GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade
{
    public abstract class CharacterUpgradeServerHandler<TConfig> : ICharacterUpgradeServerHandler
        where TConfig : class, ICharacterUpgradeConfigPresentation
    {
        protected CharacterUpgradeServerHandler()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }
        
        protected IJxLogger Logger { get; }
        protected TConfig Config { get; private set; } = null!;
        protected CharacterUpgradeHandlerContext Context { get; private set; } = null!;
        protected int UpgradeCount { get; private set; }

        public void Initialize(
            CharacterUpgradeHandlerContext context,
            ICharacterUpgradeConfigPresentation config,
            int upgradeCount)
        {
            Context = context;
            UpgradeCount = upgradeCount;
            Config = config.Cast<TConfig>();
            OnInitialized();
        }
        
        protected virtual void OnInitialized() {}
        public abstract IDisposable Apply();
    }
}