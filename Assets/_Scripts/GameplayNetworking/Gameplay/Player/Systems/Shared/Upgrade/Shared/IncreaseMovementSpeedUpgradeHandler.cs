using System;
using Configs.ServerGameplay.Presentation.Upgrades;
using UnityEngine.Scripting;

namespace GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade.Shared
{
    [Preserve]
    public class IncreaseMovementSpeedUpgradeHandler : CharacterUpgradeServerHandler<MovementCharacterUpgradeConfigPresentation>
    {
        [Preserve]
        public IncreaseMovementSpeedUpgradeHandler()
        {
        }
        
        public override IDisposable Apply()
        {
            var delta = Config.SpeedMultiplier * UpgradeCount;
            return Context.Player.NetTransform.ChangeMovementSpeed(delta);
        }
    }
}