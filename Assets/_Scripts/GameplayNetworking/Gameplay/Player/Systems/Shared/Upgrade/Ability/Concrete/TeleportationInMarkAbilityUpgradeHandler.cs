using System;
using Configs.ServerGameplay.Presentation.Upgrades.Concrete;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Abilities.Impl;
using SceneLogics.GameplayScene.Abilities.Impl.Modifications;
using UnityEngine.Scripting;

namespace GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade.Ability.Concrete
{
    [Preserve]
    public class TeleportationInMarkAbilityUpgradeHandler : AbilityUpgradeHandler<TeleportInMarkAbilityUpgradeConfigPresentation, TeleportationInMarkAbilityController>
    {
        [Preserve]
        public TeleportationInMarkAbilityUpgradeHandler()
        {
        }
        
        protected override IDisposable ApplyStage(int stage)
        {
            switch (stage)
            {
                case 0:
                    return AbilityController.ModifyCooldown(Config.CooldownMultiplier * UpgradeCount);
                case 1:
                    return AbilityController.AddGenericModification<TimeSpan>(GenericAbilityModifiedProperty.WitchMarkLifespan,
                        lifespan => lifespan + lifespan * Config.MarkLifespawnMultiplier * UpgradeCount
                    );
            }
            
            return JxDisposableAction.Empty;
        }
    }
}