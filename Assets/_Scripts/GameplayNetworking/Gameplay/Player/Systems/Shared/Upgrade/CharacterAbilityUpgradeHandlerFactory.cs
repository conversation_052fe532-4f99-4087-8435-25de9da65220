using System;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.Upgrades;
using Configs.ServerGameplay.Presentation.Upgrades.Concrete;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade.Ability;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade.Ability.Concrete;
using Jx.Utils.Objects;
using LoM.Characters.ClientIntegration;
using SceneLogics.GameplayScene.Abilities;
using UI.Screens.MainMenuScene.Characters.Upgrade;

namespace GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade
{
    public class CharacterAbilityUpgradeHandlerFactory
    {
        private readonly CharacterUpgradeHandlerContext _context;
        private readonly IServerGameplayConfig _config;

        public CharacterAbilityUpgradeHandlerFactory(CharacterUpgradeHandlerContext context, IServerGameplayConfig config)
        {
            _context = context;
            _config = config;
        }
        
        public ICharacterUpgradeServerHandler CreateOrThrow(NetCharacterStatDto dto)
        {
            if (dto.Type.GetKind() != CharacterStatKind.Ability)
                throw new ArgumentException($"Trying to create non ability upgrade handler '{dto.Type}'");

            var abilityType = _context.Player.BaseSyncData.Ability.Value.AbilityType;
            var upgradeCount = dto.Count;
            var stage = 0;
            if (dto.Type == CharacterStatType.AbilityAmplification1)
                stage = 1;
            
            switch (abilityType)
            {
                case AbilityType.EchoLocation:
                    return Build<EchoLocationAbilityUpgradeHandler, EchoLocationAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Fireball:
                    return Build<FireballAbilityUpgradeHandler, FireballAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Hook:
                    return Build<HookAbilityUpgradeHandler, HookAbilityUpgradeConfigPresentation>();
                
                case AbilityType.EscapeeMimicry:
                    return Build<EscapeeMimicryAbilityUpgradeHandler, EscapeeMimicryAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Roll:
                    return Build<RollAbilityUpgradeHandler, RollAbilityUpgradeConfigPresentation>();
                
                case AbilityType.AngelHelp:
                    return Build<AngelHelpUpgradeHandler, AngelHelpUpgradeConfigPresentation>();
                
                case AbilityType.ThrowRock:
                    return Build<ThrowRockAbilityUpgradeHandler, ThrowRockAbilityUpgradeConfigPresentation>();
                
                case AbilityType.JumpShake:
                    return Build<JumpShakeAbilityUpgradeHandler, JumpShakeAbilityUpgradeConfigPresentation>();
                
                case AbilityType.SpiderWeb:
                    return Build<SpiderWebAbilityUpgradeHandler, SpiderWebAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Invisibility:
                    return Build<InvisibilityAbilityUpgradeHandler, InvisibilityAbilityUpgradeConfigPresentation>();
                
                case AbilityType.KnockingDash:
                    return Build<KnockingDashAbilityUpgradeHandler, KnockingDashAbilityUpgradeConfigPresentation>();
                
                case AbilityType.FayWall:
                    return Build<FayWallAbilityUpgradeHandler, FayWallAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Teleport:
                    return Build<TeleportAbilityUpgradeHandler, TeleportAbilityUpgradeConfigPresentation>();
                
                case AbilityType.TimeLaps:
                    return Build<TimeLapsAbilityUpgradeHandler, TimeLapsAbilityUpgradeConfigPresentation>();
                    
                case AbilityType.ElectraField:
                    return Build<ElectraFieldAbilityUpgradeHandler, ElectraFieldAbilityUpgradeConfigPresentation>();
      
                case AbilityType.LuckyBag:
                    return Build<LuckyBagAbilityUpgradeHandler, LuckyBagAbilityUpgradeConfigPresentation>();
                
                case AbilityType.SmokeCloud:
                    return Build<SmokeCloudAbilityUpgradeHandler, SmokeCloudAbilityUpgradeConfigPresentation>();
                
                case AbilityType.Fear:
                    return Build<FearAbilityUpgradeHandler, FearAbilityUpgradeConfigPresentation>();;
                    
                case AbilityType.TeleportInMark:
                    return Build<TeleportationInMarkAbilityUpgradeHandler, TeleportInMarkAbilityUpgradeConfigPresentation>();
                
                case AbilityType.LazyRegeneration:
                    return Build<LazyRegenerationAbilityUpgradeHandler, LazyRegenerationAbilityUpgradeConfigPresentation>();
                
                case AbilityType.MicroDash:
                    return Build<MicroDashAbilityUpgradeHandler, MicroDashAbilityUpgradeConfigPresentation>();
            }

            throw new ArgumentOutOfRangeException(nameof(abilityType), abilityType, null);;

            ICharacterUpgradeServerHandler Build<THandler, TPresentation>()
                where THandler : IAbilityUpgradeHandler, new()
                where TPresentation : class, ICharacterUpgradeConfigPresentation
            {
                var config = _config.Upgrades.Abilities.GetOrThrow<TPresentation>();
                var handler = new THandler();
                handler.Initialize(_context, config, upgradeCount);
                handler.SetStage(stage);
                return handler;
            }
        }
    }
}