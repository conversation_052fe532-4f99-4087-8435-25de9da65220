using System;
using System.Collections.Generic;
using System.Linq;
using Configs.Perks;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Perks;
using GameplayNetworking.Gameplay.Player.Systems;
using GameplayNetworking.Manager.Network.Matchmaking;
using GameplayNetworking.Manager.Network.Share.Authentication;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Collections;
using Jx.Utils.Logging;
using LoM.Characters.ClientIntegration.Perks;
using LoM.Messaging.ClientIntegrations;
using LoM.Messaging.ClientIntegrations.Upgrades.Perks;
using Mirror;
using SceneLogics.GameplayScene.Components.FlyingTexts;
using SceneLogics.GameplayScene.Perks;
using Zenject;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Perk
{
    public class PerkPlayerSystem : GameplayPlayerSystem
    {
        #region SERVER

        private readonly IPerkIntegration _perkIntegration;
        
        private PerkControllerFactory _factory = null!;
        private IReadOnlyList<IPerkController> _controllers = null!;
        
        private IReadOnlyDictionary<PerkType, UserCharacterPerkProgressClientIntegration>? _cachedBotPerkProgress;

        public PerkPlayerSystem(
            IPerkIntegration perkIntegration)
        {
            _perkIntegration = perkIntegration;
        }

        protected override void OnServerStart()
        {
            base.OnServerStart();

            _factory = new PerkControllerFactory(Context.Server.GameplayContext, Context.GlobalConfig, Player);
            _controllers = CreateControllers();
        }

        protected override void OnServerReady()
        {
            base.OnServerReady();
            
            foreach (var controller in _controllers)
                controller.Start();
        }

        protected override void OnServerStop()
        {
            base.OnServerStop();

            foreach (var controller in _controllers)
                controller.Stop();
        }

        [Server]
        private IReadOnlyList<IPerkController> CreateControllers()
        {
            var controllers = new List<IPerkController>();
            var definitions = GetPerkDefinitions(Player);
            
            foreach (var definition in definitions)
            {
                var controller = _factory.CreateOrThrow(definition.Config.Type, definition.Settings);
                SyncData.AddPerk(definition.Config.Type, PerkDto.Default);
                controllers.Add(controller);
            }
            
            return controllers;
        }
        
        // [Server]
        // private IReadOnlyList<PerkType> GetAttachedPerks()
        // {
        //     var characterIndex = Player.CharacterView.CharacterPresenterData.Value.Index;
        //
        //     if (Player.IsBot())
        //     {
        //         var perks = GetBotEquippedPerkProgress(characterIndex)
        //             .Select(p => p.Key)
        //             .ToArray();
        //
        //         return perks;
        //     }
        //     
        //     return GetPlayerEquippedPerks(Player.connectionToClient.GetAuth().UserData, characterIndex);
        // }
        
        [Server]
        public IReadOnlyList<PerkDefinition> GetPerkDefinitions(NetGamePlayer player)
        {
            var authInfo = player.Client.AuthInfo;
            var characterIndex = authInfo.CharacterIdentifier.Index;

            var userEquippedPerkProgress = player.IsBot()
                ? GetBotEquippedPerkProgress(characterIndex, realClient: player.Client)
                : GetUserEquippedPerkProgress(authInfo.UserData, characterIndex);
            
            if (userEquippedPerkProgress?.Count > 0)
            {
                var result = new List<PerkDefinition>();

                foreach (var pair in userEquippedPerkProgress)
                {
                    var perkType = pair.Key;
                    var perkProgress = pair.Value;

                    if (perkProgress != null)
                    {
                        var perkConfig = _perkIntegration.FindConfig(perkType);
                        if (perkConfig != null)
                        {
                            if (!perkConfig.IsSupported(characterIndex))
                            {
                                Logger.LogError(
                                    $"Perk is not supported for provided character",
                                    trackingFactory: () => new Dictionary<string, object>()
                                    {
                                        ["PerkId"] = perkType,
                                        ["CharacterIndex"] = characterIndex,
                                    }
                                );
                                continue;
                            }

                            var levelInfo = perkConfig.FindSettings(perkProgress.CurrentLevel);
                            result.Add(new PerkDefinition(perkType, perkConfig, levelInfo));
                        }
                    }
                }

                return result;
            }

            return Array.Empty<PerkDefinition>();
        }

        [Server]
        private IReadOnlyDictionary<PerkType, UserCharacterPerkProgressClientIntegration> GetUserEquippedPerkProgress(
            UserDataClientIntegration userData,
            int characterIndex
        )
        {
            var perkProgress = FindCharacterPerkProgress(userData, characterIndex);
            var equippedPerkIds = FindCharacterEquippedPerkIds(userData, characterIndex);

            var result = new Dictionary<PerkType, UserCharacterPerkProgressClientIntegration>();
            if (equippedPerkIds?.Count > 0 && perkProgress?.Count > 0)
            {
                foreach (var equippedPerkId in equippedPerkIds)
                {
                    var perkType = (PerkType)equippedPerkId;
                    if (perkProgress.TryGetValue(perkType, out var progress) && progress != null)
                    {
                        result.Add(perkType, progress);
                    }
                }
            }

            return result;
        }

        [Server]
        private IReadOnlyDictionary<PerkType, UserCharacterPerkProgressClientIntegration> GetBotEquippedPerkProgress(int characterIndex, MatchClientInfo realClient)
        {
            return _cachedBotPerkProgress ??= Get();

            IReadOnlyDictionary<PerkType, UserCharacterPerkProgressClientIntegration> Get()
            {
                var result = new Dictionary<PerkType, UserCharacterPerkProgressClientIntegration>();
                
                var authInfo = realClient.AuthInfo;

                var slotCount = FindCharacterEquippedPerkIds(authInfo.UserData, characterIndex)?.Count ?? 0;
                if (slotCount > 1)
                {
                    var perkProgress = FindCharacterPerkProgress(authInfo.UserData, characterIndex);
                    if (perkProgress != null)
                    {
                        var perks = perkProgress.Where(p => (int)p.Key < 1000).ToList();
                        if (perks.Count > 0)
                        {
                            var randomPerks = perks.GetRandomNElements(Math.Min(slotCount, perks.Count));

                            foreach (var pair in randomPerks)
                            {
                                result.Add(pair.Key, pair.Value);
                            }
                        }
                    }
                }

                return result;
            }
        }

        [Server]
        private IReadOnlyList<PerkType> GetPlayerEquippedPerks(
            UserDataClientIntegration userData,
            int characterIndex)
        {
            var slotsIntegration = userData?.Progress?
                .Upgrades?
                .Characters?
                .CharacterProgressById?
                .GetOrDefault(characterIndex)?
                .Perks?
                .SlotStorage;

            if (slotsIntegration == null)
            {
                Logger.LogError("Failed to parse slots integration");
                return Array.Empty<PerkType>();
            }

            var perks = slotsIntegration.SlotIdByUsedPerkId.Values.Select(p => (PerkType)p).ToArray();
            return perks;
        }

        [Server]
        private IDictionary<PerkType, UserCharacterPerkProgressClientIntegration> FindCharacterPerkProgress(
            UserDataClientIntegration userData,
            int characterIndex
        )
        {
            return userData?.Progress?
               .Upgrades?
               .Characters?
               .CharacterProgressById?
               .GetOrDefault(characterIndex)?
               .Perks?
               .Perks;
        }

        [Server]
        private Dictionary<int, int>.ValueCollection FindCharacterEquippedPerkIds(UserDataClientIntegration userData, int characterIndex)
        {
            return userData?.Progress?
               .Upgrades?
               .Characters?
               .CharacterProgressById?
               .GetOrDefault(characterIndex)?
               .Perks?
               .SlotStorage?
               .SlotIdByUsedPerkId?
               .Values;
        }

        #endregion

        #region CLASSES
        
        public struct PerkDefinition
        {
            public PerkDefinition(PerkType type, IPerkConfig config, IPerkSettings settings)
            {
                Type = type;
                Config = config;
                Settings = settings;
            }

            public PerkType Type { get; }
            public IPerkConfig Config { get; }
            public IPerkSettings Settings { get; }
        }

        #endregion
    }
}