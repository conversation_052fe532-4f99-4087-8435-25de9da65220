using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Systems;
using Mirror;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.GameplayResult
{
    public class GameplayResultPlayerSystem : GameplayPlayerSystem
    {
        public GameplayResultPlayerSystem() { }

        protected override void OnServerStart()
        {
            base.OnServerStart();

            SyncData.State.SetFromServer(GameplayPlayerState.InGame);
        }

        [Server]
        public void SetFinish(bool isWinner) => SyncData.State.SetFromServer(isWinner ? GameplayPlayerState.IsWinner : GameplayPlayerState.IsLoser);
    }
}