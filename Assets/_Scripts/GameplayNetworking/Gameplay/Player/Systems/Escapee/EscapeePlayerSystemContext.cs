using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.Character;
using Configs.ServerGameplay.Presentation.Character.Escapee;

namespace GameplayNetworking.Gameplay.Player.Systems
{
    public class EscapeePlayerSystemContext : GameplayPlayerSystemContext
    {
        public EscapeePlayerSystemContext(
            NetGamePlayer player,
            BaseCharacterConfigPresentation config,
            IServerGameplayConfig globalConfig,
            GameplayServerOnlyContext server,
            GameplayPlayerClientOnlyContext clientOnly,
            GameplayPlayerSharedContext shared
        )
            : base(player, config, globalConfig, server, clientOnly, shared)
        {
            TeamConfig = globalConfig.Characters.Escapees;
        }
        
        public EscapeeTeamConfigPresentation TeamConfig { get; }
    }
}