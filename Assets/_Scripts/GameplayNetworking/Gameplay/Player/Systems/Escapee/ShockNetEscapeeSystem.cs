using System;
using Configs.ServerGameplay.Presentation.Buffs;
using Configs.ServerGameplay.Presentation.Character;
using Configs.ServerGameplay.Presentation.Character.Escapee;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Escapee;

namespace GameplayNetworking.Gameplay.Player.Systems.Escapee
{
    public class ShockNetEscapeeSystem : EscapeePlayerSystem
    {
        #region SERVER

        private IDisposable? _healthSubscription;
        private IDisposable? _shockBuffActivation;

        protected override void OnServerStart()
        {
            base.OnServerStart();

            _healthSubscription = Player.SyncData.Health.Subscribe(OnHealthChanged);
        }

        protected override void OnServerStop()
        {
            base.OnServerStop();

            _healthSubscription?.Dispose();

            _shockBuffActivation?.Dispose();
            _shockBuffActivation = null;
        }

        private void OnHealthChanged(NetEscapeeHealthDto old, NetEscapeeHealthDto @new)
        {
            if (Player.SyncData.Health.Value.IsInjured())
            {
                if (@new.Current < old.Current)
                    _shockBuffActivation ??= ActivateShockBuff();
            }
            else
            {
                _shockBuffActivation?.Dispose();
                _shockBuffActivation = null;
            }
        }

        private IDisposable ActivateShockBuff()
        {
            return Player.Buff.Activate(BuffType.Shock, ((EscapeeCharacterConfigPresentation)Context.Config).ShockDuration);
        }

        #endregion
    }
}