using System;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Escapee;
using Mirror;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Player.Systems.Health
{
    public class HealthNetEscapeeSystem : EscapeePlayerSystem
    {
        private NetEscapeeHealthDto Data => SyncData.Health.Value;
        
        #region CLIENT

#if DEBUG_GAMEPLAY_ANALYTICS
        protected override void OnClientReady()
        {
            base.OnClientReady();
            
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(Player.netId, "HP", () => $"{Data.Current}/{Data.Max}");
        }
#endif

        #endregion

        #region Server

        protected override void OnServerStart()
        {
            var maxHealth = CharacterConfig.MaxHealth;
            Player.SyncData.Health.SetFromServer(new NetEscapeeHealthDto((byte)maxHealth, (byte)maxHealth, (byte)maxHealth));
        }

        [Server]
        public void Increase(byte increaseValue = 1)
        {
            if (increaseValue <= 0)
            {
                Logger.LogError($"Invalid increase value: {increaseValue}");
                return;
            }
            
            if (Data.IsFull())
                return;

            Set(Data.Current + increaseValue);
        }

        [Server]
        public void Decrease(byte decreaseValue = 1)
        {
            if (decreaseValue <= 0)
            {
                Logger.LogError($"Invalid increase value: {decreaseValue}");
                return;
            }
            
            if (!Data.Has())
                return;

            if (Player.SyncData.Shield.Value.IsActive)
            {
                Player.Shield.Decrease(decreaseValue);
                return;
            }

            Set(Data.Current - decreaseValue);

            if (Data.Has()) // otherwise handled by StunByZeroHealthBuffComponent
                Player.Action.SetNewTemporary(ActionType.DamageReceiving, TimeSpan.FromSeconds(0.5f));
        }

        [Server]
        public EscapeeHealthSnapshot TakeSnapshot()
        {
            var snapshot = new EscapeeHealthSnapshot(Data, Restore);
            return snapshot;
        }

        [Server]
        private void Restore(NetEscapeeHealthDto dto)
        {
            Set(dto.Current);
        }

        [Server]
        private void Set(int newHealthValue)
        {
            var previous = Data.Current;
            newHealthValue = Mathf.Clamp(newHealthValue, 0, Data.Max);
            
            Player.SyncData.Health.ChangeFromServer(h => new NetEscapeeHealthDto((byte)newHealthValue, h.Max, previous));
        }

        #endregion
    }
}