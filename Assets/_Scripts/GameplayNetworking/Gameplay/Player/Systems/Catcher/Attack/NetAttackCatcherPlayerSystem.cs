using System;
using System.Collections.Generic;
using System.Linq;
using Core.Extensions;
using Core.Helpers;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player.Components;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Data;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Collections;
using Jx.Utils.Coroutines;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Mirror;
using UnityEngine;

#if !HEADLESS
using SceneLogics.GameplayScene.Client.Vfx;
using SceneLogics.GameplayScene.Components.FlyingTexts;
using SceneLogics.GameplayScene.Components.Particles;
#endif

namespace GameplayNetworking.Gameplay.Player.Systems.Catcher.Attack
{
    public class NetAttackCatcherPlayerSystem : CatcherPlayerSystem
    {
        public void Process()
        {
            if (NetworkServer.active)
                OnAttackRequestedAsync().Forget();
            else
                SyncData.Cmd_ProcessAttack();
        }
        
        #region CLIENT

        private IDisposable? _reaperWeaponVisibilitySubscription;

        protected override void OnClientStart()
        {
            base.OnClientStart();

            _reaperWeaponVisibilitySubscription = SyncData.ReaperWeaponIsVisible.SubscribeAndFire(Client_OnReaperWeaponVisibilityChanged);
            SyncData.On_ClientRpc_OnAttacked(Client_OnAttacked);
            SyncData.On_ClientRpc_OnAttackMiss(Client_OnMiss);
        }

        protected override void OnClientStop()
        {
            base.OnClientStop();
            
            _reaperWeaponVisibilitySubscription?.Dispose();
        }

#if DEBUG_GAMEPLAY_ANALYTICS
        protected override void OnClientReady()
        {
            base.OnClientReady();
            
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(
                Player.netId,
                "Full-Success attack duration", 
                () => $"{(int)GetAttackFullDuration(true).TotalMilliseconds}ms");
            
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(
                Player.netId,
                "Full-Fail attack duration", 
                () => $"{(int)GetAttackFullDuration(false).TotalMilliseconds}ms");
                
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(
                Player.netId,
                "Attack radius", 
                () => $"{JxMathf.SqrtOrDefault(GetAttackSqrRadius()):0.00}");
        }
#endif

        [Client]
        private void Client_OnMiss(NetEscapeePlayer escapeePlayer)
        {
#if !HEADLESS
            var position = escapeePlayer.transform.position;
            position.y += 1f;
            Context.ClientOnly.FlyingTextClientManager.ShowAndForget(FlyingTextType.Miss, position);
#endif
        }
        
        [Client]
        private void Client_OnAttacked(NetEscapeePlayer escapeeToHit)
        {
#if !HEADLESS
            Player.ClientAnimation.OnSuccessfulAttack();
            
            if (!escapeeToHit.SyncData.Shield.Value.IsActive)
            {
                var position = escapeeToHit.transform.position;
                position.y += 1f;
                Context.ClientOnly.FlyingTextClientManager.ShowAndForget(FlyingTextType.Hit, position);
            }
            
            Context.ClientOnly.VfxManager.Show(
                VfxBuilder
                    .Create(ParticleType.Hit)
                    .SetOwner(escapeeToHit)
                    .SetGlobalRotation(Quaternion.LookRotation(-Player.NetTransform.UnityTransform.right, Vector3.up))
                    .Build()
            );
#endif
        }
        
        private ReaperWeapon? _reaperWeapon;

        [Client]
        private void Client_OnReaperWeaponVisibilityChanged(bool isVisible)
        {
            // if no reaper - always visible and null reaper weapon
            if (isVisible && ReferenceEquals(_reaperWeapon, null))
                return;
            
            _reaperWeapon ??= Player.GetComponentInChildren<ReaperWeapon>();
        
            if (_reaperWeapon == null)
            {
                var publicData = Player.BaseSyncData.PublicInfo.Value;
        
                Logger.LogError(
                    "There is no reaper weapon component",
                    trackingFactory: () =>
                        new Dictionary<string, object>
                        {
                            ["Player name"] = Player.name,
                            ["Nickname"] = publicData.Nickname,
                            ["Concrete character index"] = publicData.CharacterIdentifier.Index,
                        }
                );
        
                return;
            }
        
            _reaperWeapon.gameObject.SetActive(isVisible);
            
        }

        [ClientRpc]
        public void SetReaperWeaponVisibility(bool isVisible) => SyncData.ReaperWeaponIsVisible.SetFromServer(isVisible);

        #endregion

        #region SERVER
        
        private const float TargetTooCloseRadius = 0.5f;

        private static readonly int _collisionLayerMask = LayerType.StaticObstacle.ToMask() |
                                                          LayerType.CatcherCollider.ToMask() |
                                                          LayerType.CatcherFormCollider.ToMask();

        private bool _isAttacking;
        private Modifiers _modifiers = null!;

        private IDisposable? _autoAttackCancellation;

        protected override void OnServerStart()
        {
            base.OnServerStart();
            
            _modifiers = new Modifiers();

            SyncData.AttackAllowed.SetFromServer(true);
            SyncData.ReaperWeaponIsVisible.SetFromServer(true);
            _isAttacking = false;

            SyncAttackSpeed();

            if (!Player.IsBot() && Player.BaseSyncData.PublicInfo.Value.AutoAttackEnabled)
                _autoAttackCancellation = AutoAttackBehaviour.Create(this, Player);

            SyncData.On_Cmd_ProcessAttack(() => OnAttackRequestedAsync().Forget());
        }

        protected override void OnServerStop()
        {
            base.OnServerStop();

            _autoAttackCancellation?.Dispose();
        }

        [Server]
        public IDisposable ModifyAttackRadius(float multiplier)
        {
            return _modifiers.AddRadiusModification(multiplier);
        }

        [Server]
        public IDisposable ModifySuccessfulAttackDuration(float multiplier)
        {
            var modificationCancellation = _modifiers.AddSpeedModification(multiplier);
            SyncAttackSpeed();
            return modificationCancellation;
        }

        [Server]
        public void SetCanAttack(bool canAttack) => SyncData.AttackAllowed.SetFromServer(canAttack);

        [Server]
        public bool SomeoneCanBeAttacked()
        {
            if (!AttackAllowed())
                return false;

            return Context.Server.GameplayContext.Spawned.Escapees.Any(CanBeAttacked);
        }
        
        [Server]
        public bool CanBeAttacked(NetEscapeePlayer target)
        {
            if (!target.CanBeAttacked())
                return false;

            var escapeePosition = target.NetTransform.Position;

            if (CheckPointBehindObstacle(Player.NetTransform.Position, escapeePosition))
                return false;

            if (!CheckAttackFieldContainsPoint(escapeePosition))
                return false;

            if (!target.Visibility.IsVisibleFor(Player))
                return false;

            return true;
        }

        [Server]
        private async UniTaskVoid OnAttackRequestedAsync()
        {
            var attackingChanged = false;

            try
            {
                if (!AttackAllowed())
                    return;
                if (_isAttacking)
                    return;

                _isAttacking = true;
                attackingChanged = true;

                var escapees = Context.Server.GameplayContext.Spawned.Escapees;
                var catcherPosition = Player.NetTransform.Position;

                var nearestEscapee = escapees
                                     .Where(CanBeAttacked)
                                     .MinByDistance(e => Vector3.SqrMagnitude(catcherPosition - e.transform.position));
                
                if (ReferenceEquals(nearestEscapee, null))
                    return;

                await DoAttackPunchAsync(GetAttackFullDuration(true));

                if (!IsAttackStillSuccessful(nearestEscapee))
                {
                    if (Player.BaseSyncData.Action.Value == ActionType.Attack)
                        TryShowMissText();
                }
                else
                {
                    SyncData.ClientRpc_OnAttacked(nearestEscapee);

                    nearestEscapee.Health.Decrease();

                    // we give score only for health/shield hp decreased, unharmed blocks scoring
                    if (!nearestEscapee.SyncData.Shield.Value.IsUnharmed)
                    {
                        Player.Score.AddInstant(ScoreSource.EnemyInjured);
                        CountSuccessfulAttackStatistics();

                        if (!nearestEscapee.SyncData.Health.Value.Has())
                        {
                            Player.Score.AddInstant(ScoreSource.EnemyKnockdown);
                        }
                    }
                }

                void TryShowMissText()
                {
                    var attackableEscapeeInAttackRadius =
                        escapees.FirstOrDefault(
                            e =>
                                e.CanBeAttacked() &&
                                JxMathf.IsInsideXZCircle(
                                    Player.NetTransform.Position,
                                    GetAttackSqrRadius(),
                                    e.transform.position
                                )
                        );

                    if (attackableEscapeeInAttackRadius != null)
                    {
                        SyncData.ClientRpc_OnAttackMiss(attackableEscapeeInAttackRadius);
                    }
                }

                void CountSuccessfulAttackStatistics()
                {
                    Player.Statistics.Increment(GameplayStatisticsType.HitEscapeeByAttack);

                    if (nearestEscapee.BaseSyncData.Action.Value != ActionType.StunnedBeZeroHealth)
                        return;

                    Player.Statistics.Increment(GameplayStatisticsType.StunEscapee);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex);
            }
            finally
            {
                if (attackingChanged)
                    _isAttacking = false;
            }
        }

        private async UniTask DoAttackPunchAsync(TimeSpan duration)
        {
            Player.Action.SetNewTemporary(ActionType.Attack, duration);
            var movementSpeed = Player.NetTransform.OverrideMovementSpeed(Context.TeamConfig.AttackingMovementSpeed);
            Player.InvokeAfterDelay(() => movementSpeed.Dispose(), duration);

            await UniTask.Delay(Player.ServerConfig.AttackAnimationDuration);
        }

        // is successful after punch
        private bool IsAttackStillSuccessful(NetEscapeePlayer target)
        {
            if (!target.CanBeAttacked())
                return false;
            if (!target.Visibility.IsVisibleFor(Player))
                return false;

            var escapeePosition = target.NetTransform.Position;
            
            if (!JxMathf.IsInsideXZCircle(Player.NetTransform.Position, GetAttackSqrRadius() * 2, escapeePosition))
                return false;

            return Player.BaseSyncData.Action.Value == ActionType.Attack;
        }

        [Server]
        private void SyncAttackSpeed()
        {
            var successfulAttackDuration = (ushort)GetAttackFullDuration(isSuccessful: true).TotalMilliseconds;
            var attackDuration = (ushort)GetAttackFullDuration(isSuccessful: false).TotalMilliseconds;

            Player.SyncData.SharedConfig.ChangeFromServer(c => c.SetAttackSpeed(attackDuration, successfulAttackDuration));
        }
        
        [Server]
        private TimeSpan GetAttackFullDuration(bool isSuccessful)
        {
            var origin = Player.ServerConfig.GetFullAttackDuration(isSuccessful);
            var modified = origin * _modifiers.AttackSpeedMultiplier;
            return modified;
        }

        [Server]
        private float GetAttackSqrRadius()
        {
            var modified = Player.ServerConfig.AttackRadius * _modifiers.AttackRadiusMultiplier;
            return Mathf.Pow(modified, 2);
        }

        private bool CheckAttackFieldContainsPoint(Vector3 point)
        {
            return JxMathf.IsInsideXZCircle(Player.NetTransform.Position, TargetTooCloseRadius, point) ||
                   JxMathf.IsInsideArcXZ(Player.NetTransform, point, Player.ServerConfig.AttackAngleInDeg, GetAttackSqrRadius());
        }

        private static bool CheckPointBehindObstacle(Vector3 playerPosition, Vector3 posToCheck)
        {
            playerPosition += new Vector3(0, .5f, 0);
            posToCheck += new Vector3(0, .5f, 0);

            var targetDirection = posToCheck - playerPosition;

            return Physics.Raycast(playerPosition, targetDirection, targetDirection.magnitude, _collisionLayerMask);
        }

        private bool AttackAllowed() => SyncData.AttackAllowed.Value && Player.BaseSyncData.Action.Value.HasPermission(ActionPermissions.Attack);

        #region CLASSES

        private class Modifiers
        {
            public float AttackRadiusMultiplier { get; private set; } = 1f;
            public float AttackSpeedMultiplier { get; private set; } = 1f;
            
            public IDisposable AddRadiusModification(float multiplier)
            {
                AttackRadiusMultiplier += multiplier;
                return JxDisposableAction.Build().AppendCallback(() => AttackRadiusMultiplier -= multiplier);
            }
            
            public IDisposable AddSpeedModification(float multiplier)
            {
                AttackSpeedMultiplier += multiplier;
                return JxDisposableAction.Build().AppendCallback(() => AttackSpeedMultiplier -= multiplier);
            }
        }

        #endregion
        
        #endregion
    }
}