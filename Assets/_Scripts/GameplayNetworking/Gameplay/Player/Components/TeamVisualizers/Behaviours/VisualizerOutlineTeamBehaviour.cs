using OUTLINE;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Player.Components.TeamVisualizers.Behaviours
{
    public class VisualizerOutlineTeamBehaviour : VisualizerTeamBehaviour
    {
        private Outline _outline = null!;

        public void Initialize(Outline outline)
        {
            _outline = outline;
        }
        
        public override void SetColor(Color color)
        {
            _outline.OutlineColor = color;
        }
    }
}