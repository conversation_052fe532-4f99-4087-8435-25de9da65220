using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.ReleaseEscapeeFromCage;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.TeleportEscapeeInCage;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Control;
using Jx.Utils.Collections;
using Jx.Utils.Extensions;
using Jx.Utils.Objects;
using Mirror;
using UnityEngine;
using Zenject;

namespace GameplayNetworking.Gameplay.Player.Components.Debugging
{
    public class DebugNetPlayerComponent : GameplayPlayerNetComponent
    {
        private bool IsDebugUser() => Player.BaseSyncData.PublicInfo.Value.IsDebugUser;

        #region Client

        public override void OnStartClient()
        {
            if (!IsDebugUser())
                return;

            StartKeyboardListening();
        }

        [Conditional("UNITY_EDITOR")]
        private void StartKeyboardListening() => StartCoroutine(KeyboardListeningRoutine());

        private IEnumerator KeyboardListeningRoutine()
        {
            while (true)
            {
                if (IsKeyDown(KeyCode.C))
                {
                    if (Player.BaseSyncData.Action.Value == ActionType.InCage)
                        Cmd_ReleaseFromCage();
                    else
                        Cmd_TeleportToCage();
                }

                if (IsKeyDown(KeyCode.T))
                    Cmd_TeleportForward(Player.NetTransform.Position + Player.NetTransform.Forward * 15f);

                if (IsKeyDown(KeyCode.H))
                    Cmd_Heal();

                if (IsKeyDown(KeyCode.I))
                    Cmd_GiveInvisibility();
                
                if (IsKeyDown(KeyCode.W))
                    Cmd_ToggleMaxSpeed();
                
                if (IsKeyDown(KeyCode.R))
                    Cmd_ResetAbility();
                
                if (IsKeyDown(KeyCode.M))
                    Player.BaseSyncData.Cmd_SendEmoji(Player.BaseSyncData.EmojiSlotsReadOnly.RandomOrDefault());

                yield return null;
            }
            
            static bool IsKeyDown(KeyCode key) => Input.GetKeyDown(key);
        }

        #endregion

        #region Server

        private IGameplayServerContextGetter _serverContextGetter = null!;
        private IGameplayServerContext _context = null!;

        private IDisposable? _overrideMovementSpeed;

        [Inject]
        private void InjectServer(IGameplayServerContextGetter serverContextGetter)
        {
            _serverContextGetter = serverContextGetter;
        }

        protected override void OnMatchStarted() => _context = _serverContextGetter.Get(Player.MatchId);

        protected override void OnMatchStopped() { }

        [Command]
        public void Cmd_ToggleMaxSpeed()
        {
            if (_overrideMovementSpeed != null)
            {
                _overrideMovementSpeed?.Dispose();
                _overrideMovementSpeed = null;
                return;
            }
            
            _overrideMovementSpeed = Player.NetTransform.OverrideMovementSpeed(15f);
        }
        
        [Command]
        public void Cmd_ResetAbility() => Player.Ability.Controller.DebugResetCooldown();

        [Command]
        public void Cmd_GiveInvisibility()
        {
            if (!IsDebugUser())
                return;

            Player.Buff.Activate(BuffType.ChamieInvisibility, TimeSpan.FromSeconds(30f));
        }

        [Command]
        private void Cmd_TeleportForward(Vector3 position)
        {
            if (!IsDebugUser())
                return;

            Player.NetTransform.ServerTeleport(position, Quaternion.identity);
        }

        [Command]
        public void Cmd_TeleportToCage()
        {
            if (!IsDebugUser())
                return;

            if (Player is not NetEscapeePlayer)
                return;

            if (Player.BaseSyncData.Action.Value != ActionType.Idle)
                return;

            FindNearestInteractionOfType(typeof(TeleportEscapeeInCageInteraction))
               .DebugProcess(Player);
        }

        [Command]
        public void Cmd_ReleaseFromCage()
        {
            if (!IsDebugUser())
                return;

            if (Player is not NetEscapeePlayer)
                return;

            if (Player.BaseSyncData.Action.Value != ActionType.InCage)
                return;

            GetInteractionsOfType(typeof(ReleaseEscapeeFromCageInteraction))
               .Select(i => i.Cast<ReleaseEscapeeFromCageInteraction>())
               .FirstOrDefault(i => i.EscapeeInsideCage == Player)?
               .DebugProcess(Player);
        }

        [Command]
        public void Cmd_Heal()
        {
            if (!IsDebugUser())
                return;

            if (!(Player is NetEscapeePlayer escapeePlayer))
                return;

            escapeePlayer.Health.Increase();
        }

        [Server]
        private IInteraction FindNearestInteractionOfType(Type interactionType)
        {
            return GetInteractionsOfType(interactionType)
               .MinByDistance(i => Vector3.SqrMagnitude(i.TargetNetworkGameplayMatchObject.transform.position - Player.NetTransform.Position));
        }

        [Server]
        private IEnumerable<IInteraction> GetInteractionsOfType(Type interactionType)
        {
            return _context.Spawned
                           .Interactions
                           .All
                           .Where(i => i.GetType() == interactionType);
        }

        #endregion
    }
}