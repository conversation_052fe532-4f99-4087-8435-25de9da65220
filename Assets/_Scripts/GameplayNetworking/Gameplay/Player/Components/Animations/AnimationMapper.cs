using System;
using GameplayNetworking.Gameplay.Player.Data;
using Jx.Utils.Logging;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Player.Components.Animations
{
    public class AnimationMapper
    {
        private const string BaseLayer = "Base Layer";
        private const string AttackLayer = "Attack";
        
        public static AnimationMapper Instance { get; } = new ();

        public readonly int AnimationIndexHash = Animator.StringToHash("AnimationIndex");
        public readonly int MovementSpeedHash = Animator.StringToHash("MovementSpeed");
        public readonly int AttackSuccessfulHash = Animator.StringToHash("AttackSuccessful");
        public readonly int DirtyTriggerHash = Animator.StringToHash("Dirty");
        
        private readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(AnimationMapper));
        
        private static readonly int _abilityPreparationSpeedHash = Animator.StringToHash("AbilityPreparationSpeed");
        private static readonly int _abilityInProgressSpeedHash = Animator.StringToHash("AbilityInProgressSpeed");
        private static readonly int _attackSpeed = Animator.StringToHash("AttackSpeed");
        private static readonly int _successfulAttackSpeed = Animator.StringToHash("SuccessfulAttackSpeed");

        private AnimationMapper() { }

        public int GetAnimationIndex(CharacterAnimationType animationType) => (int)animationType;

        public int GetAnimationSpeedHash(CharacterAnimationSpeedType animationSpeedType)
        {
            switch (animationSpeedType)
            {
                case CharacterAnimationSpeedType.MovementSpeed:
                    return MovementSpeedHash;
                
                case CharacterAnimationSpeedType.AbilityPreparationSpeed:
                    return _abilityPreparationSpeedHash;

                case CharacterAnimationSpeedType.AbilityInProgressSpeed:
                    return _abilityInProgressSpeedHash;
                
                case CharacterAnimationSpeedType.SuccessfulAttack:
                    return _successfulAttackSpeed;
                
                case CharacterAnimationSpeedType.Attack:
                    return _attackSpeed;
            }

            _logger.LogError($"Hash not found `{animationSpeedType}`");
            return 0;
        }
        
        public string GetStateName(CharacterAnimationType animationType)
        {
            return animationType.ToString();
        }

        public string GetLayer(CharacterAnimationType animationType)
        {
            return animationType switch
            {
                CharacterAnimationType.Attack or CharacterAnimationType.SuccessfulAttack => AttackLayer,
                _ => BaseLayer
            };
        }

        public CharacterAnimationType GetAnimationType(ActionType actionType)
        {
            CharacterAnimationType? type = actionType switch
            {
                ActionType.Idle => CharacterAnimationType.Idle,
                ActionType.Disarmed => CharacterAnimationType.Idle,
                ActionType.AbilityDisabled => CharacterAnimationType.Idle,
                ActionType.Attack => CharacterAnimationType.Attack,
                ActionType.Interaction => CharacterAnimationType.Interaction,
                ActionType.Lose => CharacterAnimationType.Death,
                ActionType.Win => CharacterAnimationType.Idle,
                ActionType.InCage => CharacterAnimationType.Caged,
                ActionType.StunnedBeZeroHealth => CharacterAnimationType.Stunned,
                ActionType.Stunned => CharacterAnimationType.Stunned,
                ActionType.StunnedByTrap => CharacterAnimationType.Stunned,
                ActionType.MicroStunned => CharacterAnimationType.MicroStunned,
                ActionType.DamageReceiving => CharacterAnimationType.TakeDamage,
                ActionType.AbilityPreparation => CharacterAnimationType.AbilityPreparation,
                ActionType.AbilityInProgress => CharacterAnimationType.AbilityInProgress,
                ActionType.Unharmed => CharacterAnimationType.Idle,
                ActionType.None => CharacterAnimationType.Idle,
                ActionType.ItemConsumption => CharacterAnimationType.Idle,
                _ => null
            };

            if (!type.HasValue)
            {
                _logger.LogError($"{nameof(CharacterAnimationType)} for {nameof(ActionType)} not found `{actionType}`");
                return CharacterAnimationType.Idle;
            }

            return type.Value;
        }
    }
}