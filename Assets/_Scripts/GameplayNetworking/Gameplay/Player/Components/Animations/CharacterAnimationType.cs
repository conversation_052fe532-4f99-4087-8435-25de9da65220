namespace GameplayNetworking.Gameplay.Player.Components.Animations
{
    // do not change names & indexes!
    public enum CharacterAnimationType : ushort
    {
        None = 0,
        Idle = 1,
        Attack = 2,
        Interaction = 3,
        Caged = 5,
        Stunned = 6,
        TakeDamage = 7,
        AbilityPreparation = 8,
        AbilityInProgress = 9,
        SuccessfulAttack = 10,
        CalibrationFailed = 11,
        MicroStunned = 12,
        <PERSON> = 16,
        <PERSON> = 17,
        <PERSON> = 19,
    }
}