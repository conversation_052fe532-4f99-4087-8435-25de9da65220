using System.Collections.Generic;
using System.Linq;
using Jx.Utils.Extensions;
using UnityEngine.Scripting;

namespace GameplayNetworking.Gameplay.Player.Components.Statistics.StorageFactory
{
    [Preserve]
    public class GameplayStatisticsStorageFactory : IGameplayStatisticsStorageFactory
    {
        private readonly ISet<GameplayStatisticsType> _clientOnlyStats = new HashSet<GameplayStatisticsType>()
        {
            GameplayStatisticsType.CampfireLit,
            GameplayStatisticsType.ReleasedFromCage,
            GameplayStatisticsType.PerfectMiniGame,
            GameplayStatisticsType.StunCatcherByObstacle,

            GameplayStatisticsType.BrokeCampfire,
            GameplayStatisticsType.TeleportInCage,
            GameplayStatisticsType.HitEscapeeByAttack,
            GameplayStatisticsType.BrokeObstacle,
        };

        private readonly IReadOnlyList<GameplayStatisticsType> _allTypes;

        [Preserve]
        public GameplayStatisticsStorageFactory()
        {
            _allTypes = JxEnumExtensions.GetEnumValues<GameplayStatisticsType>()
                                        .Where(e => e != GameplayStatisticsType.None)
                                        .ToArray();
        }

        public IReadOnlyDictionary<GameplayStatisticsType, int> CreateForServer() => _allTypes.ToDictionary(k => k, _ => 0);

        public IReadOnlyDictionary<GameplayStatisticsType, int> CreateForClient() =>
            _allTypes.Where(s => _clientOnlyStats.Contains(s))
                     .ToDictionary(k => k, _ => 0);
    }
}