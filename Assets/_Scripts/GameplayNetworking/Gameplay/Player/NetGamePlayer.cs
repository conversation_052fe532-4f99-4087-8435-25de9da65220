using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using Configs.Perks;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.Character;
using Cysharp.Threading.Tasks;
using GameplayComponents.Bots;
using GameplayComponents.Bushes;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Visibility;
using GameplayNetworking.Gameplay.Player.Components.Abilities;
using GameplayNetworking.Gameplay.Player.Components.Collision;
using GameplayNetworking.Gameplay.Player.Components.Debugging;
using GameplayNetworking.Gameplay.Player.Components.Immunity;
using GameplayNetworking.Gameplay.Inventory;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Components.Perks;
using GameplayNetworking.Gameplay.Player.Components.Phrase;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Components.Synchronizations;
using GameplayNetworking.Gameplay.Player.Components.TeamVisualizers;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Systems;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Transform;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Emoji;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.GameplayResult;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Mimicry;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.MiniGame;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Perk;
using GameplayNetworking.Manager.Network.Matchmaking;
using GameplayNetworking.Gameplay.SceneObjects.Static.Factory;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Control;
using GameplayNetworking.Share.Character;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Observable;
using Mirror;
using MonsterLand.Matchmaking;
using Savings;
using SceneLogics.GameplayScene.Systems.Client.Animations;
using SceneLogics.GameplayScene.Systems.Client.FlyingTexts;
using UI.Screens.GameplayScene.MatchFinish;
using UnityEngine;
using Zenject;

namespace GameplayNetworking.Gameplay.Player
{
    [RequireComponent(typeof(ImmunitySynchronizerNetPlayerComponent))]
    [RequireComponent(typeof(ScoreNetPlayerComponent))]
    [RequireComponent(typeof(CollisionPlayerComponent))]
    [RequireComponent(typeof(PhraseNetPlayerComponent))]
    [RequireComponent(typeof(DebugNetPlayerComponent))]
    [RequireComponent(typeof(VisualizerTeamComponent))]
    [RequireComponent(typeof(PerkNetPlayerComponent))]
    [RequireComponent(typeof(SynchronizationNetPlayerComponent))]
    [RequireComponent(typeof(BushFieldOfViewHandler))]
    public abstract class NetGamePlayer : NetworkGameplayMatchObject,
                                          ILocalGameplayPlayer
    {
        public abstract JxMatchmakingTeamKind Team { get; }
        public NetworkIdentity NetIdentity => netIdentity;

        public ImmunitySynchronizerNetPlayerComponent Immunity { get; private set; } = null!;
        public ScoreNetPlayerComponent Score { get; private set; } = null!;
        public CollisionPlayerComponent Collision { get; private set; } = null!;
        public DebugNetPlayerComponent Debug { get; private set; } = null!;
        public VisualizerTeamComponent TeamVisualizer { get; private set; } = null!;
        public SynchronizationNetPlayerComponent Synchronization { get; private set; } = null!;
        public BushFieldOfViewHandler BushFieldOfView { get; private set; } = null!;
        public abstract IBotBehaviour? BaseBotBehaviour { get; }

        public abstract BaseGameplayStatisticsNetPlayerComponent BaseStatistics { get; }
        public abstract NetGameplayPlayerSyncData BaseSyncData { get; }
        public InventoryPlayerSystem Inventory { get; private set; } = null!;

        private Transform? _transform;
        
        public NetTransformPlayerSystem NetTransform { get; private set; } = null!;
        public GameplayResultPlayerSystem State { get; private set; } = null!;
        public NetPlayerActionSystem Action { get; private set; } = null!;
        public ParentSetterPlayerSystem Parent { get; private set; } = null!;
        public BuffPlayerSystem Buff { get; private set; } = null!;
        public VisibilityPlayerSystem Visibility { get; private set; } = null!;
        public SharedConfigPlayerSystem SharedConfig { get; private set; } = null!;
        public MiniGamePlayerSystem MiniGame { get; private set; } = null!;
        public InputModificationPlayerSystem InputModification { get; private set; } = null!;
        public PerkPlayerSystem Perk { get; private set; } = null!;
        public NetAbilityPlayerSystem Ability { get; private set; } = null!;
        public NetIndicationPlayerSystem Indicator { get; private set; } = null!;
        public NetGameplayRewardPlayerSystem Reward { get; private set; } = null!;
        public NetInteractionPlayerSystem Interaction { get; private set; } = null!;
        public NetShieldPlayerSystem Shield { get; private set; } = null!;
        public NetCharacterViewPlayerSystem CharacterView { get; private set; } = null!;
        public NetMimicryPlayerSystem Mimicry { get; private set; } = null!;
        public NetEmojiPlayerSystem Emoji { get; private set; } = null!;
        public NetCharacterStatsPlayerSystem Stats { get; private set; } = null!;

        public virtual IEnumerable<IGameplayPlayerSystem> EnumerateSystems()
        {
            yield return NetTransform;
            yield return CharacterView;
            yield return State;
            yield return Action;
            yield return Interaction;
            yield return SharedConfig;
            yield return Parent;
            yield return Buff;
            yield return Visibility;
            yield return Inventory;
            yield return MiniGame;
            yield return InputModification;
            yield return Ability;
            yield return Perk;
            yield return Indicator;
            yield return Reward;
            yield return Shield;
            yield return Mimicry;
            yield return Emoji;
            yield return Stats;
        }

        protected abstract GameplayPlayerSystemContext CreateSystemContext();

        protected virtual void Awake()
        {
            Action = new NetPlayerActionSystem();
            Interaction = new NetInteractionPlayerSystem();
            NetTransform = new NetTransformPlayerSystem();
            CharacterView = new NetCharacterViewPlayerSystem();
            State = new GameplayResultPlayerSystem();
            SharedConfig = new SharedConfigPlayerSystem();
            Parent = new ParentSetterPlayerSystem();
            Buff = new BuffPlayerSystem();
            Visibility = new VisibilityPlayerSystem();
            Inventory = new InventoryPlayerSystem();
            MiniGame = new MiniGamePlayerSystem();
            InputModification = new InputModificationPlayerSystem();
            Ability = new NetAbilityPlayerSystem(_abilitySceneObjectFactory, _inventoryItemProvider);
            Perk = new PerkPlayerSystem(_perkIntegration);
            Indicator = new NetIndicationPlayerSystem();
            Reward = new NetGameplayRewardPlayerSystem();
            Shield = new NetShieldPlayerSystem();
            Mimicry = new NetMimicryPlayerSystem();
            Emoji = new NetEmojiPlayerSystem();
            Stats = new NetCharacterStatsPlayerSystem();

            Immunity = this.GetComponentOrThrow<ImmunitySynchronizerNetPlayerComponent>();
            Score = this.GetComponentOrThrow<ScoreNetPlayerComponent>();
            Collision = this.GetComponentOrThrow<CollisionPlayerComponent>();
            Debug = this.GetComponentOrThrow<DebugNetPlayerComponent>();
            TeamVisualizer = this.GetComponentOrThrow<VisualizerTeamComponent>();
            Synchronization = this.GetComponentOrThrow<SynchronizationNetPlayerComponent>();
            BushFieldOfView = this.GetComponentOrThrow<BushFieldOfViewHandler>();
        }

        private bool _systemsInitialized;

        protected void InitializeSystems()
        {
            if (_systemsInitialized)
                return;

            _systemsInitialized = true;

            var context = CreateSystemContext();

            foreach (var system in EnumerateSystems())
                system.Initialize(context);

            SetupGameObjectName();
        }

        #region Client

        [SerializeField]
        private GameplayCharacterView _characterView = null!;

        public GameplayCharacterView ClientGameplayCharacterView => _characterView;

        NetGameplayPlayerSyncData ILocalGameplayPlayer.SyncData => BaseSyncData;
        
        private ILocalGameplayContextSetter _localGameplayContextSetter = null!;
        private IFlyingTextClientManager _flyingTextClientManager = null!;
        protected ILocalSave LocalSave { get; private set; } = null!;
        
        public abstract PlayerClientAnimationHandler BaseClientAnimation { get; }
        public ILocalGameplayContext ClientGameplayContext { get; private set; } = null!;

#if !HEADLESS
        [Inject]
        private void InjectClient(
            ILocalGameplayContextSetter localGameplayContextSetter,
            ILocalSave localSave,
            ILocalGameplayContext clientGameplayContext,
            IFlyingTextClientManager flyingTextClientManager
        )
        {
            _localGameplayContextSetter = localGameplayContextSetter;
            LocalSave = localSave;
            ClientGameplayContext = clientGameplayContext;
        }

        [Inject]
        private void InjectClientOnly(GameplayPlayerClientOnlyContext clientOnlyContext)
        {
            ClientOnlyContext = clientOnlyContext;
        }
#endif

        protected GameplayPlayerClientOnlyContext ClientOnlyContext { get; private set; }

        public override void OnStartLocalPlayer()
        {
            base.OnStartLocalPlayer();

            if (NetworkServer.active && this.IsBot())
                return;

            _localGameplayContextSetter.SetLocalPlayer(this);
        }

        public override void OnStartClient()
        {
            _localGameplayContextSetter.AddSpawnedPlayer(this);

            InitializeSystems();

            foreach (var system in EnumerateSystems())
                system.ClientStart();
        }

        public override void OnStopClient()
        {
            foreach (var system in EnumerateSystems())
                system.ClientStop();
        }

#if UNITY_EDITOR
        protected override void OnValidate()
        {
            base.OnValidate();
            _characterView ??= GetComponent<GameplayCharacterView>();
        }
#endif

        #endregion

        #region Server

        protected DiContainer DiScope { get; private set; } = null!;
        protected GameplayPlayerSharedContext SharedContext { get; private set; } = null!;
        public GameplayServerOnlyContext ServerContext { get; private set; } = null!;
        public IJxChangeableObject<IServerGameplayConfig> GameplayConfig { get; private set; } = null!;
        public MatchClientInfo Client { get; private set; } = null!;
        
        private IGameplayServerContextGetter _serverContextGetter = null!;
        private IPerkIntegration _perkIntegration = null!;
        private IGamePlayerStatisticSender _statisticSender = null!;
        private IDynamicSceneObjectFactory _dynamicSceneObjectFactory = null!;
        private IAbilitySceneObjectFactory _abilitySceneObjectFactory = null!;
        private IInventoryItemProvider _inventoryItemProvider = null!;

        [Inject]
        private void InjectServerOnly(
            DiContainer diScope,
            GameplayPlayerSharedContext sharedContext,
            IGameplayServerContextGetter serverContextGetter,
            IJxChangeableObject<IServerGameplayConfig> gameplayConfig,
            IPerkIntegration perkIntegration,
            IGamePlayerStatisticSender statisticSender,
            IDynamicSceneObjectFactory dynamicSceneObjectFactory,
            IAbilitySceneObjectFactory abilitySceneObjectFactory,
            IInventoryItemProvider inventoryItemProvider
        )
        {
            DiScope = diScope;
            SharedContext = sharedContext;
            _serverContextGetter = serverContextGetter;
            GameplayConfig = gameplayConfig;
            _perkIntegration = perkIntegration;
            _statisticSender = statisticSender;
            _dynamicSceneObjectFactory = dynamicSceneObjectFactory;
            _abilitySceneObjectFactory = abilitySceneObjectFactory;
            _inventoryItemProvider = inventoryItemProvider;
        }

        private IDisposable? _reconnectSubscription;
        private IDisposable? _disconnectSubscription;
        
        public void Setup(MatchClientInfo client, IJxObservable<MatchClientInfo?> onReconnect, IJxObservable<MatchClientInfo?> onDisconnect)
        {
            Client = client;
            
            if (client.Connection != null)
                NetworkServer.AddPlayerForConnection(client.Connection, gameObject);
            else
                NetworkServer.Spawn(gameObject);

            _reconnectSubscription = onReconnect.Subscribe(
                c =>
                {
                    if (c == null)
                        return;
                    var connection = c.Connection;
                    if (connection != null)
                        NetworkServer.ReplacePlayerForConnection(connection, gameObject);

                    Client = c;

                    TriggerReconnection();
                }
            );
            
            _disconnectSubscription = onDisconnect.Subscribe(
                c =>
                {
                    TriggerReconnection();
                }
            );

            return;

            void TriggerReconnection()
            {
                foreach (var system in EnumerateSystems())
                    system.ServerReconnected();
            }
        }

        public override void OnStartServer()
        {
            ServerContext = new GameplayServerOnlyContext(
                _serverContextGetter.Get(MatchContext.Id), 
                _dynamicSceneObjectFactory
            );

            base.OnStartServer();
        }

        public void MarkFinished(bool isWinner)
        {
            BaseSyncData.PlayTimeSeconds.SetFromServer((ushort)(NetworkTime.time - ServerContext.GameplayContext.MatchSyncData.Timing.Value.StartTime));
            Action.SetNew(isWinner ? ActionType.Win : ActionType.Lose);
            BaseSyncData.State.SetFromServer(isWinner ? GameplayPlayerState.IsWinner : GameplayPlayerState.IsLoser);

            _statisticSender.SendAsync(ServerContext.GameplayContext, this).Forget();
        }

        #endregion

        public abstract BaseCharacterConfigPresentation BaseServerConfig { get; }

        protected override void OnMatchStarted()
        {
            BaseSyncData.MatchResult.SetFromServer(MatchResultType.InGame);
        }

        protected override void OnMatchStopped()
        {
            _reconnectSubscription?.Dispose();
            _disconnectSubscription?.Dispose();
        }

        [Conditional("UNITY_EDITOR")]
        private void SetupGameObjectName()
        {
            var publicInfo = BaseSyncData.PublicInfo.Value;
            var sb = new StringBuilder();

            sb.Append(Team.ToString().ToLowerInvariant());
            sb.Append("_");
            sb.Append(publicInfo.Nickname);
            sb.Append("_");
            sb.Append(CharacterTypeHelper.GetName(publicInfo.CharacterIdentifier.Index));
            sb.Append("_");
            sb.Append(publicInfo.Mmr);

            gameObject.name = sb.ToString();
        }
    }
}