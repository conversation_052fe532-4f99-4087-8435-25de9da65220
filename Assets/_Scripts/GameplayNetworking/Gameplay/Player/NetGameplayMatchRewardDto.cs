using System;
using System.Text;
using LoM.Messaging.Model.GameplayStatistics;

namespace GameplayNetworking.Gameplay.Player
{
    // custom reader writer
    public readonly struct NetGameplayMatchRewardDto : IEquatable<NetGameplayMatchRewardDto>
    {
        public static NetGameplayMatchRewardDto None { get; } = new NetGameplayMatchRewardDto(GameplayMatchRewardStatus.None, 0, 0, 0, 0, null, 0, 0);
        public static NetGameplayMatchRewardDto Empty { get; } = new NetGameplayMatchRewardDto(GameplayMatchRewardStatus.IsEmpty, 0, 0, 0, 0, null, 0, 0);

        // ReSharper disable once MemberCanBePrivate.Global
        public NetGameplayMatchRewardDto(
            GameplayMatchRewardStatus status,
            ushort gold,
            ushort gem,
            short profileTrophies,
            short characterTrophies,
            string? chestId,
            short mvpTrophy,
            short seasonPassXp
        )
        {
            Status = status;
            Gold = gold;
            Gem = gem;
            ProfileTrophies = profileTrophies;
            CharacterTrophies = characterTrophies;
            ChestId = chestId;
            MvpTrophy = mvpTrophy;
            SeasonPassXp = seasonPassXp;
        }

        public readonly GameplayMatchRewardStatus Status;
        public readonly ushort Gold;
        public readonly ushort Gem;
        public readonly short ProfileTrophies;
        public readonly short CharacterTrophies;
        public readonly string? ChestId;
        public readonly short MvpTrophy;
        public readonly short SeasonPassXp;

        public static NetGameplayMatchRewardDto FromMatchReward(MatchRewardClientIntegration? reward)
        {
            if (reward == null)
                return Empty;

            return new NetGameplayMatchRewardDto(
                status: GameplayMatchRewardStatus.IsSet,
                (ushort)reward.Gold,
                (ushort)reward.Gem,
                (short)reward.ProfileTrophies,
                (short)reward.CharacterTrophies,
                reward.ChestId,
                (short)reward.MvpTrophies,
                (short)reward.SeasonPassXp
            );
        }

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine("Reward");
            sb.AppendLine($"Status {Status}");
            sb.AppendLine($"Gold {Gold}");
            sb.AppendLine($"Gem {Gem}");
            sb.AppendLine($"ProfileTrophies {ProfileTrophies}");
            sb.AppendLine($"ChestId {ChestId}");
            sb.AppendLine($"MvpTrophy {MvpTrophy}");
            
            return sb.ToString();
        }

        public bool Equals(NetGameplayMatchRewardDto other) => Status == other.Status && Gold == other.Gold && Gem == other.Gem && ProfileTrophies == other.ProfileTrophies && CharacterTrophies == other.CharacterTrophies && ChestId == other.ChestId && MvpTrophy == other.MvpTrophy;
        public override bool Equals(object? obj) => obj is NetGameplayMatchRewardDto other && Equals(other);
        public override int GetHashCode() => HashCode.Combine((int)Status, Gold, Gem, ProfileTrophies, CharacterTrophies, ChestId, MvpTrophy);
        public static bool operator ==(NetGameplayMatchRewardDto left, NetGameplayMatchRewardDto right) => left.Equals(right);
        public static bool operator !=(NetGameplayMatchRewardDto left, NetGameplayMatchRewardDto right) => !left.Equals(right);
    }
}