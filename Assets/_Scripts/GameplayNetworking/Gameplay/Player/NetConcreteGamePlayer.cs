using Configs.ServerGameplay.Presentation.Character;
using Configs.ServerGameplay.Presentation.DifficultyGameplayOverrides;
using Core.Extensions.DiExtensions;
using GameplayComponents.Bots;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots;
using Mirror;
using UnityEngine.Scripting;
using Zenject;

namespace GameplayNetworking.Gameplay.Player
{
    public abstract class NetConcreteGamePlayer
        <TPlayer, TConfig, TBotBehaviour> : NetGamePlayer
        where TPlayer : NetConcreteGamePlayer<TPlayer, TConfig, TBotBehaviour>
        where TConfig : BaseCharacterConfigPresentation
        where TBotBehaviour : IBotBehaviour
    {
        public TBotBehaviour? BotBehaviour { get; private set; }
        public override IBotBehaviour? BaseBotBehaviour => BotBehaviour;

        [Server]
        public void PopulateBotBehaviour(TBotBehaviour botBehaviour)
        {
            BotBehaviour = botBehaviour;
            BaseSyncData.SharedConfig.ChangeFromServer(s => s.SetBotDifficulty(botBehaviour.FindDifficultyName()));
        }

        #region Config

        public override BaseCharacterConfigPresentation BaseServerConfig => ServerConfig;
        public TConfig ServerConfig { get; private set; } = null!;
        public DifficultyGameplayOverrideItemPresentation? GameplayParametersOverride { get; private set; }

        protected override void OnMatchStarted()
        {
            base.OnMatchStarted();

            ServerConfig = GameplayConfig.Value.GetCharacterConfigPresentation<TConfig>(BaseSyncData.PublicInfo.Value.CharacterIdentifier.Index);

            GameplayParametersOverride = GameplayConfig.Value.FindDifficultyGameplayOverride()?.FindOverride(PlayerMmrProvider.Get(LocalSave, this));

            OnConfigSet();

            InitializeSystems();

            foreach (var system in EnumerateSystems())
                system.ServerStart();
        }

        protected override void OnMatchStopped()
        {
            base.OnMatchStopped();

            foreach (var system in EnumerateSystems())
                system.ServerStop();
        }

        [Server]
        protected virtual void OnConfigSet() { }

        #endregion

        #region Factory/Pool

        [Inject]
        protected virtual void OnCreated(IMatchContext matchContext, ISpatialParameter spatialParameter)
        {
            spatialParameter.ApplyTo(transform, ignoreScale: true);
            
            if (NetworkServer.active)
                SetMatchContext(matchContext);

            gameObject.SetActive(true);
        }

        [Preserve]
        public class Factory : PlaceholderFactory<IMatchContext, ISpatialParameter, TPlayer>
        {
            public override TPlayer Create(IMatchContext param1, ISpatialParameter param2)
            {
                var player = base.Create(param1, param2);
                player.OnCreated(param1, param2);

                return player;
            }
        }

        #endregion
    }
}