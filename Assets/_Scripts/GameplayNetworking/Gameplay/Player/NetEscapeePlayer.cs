using System.Collections.Generic;
using Configs.ServerGameplay.Presentation.Character;
using Configs.ServerGameplay.Presentation.Character.Escapee;
using GameplayComponents.Bots;
using GameplayNetworking.Gameplay.Player.Components.Escapee;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Systems;
using GameplayNetworking.Gameplay.Player.Systems.Escapee;
using GameplayNetworking.Gameplay.Player.Systems.Escapee.CageRelease;
using GameplayNetworking.Gameplay.Player.Systems.Health;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Mirror;
using MonsterLand.Matchmaking;
using SceneLogics.GameplayScene.Systems.Client.Animations;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Player
{
    // todo: refactor
    [RequireComponent(typeof(EscapeeGameplayStatisticsNetComponent))]
    [RequireComponent(typeof(NetEscapeePlayerSyncData))]
    public class NetEscapeePlayer : NetConcreteGamePlayer<NetEscapeePlayer, EscapeeCharacterConfigPresentation, BotEscapeeBehaviour>
    {
        public override JxMatchmakingTeamKind Team => JxMatchmakingTeamKind.Escapee;
        
        public EscapeeGameplayStatisticsNetComponent Statistics { get; private set; } = null!;
        public NetEscapeePlayerSyncData SyncData { get; private set; } = null!;
        public override BaseGameplayStatisticsNetPlayerComponent BaseStatistics => Statistics;
        public override NetGameplayPlayerSyncData BaseSyncData => SyncData;
        public EscapeeClientAnimationHandler ClientAnimation { [Client] get; [Client] set; } = null!;

        public HealthNetEscapeeSystem Health { get; private set; } = null!;
        public NetMedkitEscapeePlayerSystem MedKit { get; private set; } = null!;
        public EliminationNetEscapeeSystem Elimination { get; private set; } = null!;
        public CageReleaseNetEscapeeSystem CageRelease { get; private set; } = null!;
        private InjuranceNetEscapeeSystem _injurance = null!;
        private ShockNetEscapeeSystem _shock = null!;
        private StunByZeroHealthNetEscapeeSystem _stunByZeroHealth = null!;

        public override IEnumerable<IGameplayPlayerSystem> EnumerateSystems()
        {
            foreach (var system in base.EnumerateSystems())
                yield return system;

            yield return Health;
            yield return MedKit;
            yield return Elimination;
            yield return _injurance;
            yield return _shock;
            yield return _stunByZeroHealth;
            yield return CageRelease;
        }

        protected override GameplayPlayerSystemContext CreateSystemContext() => new EscapeePlayerSystemContext(
            this,
            ServerConfig,
            GameplayConfig.Value,
            ServerContext,
            ClientOnlyContext,
            SharedContext
        );

        protected override void Awake()
        {
            base.Awake();

            Health = new HealthNetEscapeeSystem();
            MedKit = new NetMedkitEscapeePlayerSystem();
            Elimination = new EliminationNetEscapeeSystem();
            _injurance = new InjuranceNetEscapeeSystem();
            _shock = new ShockNetEscapeeSystem();
            _stunByZeroHealth = new StunByZeroHealthNetEscapeeSystem();
            CageRelease = new CageReleaseNetEscapeeSystem();
            
            Statistics = this.GetComponentOrThrow<EscapeeGameplayStatisticsNetComponent>();
            SyncData = this.GetComponentOrThrow<NetEscapeePlayerSyncData>();
        }

        #region CLIENT

        public override PlayerClientAnimationHandler BaseClientAnimation => ClientAnimation;

        #endregion
        
        
        #region Shock
        
        // used for run animation while injured + for injured particle effects
        
        // client only
        private readonly IJxChangeableObject<bool, bool> _isShockingOnClient = new JxChangeableObject<bool, bool>();
        public IJxChangeableObject<bool> IsShockingOnClient => _isShockingOnClient;

        [Client]
        public void SetIsShockingOnClient(bool value) => _isShockingOnClient.SetIfDistinct(value);
        
        #endregion

        #region Server

        protected override void OnMatchStopped()
        {
            base.OnMatchStopped();
            
            Statistics.Flush();
        }

        [Server]
        public bool CanBeAttacked()
        {
            if (!SyncData.Health.Value.Has())
                return false;

            if (Immunity.IsImmune)
                return false;

            if (!BaseSyncData.Action.Value.HasPermission(ActionPermissions.CanBeAttacked))
                return false;

            return true;
        }

        #endregion
    }
}