using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.SceneObjects.Static.Obstacle;
using MonsterLand.Matchmaking;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.PutObstacle
{
    public class PutObstacleInteraction : BaseInteraction<NetObstacle>
    {
        public override InteractionType InteractionType => InteractionType.PutObstacle;
        protected override bool IsInteractable => Target.State.Value == ObstacleState.Spawned;

        public override JxMatchmakingTeamKind? Team => JxMatchmakingTeamKind.Escapee;
        public override int Priority => base.Priority + 10;

        protected override void OnMatchStarted()
        { }
        
        protected override void OnMatchStopped()
        { }

        protected override bool IsInteractableForPlayer(NetGamePlayer player) => true;

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);

            Target.SetState(ObstacleState.Put);
            TryStunCatchers(player);
            
            player.BaseStatistics.Increment(GameplayStatisticsType.PutObstacle);
        }
        
        private void TryStunCatchers(NetGamePlayer initiator)
        {
            var config = GameplayConfig.Value.GetObstacleConfig();

            foreach (var catcherPlayer in GameplayContext.Spawned.Catchers)
            {
                if (Vector3.SqrMagnitude(catcherPlayer.transform.position - transform.position) > config.SqrStunRadius)
                    continue;

                catcherPlayer.Action.SetNewTemporary(ActionType.Stunned, config.CatcherStunDuration);

                initiator.Score.AddInstant(ScoreSource.PutObstacleStunnedCatcher);
                initiator.BaseStatistics.Increment(GameplayStatisticsType.StunCatcherByObstacle);
            }
        }
    }
}