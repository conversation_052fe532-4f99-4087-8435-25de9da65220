using App.Inventory.Item;
using GameplayNetworking.Gameplay.Components.Server.Client.Interaction;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Inventory;
using GameplayNetworking.Gameplay.SceneObjects.Static.InventoryItems;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using Mirror;
using MonsterLand.Matchmaking;
using Zenject;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.OpenChest
{
    public class OpenChestInteraction : BaseInteraction<NetInventoryChest>
    {
        #region CLIENT

        private OpenChestInteractionClientController _castedClientController = null!;

        public override void OnStartClient()
        {
            base.OnStartClient();
            _castedClientController = ClientController!.Cast<OpenChestInteractionClientController>();
        }

        [ClientRpc]
        private void ClientRpc_OnCompletedForPlayer(NetGamePlayer player, InventoryItemType type) 
            => _castedClientController.OnCompletedForPlayer(player, type);

        #endregion
        
        private IInventoryItemProvider _inventoryItemProvider = null!;

        [Inject]
        private void ServerInject(IInventoryItemProvider inventoryItemProvider)
        {
            _inventoryItemProvider = inventoryItemProvider;
        }
        
        public override JxMatchmakingTeamKind? Team => null;
        public override InteractionType InteractionType => InteractionType.OpenChest;
        protected override bool IsInteractable => !Target.IsOpened.Value;
        
        protected override void OnMatchStarted()
        {
        }

        protected override void OnMatchStopped()
        {
        }

        protected override bool IsInteractableForPlayer(NetGamePlayer player) => !player.Inventory.IsFull;

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);
            
            if (!player.Inventory.IsFull && TryGetItem(player, out var item))
            {
                player.Inventory.Add(item.ItemType);
                Target.Open();
                ClientRpc_OnCompletedForPlayer(player, item.ItemType);
            }
        }

        private bool TryGetItem(NetGamePlayer player, out InventoryItemSlot slot)
        {
            slot = _inventoryItemProvider.Get(player);
            if (!slot.IsEmpty)
            {
                return true;
            }

            Logger.LogError("Attempt to give empty item");
            return false;
        }
    }
}