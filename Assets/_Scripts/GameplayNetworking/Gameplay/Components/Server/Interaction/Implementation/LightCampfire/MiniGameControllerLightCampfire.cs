using System;
using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Utils.Collections;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.LightCampfire
{
    internal class MiniGameControllerLightCampfire
    {
        private readonly IDictionary<uint, int> _lastStageIndexByPlayerNetId = new Dictionary<uint, int>(4);
        private readonly IDictionary<uint, PlayerMiniGameHandlerLightCampfire> _miniGameHandlerByPlayerNetId = new Dictionary<uint, PlayerMiniGameHandlerLightCampfire>(4);

        private readonly LightCampfireInteraction _interaction;
        private readonly Action<NetEscapeePlayer, GenericMiniGameResult> _onMiniGameFinish;

        public MiniGameControllerLightCampfire(
            LightCampfireInteraction interaction,
            Action<NetEscapeePlayer, GenericMiniGameResult> onMiniGameFinish)
        {
            _interaction = interaction;
            _onMiniGameFinish = onMiniGameFinish;
        }
            
        public void Request(NetEscapeePlayer player)
        {
            if (_lastStageIndexByPlayerNetId.TryGetValue(player.netId, out var lastStage) &&
                lastStage >= _interaction.CurrentStage)
            {
                return;
            }

            var handler = _miniGameHandlerByPlayerNetId.GetOrAdd(player.netId, () => new (player, _onMiniGameFinish));
            handler.AddPendingMiniGame();
            _lastStageIndexByPlayerNetId[player.netId] = _interaction.CurrentStage;
        }

        public void Stop(NetEscapeePlayer player)
        {
            if (!_miniGameHandlerByPlayerNetId.TryGetValue(player.netId, out var handler)) 
                return;
            
            handler.Dispose();
            _miniGameHandlerByPlayerNetId.Remove(player.netId);
        }

        public void StopAll()
        {
            foreach (var handler in _miniGameHandlerByPlayerNetId.Values)
                handler.Dispose();
            
            _miniGameHandlerByPlayerNetId.Clear();
        }
    }
}