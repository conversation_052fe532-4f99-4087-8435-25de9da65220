using System;
using Core.Helpers;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Systems.Escapee.CageRelease;
using GameplayNetworking.Gameplay.SceneObjects.Static.Cage;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using Jx.Utils.Observable;
using MonsterLand.Matchmaking;
using SceneLogics.GameplayScene.Components.Indicators;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.ReleaseEscapeeFromCage
{
    // todo refactor
    public class ReleaseEscapeeFromCageInteraction : BaseInteraction<NetCage>
    {
        private readonly JxObservable<ReleaseEscapeeInfo> _onReleasedObservable = new JxObservable<ReleaseEscapeeInfo>();

        private NetEscapeePlayer? _prisoner;
        private IDisposable? _eliminationCancellation;

        public override JxMatchmakingTeamKind? Team => JxMatchmakingTeamKind.Escapee;
        public override InteractionType InteractionType => InteractionType.ReleaseEscapeeFromCage;
        protected override bool IsInteractable => Target.Status.Value == CageStatus.IsClosed;
        
        public NetEscapeePlayer? EscapeeInsideCage
        {
            get
            {
                _prisoner ??= Target.Prisoner;
                return _prisoner;
            }
        }

        protected override void OnMatchStarted()
        {
            if (_prisoner != null)
                Logger.LogError("Escapee inside cage must be null");
        }

        protected override void OnMatchStopped()
        {
            _prisoner = null;
            _eliminationCancellation?.Dispose();
        }

        protected override void OnInteractionStarted(NetGamePlayer player)
        {
            base.OnInteractionStarted(player);

            if (GetInteractingPlayerCount() <= 1)
            {
                _eliminationCancellation?.Dispose();
                _eliminationCancellation = EscapeeInsideCage.Elimination.Pause();
            }
        }

        protected override bool IsInteractableForPlayer(NetGamePlayer player)
        {
            _prisoner ??= Target.Prisoner;
            return !ReferenceEquals(_prisoner, null);
        }

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);
            
            if (ReferenceEquals(_prisoner, null))
            {
                Logger.LogError("Expected escapee inside cage");
                return;
            }

            if (player is not NetEscapeePlayer escapeePlayer)
                return;
            if (!_prisoner.CageRelease.Allowed())
                return;

            player.BaseStatistics.Increment(GameplayStatisticsType.ReleasedFromCage);

            foreach (var catcher in GameplayContext.Spawned.Catchers)
                catcher.Indicator.ShowTemporary(IndicationType.EscapeeWasFreed, Target.netIdentity);
            
            _eliminationCancellation?.Dispose();
            _prisoner.CageRelease.Release(CageReleaseReason.Teammate);

            // if actually processed
            if (_prisoner.SyncData.CageSelfRealising.Value.HasReason(CageReleaseReason.Teammate))
            {
                _onReleasedObservable.Invoke(new ReleaseEscapeeInfo(escapeePlayer, _prisoner));
                _prisoner = null;
                player.Score.AddInstant(ScoreSource.TeammateReleasedFromCage);
            }
        }

        protected override void OnInteractionInterrupted(NetGamePlayer player)
        {
            base.OnInteractionInterrupted(player);
            _eliminationCancellation?.Dispose();
        }

        public IDisposable SubscribeOnRelease(Action<ReleaseEscapeeInfo> info)
        {
            return _onReleasedObservable.Subscribe(info);
        }

        protected override void DebugProcessInternal(NetGamePlayer player)
        {
            base.DebugProcessInternal(player);
            player.Cast<NetEscapeePlayer>().CageRelease.Release(CageReleaseReason.Teammate);
        }
    }
}