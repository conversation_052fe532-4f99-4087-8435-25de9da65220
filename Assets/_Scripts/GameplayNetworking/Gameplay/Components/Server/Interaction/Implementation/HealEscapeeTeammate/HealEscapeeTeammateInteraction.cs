using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Data;
using MonsterLand.Matchmaking;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.HealEscapeeTeammate
{
    public class HealEscapeeTeammateInteraction : BaseInteraction<NetEscapeePlayer>
    {
        public override bool IsStatic => false;
        public override JxMatchmakingTeamKind? Team => JxMatchmakingTeamKind.Escapee;
        public override InteractionType InteractionType => InteractionType.HealEscapeeTeammate;
        protected override bool IsInteractable => Target.SyncData.Health.Value.IsInjured() && Target.BaseSyncData.Action.Value.HasPermission(ActionPermissions.CanBeHealed);
        public override int Priority => base.Priority - 30;
        
        protected override void OnMatchStarted()
        { }

        protected override void OnMatchStopped()
        { }

        protected override bool IsInteractableForPlayer(NetGamePlayer player) => player.netId != Target.netId;

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);
            
            player.BaseStatistics.Increment(GameplayStatisticsType.TeammateHeal);
            
            Target.Health.Increase();
            player.BaseStatistics.Increment(GameplayStatisticsType.Heal);
            
            player.Score.AddInstant(ScoreSource.TeammateHealed);
        }
    }
}