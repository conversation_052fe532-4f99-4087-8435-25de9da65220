using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using Jx.Utils.Logging;
using MonsterLand.Matchmaking;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.SelfHeal
{
    public class SelfHealInteraction : SelfBaseInteraction<NetEscapeePlayer>
    {
        public override JxMatchmakingTeamKind? Team => JxMatchmakingTeamKind.Escapee;
        public override InteractionType InteractionType => InteractionType.SelfHealing;
        protected override bool IsInteractable => true;

        protected override void OnMatchStarted()
        {
        }

        protected override void OnMatchStopped()
        {
        }

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);

            if (!(player is NetEscapeePlayer escapeePlayer))
                return;
            
            if (!escapeePlayer.SyncData.Health.Value.IsInjured())
            {
                Logger.LogError("Medkit interaction processed. but player is not injured");
                return;
            }

            if (!escapeePlayer.MedKit.TryUse())
            {
                Logger.LogError("Medkit interaction processed. but there is no medkits");
                return;
            }
            
            player.BaseStatistics.Increment(GameplayStatisticsType.SelfHeal);
        }

        protected override bool IsInteractableForPlayer(NetGamePlayer player)
        {
            if (player is NetEscapeePlayer escapeePlayer)
                return escapeePlayer.MedKit.CanBeUsed;
            
            return false;
        }

        protected override bool IsInterrupted(NetGamePlayer player)
        {
            if (player is NetEscapeePlayer escapeePlayer)
                return base.IsInterrupted(player) || !escapeePlayer.SyncData.Health.Value.IsInjured();

            return true;
        }
    }
}