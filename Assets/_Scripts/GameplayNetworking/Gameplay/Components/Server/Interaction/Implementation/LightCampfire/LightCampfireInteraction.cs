using System;
using Configs.ServerGameplay.Presentation.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Client.Interaction;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Objects;
using Mirror;
using MonsterLand.Matchmaking;
using SceneLogics.GameplayScene.Components.Indicators;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.LightCampfire
{
    public class LightCampfireInteraction : StagedInteraction<NetCampfire, StagedInteractionDataConfigPresentation>
    {
        #region CLIENT
        
        private LightCampfireInteractionClientController? _clientController;
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            
            _clientController = ClientController?.CastOrNull<LightCampfireInteractionClientController>();
        }

        [ClientRpc]
        private void ClientRpc_OnMiniGamePerfect() => _clientController?.OnPerfectMiniGame();

        #endregion
        
        #region SERVER

        private MiniGameControllerLightCampfire _miniGamesController = null!;
        private IDisposable? _campfireStateSubscription;

        
        public override JxMatchmakingTeamKind? Team => JxMatchmakingTeamKind.Escapee;
        public override InteractionType InteractionType => InteractionType.LightCampfire;
        protected override bool IsInteractable => Target.State.Value != CampfireState.InFire;

        protected override void OnMatchStarted()
        {
            base.OnMatchStarted();
            
            _miniGamesController = new MiniGameControllerLightCampfire(this, OnMiniGameFinish);
            _campfireStateSubscription = Target.State.SubscribeAndFire(HandleCampfireStateChange);
        }

        protected override void OnMatchStopped()
        {
            _campfireStateSubscription?.Dispose();
            _miniGamesController.StopAll();
        }
        
        protected override bool IsInteractableForPlayer(NetGamePlayer player)
        {
            return GameplayContext.MatchSyncData.GameplayInfo.Value.Goal == GameplayGoal.Campfires;
        }
        
        protected override void OnInteractionStarted(NetGamePlayer player)
        {
            base.OnInteractionStarted(player);

            if (Target.State.Value == CampfireState.Extinguished)
                Target.SetState(CampfireState.FireInProgress);

            if (player is NetEscapeePlayer escapeePlayer)
                _miniGamesController.Request(escapeePlayer);
        }
        
        protected override void OnInteractionInterrupted(NetGamePlayer player)
        {
            if (player is not NetEscapeePlayer escapeePlayer)
                return;
            
            _miniGamesController.Stop(escapeePlayer);
            
            if (!InUse())
            {
                if (Progress0100 < 100)
                {
                    RevertProgressToCurrentStage(); 
                }

                if (Progress0100 == 0)
                {
                    Target.SetState(CampfireState.Extinguished);
                }
            }
            
            base.OnInteractionInterrupted(player);
        }
        
        protected override void OnInteractionCompleted()
        {
            base.OnInteractionCompleted();

            foreach (var player in GameplayContext.Spawned.Players)
            {
                if (player is NetCatcherPlayer)
                    player.Indicator.ShowTemporary(IndicationType.CampfireWasLit, Target.netIdentity);

                player.BaseStatistics.Increment(GameplayStatisticsType.TotalCampfiresLight);
            }
        }
        
        protected override void OnStageCompleted()
        {
        }
        
        protected override void OnStageCompletedForPlayer(NetGamePlayer player)
        {
            if (player is NetEscapeePlayer escapeePlayer)
                _miniGamesController.Request(escapeePlayer);
            
            player.Score.AddInstant(ScoreSource.CampfireStagePassed);
        }

        protected override void OnInteractionCompletedForPlayer(NetGamePlayer player)
        {
            base.OnInteractionCompletedForPlayer(player);

            if (player is NetEscapeePlayer escapeePlayer)
                _miniGamesController.Stop(escapeePlayer);
            
            Target.SetState(CampfireState.InFire);

            player.Score.AddInstant(ScoreSource.CampfireStagePassed);
            player.Score.AddInstant(ScoreSource.CampfireLit);
            player.BaseStatistics.Increment(GameplayStatisticsType.CampfireLit);
        }

        protected override bool IsInterrupted(NetGamePlayer player)
        {
            return base.IsInterrupted(player) || GameplayContext.MatchSyncData.GameplayInfo.Value.Goal != GameplayGoal.Campfires;
        }

        private void HandleCampfireStateChange(CampfireState campfireState)
        {
            if (campfireState != CampfireState.Extinguished) 
                return;
            
            Progress.Reset();
            CurrentStage = 0;
        }
        
        private void OnMiniGameFinish(NetGamePlayer player, GenericMiniGameResult result)
        {
            if (result is GenericMiniGameResult.None or GenericMiniGameResult.Success)
                return;
            
            var config = InteractionConfig;
            switch (result)
            {
                case GenericMiniGameResult.Ignore:
                case GenericMiniGameResult.Fail:
                    ModifyProgressOnDelta(config.ProgressDeBuffModifier, config.ProgressDeBuffDuration);
                    Target.TriggerMinigameFailed();
                    break;
                case GenericMiniGameResult.PerfectSuccess:
                    ModifyProgressOnDelta(config.ProgressBuffModifier, config.ProgressBuffDuration);
                    player.Score.AddInstant(ScoreSource.PerfectedMiniGame);
                    ClientRpc_OnMiniGamePerfect();
                    break;
                default:
                    Logger.LogError($"Unknown result type '{result}'");
                    break;
            }
        }

        #endregion
    }
}