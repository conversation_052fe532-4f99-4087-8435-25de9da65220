using Configs.ServerGameplay.Presentation.Interaction;
using Core.Extensions;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Ensurance;
using Jx.Utils.Objects;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction
{
    public abstract class StagedInteraction<TTarget, TConfig> : BaseInteraction<TTarget>
        where TTarget : NetworkGameplayMatchObject
        where TConfig : StagedInteractionDataConfigPresentation
    {
        protected TConfig StagedConfig { get; private set; }
        public int CurrentStage { get; set; }
        protected int StageCount { get; private set; }
        protected float StageProgressSize01 { get; private set; }
        
        protected override void OnMatchStarted()
        {
            StagedConfig = InteractionConfig.Cast<TConfig>();
            StageCount = StagedConfig.StageCount;
            
            Ensure.Condition(() => StageCount > 0, "Stage count <= 0 is not allowed");
            
            StageProgressSize01 = 1f / (float)StageCount;
            Logger.LogIfTrue(CurrentStage != 0, "Current stage is zero");
        }

        protected override void OnMatchStopped()
        {
            CurrentStage = 0;
        }

        public override void TutorialSetProgress(float progress01)
        {
            base.TutorialSetProgress(progress01);

            CurrentStage = CalculateCurrentStage(progress01);
            RevertProgressToCurrentStage();
            OnTick(progress01);
        }

        protected override void OnTick(float progress01)
        {
            base.OnTick(progress01);

            var stage = CalculateCurrentStage(progress01);
            if (stage == CurrentStage || stage == StageCount)
                return;
            
            CurrentStage = stage;
            ProcessStageCompleted();
        }

        private void ProcessStageCompleted()
        {
            OnStageCompleted();
            ClientRpc_OnStageCompleted();
            
            foreach (var player in EnumerateInteractingPlayers())
            {
                OnStageCompletedForPlayer(player);
                ClientRpc_OnStageCompleted(player);
            }
        }

        private int CalculateCurrentStage(float progress01)
        {
            if (StageProgressSize01 == 0)
                return 0;
            
            return Mathf.FloorToInt(progress01 / StageProgressSize01);
        }

        protected abstract void OnStageCompleted();
        protected abstract void OnStageCompletedForPlayer(NetGamePlayer player);

        protected void RevertProgressToCurrentStage() => RevertProgressToStage(CurrentStage);
        private void RevertProgressToStage(int stage) => Progress.Set(stage * StageProgressSize01);
    }
}