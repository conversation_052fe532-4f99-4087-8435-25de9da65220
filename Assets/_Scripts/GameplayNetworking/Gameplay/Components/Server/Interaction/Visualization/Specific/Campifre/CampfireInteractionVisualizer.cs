using System.Linq;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.Interaction;
using Core.Helpers;
using Core.Helpers.Navigation;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.LightCampfire;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Visualization.Specific.Staged;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Ensurance;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Visualization.Specific.Campfire
{
    public class CampfireInteractionVisualizer : MonoBehaviour, IInteractionVisualizer
    {
        private const float IndicatorProgressSpeed = 8f;
        private const float VisualizerRotationSpeed = 8f;
        private const float IndicatorShowSpeed = 5f;
        private const float ProgressSpeed = 12f;

        [SerializeField]
        private StagedInteractionVisualizer _stagedInteractionVisualizer = null!;

        private ILocalGameplayContext _localGameplayContext = null!;
        private INavigation _navigation = null!;
        private StageInteractionIndicator.Factory _indicatorFactory = null!;
        private IJxChangeableObject<IServerGameplayConfig> _gameplayConfig = null!;

        private NetEscapeePlayer? _localEscapee;

        private Transform? _visualizerTransform;
        private StageInteractionIndicator? _indicator;
        private LightCampfireInteraction? _interaction;
        private InteractionVisualizerParameters? _parameters;

        private float _lightCampfireDurationInSec;
        private int _stageCount;

        [Inject]
        private void Inject(
            ILocalGameplayContext localGameplayContext,
            INavigation navigation,
            StageInteractionIndicator.Factory indicatorFactory,
            IJxChangeableObject<IServerGameplayConfig> gameplayConfig
        )
        {
            _localGameplayContext = localGameplayContext;
            _navigation = navigation;
            _indicatorFactory = indicatorFactory;
            _gameplayConfig = gameplayConfig;
        }

        private void LateUpdate()
        {
            if (_parameters == null || _indicator == null)
                return;

            var progress01 = _parameters.GetProgress();

            RenderVisualizerOnTick(progress01);
            RenderEscapeeIndicatorOnTick(progress01);
        }

        private void RenderVisualizerOnTick(float progress01)
        {
            _stagedInteractionVisualizer.SetProgress(Mathf.Lerp(_stagedInteractionVisualizer.Progress01, progress01, Time.deltaTime * ProgressSpeed));

            if (_interaction!.InUse())
            {
                _visualizerTransform!.RotateAround(_visualizerTransform.position, _visualizerTransform.up, Time.deltaTime * VisualizerRotationSpeed);
            }
        }

        private void RenderEscapeeIndicatorOnTick(float progress01)
        {
            // catcher team doesn't see the indicator
            if (_localEscapee.IsNullObj())
            {
                _indicator!.SetProgress(0f);
                _indicator.SetAlpha(0f);
                return;
            }

            var syncData = _localEscapee!.SyncData;
            var availableInteraction = syncData.AvailableInteraction.Value;
            var action = syncData.Action.Value;
            var minigameType = syncData.MiniGame.Value.Type;

            var showIndicator =
                availableInteraction.NetId == _interaction!.NetId &&
                availableInteraction.InteractionType == InteractionType.LightCampfire &&
                action == ActionType.Interaction &&
                minigameType != MiniGameType.Campfire;

            _indicator!.SetAlpha(Mathf.Lerp(_indicator.Alpha01, showIndicator ? 1f : 0f, Time.deltaTime * IndicatorShowSpeed));

            if (progress01 - Mathf.Epsilon < 1f)
            {
                var stageProgress = progress01 % (1f / _stageCount) * _stageCount;
                if (stageProgress <= 0.01f)
                {
                    _indicator!.SetProgress(stageProgress);
                    _indicator.DoPunch();
                }
                else
                {
                    _indicator!.SetProgress(Mathf.Lerp(_indicator.Progress01, stageProgress, Time.deltaTime * IndicatorProgressSpeed));
                }

                _indicator.SetTimerSeconds(Mathf.CeilToInt((1f - stageProgress) * _lightCampfireDurationInSec));
            }
        }

        #region Factory/Pool

        private IMemoryPool? _pool;

        void IPoolable<InteractionVisualizerParameters, IMemoryPool>.OnSpawned(InteractionVisualizerParameters parameters, IMemoryPool pool)
        {
            _localEscapee = _localGameplayContext.Player as NetEscapeePlayer;

            _pool = pool;
            _parameters = parameters;

            _interaction = _localGameplayContext.Interactions
                                                .OfType<LightCampfireInteraction>()
                                                .FirstOrDefault(i => i.NetId == parameters.InteractionNetId);

            if (_interaction.IsNullObj())
            {
                Dispose();
                return;
            }

            var interactionConfig = _gameplayConfig.Value.GetInteractionConfig<StagedInteractionDataConfigPresentation>(InteractionType.LightCampfire);
            _stageCount = interactionConfig.StageCount;
            Ensure.IsTrue(_stageCount > 0, "Stage count > 0");
            _lightCampfireDurationInSec = (float)interactionConfig.Duration.TotalSeconds / _stageCount;

            transform.SetParent(_interaction!.transform, false);
            gameObject.SetActive(true);

            // gameObject MUST be active, before this call (sprite renderer shader calculations need it):
            _stagedInteractionVisualizer.Setup(_stageCount);
            _visualizerTransform ??= _stagedInteractionVisualizer.transform;

            var canvasTransform = (RectTransform)_navigation.Canvas.Value!.transform;
            _indicator = _indicatorFactory.Create(
                canvasTransform,
                new StageInteractionIndicatorParameters(parameters.Pivot, Camera.main, canvasTransform)
            );
            _indicator.SetProgress(0f);
            _indicator.SetAlpha(0f);
        }

        void IPoolable<InteractionVisualizerParameters, IMemoryPool>.OnDespawned()
        {
            if (!_indicator.IsNullObj())
                _indicator!.Dispose();

            if (!_stagedInteractionVisualizer.IsNullObj())
                _stagedInteractionVisualizer.Cleanup();

            if (this.IsNullObj())
                return;

            gameObject.SetActive(false);
        }

        public void Dispose()
        {
            _pool?.Despawn(this);
            _pool = null;
        }

        [Preserve]
        public class Factory : PlaceholderFactory<InteractionVisualizerParameters, CampfireInteractionVisualizer> { }

        #endregion
    }
}