using System;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Components.Server.Interaction.Progress
{
    public class SingleInteractionProgress : IInteractionProgress
    {
        private readonly JxChangeableObject<float, float> _value01 = new JxChangeableObject<float, float>(0f);
        public IJxChangeableObject<float> Value01 => _value01;

        public void Set(float progress01)
        {
            var currentProgress = _value01.Value;
            if (Math.Abs(currentProgress - progress01) < float.Epsilon)
                return;
            
            _value01.Set(Mathf.Clamp(progress01, 0f, 1f));
        }

        public void Reset() => Set(0f);

        public void OnInteractionStarted() => Reset();

        public void OnInteractionInterrupted() => Reset();

        public void OnInteractionCompleted() => Reset();
    }
}