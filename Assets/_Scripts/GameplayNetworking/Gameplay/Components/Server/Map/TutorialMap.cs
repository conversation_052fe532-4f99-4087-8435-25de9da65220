using System.Collections.Generic;
using GameplayNetworking.Gameplay.Components.Server.Map.Locations;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using GameplayNetworking.Share.Character;

namespace GameplayNetworking.Gameplay.Components.Server.Map
{
    public class TutorialMap : Map
    {
        public override IReadOnlyList<LocationData> GetCharacterLocationData(CharacterType characterType)
        {
            return characterType switch
            {
                CharacterType.Catcher => CatcherPositionsContainer.GetAllLocations(),
                CharacterType.Escapee => EscapeePositionsContainer.GetAllLocations(),
                _ => default
            };
        }

        public override IReadOnlyList<LocationData> GetSceneObjectLocationData(NetSceneObjectType sceneObjectType)
        {
            return sceneObjectType switch
            {
                NetSceneObjectType.CampFire => CampfirePositionsContainer.GetAllLocations(),
                _ => default
            };
        }
    }
}