using System;
using Audio.Manager;
using Audio.Manager.Handler;
using Audio.Provider.Sounds;
using GameplayNetworking.Gameplay.Player;

namespace GameplayNetworking.Gameplay.Components.Server.Client.Interaction
{
    public abstract class InteractionClientController : IDisposable
    {
        protected InteractionClientController(InteractionClientControllerContext context)
        {
            Context = context;
        }
        
        protected abstract SoundEventIdentifier? SoundOnProcess { get; }
        protected InteractionClientControllerContext Context { get; }
        protected IClientAudioInstanceHandler? ProcessSoundHandler { get; private set; }
        
        public virtual void Dispose()
        {
            ProcessSoundHandler?.Dispose();
        }

        /// <summary>
        /// Can be called when the intraction has been already started, but a player joined only now
        /// </summary>
        /// <param name="player"></param>
        public virtual void OnStarted(NetGamePlayer player)
        { }

        /// <summary>
        /// Called when first player started interaction. Isn't called if progress > 0.
        /// </summary>
        public virtual void OnStarted()
        {
            if (SoundOnProcess.HasValue)
            {
                ProcessSoundHandler?.Dispose();
                ProcessSoundHandler = JxAudioManager.Instance.Sounds.GetHandler(SoundOnProcess.Value, Context.Interaction.Transform);
                ProcessSoundHandler.Play();
            }
        }
        
        /// <summary>
        /// Called for each player completed the interaction
        /// </summary>
        /// <param name="player"></param>
        public virtual void OnCompleted(NetGamePlayer player) {}

        /// <summary>
        /// Called once the interaction is completed
        /// </summary>
        public virtual void OnCompleted()
        {
            ProcessSoundHandler?.Dispose();
        }
        
        /// <summary>
        /// Can be called when the interaction is still in progress, but a player has stopped interacting
        /// </summary>
        /// <param name="player"></param>
        public virtual void OnInterrupted(NetGamePlayer player) {}

        /// <summary>
        /// Called when the interaction isn't completed and no players are interacting
        /// </summary>
        public virtual void OnInterrupted()
        {
            ProcessSoundHandler?.Dispose();
        }
        
        public virtual void OnStageCompleted(NetGamePlayer player) {}
        public virtual void OnStageCompleted() {}
    }
}