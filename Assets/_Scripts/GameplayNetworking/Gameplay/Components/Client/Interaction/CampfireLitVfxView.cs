using Core.Extensions;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using SceneLogics.GameplayScene.Components.Particles;
using SceneLogics.GameplayScene.Components.Particles.Factory;
using UnityEngine;
using Zenject;

namespace GameplayNetworking.Gameplay.Components.Server.Client.Interaction
{
    public class CampfireLitVfxView : VfxView
    {
        [SerializeField]
        private ParticleSystem _lightedParticleSystem = null!;

        [SerializeField]
        private ParticleSystem _bigFireParticle = null!;
        
        [SerializeField]
        private ParticleSystem _fireParticleSystem = null!;
        
        [Header("Settings")]
        [SerializeField]
        [Min(0f)]
        private float _scaleBigFireDurationInSeconds = 1f;

        private readonly IAnimationSlot _litAnimationSlot = new AnimationSlot("lit");
        
        private Vector3 _startLitParticleLocalScale;

        private void Awake()
        {
            _startLitParticleLocalScale = _bigFireParticle.transform.localScale;
        }

        private void OnDisable()
        {
            _litAnimationSlot.CompleteAndStop();
            _bigFireParticle.transform.localScale = _startLitParticleLocalScale;
        }

        private void OnDestroy()
        {
            _litAnimationSlot.Destroy();
        }

        public override void OnSpawned(VfxParameters parameters, IMemoryPool pool)
        {
            base.OnSpawned(parameters, pool);
            
            _bigFireParticle.Stop(true);
            _lightedParticleSystem.Stop(true);
            _fireParticleSystem.Stop(true);
            
            _litAnimationSlot.PlayNew(DoLit());
        }

        private Tween DoLit()
        {
            _bigFireParticle.Play(true);
            _bigFireParticle.transform.localScale = Vector3.zero;

            return _litAnimationSlot.PlayNew(
                DOTween.Sequence()
                    .Append(_bigFireParticle.transform.DOScale(Vector3.one, _scaleBigFireDurationInSeconds))
                    .AppendCallback(() =>
                        {
                            _lightedParticleSystem.Play(true);
                            _fireParticleSystem.Play(true);
                        }
                    )
                    .Append(_bigFireParticle.transform.DOScale(Vector3.zero, _scaleBigFireDurationInSeconds))
            );
        }
    }
}