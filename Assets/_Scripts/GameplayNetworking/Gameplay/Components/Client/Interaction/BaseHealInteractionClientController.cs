using System;
using Audio.Manager;
using Audio.Provider;
using Audio.Provider.Sounds;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components;
using SceneLogics.GameplayScene.Client.Vfx;
using SceneLogics.GameplayScene.Components.Particles;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Components.Server.Client.Interaction
{
    public abstract class BaseHealInteractionClientController : InteractionClientController
    {
        private IDisposable? _vfxCancellation;

        protected BaseHealInteractionClientController(
            InteractionClientControllerContext context) : base(context)
        {
        }
        
        protected override SoundEventIdentifier? SoundOnProcess
            => AudioLibrary.Instance.Sounds.Gameplay.Interactions.HealProcessLoop;


        public override void Dispose()
        {
            base.Dispose();
            
            _vfxCancellation?.Dispose();
        }

        protected abstract NetGamePlayer GetInteractionTarget();

        public override void OnStarted(NetGamePlayer player)
        {
            base.OnStarted(player);

            var target = GetInteractionTarget();

            _vfxCancellation?.Dispose();
            _vfxCancellation = Context.VfxManager.Show(
                VfxBuilder
                    .Create(ParticleType.HealProcess)
                    .SetLocalPosition(new Vector3(0f, 0.15f, 0f))
                    .SetGlobalRotation(Quaternion.Euler(90f, 0f, 0f))
                    .SetSyncRotation(SyncVfxMask.WithoutX())
                    .SetOwner(target)
                    .Build()
            );
        }

        public override void OnInterrupted()
        {
            base.OnInterrupted();
            
            _vfxCancellation?.Dispose();
        }

        public override void OnCompleted(NetGamePlayer player)
        {
            base.OnCompleted(player);
            
            _vfxCancellation?.Dispose();
            
            JxAudioManager.Instance.Sounds.PlayOneShot(
                AudioLibrary.Instance.Sounds.Gameplay.Interactions.Healed,
                player.transform
            );
        }
    }
}