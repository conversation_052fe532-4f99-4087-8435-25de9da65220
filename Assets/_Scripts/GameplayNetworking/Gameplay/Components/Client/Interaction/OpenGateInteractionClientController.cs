using Audio.Provider;
using Audio.Provider.Sounds;

namespace GameplayNetworking.Gameplay.Components.Server.Client.Interaction
{
    public class OpenGateInteractionClientController : InteractionClientController
    {
        public OpenGateInteractionClientController(InteractionClientControllerContext context) : base(context)
        {
        }

        protected override SoundEventIdentifier? SoundOnProcess => AudioLibrary.Instance.Sounds.Gameplay.Interactions.GateOpenProcessLoop;
    }
}