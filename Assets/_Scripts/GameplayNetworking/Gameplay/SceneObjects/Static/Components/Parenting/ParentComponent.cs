using System;
using System.Collections.Generic;
using Core.UnityUtils.TransformSnapshots;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Coroutines;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using UnityEngine;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting
{
    public class ParentComponent : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(ParentComponent));

        [SerializeField]
        private Transform _pointTransform = null!;

        private readonly JxAtomicFlag _attachPointTaken = false;

        private IDisposable? _previousParenting;
        private LocalTransformSnapshot? _initialPointSnapshot;
        private NetGamePlayer? _child;
        
        public Vector3 PointPosition => _pointTransform.position;

        public IDisposable SetChild(NetGamePlayer child)
        {
            if (ReferenceEquals(child, null))
                throw new NullReferenceException(nameof(child));
            
            _previousParenting?.Dispose();

            SetChildInternal(child);
            
            _previousParenting = JxDisposableAction.Build()
                                                 .AppendDispose(new TransformSynchronizer(child, _pointTransform))
                                                 .AppendCallback(TryResetChild);

            return _previousParenting;
        }

        public IDisposable ReserveAttachPoint(out Transform attachPointTransform)
        {
            _initialPointSnapshot ??= _pointTransform.GetLocalSnapshot();

            // attach point already taken, empty point will be returned
            if (!_attachPointTaken.TrySet())
            {
                _logger.LogError($"Attach point already taken `{gameObject.name}`");
                var dummyPoint = CreateDummyAttachPoint(_initialPointSnapshot.Value);
                attachPointTransform = dummyPoint;
                return JxDisposableAction.Build().AppendCallback(() => Destroy(dummyPoint.gameObject));
            }

            attachPointTransform = _pointTransform;

            return JxDisposableAction.Build()
                                     .AppendCallback(
                                          () =>
                                          {
                                              _attachPointTaken.TryReset();

                                              if (this.IsDestroyed())
                                                  return;

                                              _pointTransform.ApplySnapshot(_initialPointSnapshot.Value);
                                          }
                                      );
        }

        public void TryResetChild()
        {
            if (ReferenceEquals(_child, null))
                return;
            
            _child.Parent.Reset();
            _child = null;
        }

        private void SetChildInternal(NetGamePlayer? child)
        {
            if (!ReferenceEquals(child, null) && !ReferenceEquals(_child, null))
            {
                // main thread save
                var newName = child.gameObject.name;
                var oldName = _child.gameObject.name;
                
                _logger.LogError(
                    "Attempt to set child for object that already contains child",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["New child name"] = newName,
                        ["Old child name"] = oldName,
                    }
                );
            }

            _child = child;
        }

        private static Transform CreateDummyAttachPoint(LocalTransformSnapshot snapshot)
        {
            var dummyPoint = new GameObject("DummyPoint").transform;
            dummyPoint.ApplySnapshot(snapshot);

            return dummyPoint;
        }

        #region CLASSES

        private class TransformSynchronizer : IDisposable
        {
            private readonly Transform _playerTransform;
            private readonly Transform _target;
            private readonly JxAtomicFlag _disposed;

            private IDisposable? _cancellation;

            public TransformSynchronizer(NetGamePlayer player, Transform target)
            {
                _playerTransform = player.transform;
                _target = target;
                _disposed = false;
                
                player.NetTransform.ServerTeleport(
                    _target.position,
                    _target.rotation,
                    onTeleported: (movementStop) =>
                    {
                        if (_disposed.IsSet)
                        {
                            movementStop.Restore(_target.position, immediateTeleport: false);
                            return;
                        }

                        _cancellation = JxDisposableAction.Build()
                                                          .AppendCallback(() => movementStop.Restore(_target.position, immediateTeleport: false))
                                                          .AppendDispose(player.InvokeEveryFrame(SyncToTarget));
                    }
                );
            }

            public void Dispose()
            {
                _disposed.TrySet();
                _cancellation?.Dispose();
            }

            private void SyncToTarget()
            {
                _playerTransform.position = _target.position;
                _playerTransform.rotation = _target.rotation;

                var playerEulerAngles = _playerTransform.eulerAngles;
                _playerTransform.eulerAngles = new Vector3(playerEulerAngles.x, playerEulerAngles.y, 0f);
            }
        }

        #endregion
    }
}