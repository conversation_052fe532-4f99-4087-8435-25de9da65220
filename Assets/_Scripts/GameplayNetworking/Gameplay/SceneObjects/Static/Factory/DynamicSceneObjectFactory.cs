using System.Collections.Generic;
using System.Linq;
using Configs.NetGameObjects;
using Core.Extensions.DiExtensions;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using Jx.Utils.Logging;
using Mirror;
using UnityEngine.Scripting;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Factory
{
    public class DynamicSceneObjectFactory : IDynamicSceneObjectFactory
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(DynamicSceneObjectFactory));

        private readonly IDictionary<DynamicObjectType, DynamicObjectComponent.Factory> _dynamicSceneObjectSubFactories;

        [Preserve]
        public DynamicSceneObjectFactory(IEnumerable<DynamicObjectComponent.Factory> dynamicSceneObjectSubFactories)
        {
            _dynamicSceneObjectSubFactories = dynamicSceneObjectSubFactories.ToDictionary(f => f.ObjectType, f => f);
        }

        public DynamicObjectComponent Create(IMatchContext matchContext, DynamicObjectType type, ISpatialParameter spatialParameter, NetGamePlayer owner)
        {
            if (!_dynamicSceneObjectSubFactories.ContainsKey(type))
            {
                _logger.LogError($"Factory for [{type}] is not registered");
                return _dynamicSceneObjectSubFactories.Values.First().Create(matchContext, spatialParameter);
            }

            var spawned = _dynamicSceneObjectSubFactories[type].Create(matchContext, spatialParameter);
            spawned.SetOwner(owner);

            NetworkServer.Spawn(spawned.gameObject);

            return spawned;
        }
    }
}