using System;
using System.Collections.Generic;
using System.Linq;
using App.Inventory.Item;
using Core.Helpers;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Gameplay.Networking;
using GameplayNetworking.Gameplay.Buffs;
using GameplayNetworking.Gameplay.Components.Server.Visibility;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Components.GameplayIndication;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Components.InputModification;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player.Components.Movement;
using GameplayNetworking.Gameplay.Player.Components.Movement.Modification;
using GameplayNetworking.Gameplay.Player.Components.Perks;
using GameplayNetworking.Gameplay.Player.Components.PublicClientInfo;
using GameplayNetworking.Gameplay.Player.Components.Shield;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Shared.Ability;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Emoji;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Mimicry;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Helpers;
using Jx.Utils.IdGeneration;
using Jx.Utils.Observable;
using LoM.Characters.ClientIntegration.Perks;
using LoM.Emojis.ClientIntegration;
using Managers.Emoji;
using Mirror;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using SceneLogics.GameplayScene.Abilities.Parameters;
using SceneLogics.GameplayScene.Components.FlyingTexts;
using UI.Screens.GameplayScene.MatchFinish;
using UnityEngine;
using SyncDictOperation =
    Mirror.SyncIDictionary<LoM.Characters.ClientIntegration.Perks.PerkType, GameplayNetworking.Gameplay.Player.Components.Perks.PerkDto>.Operation;

namespace GameplayNetworking.Gameplay.Player
{
    [RequireComponent(typeof(NetGameplayPlayerImmediateSyncData))]
    public partial class NetGameplayPlayerSyncData : NetGameplayObjectSyncData
    {
        [JxSyncField(priority: 100)] // without priority, animation can be synced after a character view changed
        private CharacterViewIdentifier _characterView;
        
        [JxSyncField(priority: 200)] // priority required, otherwise sync var hook will be invoked BEFORE action change
        private byte _actionChangeIndex; // to identify on client, that action changed too fast
        
        [JxSyncField]
        private NetVisibilityStateDto _visibility;
        [JxSyncField]
        private NetSharedConfigDto _sharedConfig;
        [JxSyncField]
        private InventoryItemSlot _inventoryItemSlot;
        [JxSyncField]
        private NetPlayerMiniGameDto _miniGame;
        [JxSyncField]
        private InputModificatorType _inputModificator;
        [JxSyncField]
        private PublicClientInfo _publicInfo;
        [JxSyncField]
        public GameplayPlayerState _state;
        [JxSyncField]
        private ushort _playTimeSeconds;
        [JxSyncField]
        private NetAbilityDto _ability;
        [JxSyncField]
        private NetTransformSpeedDto _speed;
        [JxSyncField]
        private GenericMovementModification _movementModification;
        [JxSyncField]
        private LayerType _gameObjectLayer;
        [JxSyncField]
        private ActionType _action;
        [JxSyncField]
        private NetGameplayMatchRewardDto _reward;
        [JxSyncField]
        private NetPlayerAvailableInteractionDto _availableInteraction;
        [JxSyncField]
        private byte _abilityAnimationIndex;
        [JxSyncField]
        private ShieldDto _shield;
        [JxSyncField]
        private MatchResultType _matchResult;
        [JxSyncField]
        private NetMimicryStateDto _mimicry;
        [JxSyncField]
        private NetEmojiDto _emoji;

        public NetGameplayPlayerImmediateSyncData Immediate { get; private set; } = null!;

        protected override void Awake()
        {
            base.Awake();

            Immediate = this.GetComponentOrThrow<NetGameplayPlayerImmediateSyncData>();
        }

        [JxCommand]
        private void SetMiniGameResult(GenericMiniGameResult result) { }

        [JxCommand]
        private void ConsumeItem(InventoryItemSlot slot) { }

        [JxCommand]
        private void UseAbility(GenericAbilityInputParameters parameters) { }

        [JxCommand]
        private void TakeMovementAuthority(byte authorityIndex) { }

        [JxTargetRpc]
        private void RequestIndication(GameplayIndicationRequest request) { }
        
        [JxClientRpc]
        private void SendAbilityStateChange(AbilityClientStateDto dto) { }
        
        [JxCommand]
        private void RequestInteraction(InteractionRequest request) { }
        
        [JxClientRpc]
        private void OnAttackMiss(NetEscapeePlayer escapee){}

        [JxClientRpc]
        private void OnAttacked(NetEscapeePlayer escapee) {}
        
        [JxClientRpc]
        private void OnShieldBlocked(){}
        
        // server general callbacks
        public override void OnStartServer()
        {
            base.OnStartServer();

            _appliedBuffSyncList.Clear();
            _emojiSlotsSyncList.Clear();
        }

        public override void OnStopServer()
        {
            base.OnStopServer();
            _perkSyncDictionary.Clear();
        }
        
        // client general callbacks
        public override void OnStartClient()
        {
            base.OnStartClient();

            _appliedBuffSyncList.OnChanged += OnAppliedBuffListChanged;
            OnAppliedBuffListChanged();

            _perkSyncDictionary.Callback += OnPerksChange;
            foreach (var keyValuePair in _perkSyncDictionary)
                OnPerksChange(SyncDictOperation.OP_SET, keyValuePair.Key, keyValuePair.Value);
        }

        public override void OnStopClient()
        {
            base.OnStopClient();

            _appliedBuffSyncList.OnChanged -= OnAppliedBuffListChanged;
            _perkSyncDictionary.Callback -= OnPerksChange;

            _perksClientOnly.Clear();
        }

        #region INVENTORY

        private readonly JxObservable<InventoryItemSlot> _onInventoryItemUsed = new JxObservable<InventoryItemSlot>();
        public IJxObservable<InventoryItemSlot> OnInventoryItemUsed => _onInventoryItemUsed;
        
        [Server]
        public void NotifyInventoryItemConsumed(InventoryItemSlot item)
        {
            _onInventoryItemUsed.Invoke(item);
            
            // to prevent duplicated invoke on host
            if (!NetworkServer.active)
                ClientRpc_NotifyInventoryItemConsumed(item);
        }

        [ClientRpc]
        private void ClientRpc_NotifyInventoryItemConsumed(InventoryItemSlot item) => _onInventoryItemUsed.Invoke(item);

        #endregion

        #region PERKS

        private readonly SyncIDictionary<PerkType, PerkDto> _perkSyncDictionary = new SyncDictionary<PerkType, PerkDto>();

        public IReadOnlyDictionary<PerkType, PerkDto> Perks_ServerOnly
        {
            [Server] get => _perkSyncDictionary;
        }
        
        private readonly Dictionary<PerkType, IJxChangeableObject<PerkDto, PerkDto>> _perksClientOnly = new();

        public IReadOnlyDictionary<PerkType, IJxChangeableObject<PerkDto, PerkDto>> Perks_ClientOnly
        {
            [Client] get => _perksClientOnly;
        }
        
        private void OnPerksChange(SyncDictOperation op, PerkType key, PerkDto item)
        {
            switch (op)
            {
                case SyncDictOperation.OP_ADD:
                    _perksClientOnly.Add(key, new JxChangeableObject<PerkDto, PerkDto>(item));
                    break;
                case SyncDictOperation.OP_CLEAR:
                    _perksClientOnly.Clear();
                    break;
                case SyncDictOperation.OP_REMOVE:
                    _perksClientOnly.Remove(key);
                    break;
                case SyncDictOperation.OP_SET:
                    if (_perksClientOnly.TryGetValue(key, out var dto))
                        dto.Set(item);
                    else
                        _perksClientOnly[key] = new JxChangeableObject<PerkDto, PerkDto>(item);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(op), op, null);
            }
        }
        
        [Server]
        public void AddPerk(PerkType type, PerkDto dto) => _perkSyncDictionary.Add(type, dto);

        [Server]
        public void SetPerkDto(PerkType type, PerkDto dto) => _perkSyncDictionary[type] = dto;

        #endregion

        #region BUFFS

        private readonly SyncList<NetAppliedBuffDto> _appliedBuffSyncList = new SyncList<NetAppliedBuffDto>();
        
        private readonly IJxChangeableObject<IReadOnlyList<NetAppliedBuffDto>, IReadOnlyList<NetAppliedBuffDto>> _appliedBuffListClientOnly =
            new JxChangeableObject<IReadOnlyList<NetAppliedBuffDto>, IReadOnlyList<NetAppliedBuffDto>>(Array.Empty<NetAppliedBuffDto>());

        public IJxChangeableObject<IReadOnlyList<NetAppliedBuffDto>> AppliedBuffList_ClientOnly
        {
            [Client] get => _appliedBuffListClientOnly;
        }

        public bool BuffIsApplied(BuffType type) => GetAppliedBuffIndex(type) >= 0;

        public bool TryGetAppliedBuff(BuffType type, out NetAppliedBuffDto buff)
        {
            buff = default;

            var index = GetAppliedBuffIndex(type);
            if (index >= 0)
            {
                buff = _appliedBuffSyncList[index];
                return true;
            }

            return false;
        }

        private int GetAppliedBuffIndex(BuffType type)
        {
            for (var i = 0; i < _appliedBuffSyncList.Count; i++)
            {
                var appliedBuff = _appliedBuffSyncList[i];
                if (appliedBuff.Type != type)
                    continue;

                return i;
            }

            return -1;
        }
        
        private readonly JxIdGenerator _buffIdGenerator = new JxIdGenerator();

        [Server]
        public IDisposable AddNewBuff(BuffType type, TimeSpan? duration, Action? onComplete)
        {
            var isStackable = type.IsStackable();
            var buffIndex = GetAppliedBuffIndex(type);

            if (buffIndex >= 0 && !isStackable)
                return new BuffHandler(null, onComplete);

            NetAppliedBuffDto buff;

            if (buffIndex >= 0 && isStackable)
            {
                buff = _appliedBuffSyncList[buffIndex].CreateStackCountIncrement();
            }
            else
            {
                buff = duration == null
                    ? NetAppliedBuffDto.CreatePersistent((ushort)_buffIdGenerator.Generate(), type)
                    : NetAppliedBuffDto.CreateTemporary((ushort)_buffIdGenerator.Generate(), type, duration.Value);
            }

            if (buffIndex >= 0)
                _appliedBuffSyncList[buffIndex] = buff;
            else
                _appliedBuffSyncList.Add(buff);

            return new BuffHandler(
                () =>
                {
                    for (var i = 0; i < _appliedBuffSyncList.Count; i++)
                    {
                        var appliedBuff = _appliedBuffSyncList[i];
                        if (appliedBuff.Id != buff.Id)
                            continue;

                        if (appliedBuff.StackCount > 1)
                            _appliedBuffSyncList[i] = appliedBuff.CreateStackCountDecrement();
                        else
                            _appliedBuffSyncList.RemoveAt(i);

                        break;
                    }
                },
                onComplete
            );
        }

        [Server]
        public IDisposable SubscribeToBuffListChange(Action callback)
        {
            _appliedBuffSyncList.OnChanged += callback;
            return JxDisposableAction.Build().AppendCallback(() => _appliedBuffSyncList.OnChanged -= callback);
        }

        [Server]
        public IEnumerable<BuffType> EnumerateAppliedBuffs() => _appliedBuffSyncList.Select(d => d.Type);
        
        private void OnAppliedBuffListChanged() => _appliedBuffListClientOnly.Set(_appliedBuffSyncList);

        #region CLASSES

        private class BuffHandler : IDisposable
        {
            private readonly Action? _disposeCallback;
            private readonly Action? _onComplete;

            public BuffHandler(Action? disposeCallback, Action? onComplete)
            {
                _disposeCallback = disposeCallback;
                _onComplete = onComplete;
            }

            public void Dispose()
            {
                _disposeCallback?.Invoke();
                _onComplete?.Invoke();
            }
        }

        #endregion

        #endregion

        #region EMOJI
        
        private readonly SyncList<EmojiType> _emojiSlotsSyncList = new SyncList<EmojiType>();
        public IReadOnlyList<EmojiType> EmojiSlotsReadOnly => _emojiSlotsSyncList;

        [Server]
        public void SetEmojiSlots(IReadOnlyList<EmojiType> emojiSlots)
        {
            if (emojiSlots.IsNullOrEmpty())
                throw new NullReferenceException();
            
            foreach (var slot in emojiSlots)
                _emojiSlotsSyncList.Add(slot);
        }

        [JxCommand]
        private void SendEmoji(EmojiType emojiType) {}

        #endregion
    }
}