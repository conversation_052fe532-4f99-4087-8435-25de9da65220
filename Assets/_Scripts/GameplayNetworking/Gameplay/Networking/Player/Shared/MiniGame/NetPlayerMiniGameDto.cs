using System;

namespace GameplayNetworking.Gameplay.Player.Shared.MiniGame
{
    public readonly struct NetPlayerMiniGameDto : IEquatable<NetPlayerMiniGameDto>
    {
        public static NetPlayerMiniGameDto Default { get; } = new NetPlayerMiniGameDto(MiniGameType.None, GenericMiniGameResult.None);

        private NetPlayerMiniGameDto(
            MiniGameType type,
            GenericMiniGameResult result
        )
        {
            Type = type;
            Result = result;
        }

        public readonly MiniGameType Type;
        public readonly GenericMiniGameResult Result;

        public bool IsRunning => Type != MiniGameType.None;
        
        public NetPlayerMiniGameDto SetType(MiniGameType type) => new(type, Result);
        public NetPlayerMiniGameDto SetResult(GenericMiniGameResult result) => new(MiniGameType.None, result);
        
        
        public bool Equals(NetPlayerMiniGameDto other) => Type == other.Type && Result == other.Result;
        public override bool Equals(object? obj) => obj is NetPlayerMiniGameDto other && Equals(other);
        public override int GetHashCode() => HashCode.Combine((int)Type, (int)Result);
        public static bool operator ==(NetPlayerMiniGameDto left, NetPlayerMiniGameDto right) => left.Equals(right);
        public static bool operator !=(NetPlayerMiniGameDto left, NetPlayerMiniGameDto right) => !left.Equals(right);
    }
}