using System;
using System.Linq;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Manager.Network.Share.Authentication;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using Jx.Utils.Ensurance;
using Jx.Utils.Logging;
using LoM.Messaging.Model.GameplayStatistics;
using LoM.Messaging.Requests;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Meta.Api;

namespace GameplayNetworking.Gameplay
{
    public class GamePlayerStatisticSender : IGamePlayerStatisticSender
    {
        private readonly IJxLogger _logger;
        private readonly IJxMonsterLandMetaServerApi _serverApi;

        public GamePlayerStatisticSender(IJxMonsterLandMetaServerApi serverApi)
        {
            _logger = JxLoggerFactory.CreateLogger(nameof(GamePlayerStatisticSender));
            _serverApi = serverApi;
        }

        public async UniTask SendAsync(IGameplayServerContext context, NetGamePlayer player)
        {
            try
            {
                if (!WasRealPlayer(player))
                {
                    player.BaseSyncData.Reward.SetFromServer(NetGameplayMatchRewardDto.Empty);
                    return;
                }

                var finishContext = CreateFinishedPlayerContext(context, player);
                var reward = await GetFinishedPlayerReward(finishContext);
                
                player.BaseSyncData.Reward.SetFromServer(reward);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                
                player.BaseSyncData.Reward.SetFromServer(NetGameplayMatchRewardDto.Empty);
            }
        }

        private FinishedPlayerContext CreateFinishedPlayerContext(IGameplayServerContext context, NetGamePlayer player)
        {
            var authInfo = player.Client.AuthInfo;
            if (authInfo == null)
                throw new NullReferenceException(nameof(authInfo));
            var userToken = authInfo.UserToken;
            if (string.IsNullOrEmpty(userToken))
                throw new NullReferenceException(nameof(userToken));

            var statistics = GetStatistic(context, player, authInfo);

            return new FinishedPlayerContext(userToken, statistics, player.MatchContext.Id, player.MatchContext.GameMode);
        }

        private async UniTask<NetGameplayMatchRewardDto> GetFinishedPlayerReward(FinishedPlayerContext context)
        {
            try
            {
                var reward = await _serverApi.ReportPlayerStatisticsAsync(context.UserToken, context.Statistics, context.MatchId, context.GameMode);
                return NetGameplayMatchRewardDto.FromMatchReward(reward);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }

            return NetGameplayMatchRewardDto.Empty;
        }

        private PlayerMatchStatisticClientIntegration GetStatistic(IGameplayServerContext context, NetGamePlayer player, PlayerServerAuthInfo authInfo)
        {
            return player switch
            {
                NetEscapeePlayer escapee => GetEscapeeStatistic(escapee, authInfo),
                NetCatcherPlayer catcher => GetCatcherStatistic(context, catcher, authInfo),
                _ => throw new NotSupportedException($"Unknown player type '{player?.GetType().Name}'")
            };
        }

        private EscapeePlayerMatchStatisticClientIntegration GetEscapeeStatistic(NetEscapeePlayer escapee, PlayerServerAuthInfo authInfo)
        {
            var statistics = new EscapeePlayerMatchStatisticClientIntegration();

            if (escapee.connectionToClient is not { isReady: true }) // leave from session
            {
                statistics.LeaveFromSession = true;
                return statistics;
            }

            GetBaseStatistic(statistics, escapee, authInfo);

            statistics.Survived = escapee.SyncData.State.Value == GameplayPlayerState.IsWinner;

            var personalStatistics = escapee.Statistics;

            personalStatistics.FillModel(ref statistics);

            return statistics;
        }

        private CatcherPlayerMatchStatisticClientIntegration GetCatcherStatistic(
            IGameplayServerContext context,
            NetCatcherPlayer catcher,
            PlayerServerAuthInfo authInfo
        )
        {
            var statistics = new CatcherPlayerMatchStatisticClientIntegration();

            if (!catcher.connectionToClient.isReady) // leave from session
            {
                statistics.LeaveFromSession = true;
                return statistics;
            }

            GetBaseStatistic(statistics, catcher, authInfo);

            statistics.EscapeesDeathCount = context.Spawned.Escapees.Count(e => e.SyncData.State.Value == GameplayPlayerState.IsLoser);

            var personalStatistics = catcher.Statistics;

            personalStatistics.FillModel(ref statistics);

            return statistics;
        }

        private void GetBaseStatistic(PlayerMatchStatisticClientIntegration statistics, NetGamePlayer player, PlayerServerAuthInfo authInfo)
        {
            statistics.ConcreteCharacterIndex = authInfo.CharacterIdentifier.Index;
            statistics.DurationInSec = player.BaseSyncData.PlayTimeSeconds.Value;
            statistics.IsMvp = player.Score.IsMvp;
            statistics.IsWinner = player.BaseSyncData.State.Value == GameplayPlayerState.IsWinner;
        }
        
        private bool WasRealPlayer(NetGamePlayer player)
        {
            if (!player.IsBot())
                return true;

            return player.Client.Connection != null;
        }

        #region CLASSES

        private class FinishedPlayerContext
        {
            public FinishedPlayerContext(
                string userToken,
                PlayerMatchStatisticClientIntegration statistics,
                Guid matchId,
                JxMonsterLandGameModeKind gameMode
            )
            {
                Ensure.IsNotNullOrEmpty(userToken, nameof(userToken));
                Ensure.NotNull(statistics, nameof(statistics));

                UserToken = userToken;
                Statistics = statistics;
                MatchId = matchId;
                GameMode = gameMode;
            }

            public string UserToken { get; }
            public PlayerMatchStatisticClientIntegration Statistics { get; }
            public Guid MatchId { get; }
            public JxMonsterLandGameModeKind GameMode { get; }
        }

        #endregion
    }
}