using UnityEngine;

namespace GameplayNetworking.Gameplay.Visibility.Client
{
    public abstract class ClientVisibilityHandler : MonoBehaviour
    {
        private RendererVisibilityMode? _mode;
        private bool _enabled;
        
        private void OnEnable()
        {
            _enabled = true;

            if (_mode != null)
                RenderMode(_mode.Value);
        }

        private void OnDisable() => _enabled = false;
        
        public void SetVisibility(RendererVisibilityMode mode)
        {
            if (_mode == mode)
                return;

            _mode = mode;
            OnRendererModeChanged(mode);
        }

        private void RenderMode(RendererVisibilityMode mode)
        {
            if (!_enabled)
                return;
            
            OnRendererModeChanged(mode);
        }

        protected abstract void OnRendererModeChanged(RendererVisibilityMode mode);
    }
}