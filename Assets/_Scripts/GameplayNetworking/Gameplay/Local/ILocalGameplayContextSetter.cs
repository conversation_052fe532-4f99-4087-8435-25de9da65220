using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Map.Configuration;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Matchmaking.Dto.Maps;
using SceneLogics.GameplayScene.Client.VignetteController;
using SceneLogics.GameplayScene.Traps;

namespace GameplayNetworking.Gameplay.Local
{
    public interface ILocalGameplayContextSetter
    {
        void SetMatchSyncData(MatchSyncData matchSyncData);
        void SetMap(JxMonsterLandMap map);
        void SetMapConfig(IMapConfiguration configuration);
        void SetGameMode(JxMonsterLandGameModeKind gameMode);
        void SetLocalPlayer(ILocalGameplayPlayer player);
        void SetGhost(GhostPlayer ghost);

        void AddSpawnedPlayer(NetGamePlayer player);

        void RegisterInteraction(ILocalInteraction interaction);
        void UnRegisterInteraction(ILocalInteraction interaction);

        void RegisterVignetteController(IVignetteController vignetteController);

        void RegisterSceneObject(NetInteractableSceneObject sceneObject);
        
        void InvokeTrapSpawnEvent(INetTrapReadOnly trap);
    }
}