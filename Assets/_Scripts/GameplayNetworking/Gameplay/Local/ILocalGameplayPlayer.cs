using System;
using GameplayComponents.Bushes;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Abilities;
using GameplayNetworking.Gameplay.Player.Components.Collision;
using GameplayNetworking.Gameplay.Player.Components.Immunity;
using GameplayNetworking.Gameplay.Inventory;
using GameplayNetworking.Gameplay.Player.Components.Score;
using GameplayNetworking.Gameplay.Player.Components.Synchronizations;
using GameplayNetworking.Gameplay.Player.Components.TeamVisualizers;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Transform;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Mimicry;
using GameplayNetworking.Gameplay.Visibility;
using Mirror;
using MonsterLand.Matchmaking;

namespace GameplayNetworking.Gameplay.Local
{
    public interface ILocalGameplayPlayer
    {
        JxMatchmakingTeamKind Team { get; }
        NetworkIdentity NetIdentity { get; }
        Guid MatchId { get; }

        NetGameplayPlayerSyncData SyncData { get; }
        NetPlayerActionSystem Action { get; }
        ParentSetterPlayerSystem Parent { get; }
        SynchronizationNetPlayerComponent Synchronization { get; }
        BuffPlayerSystem Buff { get; }
        NetInteractionPlayerSystem Interaction { get; }
        NetTransformPlayerSystem NetTransform { get; }
        InputModificationPlayerSystem InputModification { get; }
        NetAbilityPlayerSystem Ability { get; }
        NetGameplayRewardPlayerSystem Reward { get; }
        NetIndicationPlayerSystem Indicator { get; }
        ImmunitySynchronizerNetPlayerComponent Immunity { get; }
        CollisionPlayerComponent Collision { get; }
        ScoreNetPlayerComponent Score { get; }
        VisualizerTeamComponent TeamVisualizer { get; }
        BushFieldOfViewHandler BushFieldOfView { get; }
        VisibilityPlayerSystem Visibility { get; }
        InventoryPlayerSystem Inventory { get; }
        NetCharacterViewPlayerSystem CharacterView { get; }
        NetMimicryPlayerSystem Mimicry { get; }
    }
}