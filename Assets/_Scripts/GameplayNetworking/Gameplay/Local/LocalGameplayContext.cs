using System;
using System.Collections.Generic;
using System.Threading;
using Core.Helpers.Camera;
using Core.Helpers.SceneManagement;
using Core.Helpers.SceneManagement.TransitionParameters;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Map.Configuration;
using GameplayNetworking.Gameplay.Local.Visibility;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using GameplayNetworking.Gameplay.SceneObjects.Static.Cage;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using GameplayNetworking.Gameplay.SceneObjects.Static.InventoryItems;
using GameplayNetworking.Manager.Network.Server;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using Jx.Telemetry.Actions;
using Jx.User.Analytics.Api;
using Jx.Utils.Coroutines;
using Jx.Utils.Extensions;
using Jx.Utils.Objects;
using Jx.Utils.Observable;
using Jx.Utils.Threading;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Matchmaking.Dto.Maps;
using SceneLogics.GameplayScene.Client.VignetteController;
using SceneLogics.GameplayScene.Components;
using SceneLogics.GameplayScene.Context;
using SceneLogics.GameplayScene.Traps;
using UnityEngine.Scripting;

namespace GameplayNetworking.Gameplay.Local
{
    [Preserve]
    public class LocalGameplayContext : ILocalGameplayContext,
                                        ILocalGameplayContextSetter,
                                        IDisposable,
                                        IJxTelemetryGlobalActionPopulator
    {
        private readonly IJxTelemetryActionTracker _actionTracker;
        private readonly List<NetGamePlayer> _spawnedPlayers;
        private readonly List<ILocalInteraction> _interactions;
        private readonly List<NetCampfire> _campfires;
        private readonly List<NetGate> _gates;
        private readonly List<NetCage> _cages;
        private readonly List<NetInventoryChest> _chests;
        private readonly JxObservable<INetTrapReadOnly> _trapSpawnEvent;
        private readonly JxAtomicFlag _initialized;
        private readonly IDisposable _globalPopulatorRegistration;

        [Preserve]
        public LocalGameplayContext()
        {
            _spawnedPlayers = new List<NetGamePlayer>();
            _interactions = new List<ILocalInteraction>();
            _campfires = new List<NetCampfire>();
            _gates = new List<NetGate>();
            _cages = new List<NetCage>();
            _chests = new List<NetInventoryChest>();
            _trapSpawnEvent = new JxObservable<INetTrapReadOnly>();

            Spectating = new SpectatingClientHandler(this);
            InteractionSpawnEvent = new JxObservable<ILocalInteraction>();
            InteractionDeSpawnEvent = new JxObservable<ILocalInteraction>();
            Visibility = new LocalVisibilityContext(this);
            _initialized = false;

            _actionTracker = JxTelemetryIntegration.Instance.ActionTracker.With(new JxTelemetryCallbackActionPopulator(b => b.Category("gameplay")));
            _globalPopulatorRegistration = JxTelemetryIntegration.Instance.RegisterGlobalPopulator(this);
        }

        public IJxTelemetryActionTracker ActionTracker => _actionTracker;
        public ILocalGameplayPlayer Player { get; private set; } = null!;
        public GhostPlayer Ghost { get; private set; } = null!;
        public JxMonsterLandMap Map { get; private set; }
        public IMapConfiguration MapConfiguration { get; private set; } = null!;
        public JxMonsterLandGameModeKind GameMode { get; private set; }
        public MatchSyncData MatchSyncData { get; private set; } = null!;
        public ISpectatingClientHandler Spectating { get; }
        public ILocalVisibilityContext Visibility { get; }
        public IGameplayCameraController CameraController { get; } = new GameplayCameraController();
        public IVignetteController VignetteController { get; private set; }
        public IReadOnlyList<NetGamePlayer> SpawnedPlayers => _spawnedPlayers;
        public IReadOnlyList<NetCampfire> Campfires => _campfires;
        public IReadOnlyList<NetGate> Gates => _gates;
        public IReadOnlyList<NetCage> Cages => _cages;
        public IReadOnlyList<NetInventoryChest> Chests => _chests;
        public IReadOnlyList<ILocalInteraction> Interactions => _interactions;
        public JxObservable<ILocalInteraction> InteractionSpawnEvent { get; }
        public JxObservable<ILocalInteraction> InteractionDeSpawnEvent { get; }
        public IJxObservable<INetTrapReadOnly> TrapSpawnEvent => _trapSpawnEvent;

        public void SetMap(JxMonsterLandMap map) => Map = map;
        public void SetMapConfig(IMapConfiguration configuration) => MapConfiguration = configuration;

        public void SetGameMode(JxMonsterLandGameModeKind gameMode) => GameMode = gameMode;
        public void SetLocalPlayer(ILocalGameplayPlayer player) => Player = player;
        public void SetMatchSyncData(MatchSyncData matchSyncData) => MatchSyncData = matchSyncData;

        public void SetGhost(GhostPlayer ghost) => Ghost = ghost;
        public void AddSpawnedPlayer(NetGamePlayer player) => _spawnedPlayers.Add(player);

        public void RegisterInteraction(ILocalInteraction interaction)
        {
            _interactions.Add(interaction);
            InteractionSpawnEvent.Invoke(interaction);
        }

        public void UnRegisterInteraction(ILocalInteraction interaction)
        {
            if (_interactions.Remove(interaction))
                InteractionDeSpawnEvent.Invoke(interaction);
        }

        public void RegisterVignetteController(IVignetteController vignetteController)
        {
            VignetteController = vignetteController;
        }

        public void RegisterSceneObject(NetInteractableSceneObject sceneObject)
        {
            if (sceneObject.TryCast<NetCampfire>(out var campfire))
                _campfires.Add(campfire);

            if (sceneObject.TryCast<NetGate>(out var gate))
                _gates.Add(gate);

            if (sceneObject.TryCast<NetCage>(out var cage))
                _cages.Add(cage);

            if (sceneObject.TryCast<NetInventoryChest>(out var chest))
                _chests.Add(chest);
        }

        public void UnRegisterSceneObject(NetInteractableSceneObject sceneObject)
        {
            if (sceneObject.TryCast<NetCampfire>(out var campfire))
                _campfires.Remove(campfire);

            if (sceneObject.TryCast<NetGate>(out var gate))
                _gates.Remove(gate);

            if (sceneObject.TryCast<NetCage>(out var cage))
                _cages.Remove(cage);

            if (sceneObject.TryCast<NetInventoryChest>(out var chest))
                _chests.Remove(chest);
        }

        public void InvokeTrapSpawnEvent(INetTrapReadOnly trap) => _trapSpawnEvent.Invoke(trap);

        public async UniTask InitializeAsync(CancellationToken cancellationToken)
        {
            await WaitSpawnedAsync(cancellationToken);

            if (!_initialized.TrySet())
                return;

            foreach (var player in SpawnedPlayers)
            foreach (var system in player.EnumerateSystems())
                system.ClientReady();
        }

        public async UniTask WaitReadyAsync(CancellationToken cancellationToken)
        {
            await WaitSpawnedAsync(cancellationToken);

            while (!cancellationToken.IsCancellationRequested)
            {
                await UniTask.Delay(TimeSpan.FromMilliseconds(100), cancellationToken: cancellationToken);

                if (_initialized.IsSet)
                    break;
            }
        }

        public void Dispose()
        {
            _globalPopulatorRegistration?.Dispose();
            Visibility?.TryDispose();
        }

        private async UniTask WaitSpawnedAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await UniTask.Delay(TimeSpan.FromMilliseconds(100), cancellationToken: cancellationToken);

                if (ReferenceEquals(Player, null))
                    continue;
                if (ReferenceEquals(MatchSyncData, null))
                    continue;

                var wordDescription = MatchSyncData.WorldDescription.Value;
                if (!wordDescription.IsSet)
                    continue;

                if (wordDescription.Players != SpawnedPlayers.Count)
                    continue;
                if (wordDescription.Cages != Cages.Count)
                    continue;
                if (wordDescription.Campfires != Campfires.Count)
                    continue;
                if (wordDescription.Chests != Chests.Count)
                    continue;
                if (wordDescription.Interactions != Interactions.Count)
                    continue;

                break;
            }
        }
        
        #region TELEMETRY
        
        private GameplaySceneTransitionParameters? _transitionParameters;
        private int? _realPlayerCount;

        void IJxTelemetryActionPopulator.Populate(IJxTelemetryActionBuilder actionBuilder)
        {
            if (!_initialized.IsSet)
            {
                return;
            }
            
            _transitionParameters ??= MonsterLandSceneManager.Instance.GetTransitionParameters<GameplaySceneTransitionParameters>();
            if (_transitionParameters == null)
            {
                throw new InvalidOperationException("Transition parameters are not set");
            }
            
            actionBuilder.SetExtras("reconnect", _transitionParameters.Reconnection ? "1" : "0");

            var isRemote = !JxNetworkServer.Instance.IsActive;
            
            actionBuilder.SetExtras("remote", isRemote ? "1" : "0");
            if (isRemote) // todo: refactor
            {
                actionBuilder.SetExtras("ping", PingAlertComponent.LastShownPingInMills.ToString());
            }

            var matchSyncData = MatchSyncData;
            if (!ReferenceEquals(null, matchSyncData))
            {
                actionBuilder.SetExtras("state", matchSyncData.State.Value.ToString().ToLowerInvariant());
                actionBuilder.SetExtras("goal", matchSyncData.GameplayInfo.Value.Goal.ToString().ToLowerInvariant());
                actionBuilder.SetExtras("restcampfires", matchSyncData.GameplayInfo.Value.RestCampfires.ToString().ToLowerInvariant());
                actionBuilder.SetExtras("map", Map.ToString().ToLowerInvariant());
            }

            var player = Player;
            if (!ReferenceEquals(player, null))
            {
                actionBuilder.SetExtras("team", Player.Team.ToString().ToLowerInvariant());
                
                var playerSyncData = player.SyncData;
                if (!ReferenceEquals(playerSyncData, null))
                {
                    actionBuilder.SetExtras("playeraction", playerSyncData.Action.Value.ToString().ToLowerInvariant());
                    actionBuilder.SetExtras("inventory", playerSyncData.InventoryItemSlot.Value.ItemType.ToString().ToLowerInvariant());

                    var publicInfo = playerSyncData.PublicInfo.Value;
                    
                    actionBuilder.SetExtras("character", publicInfo.CharacterIdentifier.Index.ToString().ToLowerInvariant());   
                    actionBuilder.SetExtras("skin", publicInfo.CharacterIdentifier.SkinEntityId.ToLowerInvariant());   
                    actionBuilder.SetExtras("nickname", publicInfo.Nickname);   
                    actionBuilder.SetExtras("characterlevel", publicInfo.CharacterLevel.ToString());   
                    actionBuilder.SetExtras("mmr", publicInfo.Mmr.ToString());
                }

                if (player is NetEscapeePlayer escapeePlayer)
                {
                    actionBuilder.SetExtras("playerhealth", escapeePlayer.SyncData.Health.Value.Current.ToString().ToLowerInvariant());   
                    actionBuilder.SetExtras("playermedkit", escapeePlayer.SyncData.Medkit.Value.Current.ToString().ToLowerInvariant());   
                    actionBuilder.SetExtras("playerelimination", escapeePlayer.SyncData.Elimination.Value.Progress.ToString().ToLowerInvariant());   
                }
            }

            if (_realPlayerCount == null)
            {
                _realPlayerCount = 0;
                
                foreach (var p in SpawnedPlayers)
                {
                    var isBot = string.IsNullOrEmpty(p.BaseSyncData.PublicInfo.Value.PublicId);
                    if (!isBot)
                    {
                        ++_realPlayerCount;
                    }
                }
            }

            actionBuilder.SetExtras("realplayers", _realPlayerCount.Value.ToString());
        }
        
        #endregion
    }
}