using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;

namespace GameplayNetworking.Gameplay.Buffs
{
    public class BuffMechanicContext
    {
        public BuffMechanicContext(
            NetGamePlayer player, 
            IGameplayServerContext gameplayContext)
        {
            Player = player;
            GameplayContext = gameplayContext;
        }

        public NetGamePlayer Player { get; }
        public IGameplayServerContext GameplayContext { get; }
    }
}