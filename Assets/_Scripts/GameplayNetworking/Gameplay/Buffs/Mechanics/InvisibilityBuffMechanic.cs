using System;
using GameplayNetworking.Gameplay.Components.Server.Visibility;
using GameplayNetworking.Gameplay.Visibility;

namespace GameplayNetworking.Gameplay.Buffs.Mechanics
{
    public class InvisibilityBuffMechanic : BuffMechanic
    {
        private readonly NetInvisibilityReason _reason;

        public InvisibilityBuffMechanic(NetInvisibilityReason reason)
        {
            _reason = reason;
        }

        protected override IDisposable ActivateInternal(BuffMechanicContext context)
        {
            return context.Player.Visibility.SetInvisible(_reason);
        }
    }
}