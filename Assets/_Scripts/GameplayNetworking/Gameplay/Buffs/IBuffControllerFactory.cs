using System.Collections.Generic;
using GameplayNetworking.Gameplay.Buffs.Config;
using GameplayNetworking.Gameplay.Player.Components.Buffs;

namespace GameplayNetworking.Gameplay.Buffs
{
    public interface IBuffControllerFactory
    {
        IBuffController Create(JxBuffConfigDto buffConfig, BuffType type);
        
#if UNITY_EDITOR
        IReadOnlyList<IBuffMechanic> GetEditorOnlyDefinition(BuffType type);
#endif
    }
}