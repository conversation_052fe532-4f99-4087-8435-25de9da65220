using System;
using Audio.Manager;
using Audio.Provider;
using GameplayNetworking.Gameplay.Player.Components;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Client.Vfx;
using SceneLogics.GameplayScene.Components.Particles;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class MicroDashClientBuffHandler : ClientBuffHandler
    {
        private static readonly Vector3 _dashLocalPosition = new Vector3(0f, 0.405f, 0f);
        private static readonly Vector3 _dashedLocalPosition = new Vector3(0f, 0.405f, 0f);
        private static readonly Quaternion _dashLocalRotation = Quaternion.Euler(0f, -180f, 0f);

        public MicroDashClientBuffHandler(ClientBuffControllerContext context) : base(context)
        {
        }

        protected override IDisposable OnStart()
        {
            JxAudioManager.Instance.Sounds.PlayOneShot(
                AudioLibrary.Instance.Sounds.Gameplay.Characters.Abilities.Activation.Trickie,
                Context.BuffOwner
            );
            
            return JxDisposableAction.Build()
                .AppendDispose(
                    Context.VfxManager.Show(
                        VfxBuilder
                            .Create(ParticleType.MicroDashStart)
                            .SetLocalPosition(_dashLocalPosition)
                            .SetSyncRotation(SyncVfxMask.Full())
                            .SetLocalRotation(_dashLocalRotation)
                            .SetOwner(Context.BuffOwner)
                            .Build()
                    )
                )
                .AppendCallback(() =>
                    Context.VfxManager.ShowAndForget(
                        VfxBuilder
                            .Create(ParticleType.MicroDashStop)
                            .SetGlobalPosition(Context.BuffOwner.NetTransform.Position + _dashedLocalPosition)
                            .Build()
                    )
                );
        }
    }
}