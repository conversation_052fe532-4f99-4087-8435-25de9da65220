using System;
using Audio.Manager;
using Audio.Provider;
using GameplayNetworking.Gameplay.Player.Components;
using SceneLogics.GameplayScene.Client.Vfx;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class LizSpeedUpBuffHandler : ClientBuffHandler
    {
        public LizSpeedUpBuffHandler(ClientBuffControllerContext context) : base(context)
        {
        }

        protected override IDisposable OnStart()
        {
            JxAudioManager.Instance.Sounds.PlayOneShot(
                AudioLibrary.Instance.Sounds.Gameplay.Buffs.Adrenaline,
                Context.BuffOwner
            );
            
            return Context.VfxManager.Show(
                VfxBuilder
                    .Create(ParticleType.FastMoveTrail)
                    .SetOwner(Context.BuffOwner)
                    .Build()
            );
        }
    }
}