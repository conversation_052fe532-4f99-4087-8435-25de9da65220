using System;
using App.Inventory.Item;
using GameplayNetworking.Gameplay.Player.Components;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Client.Vfx;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class PotionPowerBuffHandler : PotionClientBuffHandler
    {
        public PotionPowerBuffHandler(ClientBuffControllerContext context) : base(context)
        {
        }

        protected override InventoryItemType Potion => InventoryItemType.PotionPower;

        protected override IDisposable OnStart()
        {
            return JxDisposableAction.Build()
                .AppendDispose(base.OnStart())
                .AppendDispose(Context.VfxManager.Show(
                    VfxBuilder
                        .Create(ParticleType.Unharmed)
                        .SetOwner(Context.BuffOwner)
                        .SetLocalPosition(new Vector3(0f, 0.915f, 0f))
                        .Build()
                )
            );
        }
    }
}