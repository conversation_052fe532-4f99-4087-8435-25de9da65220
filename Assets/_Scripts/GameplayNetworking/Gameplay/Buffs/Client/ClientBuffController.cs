using System;
using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player.Components.Buffs;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class ClientBuffController : IDisposable
    {
        private readonly ClientBuffControllerContext _context;
        private IDictionary<ushort, IClientBuffHandler> _buffHandlerById;
        private IDictionary<ushort, IClientBuffHandler> _tempBuffHandlerById;

        public ClientBuffController(ClientBuffControllerContext context)
        {
            _context = context;
            _buffHandlerById = new Dictionary<ushort, IClientBuffHandler>();
            _tempBuffHandlerById = new Dictionary<ushort, IClientBuffHandler>();
        }

        public void Refresh(IReadOnlyList<NetAppliedBuffDto> appliedBuffList)
        {
            // not required. only for error cases
            if (_tempBuffHandlerById.Count > 0)
                _tempBuffHandlerById.Clear();

            // save `applied` buffs to temp and remove from current
            foreach (var appliedBuff in appliedBuffList)
                _tempBuffHandlerById.Add(
                    appliedBuff.Id,
                    _buffHandlerById.Remove(appliedBuff.Id, out var listener)
                        ? listener.RefreshStack(appliedBuff.StackCount)
                        : Create(_context, appliedBuff.Type).Start()
                );

            // dispose `not applied` views from current after `applied` deletion
            foreach (var listener in _buffHandlerById.Values)
                listener.Stop();

            _buffHandlerById.Clear();

            // swap temp with current. after all - tempBuffViewById is empty
            (_buffHandlerById, _tempBuffHandlerById) = (_tempBuffHandlerById, _buffHandlerById);
        }

        public void Dispose() => _buffHandlerById.Clear();

        private static IClientBuffHandler Create(ClientBuffControllerContext context, BuffType type)
        {
            return type switch
            {
                BuffType.Shock => new ShockClientBuffHandler(context),
                BuffType.Roll => new RollClientBuffHandler(context),
                BuffType.StunnedByZeroHealth => new StunByZeroHealthClientBuffHandler(context),
                BuffType.StunnedByRock => new StunHitClientBuffHandler(context),
                BuffType.StunByRolling => new StunHitClientBuffHandler(context),
                BuffType.AngelSleep => new AngelSleepClientBuffHandler(context),
                BuffType.InsideBush => new InsideBushClientBuffHandler(context),
                BuffType.TrapSlowDown => new TrapSlowDownClientBuffHandler(context),
                BuffType.TrapDisclosure => new TrapDisclosureClientBuffHandler(context),
                BuffType.TrapCatch => new TrapCatchClientBuffHandler(context),
                BuffType.ChamieInvisibility => new ChamieInvisibilityClientBuffHandler(context),
                BuffType.KnockingDash => new KnockingDashClientBuffHandler(context),
                BuffType.MicroStunnedByKnockingDash => new MicroStunnedClientByKnockingDash(context),
                BuffType.AttachedToKnockingDash => new AttachedToKnockingDashClientBuffHandler(context),
                BuffType.FayWallSpeedUp => new FayWallSpeedUpClientBuffHandler(context),
                BuffType.MicroStunnedByFayWall => new MicroStunnedClientByFayWallBuffHandler(context),
                BuffType.MicroStunnedByElectraField => new MicroStunnedClientBuffHandler(context),
                BuffType.TrapFire => new TrapFireClientBuffHandler(context),
                BuffType.CatcherRage => new CatcherRageClientBuffHandler(context),
                BuffType.PhantomInvisibility => new PhantomInvisibilityClientBuffHandler(context),
                BuffType.EchoSpeedUp => new EchoSpeedUpClientBuffHandler(context),
                BuffType.Injurance => new InjuranceClientBuffHandler(context),
                BuffType.GolemShield => new GolemShieldBuffHandler(context),
                BuffType.LazyRegeneration => new LazyRegenerationClientBuffHandler(context),
                BuffType.ShieldLiz => new LizShieldClientBuffHandler(context),
                BuffType.SpeedUpLiz => new LizSpeedUpBuffHandler(context),
                BuffType.PotionSpeedUp => new PotionSpeedUpClientBuffHandler(context),
                BuffType.PotionPower => new PotionPowerBuffHandler(context),
                BuffType.PotionHearing => new PotionHearingBuffHandler(context),
                BuffType.PotionInvisibility => new PotionInvisibilityBuffHandler(context),
                BuffType.MicroDash => new MicroDashClientBuffHandler(context),
                BuffType.OwnerSlowdownByElectraField => new SlowedDownByElectraFieldBuffHandler(context),
                _ => ClientBuffEmptyHandler.Instance
            };
        }
    }
}