using System;
using Audio.Manager;
using Audio.Provider;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Client.Vfx;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class ShockClientBuffHandler : ClientBuffHandler
    {
        public ShockClientBuffHandler(ClientBuffControllerContext context)
            : base(context) { }

        protected override IDisposable OnStart()
        {
            NetEscapeePlayer? escapee = Context.BuffOwner as NetEscapeePlayer;
            escapee?.SetIsShockingOnClient(true);
            
            JxAudioManager.Instance.Sounds.PlayOneShot(
                AudioLibrary.Instance.Sounds.Gameplay.Buffs.Adrenaline,
                Context.BuffOwner
            );

            return JxDisposableAction.Build().AppendDispose(
                                          Context.VfxManager.Show(
                                              VfxBuilder.Create(ParticleType.FastMoveTrail)
                                                        .SetOwner(Context.BuffOwner)
                                                        .Build()
                                          )
                                      )
                                     .AppendCallback(() => escapee?.SetIsShockingOnClient(false));
        }
    }
}