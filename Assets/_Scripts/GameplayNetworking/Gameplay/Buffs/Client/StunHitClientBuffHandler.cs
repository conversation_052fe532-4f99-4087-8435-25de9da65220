using System;
using GameplayNetworking.Gameplay.Player.Components;
using SceneLogics.GameplayScene.Client.Vfx;
using SceneLogics.GameplayScene.Components.Particles;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class StunHitClientBuffHandler : ClientBuffHandler
    {
        public StunHitClientBuffHandler(ClientBuffControllerContext context)
            : base(context) { }

        protected override IDisposable OnStart()
        {
            Context.VfxManager.Show(
                VfxBuilder.Create(ParticleType.StunHit)
                    .SetOwner(Context.BuffOwner)
                    .Build()
            );

            return Context.VfxManager.Show(
                VfxBuilder.Create(ParticleType.Stunned)
                    .SetOwner(Context.BuffOwner)
                    .SetSyncRotation(SyncVfxMask.Nothing())
                    .SetGlobalRotation(Quaternion.Euler(270f, 0f, 0f))
                    .Build()
            );
        }
    }
}