using System;
using GameplayNetworking.Gameplay.Player.Components;
using SceneLogics.GameplayScene.Client.Vfx;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class SlowedDownByElectraFieldBuffHandler : ClientBuffHandler
    {
        private static readonly Vector3 _activatedLocalPosition = new Vector3(0f, 0.35f, 0f);
        private static readonly Quaternion _activatedGlobalRotation = Quaternion.Euler(90f, 0f, 90f);
        
        public SlowedDownByElectraFieldBuffHandler(ClientBuffControllerContext context) : base(context)
        {
        }

        protected override IDisposable OnStart()
        {
            Context.VfxManager.ShowAndForget(VfxBuilder
                .Create(ParticleType.ElectraFieldStart)
                .SetOwner(Context.BuffOwner)
                .Build()
            );
            
            return Context.VfxManager.Show(VfxBuilder
                .Create(ParticleType.ElectraFieldProcess)
                .SetOwner(Context.BuffOwner)
                .SetLocalPosition(_activatedLocalPosition)
                .SetGlobalRotation(_activatedGlobalRotation)
                .Build()
        );
        }
    }
}