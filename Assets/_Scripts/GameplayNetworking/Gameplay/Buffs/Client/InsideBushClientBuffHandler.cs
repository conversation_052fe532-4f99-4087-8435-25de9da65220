using System;
using GameplayNetworking.Gameplay.Player.Components;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Client.Vfx;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class InsideBushClientBuffHandler : ClientBuffHandler
    {
        public InsideBushClientBuffHandler(ClientBuffControllerContext context)
            : base(context) { }

        protected override IDisposable OnStart()
        {
            PlayLeavesParticles(Quaternion.LookRotation(Context.BuffOwner.NetTransform.UnityTransform.right, Context.BuffOwner.NetTransform.UnityTransform.up));

            return JxDisposableAction.Build()
                                     .AppendCallback(
                                          () => PlayLeavesParticles(
                                              Quaternion.LookRotation(-Context.BuffOwner.NetTransform.UnityTransform.right, Context.BuffOwner.NetTransform.UnityTransform.up)
                                          )
                                      );
        }

        private void PlayLeavesParticles(Quaternion rotation)
        {
            Context.VfxManager.Show(
                VfxBuilder
                   .Create(ParticleType.LeavesFall)
                   .SetGlobalPosition(Context.BuffOwner.NetTransform.Position)
                   .SetGlobalRotation(rotation)
                   .Build()
            );
        }
    }
}