using System;
using GameplayNetworking.Gameplay.Player.Components;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Client.Vfx;
using SceneLogics.GameplayScene.Components.Particles;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs.Client
{
    public class KnockingDashClientBuffHandler : ClientBuffHandler
    {
        private const float TrailParticlesPositionY = 0.405f;
        private const float TrailParticlesScaleFactor = 0.65f;
        
        public KnockingDashClientBuffHandler(ClientBuffControllerContext context)
            : base(context) { }

        protected override IDisposable OnStart() => ShowParticles();

        private IDisposable ShowParticles()
        {
            var trailRotation = Quaternion.LookRotation(-Context.BuffOwner.NetTransform.Forward, Vector3.up);
            
            var trail = Context.VfxManager.Show(
                VfxBuilder
                   .Create(ParticleType.GorKnockingDashRocketTrail)
                   .SetSyncPosition(SyncVfxMask.WithoutY())
                   .SetGlobalPosition(new Vector3(0f, TrailParticlesPositionY, 0f))
                   .SetGlobalRotation(trailRotation)
                   .SetOwner(Context.BuffOwner)
                   .SetScaleMultiplier(TrailParticlesScaleFactor)
                   .Build()
            );

            var dust = Context.VfxManager.Show(
                VfxBuilder
                   .Create(ParticleType.DustStream)
                   .SetOwner(Context.BuffOwner)
                   .SetSyncRotation(SyncVfxMask.OnlyY())
                   .Build()
            );

            return JxDisposableAction.Build()
                                     .AppendDispose(trail)
                                     .AppendDispose(dust);
        }
    }
}