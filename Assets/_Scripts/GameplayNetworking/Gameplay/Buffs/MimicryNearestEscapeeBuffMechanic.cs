using System;
using Core.Helpers;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Coroutines;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Buffs
{
    public class MimicryNearestEscapeeBuffMechanic : BuffMechanic
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(MimicryNearestEscapeeBuffMechanic));
        
        protected override IDisposable ActivateInternal(BuffMechanicContext context)
        {
            var escapee = GetEscapeeToMimicry(context);
            if (escapee.IsDestroyed())
            {
                _logger.LogError("Failed to find escapee to mimicry");
                return JxDisposableAction.Empty;
            }

            return JxDisposableAction.Build().AppendDispose(context.Player.Mimicry.TurnInto(escapee, LayerType.EscapeeForm));
        }
        
        private static NetEscapeePlayer GetEscapeeToMimicry(BuffMechanicContext context)
            => context.GameplayContext.Spawned.Escapees.MinByDistance(
                e => Vector3.SqrMagnitude(context.Player.NetTransform.Position - e.NetTransform.Position)
            );
    }
}