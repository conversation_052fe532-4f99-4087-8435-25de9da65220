using System;
using System.Collections;
using Core.Extensions;
using Core.Helpers;
using Core.Managers.AssetLoader;
using Core.UIReusable.Timer.TimeProviders;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameplayNetworking.Contexts.Client.Components.Helpers;
using GameplayNetworking.Contexts.Client.Components.Markers;
using GameplayNetworking.Gameplay.Local.Input;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player.Components.Synchronizations.Abilities;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.Player.Shared.Ability;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Extensions;
using Jx.Utils.Observable;
using Mirror;
using SceneLogics.GameplayScene.Abilities;
using SceneLogics.GameplayScene.Abilities.Impl.Projectiles;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;
using UnityEngine.UI;

namespace GameplayNetworking.Contexts.Client.Components.AbilityButton
{
    // todo: full refactor
    [RequireComponent(typeof(StyleSetterAbilityInputButtonComponent))]
    public class UseAbilityButton : MonoBehaviour
    {
        private const float ReloadedAnimationDurationInSec = 4f;
        
        [SerializeField]
        private DirectionAbilityInputJoystick _directionAbilityInputJoystick = null!;

        [SerializeField]
        private DirectionMarker _directionMarkerPrefab = null!;

        [SerializeField]
        private Image _rangeImage = null!;

        [SerializeField]
        private StyleSetterAbilityInputButtonComponent _styleSetter = null!;

        [SerializeField]
        private Image _reloadMask = null!;

        [SerializeField]
        private Image[] _abilityIcons = null!;

        [SerializeField]
        private CanvasGroup _abilityIconDuplicatorCanvasGroup = null!;

        [SerializeField]
        [Range(0f, 1f)]
        private float _abilityIconDuplicatorAlpha = 0.3f;
        
        [SerializeField]
        private ChargesContainerAbilityButton _chargesContainerAbilityButton = null!;

        [SerializeField]
        private Animator _animator = null!;
        
        private ILocalGameplayContext _localGameplayContext = null!;
        private ILocalInputContext _localInputContext = null!;
        private UseAbilityButtonAnimationController _animationController = null!;
        
        private DirectionMarker? _directionMarker;

        private bool _isReloading = false;
        private float _playerCollisionRadius;
        private float _startRangeAlpha01;
        private float _previousAvailabilityTimestamp;

        private bool _isDirectional;
        private int? _previousMaxChargeCount;

        private bool _initialized;

        private IDisposable? _configurationSubscription;
        private IDisposable? _reloadTimestampChangeSubscription;
        private IDisposable? _selectionSubscription;
        private IDisposable? _tapSubscription;
        private IDisposable? _stickMovementDisableCancellation;
        private IDisposable? _tickCancellation;

        public CallbackEmitter Callbacks { get; } = new CallbackEmitter();
        public bool IsInteractable => _directionAbilityInputJoystick.Interactable;
        private ITimeProvider TimeProvider => NetworkTimeTimeProvider.Instance;

        private void OnEnable()
        {
            if (!_initialized)
                return;
            
            Setup();
        }

        private void OnDisable()
        {
            Cleanup();
        }

        private void OnDestroy()
        {
            Cleanup();
            _chargesContainerAbilityButton.DeInitialize();
        }
        
        public void Initialize(
            ILocalGameplayContext localGameplayContext,
            ILocalInputContext localInputContext
        )
        {
            _localGameplayContext = localGameplayContext;
            _localInputContext = localInputContext;
            _isDirectional = localGameplayContext.Player.SyncData.Ability.Value.InputType == AbilityInputType.Direction;
            _animationController = new UseAbilityButtonAnimationController(_animator);

            InitializeShared();

            if (_isDirectional)
                InitializeDirectional();

            _initialized = true;
            
            if (isActiveAndEnabled)
                Setup();
        }

        public void SimulateTap()
        {
            if (_isDirectional)
                Callbacks.OnAuto();
            else
                Callbacks.OnInput(AbilityParametersFactory.CreateNoTarget());
        }

        private void Setup()
        {
            _reloadTimestampChangeSubscription = _localGameplayContext.Player.SyncData.Ability.SubscribeAndFire(UpdateCharges); 
            
            _selectionSubscription = _directionAbilityInputJoystick.SubscribeToSelection(OnSelection);
            _tapSubscription = _directionAbilityInputJoystick.SubscribeToTap(SimulateTap);
            
            if (_isDirectional)
                _configurationSubscription = _localGameplayContext.Player.Synchronization.Ability.DirectionalInputConfiguration.SubscribeAndFire(OnAbilityConfigurationChange);

            _tickCancellation = StartCoroutine(TickRoutine()).BuildCancellation(this);
        }

        private void Cleanup()
        {
            if (!_directionMarker.IsDestroyed())
                _directionMarker.gameObject.SetActive(false);
            
            _tickCancellation?.Dispose();
            _tapSubscription?.Dispose();
            _selectionSubscription?.Dispose();
            _reloadTimestampChangeSubscription?.Dispose();
            _configurationSubscription?.Dispose();
            _stickMovementDisableCancellation?.Dispose();
            _animationController.Cleanup();
        }

        private void InitializeShared()
        {
            _startRangeAlpha01 = _rangeImage.color.a;
            _playerCollisionRadius = _localGameplayContext.Player.Collision.Radius;
            _rangeImage.SetAlpha(0f);
            _isReloading = false;
            _styleSetter.Apply(_isReloading);
            SetIcon(JxResourceLoader.Instance.LoadCharacterAbilityIcon(_localGameplayContext.Player.SyncData.Ability.Value.AbilityType));
            _abilityIconDuplicatorCanvasGroup.alpha = 0f;
        }

        private void InitializeDirectional()
        {
            _directionMarker = Instantiate(_directionMarkerPrefab, _localGameplayContext.Player.NetTransform.UnityTransform, worldPositionStays: false);
        }
        
        private IEnumerator TickRoutine()
        {
            while (true)
            {
                yield return null;
                
                var inInteraction = _directionAbilityInputJoystick.InInteraction();
                var dto = _localGameplayContext.Player.SyncData.Ability.Value;

                RenderAnimationOnTick(inInteraction, dto);
                RenderDuplicatorOnTick(inInteraction);
                RenderRangeOnTick(inInteraction);
                RenderDirectionOnTick(inInteraction);

                RenderReloadingOnTick(dto);
            }
        }

        private void RenderAnimationOnTick(bool inInteraction, NetAbilityDto dto)
        {
            var reloaded = dto.AvailabilityTimestamp <= TimeProvider.Now;
            if (inInteraction || !reloaded || !_localGameplayContext.Player.SyncData.Action.Value.HasPermission(ActionPermissions.UseAbility))
            {
                _animationController.SetDefaultState();
                return;
            }
            
            var reloadedRecently = Math.Abs(TimeProvider.Now - dto.AvailabilityTimestamp) < ReloadedAnimationDurationInSec;
            if (reloadedRecently)
            {
                _animationController.SetReloadedRecentlyState();
                return;
            }
            if (!_animationController.IsInReloadedRecentlyState)
            {
                _animationController.SetReloadedState();
            }
        }

        private void RenderDirectionOnTick(bool inInteraction)
        {
            if (_isDirectional && !_directionMarker.IsNullObj())
            {
                if (!inInteraction)
                {
                    _directionMarker!.gameObject.SetActive(false);
                }
                else
                {
                    _directionMarker!.gameObject.SetActive(true);
                    _directionMarker.Adjust(_playerCollisionRadius, _directionAbilityInputJoystick.Direction.normalized);
                }
            }
        }

        private void RenderRangeOnTick(bool inInteraction)
        {
            _rangeImage.SetAlpha(Mathf.Lerp(_rangeImage.color.a, inInteraction ? _startRangeAlpha01 : 0f, Time.deltaTime * 9f));
        }

        private void RenderDuplicatorOnTick(bool inInteraction)
        {
            _abilityIconDuplicatorCanvasGroup.alpha = Mathf.Lerp(
                _abilityIconDuplicatorCanvasGroup.alpha,
                inInteraction ? _abilityIconDuplicatorAlpha : 0f,
                Time.deltaTime * 9f
            );
        }

        private void RenderReloadingOnTick(NetAbilityDto dto)
        {
            if (dto.AvailabilityTimestamp > NetworkTime.time)
            {
                var fillAmount = JxMathf.DivideOrDefault((float)NetworkTime.time - dto.StartReloadTimestamp, dto.AvailabilityTimestamp - dto.StartReloadTimestamp);
                _reloadMask.fillAmount = Mathf.Clamp01(fillAmount);
            }
            else
            {
                _reloadMask.fillAmount = 0f;
            }
        }
        
        private void UpdateCharges(NetAbilityDto dto)
        {
            var isSupportedCharges = dto.ChargingSupported();
            _chargesContainerAbilityButton.gameObject.SetActive(isSupportedCharges);
            if (!isSupportedCharges)
            {
                _previousMaxChargeCount = null;
                _chargesContainerAbilityButton.DeInitialize();
                return;
            }
           
            if (_previousMaxChargeCount != dto.MaxChargeCount)
            {
                _chargesContainerAbilityButton.DeInitialize();
                _chargesContainerAbilityButton.Initialize(_localGameplayContext.Player.SyncData.Ability.Value.MaxChargeCount);
                _previousMaxChargeCount = dto.MaxChargeCount;
            }
            
            _chargesContainerAbilityButton.SetReady(dto.CurrentChargeCount);
        }

        private void SetIcon(Sprite icon)
        {
            foreach (var i in _abilityIcons)
            {
                i.sprite = icon;
            }
        }

        public void SetIsInteractable(bool isInteractable)
        {
            _isReloading = !isInteractable;
            _styleSetter.Apply(_isReloading);

            _directionAbilityInputJoystick.SetInteractable(isInteractable);

            if (isInteractable)
                _stickMovementDisableCancellation?.Dispose();
            else
                _stickMovementDisableCancellation = _directionAbilityInputJoystick.DisableMovement();
        }

        private void OnSelection(Vector2 direction)
        {
            OnSelectionAsync().Forget();
            return;

            async UniTask OnSelectionAsync()
            {
                if (_isDirectional)
                {
                    var dir = new Vector3(direction.x, 0f, direction.y);
                    var localPlayer = _localGameplayContext.Player;
                    
                    var startPosition = localPlayer.NetTransform.Position + dir * localPlayer.Collision.Radius;
                    var targetPosition = startPosition + dir * _directionMarker!.GetHitDistance();

                    SetIsInteractable(false);
                    var inputJoystickIgnoranceHandler = _localInputContext.IgnoreMovement();

                    await _localGameplayContext.Player
                        .NetTransform
                        .UnityTransform
                        .DOLookAt(targetPosition, 0.1f)
                        .AsyncWaitForCompletion();

                    inputJoystickIgnoranceHandler.Dispose();
                    Callbacks.OnInput(AbilityParametersFactory.CreatePointTarget(startPosition, targetPosition));
                }
                else
                {
                    Callbacks.OnInput(AbilityParametersFactory.CreateNoTarget());
                }
            }
        }

        private void OnAbilityConfigurationChange(DirectionalAbilityInputConfiguration configuration)
        {
            if (_isDirectional)
            {
                _directionMarker!.Setup(new ProjectileAbilityCollisionService(_localGameplayContext.Player), configuration);
            }
        }



        #region CLASSES

        public class CallbackEmitter
        {
            private readonly JxObservable<GenericAbilityInputParameters> _abilityInputObservable = new JxObservable<GenericAbilityInputParameters>();
            private readonly JxObservable _autoAbilityUseObservable = new JxObservable();

            public IDisposable SubscribeToAbilityInput(Action<GenericAbilityInputParameters> callback) => _abilityInputObservable.Subscribe(callback);
            public IDisposable SubscribeToAutoAbilityUse(Action callback) => _autoAbilityUseObservable.Subscribe(callback);

            public void OnInput(GenericAbilityInputParameters parameters) => _abilityInputObservable.Invoke(parameters);
            public void OnAuto() => _autoAbilityUseObservable.Invoke();
        }

        [Serializable]
        private class IconHandler
        {
            [SerializeField]
            private Image _mainIcon = null!;

            [SerializeField]
            private Image _reloadIcon = null!;

            public void SetIcon(Sprite icon)
            {
                _mainIcon.sprite = icon;
                _reloadIcon.sprite = icon;
            }
        }

        #endregion
    }
}