using System;
using UnityEngine;

namespace GameplayNetworking.Types
{
    public readonly struct JxUShortVector3 : IEquatable<JxUShortVector3>
    {
        public JxUShortVector3(JxUShortFloat x, JxUShortFloat y, JxUShortFloat z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        public JxUShortVector3(Vector3 value)
            : this(new JxUShortFloat(value.x), new JxUShortFloat(value.y), new JxUShortFloat(value.z)) { }

        public readonly JxUShortFloat X;
        public readonly JxUShortFloat Y;
        public readonly JxUShortFloat Z;
        
        public static implicit operator JxUShortVector3(Vector3 value) => new(value);
        public static implicit operator Vector3(JxUShortVector3 value) => new Vector3(value.X,value.Y, value.Z);
        
        public bool Equals(JxUShortVector3 other) => X.Equals(other.X) && Y.Equals(other.Y) && Z.Equals(other.Z);

        public override bool Equals(object? obj) => obj is JxUShortVector3 other && Equals(other);

        public override int GetHashCode() => HashCode.Combine(X, Y, Z);

        public static bool operator ==(JxUShortVector3 left, JxUShortVector3 right) => left.Equals(right);

        public static bool operator !=(JxUShortVector3 left, JxUShortVector3 right) => !left.Equals(right);
    }
}