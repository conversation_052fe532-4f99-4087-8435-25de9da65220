using System;
using System.Net;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Observable;
using Mirror;

namespace GameplayNetworking.Manager.Network.Client
{
    public interface IMatchNetworkManagerClient : IDisposable
    {
        IJxChangeableObject<bool> IsConnected { get; }
        NetworkAuthenticator Authenticator { get; }

        void Initialize(IPEndPoint connectionPoint, TimeSpan timeout);

        void StartClient();
        void StopClient();
    }
}