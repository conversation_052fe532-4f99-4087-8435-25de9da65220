using System;
using System.Collections.Generic;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Authentication;
using GameplayNetworking.Gameplay.Player.Systems.Shared.Upgrade;
using LoM.Emojis.ClientIntegration;
using LoM.Messaging.ClientIntegrations;
using MonsterLand.Matchmaking;

namespace GameplayNetworking.Manager.Network.Share.Authentication
{
    public class PlayerServerAuthInfo
    {
        public PlayerServerAuthInfo(
            MonsterLandAuthRequestDto request,
            UserDataClientIntegration userData,
            CharacterViewIdentifier characterIdentifier
        )
        {
            UserToken = request.UserToken;
            UserData = userData;
            Team = request.Team;
            CharacterIdentifier = characterIdentifier;
            CharacterLevel = request.CharacterLevel;
            ProfileLevel = request.ProfileLevel;
            CharacterTrophies = request.CharacterTrophies;
            TicketId = request.TicketId;
            CharacterStats = request.CharacterStats;
            EquippedEmojis = request.EquippedEmojis;
        }

        public string UserToken { get; }
        public UserDataClientIntegration UserData { get; }
        public JxMatchmakingTeamKind Team { get; }
        public Guid TicketId { get; }
        public CharacterViewIdentifier CharacterIdentifier { get; private set; }
        public int CharacterLevel { get; private set; }
        public int ProfileLevel { get; }
        public int CharacterTrophies { get; private set; }
        public IReadOnlyList<NetCharacterStatDto> CharacterStats { get; }
        public IReadOnlyList<EmojiType> EquippedEmojis { get; private set; }
    }
}