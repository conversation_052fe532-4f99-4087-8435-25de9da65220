using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay.Systems;
using Jx.Utils.Collections;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Components.Indicators;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation
{
    public class IndicatorsGameplaySystem : AbstractMatchGameplaySystem
    {
        private readonly JxDisposableAction _disposableAction;

        [Preserve]
        public IndicatorsGameplaySystem(MatchGameplaySystemContext context)
            : base(context)
        {
            _disposableAction = JxDisposableAction.Build();
        }

        public override UniTask LaunchAsync(CancellationToken cancellationToken)
        {
            Context.Gameplay.Spawned.Campfires.ForEach(RegisterCampfireIndication);
            return UniTask.CompletedTask;
        }

        public override UniTask DeInitializeAsync()
        {
            _disposableAction.Dispose();
            return UniTask.CompletedTask;
        }

        private void RegisterCampfireIndication(NetCampfire campfire)
        {
            _disposableAction.AppendDispose(
                campfire.MinigameFailedObservable.Subscribe(
                    () =>
                    {
                        Context.Gameplay.Spawned.Catchers.ForEach(
                            catcher => catcher.Indicator.ShowTemporary(IndicationType.CalibrationFailed, campfire.netIdentity)
                        );
                    }
                )
            );
        }
    }
}