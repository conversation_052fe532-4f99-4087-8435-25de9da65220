using System;
using System.Collections.Generic;
using GameplayComponents.Bots;
using GameplayComponents.Bots.ConcreteBehaviours.Catcher;
using GameplayComponents.Bots.ConcreteBehaviours.Escapee;
using GameplayNetworking.Share.Character;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots
{
    public static class BotBehaviourMapping
    {
        private static readonly Type _fallbackEscapeeType;
        private static readonly Type _fallbackCatcherType;

        private static readonly IReadOnlyDictionary<EscapeeType, Type> _escapees;
        private static readonly IReadOnlyDictionary<CatcherType, Type> _catchers;

        static BotBehaviourMapping()
        {
            _fallbackEscapeeType = typeof(BotEscapeeBehaviour);
            _fallbackCatcherType = typeof(BotCatcherBehaviour);

            _escapees = new Dictionary<EscapeeType, Type>
            {
                [EscapeeType.Golem] = typeof(BotGolemEarthBehaviour),
                [EscapeeType.Rab] = typeof(BotRabbitBehaviour),
                [EscapeeType.Spidi] = typeof(BotSpiderBehaviour),
                [EscapeeType.Sup] = typeof(BotAngelBehaviour),
                [EscapeeType.Chamie] = typeof(BotChamieBehaviour),
                [EscapeeType.Marty] = typeof(BotMartyBehaviour),
                [EscapeeType.Electra] = typeof(BotElectraBehaviour),
                [EscapeeType.Lucky] = typeof(BotLuckyBehaviour),
                [EscapeeType.Fay] = typeof(BotFayBehaviour),
                [EscapeeType.Pango] = typeof(BotPangoBehaviour),
                [EscapeeType.Robby] = typeof(BotRobbyBehaviour),
                [EscapeeType.Gor] = typeof(BotGorBehaviour),
                [EscapeeType.Liz] = typeof(BotLizBehaviour),
                [EscapeeType.Trickie] = typeof(BotTrickieBehaviour),
            };

            _catchers = new Dictionary<CatcherType, Type>
            {
                [CatcherType.Echo] = typeof(BotBatLordBehaviour),
                [CatcherType.Goliath] = typeof(BotSkeletonGiantBehaviour),
                [CatcherType.Wiz] = typeof(BotSkeletonMageBehaviour),
                [CatcherType.Ripp] = typeof(BotReaperBehaviour),
                [CatcherType.Spec] = typeof(BotDeathMageBehaviour),
                [CatcherType.Phantom] = typeof(BotPhantomBehaviour),
                [CatcherType.Witch] = typeof(BotWitchBehaviour),
            };
        }

        public static Type? FindBotBehaviourType(int characterIndex)
        {
            if (CharacterTypeHelper.TryGetCatcherTypeFromIndex(characterIndex, out var catcher))
                return _catchers.GetValueOrDefault(catcher, _fallbackCatcherType);
            if (CharacterTypeHelper.TryGetEscapeeTypeFromIndex(characterIndex, out var escapee))
                return _escapees.GetValueOrDefault(escapee, _fallbackEscapeeType);

            return null;
        }
    }
}