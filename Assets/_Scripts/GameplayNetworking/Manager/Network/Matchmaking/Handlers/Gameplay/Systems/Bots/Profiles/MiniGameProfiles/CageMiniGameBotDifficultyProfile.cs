using Newtonsoft.Json;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles.MiniGameProfiles
{
    [JsonObject(MemberSerialization.OptIn)]
    public class CageMiniGameBotDifficultyProfile : IMiniGameBotDifficultyProfile
    {
        [JsonProperty("useKeyProbability01")]
        public float UseKeyProbability01 { get; set; }
        
        [JsonProperty("useKeyDelayInMills")]
        public int UseKeyDelayInMills { get; set; }
        
        [JsonProperty("chunkPerfectProbability01")]
        public float ChunkPerfectSuccessProbability { get; set; }
        
        [JsonProperty("chunkSuccessProbability")]
        public float ChunkSuccessProbability { get; set; }
        
        [JsonProperty("additionalStartDelayInMills")]
        public int AdditionalStartDelayInMills { get; set; }
    }
}