using System;
using System.Threading;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.SceneObjects.Static.Obstacle;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems;
using UnityEngine;

namespace GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay.Systems.Obstacles
{
    public class DestroyedObstacleRestorationGameplaySystem : AbstractMatchGameplaySystem
    {
        private const float PlayerVisionRadius = 90f; //74f // ~ max camera view field radius(не забываем, что когда в клетке, видно выше
        private const float Delay = 40f;
        private const int MaxRestoredPerIteration = 1;

        private readonly CancellationTokenSource _cancellationTokenSource;

        public DestroyedObstacleRestorationGameplaySystem(MatchGameplaySystemContext context)
            : base(context)
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public override UniTask LaunchAsync(CancellationToken cancellationToken)
        {
            StartAsync(cancellationToken).Forget();
            return UniTask.CompletedTask;
        }

        public override UniTask DeInitializeAsync()
        {
            _cancellationTokenSource.CancelAndDispose();
            return UniTask.CompletedTask;
        }

        private async UniTask StartAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            var delay = TimeSpan.FromSeconds(Delay);
            
            while (!cancellationToken.IsCancellationRequested)
            {
                await UniTask.Delay(delay);
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                await RestoreObstaclesAsync(cancellationToken);
            }
        }

        private async UniTask RestoreObstaclesAsync(CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            
            var restoredObstacles = 0;
            
            foreach (var obstacle in Context.Gameplay.Spawned.Obstacles)
            {
                if (!await TryRestoreObstacleAsync(obstacle, cancellationToken))
                    continue;
                
                ++restoredObstacles;
                if (restoredObstacles >= MaxRestoredPerIteration)
                    break;
            }
        }

        private async UniTask<bool> TryRestoreObstacleAsync(NetObstacle obstacle, CancellationToken cancellationToken)
        {
            if (obstacle.State.Value != ObstacleState.Destroyed)
                return false;

            // throttling
            await UniTask.Delay(TimeSpan.FromSeconds(1f));
            
            if (cancellationToken.IsCancellationRequested)
                return false;
            
            foreach (var player in Context.Gameplay.Spawned.Players)
            {
                var isVisible = Vector3.SqrMagnitude(player.NetTransform.Position - obstacle.transform.position) <= PlayerVisionRadius;
                if (isVisible)
                    return false;
            }
            
            obstacle.SetState(ObstacleState.Spawned);
            return true;
        }
    }
}