using System;
using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Data;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Helpers;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.FinishCheck
{
    public class ZeroActiveEscapeeFinishCheckHandler : FinishCheckHandler
    {
        private IDisposable? _subscriptions;

        public ZeroActiveEscapeeFinishCheckHandler(FinishCheckHandlerContext context)
            : base(context)
        {
        }

        protected override void OnInitialize()
        {
            var subscriptions = JxDisposableAction.Build();

            foreach (var escapee in GameplayContext.Spawned.Escapees)
                subscriptions.AppendDispose(escapee.BaseSyncData.Action.Subscribe(OnAnyEscapeeActionChanged));

            _subscriptions = subscriptions;
            OnAnyEscapeeActionChanged();
        }

        protected override void OnDispose() => _subscriptions?.Dispose();

        private void OnAnyEscapeeActionChanged()
        {
            var escapees = GameplayContext.Spawned.Escapees;
            
            foreach (var escapee in escapees)
                if (escapee.BaseSyncData.Action.Value.IsPlaying())
                    return;
            
            FinishEscapees(escapees);
        }

        private void FinishEscapees(IReadOnlyList<NetEscapeePlayer> escapees)
        {
            foreach (var escapee in escapees)
            {
                if (escapee.SyncData.State.Value == GameplayPlayerState.InGame)
                    SetEscapeeFinishedState(escapee, isWinner: false);
            }
        }
    }
}