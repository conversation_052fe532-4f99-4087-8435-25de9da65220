using System.Threading;
using App.Inventory.Item;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay.Systems;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Inventory.Controller;
using Jx.Utils.Collections;
using Jx.Utils.Helpers;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Inventory
{
    [Preserve]
    public class InventoryMatchGameplaySystem : AbstractMatchGameplaySystem
    {
        private readonly JxDisposableAction _playerInventorySubscriptions;
        private readonly InventoryItemControllersSystem _inventoryItemControllersSystem;

        private TrapSpawner _trapSpawner = null!;

        [Preserve]
        public InventoryMatchGameplaySystem(MatchGameplaySystemContext context)
            : base(context)
        {
            _inventoryItemControllersSystem = new InventoryItemControllersSystem(Context.GameplayConfig);
            _inventoryItemControllersSystem = new InventoryItemControllersSystem(Context.GameplayConfig);
            _playerInventorySubscriptions = JxDisposableAction.Build();
        }

        public override UniTask LaunchAsync(CancellationToken cancellationToken)
        {
            _trapSpawner = new TrapSpawner(
                Context.DynamicSceneObjectFactory,
                Context.GameplayConfig,
                Context.Match.Context,
                Context.Gameplay
            );

            _inventoryItemControllersSystem.Initialize();
            Context.Gameplay.Spawned.Players.ForEach(SubscribeToPlayerInventory);

            return UniTask.CompletedTask;
        }

        public override UniTask DeInitializeAsync()
        {
            _inventoryItemControllersSystem.DeInitialize();
            _playerInventorySubscriptions.Dispose();

            return UniTask.CompletedTask;
        }

        private void SubscribeToPlayerInventory(NetGamePlayer player)
        {
            _playerInventorySubscriptions.AppendDispose(player.BaseSyncData.OnInventoryItemUsed.Subscribe(id => OnItemConsumed(player, id)));
        }

        private void OnItemConsumed(NetGamePlayer player, InventoryItemSlot slot)
        {
            var itemType = slot.ItemType;
            if (itemType.IsTrap())
                _trapSpawner.Spawn(player, itemType);

            _inventoryItemControllersSystem.Execute(player, slot);
        }
    }
}