using System;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.FinishCheck;
using Jx.Utils.Threading;

namespace GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay.Systems.FinishCheck.MatchDurationProlongation
{
    public abstract class MatchProlongation : IMatchProlongation
    {
        private readonly JxAtomicFlag _timeAdded;

        protected MatchProlongation(FinishCheckHandlerContext context)
        {
            Context = context;
            
            _timeAdded = false;
        }

        protected FinishCheckHandlerContext Context { get; }
      
        public void Initialize()
        {
            _timeAdded.TryReset();
            OnInitialize();
        }
        
        public virtual void Dispose() { }

        protected abstract void OnInitialize();

        protected void AddTime(TimeSpan duration)
        {
            if (!_timeAdded.TrySet())
                return;
            
            Context.MatchSyncData.Timing.ChangeFromServer(t => new NetMatchTimingDto(t.StartTime, t.EndTime + duration.TotalSeconds));
        }
    }
}