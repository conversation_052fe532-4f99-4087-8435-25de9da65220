using Configs.ServerGameplay.Presentation.CatcherRage;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.CatcherRage.Processors
{
    public class CatcherRageServerProcessorContext : ICatcherRageServerProcessorContext
    {
        public IGameplayServerContext GameplayContext { get; set; } = null!;
        public CatcherRageConfig Config { get; set; } = null!;
    }
}