using System;
using System.Collections.Generic;
using System.Linq;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using GameplayNetworking.Gameplay.SceneObjects.Static.Cage;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using GameplayNetworking.Gameplay.SceneObjects.Static.InventoryItems;
using GameplayNetworking.Gameplay.SceneObjects.Static.Obstacle;
using GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay;
using Mirror;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning
{
    public class GameplaySpawnedServerContext : IGameplaySpawnedServerContext
    {
        private readonly List<NetGamePlayer> _players = new List<NetGamePlayer>();
        private readonly List<NetCatcherPlayer> _catchers = new List<NetCatcherPlayer>();
        private readonly List<NetEscapeePlayer> _escapees = new List<NetEscapeePlayer>();
        private readonly List<NetCampfire> _campfires = new List<NetCampfire>();
        private readonly List<NetCage> _cages = new List<NetCage>();
        private readonly List<NetObstacle> _obstacles = new List<NetObstacle>();
        private readonly List<NetGate> _gates = new List<NetGate>();
        private readonly List<NetTeleport> _teleport = new List<NetTeleport>();
        private readonly List<NetInventoryChest> _inventoryChests = new List<NetInventoryChest>();
        private readonly InteractionRepository _interactions = new InteractionRepository();

        public IReadOnlyList<NetGamePlayer> Players => _players;
        public IEnumerable<NetGamePlayer> RealPlayers => _players.Where(p => !p.IsBot());
        public IReadOnlyList<NetCatcherPlayer> Catchers => _catchers;
        public IReadOnlyList<NetEscapeePlayer> Escapees => _escapees;
        public IReadOnlyList<NetCampfire> Campfires => _campfires;
        public IReadOnlyList<NetCage> Cages => _cages;
        public IReadOnlyList<NetObstacle> Obstacles => _obstacles;
        public IReadOnlyList<NetGate> Gates => _gates;
        public IReadOnlyList<NetTeleport> Teleports => _teleport;
        public IReadOnlyList<NetInventoryChest> InventoryChests => _inventoryChests;
        public IInteractionRepository Interactions => _interactions;
        public MatchSyncData MatchData { get; private set; } = null!;

        public void HandleIdentitySpawn(NetworkIdentity identity)
        {
            _interactions.Register(identity);

            if (identity.TryGetComponent<NetGamePlayer>(out var player))
            {
                _players.Add(player);

                switch (player)
                {
                    case NetEscapeePlayer escapee:
                        _escapees.Add(escapee);
                        return;
                    case NetCatcherPlayer catcher:
                        _catchers.Add(catcher);
                        return;
                    default:
                        throw new ArgumentException($"invalid player type {player.GetType()}");
                }
            }

            if (identity.TryGetComponent<NetInteractableSceneObject>(out var interactableSceneObject))
            {
                switch (interactableSceneObject)
                {
                    case NetCampfire campfire:
                        _campfires.Add(campfire);
                        return;
                    case NetCage cage:
                        _cages.Add(cage);
                        return;
                    case NetObstacle obstacle:
                        _obstacles.Add(obstacle);
                        return;
                    case NetGate gate:
                        _gates.Add(gate);
                        return;
                    case NetTeleport teleport:
                        _teleport.Add(teleport);
                        return;
                    case NetInventoryChest chest:
                        _inventoryChests.Add(chest);
                        return;
                    default:
                        throw new ArgumentException($"invalid interactable object type {interactableSceneObject.GetType()}");
                }
            }
        }

        public void HandleIdentityDeSpawn(NetworkIdentity identity)
        {
            if (identity.TryGetComponent<NetGamePlayer>(out var player))
            {
                _players.Remove(player);

                switch (player)
                {
                    case NetEscapeePlayer escapee:
                        _escapees.Remove(escapee);
                        return;
                    case NetCatcherPlayer catcher:
                        _catchers.Remove(catcher);
                        return;
                    default:
                        throw new ArgumentException($"invalid player type {player.GetType()}");
                }
            }

            if (identity.TryGetComponent<NetInteractableSceneObject>(out var interactableSceneObject))
            {
                switch (interactableSceneObject)
                {
                    case NetCampfire campfire:
                        _campfires.Remove(campfire);
                        return;
                    case NetCage cage:
                        _cages.Remove(cage);
                        return;
                    case NetObstacle obstacle:
                        _obstacles.Remove(obstacle);
                        return;
                    case NetGate gate:
                        _gates.Remove(gate);
                        return;
                    case NetTeleport teleport:
                        _teleport.Remove(teleport);
                        return;
                    case NetInventoryChest chest:
                        _inventoryChests.Remove(chest);
                        return;
                    default:
                        throw new ArgumentException($"invalid interactable object type {interactableSceneObject.GetType()}");
                }
            }

            _interactions.UnRegister(identity);
        }
    }
}