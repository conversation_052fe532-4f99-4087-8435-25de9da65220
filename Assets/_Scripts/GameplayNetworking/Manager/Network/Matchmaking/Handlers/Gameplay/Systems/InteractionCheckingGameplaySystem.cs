using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Manager.Network.Matchmaking.Handlers.Gameplay.Systems;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation
{
    public class InteractionCheckingGameplaySystem : AbstractMatchGameplaySystem
    {
        private IDisposable? _checkCancellation;

        [Preserve]
        public InteractionCheckingGameplaySystem(MatchGameplaySystemContext context)
            : base(context) { }

        public override async UniTask LaunchAsync(CancellationToken cancellationToken)
        {
            _checkCancellation = await Context.InteractionCheckingTaskRunner.RegisterAsync(new InteractionCheckingMatchContext(Context.Gameplay));
        }

        public override UniTask DisableAsync()
        {
            _checkCancellation?.Dispose();
            return base.DisableAsync();
        }

        public override UniTask DeInitializeAsync()
        {
            _checkCancellation?.Dispose();
            return UniTask.CompletedTask;
        }
    }
}