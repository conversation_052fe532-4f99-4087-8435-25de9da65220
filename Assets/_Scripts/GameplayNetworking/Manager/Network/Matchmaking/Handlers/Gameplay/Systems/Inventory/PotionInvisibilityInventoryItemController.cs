using System;
using App.Inventory.Item;
using Configs.ServerGameplay;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Data;
using Jx.Utils.ChangeableObjects;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Inventory.Controller.Concrete
{
    public class PotionInvisibilityInventoryItemController : InventoryItemController
    {
        public PotionInvisibilityInventoryItemController(IServerGameplayConfig serverGameplayConfig) : base(serverGameplayConfig)
        {
        }

        protected override InventoryItemType SupportedItemType => InventoryItemType.PotionInvisibility;

        public override void Execute(NetGamePlayer player)
        {
            IDisposable? buffCancellation = null;
            IDisposable? actionSubscription = null!;

            buffCancellation = player.Buff.Activate(BuffType.PotionInvisibility, BasePotionConfig.Duration, onComplete: () => actionSubscription?.Dispose());
            actionSubscription = player.BaseSyncData.Action.SubscribeAndFire(OnActionChanged);
            
            return;

            void OnActionChanged(ActionType action)
            {
                if (action.HasPermission(ActionPermissions.Invisibility))
                    return;
                
                buffCancellation?.Dispose();
                actionSubscription?.Dispose();
            }
        }
    }
}