using App.Inventory.Item;
using Configs.ServerGameplay;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Buffs;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Inventory.Controller.Concrete
{
    public class PotionSpeedUpInventoryItemController : InventoryItemController
    {
        public PotionSpeedUpInventoryItemController(IServerGameplayConfig serverGameplayConfig) : base(serverGameplayConfig)
        {
        }

        protected override InventoryItemType SupportedItemType => InventoryItemType.PotionSpeedUp;

        public override void Execute(NetGamePlayer player)
        {
            player.Buff.Activate(BuffType.PotionSpeedUp, BasePotionConfig.Duration);
        }
    }
}