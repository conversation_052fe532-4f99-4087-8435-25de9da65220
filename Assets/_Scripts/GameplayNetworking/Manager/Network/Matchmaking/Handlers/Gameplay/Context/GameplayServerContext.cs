using System;
using GameplayNetworking.Gameplay.Components.Server.Map;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Slot;
using HeadlessOnly.Metrics.Ping;
using Jx.Utils.Collections;
using Jx.Utils.Objects;
using Mirror;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context
{
    public class GameplayServerContext : IGameplayServerContext,
                                         IDisposable
    {
        private readonly MatchInterestManagement _matchInterestManagement;
        private readonly Guid _matchId;

        private readonly GameplaySpawnedServerContext _spawned = new GameplaySpawnedServerContext();

        [Preserve]
        public GameplayServerContext(Guid matchId, IMapInfo mapInfo, MatchSyncData matchSyncData, MatchSlotStorage slotStorage)
        {
            _matchId = matchId;
            MapInfo = mapInfo;
            MatchSyncData = matchSyncData;
            SlotStorage = slotStorage;
            _matchInterestManagement = NetworkServer.aoi.Cast<MatchInterestManagement>();

            _matchInterestManagement.MatchObjectSpawnedEvent.AddListener(HandleIdentitySpawn);
            _matchInterestManagement.MatchObjectDeSpawnedEvent.AddListener(HandleIdentityDeSpawn);

            if (_matchInterestManagement.MatchObjects.ContainsKey(matchId))
                _matchInterestManagement.MatchObjects[matchId].ForEach(identity => HandleIdentitySpawn(matchId, identity));

            PingTracker = new HeadlessMatchPingTracker();
        }

        public IGameplaySpawnedServerContext Spawned => _spawned;
        public IMapInfo MapInfo { get; }
        public MatchSyncData MatchSyncData { get; }
        public MatchSlotStorage SlotStorage { get; }
        public HeadlessMatchPingTracker PingTracker { get; }

        public void Dispose()
        {
            _matchInterestManagement.MatchObjectSpawnedEvent.RemoveListener(HandleIdentitySpawn);
            _matchInterestManagement.MatchObjectDeSpawnedEvent.RemoveListener(HandleIdentityDeSpawn);
        }

        private void HandleIdentitySpawn(Guid matchId, NetworkIdentity identity)
        {
            if (_matchId != matchId)
                return;
            
            _spawned.HandleIdentitySpawn(identity);
        }

        private void HandleIdentityDeSpawn(Guid matchId, NetworkIdentity identity)
        {
            if (_matchId != matchId)
                return;
            
            _spawned.HandleIdentityDeSpawn(identity);
        }
    }
}