using App.Inventory.Item;
using Configs.ServerGameplay;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Buffs;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Inventory.Controller.Concrete
{
    public class PotionPowerInventoryItemController : InventoryItemController
    {
        public PotionPowerInventoryItemController(IServerGameplayConfig serverGameplayConfig) : base(serverGameplayConfig)
        {
        }

        protected override InventoryItemType SupportedItemType => InventoryItemType.PotionPower;

        public override void Execute(NetGamePlayer player)
        {
            player.Buff.Activate(BuffType.PotionPower, BasePotionConfig.Duration);
        }
    }
}