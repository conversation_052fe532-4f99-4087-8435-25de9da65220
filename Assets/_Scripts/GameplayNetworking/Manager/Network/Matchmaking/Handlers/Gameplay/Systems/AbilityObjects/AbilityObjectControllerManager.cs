using System;
using Audio.Manager.Handler;
using Configs.ServerGameplay;
using GameplayNetworking.Gameplay.SceneObjects.Static.Factory;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.AbilityObjects.Controllers;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.AbilityObjects
{
    public class AbilityObjectControllerManager : IDisposable
    {
        private readonly AbilityObjectPool _pool;

        public AbilityObjectControllerManager(
            IGameplayServerContext gameplayContext,
            IServerGameplayConfig gameplayConfig
        )
        {
            _pool = new AbilityObjectPool(new AbilityObjectControllerFactory(gameplayContext, gameplayConfig));
        }

        public void Dispose() => _pool.Dispose();

        public IAbilityObjectController GetOrThrow(AbilityObjectSpawnInfo info) => _pool.Get(info);
    }
}