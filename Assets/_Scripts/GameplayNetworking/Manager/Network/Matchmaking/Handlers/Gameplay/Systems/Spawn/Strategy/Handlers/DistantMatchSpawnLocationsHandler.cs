using System.Collections.Generic;
using System.Linq;
using GameplayNetworking.Gameplay.Components.Server.Map.Locations;
using Jx.Utils.Collections;
using UnityEngine;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Spawn.Strategy.Handlers
{
    public class DistantMatchSpawnLocationsHandler
    {
        public DistantMatchSpawnLocationsHandler()
        {
        }

        public IEnumerable<LocationData> EnumerateFarthestFromTargets(
            IEnumerable<LocationData> originLocations,
            IEnumerable<LocationData> targetLocations
        )
        {
            return originLocations
               .OrderByDescending(e => Vector3.SqrMagnitude(e.Position - targetLocations.RandomOrDefault().Position));
        }

        public IEnumerable<LocationData> EnumerateDistantFromTargets(
            IEnumerable<LocationData> origins,
            IEnumerable<LocationData> targets,
            float minSqrDistance
        )
        {
            minSqrDistance = Mathf.Max(1f, minSqrDistance);

            targets = targets.ToList(); // иначе mulpliple enumeration

            return origins
                  .Where(origin => targets.All(t => Vector3.SqrMagnitude(t.Position - origin.Position) >= minSqrDistance))
                  .ToList();
        }
    }
}