using System.Collections.Generic;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Lobby.Systems.Implementation.BotConnection.Factory.Providers.Nicknames
{
    public static class HardcodedBotNicknameList
    {
         public static readonly IList<string> ExplicitBotNicknameList = new List<string>
        {
            "<PERSON><PERSON>_<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>_Bunny8",
            "<PERSON><PERSON><PERSON>Je<PERSON>",
            "Bot_Pogue",
            "Bot_Mirrox",
            "Bot_SmoKKeR",
            "Bo<PERSON>_Cherry",
        };

        public static readonly IList<string> ImplicitBotNicknameList = new List<string>
        {
            "Guest_75c4",
            "Guest_664f",
            "Guest_a853",
            "Guest_943e",
            "Guest_d763",
            "Guest_86b2",
            "Guest_952f",
            "Guest_8710",
            "Guest_b961",
            "Guest_9e70",
            "Guest_65a5",
            "Guest_7072",
            "Guest_8e80",
            "Guest_5f56",
            "Guest_a673",
            "Guest_d781",
            "Guest_b890",
            "Guest_5e65",
            "Guest_68e2",
            "Guest_790f",
            "Guest_4a57",
            "Guest_574a",
            "Guest_6910",
            "Guest_466f",
            "Guest_5d83",
            "Guest_a475",
            "Guest_5f92",
            "Guest_4804",
            "Guest_493f",

            "Guest_a457",
            "Guest_64e6",
            "Guest_85b3",
            "Guest_f493",
            "Guest_76a3",
            "Guest_a862",
            "Guest_9b52",
            "Guest_8c71",
            "Guest_961d",
            "Guest_e970",
            "Guest_6a55",
            "Guest_7702",
            "Guest_88b0",
            "Guest_c556",
            "Guest_67d3",
            "Guest_781e",
            "Guest_890f",
            "Guest_56a5",
            "Guest_b682",
            "Guest_7c90",
            "Guest_457d",
            "Guest_e574",
            "Guest_6091",
            "Guest_46f6",
            "Guest_58a3",
            "Guest_4b75",
            "Guest_592c",
            "Guest_4084",
            "Guest_d493",
            // "Hightower",
            // "PapaSmurf",
            "miggy",
            "Butcher",
            // "Pepper",
            "Becky",
            "Houston",
            // "Pinball",
            "Genius",
            // "Hyper",
            "Hobo",
            "Jigsaw",
            "Prometheus",
            "void",
            "Joker",
            "pSycho",
            "King",
            // "Judge",
            // "Pusher",
            // "Bitmap",
            "Scarlett",
            "Riff",
            "Blister",
            "kalie",
            "jojon",
            "Bowie",
            "dagonn",
            "Rooster",
            // "Bowler",
            // "Kickstart",
            "Larry",
            // "Breadmaker",
            "proGamer",
            "Scrapper",
            "Broomspun",
            // "Kingfisher",
            "Screwtape",
            // "Buckshot",
            "Newbie7",
            // "Marlone",
            // "Bugger",
            // "Knuckles",
            "royal",
        };
    }
}