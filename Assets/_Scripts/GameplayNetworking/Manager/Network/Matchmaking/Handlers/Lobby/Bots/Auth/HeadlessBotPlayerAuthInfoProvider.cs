#if HEADLESS
using Configs.Entities;
using Core.Helpers.SceneManagement;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Lobby.Systems.Implementation.BotConnection.Factory.Providers.Nicknames;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.CharacterPath;
using MonsterLand.Meta.Api;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Lobby.Systems.Implementation.BotConnection.Factory.Providers.Auth
{
    public class HeadlessBotPlayerAuthInfoProvider : BotPlayerAuthInfoProvider
    {
        private readonly IJxMonsterLandMetaServerApi _serverApi;

        [Preserve]
        public HeadlessBotPlayerAuthInfoProvider(
            IBotNicknameProvider nicknameProvider,
            IMonsterLandSceneManager sceneManager,
            IJxMonsterLandMetaServerApi serverApi
        )
            : base(nicknameProvider, sceneManager)
        {
            _serverApi = serverApi;
        }

        protected override CharacterPathConfigClientIntegration? GetCharacterPathConfig()
        {
            return _serverApi.Configuration?.Upgrades.CharacterPath;
        }
    }
}
#endif