using Cysharp.Threading.Tasks;
using Jx.Utils.Network;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Server.Runner.Configuration
{
    public class OfflineGameServerHostConfigurationProvider : IGameServerHostConfigurationProvider
    {
        [Preserve]
        public OfflineGameServerHostConfigurationProvider() { }

        public UniTask<IGameServerHostConfiguration> GetAsync() =>
            UniTask.FromResult<IGameServerHostConfiguration>(new GameServerHostConfiguration(JxNetworkHelper.GetRandomAvailablePort(7778)));
    }
}