using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Core.Helpers.SceneManagement;
using Core.Helpers.SceneManagement.TransitionParameters;
using Core.Managers.MapSelection;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Manager.Network.Matchmaking;
using GameplayNetworking.Manager.Network.Server.Matchmaking.Service.Storage;
using GameplayNetworking.Manager.Network.Share.Authentication;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using Jx.Utils.Logging;
using Mirror;
using MonsterLand.Matchmaking;
using MonsterLand.Matchmaking.Dto;
using MonsterLand.Matchmaking.Dto.Maps;
using UnityEngine.SceneManagement;
using UnityEngine.Scripting;

namespace GameplayNetworking.Manager.Network.Server.Matchmaking.Service
{
    public class MatchManager : IMatchManager
    {
        private readonly IJxLogger _logger;
        private readonly Match.Factory _matchFactory;
        private readonly IMatchStorage _matchStorage;
        private readonly ILocalMapProvider _localMapProvider;
        private readonly IDictionary<NetworkConnectionToClient, Match> _matchByClientConnection;

        private bool _allBotsMatchesStarted;

        [Preserve]
        public MatchManager(Match.Factory matchFactory, ILocalMapProvider localMapProvider)
        {
            _logger = JxLoggerFactory.CreateLogger(GetType().Name);
            _matchFactory = matchFactory;
            _localMapProvider = localMapProvider;
            _matchStorage = new MatchStorage();
            _matchByClientConnection = new Dictionary<NetworkConnectionToClient, Match>();
            _allBotsMatchesStarted = false;
        }

        public int CurrentMatchCount => _matchStorage.Count;
        
        public UniTask InitializeAsync() => UniTask.CompletedTask;

        public async UniTask<bool> AllocateMatchAsync(MatchParameters parameters)
        {
            JxNetworkServer.Instance.EnsureIsActive();

            if (_matchStorage.Contains(parameters.MatchId))
            {
                _logger.LogError($"Requested match already allocated '{parameters.MatchId}'");
                return false;
            }

            var match = _matchFactory.Create(parameters);
            await match.InitializeAsync(OnReadyToDestroy);

            NetworkServer.Spawn(match.gameObject);

            _matchStorage.Add(match);

            return true;
        }

        public async UniTask<MatchJoinStatus> JoinPlayerAsync(NetworkConnectionToClient connection, PlayerServerAuthInfo authInfo)
        {
            JxNetworkServer.Instance.EnsureIsActive();

            var joinStatus = await JoinPlayerInternalAsync(connection, authInfo);

#if UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_ANDROID
            if (joinStatus != MatchJoinStatus.Joined)
            {
                await AllocateLocalMatchAsync(authInfo);
                joinStatus = await JoinPlayerInternalAsync(connection, authInfo);
            }
#endif

            return joinStatus;
        }

        public bool TryGetClientMatch(NetworkConnectionToClient connection, out Match match) => _matchByClientConnection.TryGetValue(connection, out match);

        public async UniTask<(bool success, JxMatchmakingTeamKind team, Guid matchId, JxMonsterLandGameModeKind gameMode)> CanReconnectAsync(Guid tickedId)
        {
            JxNetworkServer.Instance.EnsureIsActive();

            foreach (var match in _matchStorage.All())
            {
                if (!match.CanReconnect(tickedId, out var team))
                    continue;

                return (true, team, match.Context.Id, match.Context.GameMode);
            }

            return (false, default, default, default);
        }

        public UniTask DisconnectClientAsync(NetworkConnectionToClient connection)
        {
            JxNetworkServer.Instance.EnsureIsActive();

            if (_matchByClientConnection.TryGetValue(connection, out var matchHandler))
            {
                matchHandler.DisconnectClient(connection);
                _matchByClientConnection.Remove(connection);
            }

            return UniTask.CompletedTask;
        }

        public async UniTask DeInitializeAsync()
        {
            foreach (var match in _matchStorage.All())
                await match.DestroyAsync();

            _matchStorage.Flush();
        }

        private void OnReadyToDestroy(Match match) => _matchStorage.TryRemove(match.Context.Id);

        private UniTask<MatchJoinStatus> JoinPlayerInternalAsync(NetworkConnectionToClient connection, PlayerServerAuthInfo authInfo)
        {
            foreach (var match in _matchStorage.All())
            {
                if (!match.TryJoinClient(new MatchClientInfo(authInfo, connection)))
                    continue;

                _matchByClientConnection.Add(connection, match);
                return UniTask.FromResult(MatchJoinStatus.Joined);
            }

            return UniTask.FromResult(MatchJoinStatus.NoSuitableMatch);
        }

        private async UniTask AllocateLocalMatchAsync(PlayerServerAuthInfo hostPlayerAuthInfo)
        {
            var gameMode = GetLocalGameMode();

            var allocated = await AllocateMatchAsync(
                new MatchParameters(
                    Guid.NewGuid(),
                    gameMode,
                    _localMapProvider.Get(gameMode),
                    new List<MatchPlayerConnectionInfo>
                        { new(hostPlayerAuthInfo.TicketId, hostPlayerAuthInfo.Team == JxMatchmakingTeamKind.Catcher), }
                )
            );

            if (!allocated)
            {
                _logger.LogError("Local match allocation failed");
            }
        }

        public void CreateAllBotsMatchesInBackground(
            int initialMatchesCount,
            TimeSpan delayBetweenNewMatchCreations,
            int newMatchesCount,
            CancellationToken cancellationToken
        )
        {
            if (IsTutorialScene())
                return;

            if (_allBotsMatchesStarted)
                return;

            _allBotsMatchesStarted = true;

            if (cancellationToken.IsCancellationRequested)
                return;

            UniTask.Create(
                () => StartBotMatchCreationInfiniteLoop(
                    initialMatchesCount,
                    delayBetweenNewMatchCreations,
                    newMatchesCount,
                    cancellationToken
                )
            );
        }

        private async UniTask StartBotMatchCreationInfiniteLoop(
            int initialMatchesCount,
            TimeSpan delayBetweenNewMatchCreations,
            int newMatchesCount,
            CancellationToken cancellationToken
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            try
            {
                for (var i = 0; i < initialMatchesCount; ++i)
                    await AllocateBotOnlyMatchAsync();

                while (true)
                {
                    await UniTask.Delay(delayBetweenNewMatchCreations, DelayType.DeltaTime, cancellationToken: cancellationToken);

                    for (var i = 0; i < newMatchesCount; ++i)
                        await AllocateBotOnlyMatchAsync();
                }
            }
            catch (Exception exception)
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                _logger.LogError("CreateAllBotsMatchesInBackground failed with exception", exception);
            }
        }

        private bool IsTutorialScene() => string.Equals(
            SceneManager.GetActiveScene().name,
            MonsterLandSceneManager.GetSceneConfiguration(MonsterLandSceneType.Tutorial).Name
        );

        private JxMonsterLandGameModeKind GetLocalGameMode()
        {
            if (IsTutorialScene())
                return JxMonsterLandGameModeKind.Tutorial;

            if (MonsterLandSceneManager.Instance.TryGetTransitionParameters<GameplaySceneTransitionParameters>(out var parameters))
                return parameters.GameMode;

            return JxMonsterLandGameModeKind.Turbo;
        }

        private async UniTask AllocateBotOnlyMatchAsync()
        {
            var allocated = await AllocateMatchAsync(
                new MatchParameters(
                    matchId: Guid.NewGuid(),
                    gameMode: JxMonsterLandGameModeKind.Turbo,
                    map: JxMonsterLandMap.SmallDarkIsland1,
                    players: new List<MatchPlayerConnectionInfo>()
                )
            );

            if (!allocated)
                _logger.LogError("Local match allocation failed");
        }
    }
}