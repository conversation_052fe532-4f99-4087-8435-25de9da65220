using System.Collections.Generic;
using Jx.Utils.Logging;
using LoM.Characters.ClientIntegration.Perks;
using Newtonsoft.Json;

namespace Savings.Histories.CharacterUpgrade
{
    [JsonObject(MemberSerialization.OptIn)]
    public class CharacterUpgradeWatchHistory : ICharacterUpgradeWatchHistoryReadOnly
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(CharacterUpgradeWatchHistory));
        
        // hashset не сериализуется из-за юнити
        [JsonProperty("unknownPerks")]
        private Dictionary<string, byte> _unknownPerks;

        [JsonConstructor]
        public CharacterUpgradeWatchHistory(Dictionary<string, byte> watchedPerks)
        {
            _unknownPerks = watchedPerks ?? new Dictionary<string, byte>();
        }

        public bool IsPerkUnknown(int characterIndex, PerkType perk)
        {
            if (perk == PerkType.None)
            {
                return true;
            }
        
            var record = BuildWatchRecord(characterIndex, perk);
            return _unknownPerks.ContainsKey(record);
        }
        
        public void MarkPerkAsWatched(int characterIndex, PerkType perk)
        {
            var record = BuildWatchRecord(characterIndex, perk);

            if (perk == PerkType.None || !_unknownPerks.ContainsKey(record))
            {
                return;
            }
            
            _unknownPerks.Remove(record);
        }

        public void AddUnknownPerk(int characterIndex, PerkType perk)
        {
            if (perk == PerkType.None)
            {
                return;
            }
            
            var record = BuildWatchRecord(characterIndex, perk);

            _unknownPerks[record] = default;
        }

        private string BuildWatchRecord(int characterIndex, PerkType perk)
        {
            return $"{characterIndex}.{perk}";
        }
    }
}