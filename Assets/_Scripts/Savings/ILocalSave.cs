using System;
using System.Threading;
using Jx.Utils.ChangeableObjects;
using Savings.Data;
using Savings.Handlers;

namespace Savings
{
    public interface ILocalSave : IJxChangeableObject<ILocalSaveModel>
    {
        IFrequentSaveHandler CreateFrequentSaveHandler(CancellationToken cancellationToken, TimeSpan delay);
        void Change(Action<LocalSaveModel> updater);
        void Clear();

        IDebugOptions Debug { get; }
    }
}