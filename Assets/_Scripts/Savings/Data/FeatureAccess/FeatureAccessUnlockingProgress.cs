using System.Collections.Generic;
using Core.Extensions;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using MonsterLand.Matchmaking.Dto;
using Newtonsoft.Json;

namespace Savings.Data.FeatureAccess
{
    [JsonObject(MemberSerialization.OptIn)]
    public class FeatureAccessUnlockingProgress : IFeatureAccessUnlockingProgress
    {
        [JsonProperty("we")]
        private Dictionary<int, bool> _watchedFeatures;

        [JsonConstructor]
        public FeatureAccessUnlockingProgress()
        {
            _watchedFeatures = new Dictionary<int, bool>();
        }

        public void MaskAsWatched(MonsterLandFeatureAccessType featureType)
        {
            _watchedFeatures[(int)featureType] = true;
        }

        public void MarkGamemodeAsWatched(JxMonsterLandGameModeKind kind)
        {
            MaskAsWatched(kind.ToFeatureAccessType());
        }
        
        public bool IsWatched(MonsterLandFeatureAccessType featureType)
        {
            return _watchedFeatures.ContainsKey((int)featureType);
        }
    }
}