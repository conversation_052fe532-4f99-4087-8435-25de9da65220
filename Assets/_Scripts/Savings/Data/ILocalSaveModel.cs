using Audio.Settings;
using Configs.LoadingHints.Providers;
using ExternalServices.Auth;
using GameplayNetworking.Share.Character;
using Managers.Suggestions;
using MonsterLand.Matchmaking.Dto;
using Savings.Data.FeatureAccess;
using Savings.Data.LastMatchDisconnection;
using Savings.Histories;
using Savings.Histories.CharacterUpgrade;
using Savings.Histories.TrophyPath;

namespace Savings.Data
{
    public interface ILocalSaveModel
    {
        bool GdprAccepted { get; }
        AuthProviderType AuthProvider { get; }
        AudioSettings AudioSettings { get; }
        string AgeGroup { get; }
        int SessionNumber { get; }
        int LastLaunchDay { get; }
        int LaunchDayCount { get; }
        float SessionLength { get; }
        int SessionLengthIndex { get; }
        JxMonsterLandGameModeKind GameMode { get; }
        ChestManagementScreenStatus ChestManagementStatus { get; }
        LoadingHintProgress LoadingHintProgress { get; }
        int SelectedCatcherCharacterIndex { get; }
        int SelectedEscapeeCharacterIndex { get; }
        ITaskPopupWatchHistoryReadonly TaskPopupWatchHistory { get; }
        ICharacterUpgradeWatchHistoryReadOnly CharacterUpgradeWatchHistory { get; }
        int LastMvpValue { get; }
        int CagedCountWithKeys { get; }
        int LastRenderedCurrentGlobalTrophy { get; }
        int Age { get; }
        
        SuggestionsProgress SuggestionsProgress { get; }
        
        TutorialLoadingProgressHistory TutorialLoadingHintProgress { get; }
        IFeatureAccessUnlockingProgress FeatureAccessUnlockingProgress { get; }
        ITrophyPathWatchHistory TrophyPathWatchHistory { get; }
        ILastMatchDisconnectionSave LastMatchDisconnection { get; }
        
        ReconnectionRecordDto? ReconnectionRecord { get; }

        SelectedCharacterSave GetSelectedCharacterSave(CharacterType characterType);

        SelectedCharacterSave GetCharacterSave(int characterIndex);

        bool IsEntityShown(string entityId);
    }
}