using Api.Client.User;
using Audio.Settings;
using Configs.LoadingHints.Providers;
using Core.Helpers.SceneManagement.TransitionParameters;
using ExternalServices.Auth;
using GameplayNetworking.Share.Character;
using Jx.Utils.Extensions;
using Managers.Suggestions;
using Newtonsoft.Json;
using Savings.Data.FeatureAccess;
using Savings.Histories;
using Savings.Histories.CharacterUpgrade;
using Savings.Histories.TrophyPath;
using System;
using System.Collections.Generic;
using Configs.Entities;
using MonsterLand.Matchmaking.Dto;
using Savings.Data.LastMatchDisconnection;

namespace Savings.Data
{
    [Serializable]
    [JsonObject(MemberSerialization.OptIn)]
    public class LocalSaveModel : ILocalSaveModel
    {
        [JsonConstructor]
        public LocalSaveModel(
            bool gdprAccepted,
            Dictionary<int, SelectedCharacterSave> selectedCharacterSaveByCharacterIndex,
            int selectedCatcherCharacterIndex,
            int selectedEscapeeCharacterIndex,
            DebugOptions debugOptions,
            AuthProviderType authProvider,
            IDictionary<string, byte> watchedEntities,
            AudioSettings audioSettings,
            SuggestionsProgress suggestionsProgress,
            string ageGroup,
            int sessionNumber,
            int lastLaunchDay,
            int launchDayCount,
            float sessionLength,
            int sessionLengthIndex,
            LoadingHintProgress loadingHintProgress,
            TaskPopupWatchHistory taskPopupWatchHistory,
            ChestManagementScreenStatus chestManagementStatus,
            CharacterUpgradeWatchHistory characterUpgradeWatchHistory,
            TutorialLoadingProgressHistory tutorialLoadingHintProgress,
            FeatureAccessUnlockingProgress featureAccessUnlockingProgress,
            TrophyPathWatchHistory trophyPathWatchHistory,
            JxMonsterLandGameModeKind? gameMode,
            JxMonsterLandGameModeKind? lobbyGameMode,
            ReconnectionRecordDto? reconnectionRecord,
            LastMatchDisconnectionSave lastMatchDisconnection,
            int age
        )
        {
            GdprAccepted = gdprAccepted;
            SelectedCharacterSaveByCharacterIndex = selectedCharacterSaveByCharacterIndex ?? new Dictionary<int, SelectedCharacterSave>();
            SelectedCatcherCharacterIndex = selectedCatcherCharacterIndex == 0 ? CatcherType.Goliath.ToIndex() : selectedCatcherCharacterIndex;
            SelectedEscapeeCharacterIndex = selectedEscapeeCharacterIndex == 0 ? EscapeeType.Golem.ToIndex() : selectedEscapeeCharacterIndex;
            DebugOptions = debugOptions ?? new DebugOptions();
            DebugOptions.OverridenEscapeeBotCharacters ??= new[] { EscapeeType.Golem, EscapeeType.Golem, EscapeeType.Golem, EscapeeType.Golem };
            DebugOptions.OverridenCatcherBotCharacters ??= new[] { CatcherType.Goliath };
            AuthProvider = authProvider;
            WatchedEntities = watchedEntities ?? new Dictionary<string, byte>();
            AudioSettings = audioSettings ?? AudioSettings.CreateDefault();
            SuggestionsProgress = suggestionsProgress ?? SuggestionsProgress.CreateDefault();
            LastMatchDisconnection = lastMatchDisconnection ?? LastMatchDisconnectionSave.Default;
            AgeGroup = ageGroup;
            SessionNumber = sessionNumber;
            LastLaunchDay = lastLaunchDay > 0 ? lastLaunchDay : DateTime.Now.GetDaysSinceEpoch();
            LaunchDayCount = launchDayCount;
            SessionLength = sessionLength;
            SessionLengthIndex = sessionLengthIndex;
            LoadingHintProgress = loadingHintProgress ?? LoadingHintProgress.CreateDefault();
            TaskPopupWatchHistory = taskPopupWatchHistory ?? new TaskPopupWatchHistory(default, default);
            ChestManagementStatus = chestManagementStatus ?? ChestManagementScreenStatus.CreateDefault();
            CharacterUpgradeWatchHistory = characterUpgradeWatchHistory ?? new CharacterUpgradeWatchHistory(default);
            TutorialLoadingHintProgress = tutorialLoadingHintProgress ?? TutorialLoadingProgressHistory.CreateDefault();
            LastMvpValue = 0;
            CagedCountWithKeys = 0;
            LastRenderedCurrentGlobalTrophy = 0;
            FeatureAccessUnlockingProgress = featureAccessUnlockingProgress ?? new FeatureAccessUnlockingProgress();
            TrophyPathWatchHistory = trophyPathWatchHistory ?? new TrophyPathWatchHistory();
            ReconnectionRecord = reconnectionRecord;
            GameMode = gameMode ?? JxMonsterLandGameModeKind.Turbo;
            Age = age;
        }
        
        /*  Used. Do not reuse this serialize key
         *  [JsonProperty("playedMaps")]
         *  public IDictionary<MapType, byte> PlayedMaps { get; }
         */

        [JsonProperty("ga")]
        public bool GdprAccepted { get; set; }

        [JsonProperty("scsbci")]
        public Dictionary<int, SelectedCharacterSave> SelectedCharacterSaveByCharacterIndex { get; }

        [JsonProperty("scci")]
        public int SelectedCatcherCharacterIndex { get; private set; }

        [JsonProperty("seci")]
        public int SelectedEscapeeCharacterIndex { get; private set; }

        [JsonProperty("dbg")]
        public DebugOptions DebugOptions { get; }

        [JsonProperty("apt")]
        public AuthProviderType AuthProvider { get; set; }

        // hashset не сериализуется из-за юнити
        [JsonProperty("wee")]
        public IDictionary<string, byte> WatchedEntities { get; }

        [JsonProperty("as")]
        public AudioSettings AudioSettings { get; }

        [JsonProperty("hwp")]
        public LoadingHintProgress LoadingHintProgress { get; }

        [JsonProperty("sp")]
        public SuggestionsProgress SuggestionsProgress { get; }

        // [JsonProperty("sl")]
        // public int ShownLevel { get; set; }

        [JsonProperty("ageg")]
        public string AgeGroup { get; set; }

        [JsonProperty("sn")]
        public int SessionNumber { get; set; }

        [JsonProperty("lld")]
        public int LastLaunchDay { get; set; }

        [JsonProperty("ldc")]
        public int LaunchDayCount { get; set; }

        [JsonProperty("sessionLength")]
        public float SessionLength { get; set; }
        
        [JsonProperty("sessionLengthIndex")]
        public int SessionLengthIndex { get; set; }

        [JsonProperty("gamemode")] // do not use "gm"
        public JxMonsterLandGameModeKind GameMode { get; set; }

        [JsonProperty("chms")]
        public ChestManagementScreenStatus ChestManagementStatus { get; set; }

        [JsonProperty("tpwh")]
        public TaskPopupWatchHistory TaskPopupWatchHistory { get; }
        
        [JsonProperty("cuwh")]
        public CharacterUpgradeWatchHistory CharacterUpgradeWatchHistory { get; }
        
        [JsonProperty("tlhp")]
        public TutorialLoadingProgressHistory TutorialLoadingHintProgress { get; }
        
        [JsonProperty("lmv")]
        public int LastMvpValue { get; set; }
        
        [JsonProperty("ccws")]
        public int CagedCountWithKeys { get; set; }
        
        [JsonProperty("lrcgt")]
        public int LastRenderedCurrentGlobalTrophy { get; set; }
        
        [JsonProperty("faup")]
        public FeatureAccessUnlockingProgress FeatureAccessUnlockingProgress { get; set; }
        
        [JsonProperty("rr")]
        public ReconnectionRecordDto? ReconnectionRecord { get; set; }
        
        [JsonProperty("ageval")]
        public int Age { get; private set; }

        [JsonIgnore]
        IFeatureAccessUnlockingProgress ILocalSaveModel.FeatureAccessUnlockingProgress => FeatureAccessUnlockingProgress;
        
        [JsonProperty("trophypathwatchhistory")]
        public TrophyPathWatchHistory TrophyPathWatchHistory { get; set; }
        
        [JsonProperty("lmdrs")]
        public LastMatchDisconnectionSave LastMatchDisconnection { get; set; }
        
        [JsonIgnore]
        ITrophyPathWatchHistory ILocalSaveModel.TrophyPathWatchHistory => TrophyPathWatchHistory;

        [JsonIgnore]
        public DebugOptions Debug => DebugOptions;

        [JsonIgnore]
        ITaskPopupWatchHistoryReadonly ILocalSaveModel.TaskPopupWatchHistory => TaskPopupWatchHistory;
        
        [JsonIgnore]
        ICharacterUpgradeWatchHistoryReadOnly ILocalSaveModel.CharacterUpgradeWatchHistory => CharacterUpgradeWatchHistory;
        
        [JsonIgnore]
        ILastMatchDisconnectionSave ILocalSaveModel.LastMatchDisconnection => LastMatchDisconnection;

        public IDebugOptions GetDebugOptions(IUserContext? userContext)
        {
            return userContext?.IsDebugUser.Value == true ? Debug : DebugOptions.Empty;
        }

        public SelectedCharacterSave GetSelectedCharacterSave(CharacterType characterType)
        {
            var characterIndex = characterType == CharacterType.Catcher ? SelectedCatcherCharacterIndex : SelectedEscapeeCharacterIndex;
            return GetCharacterSave(characterIndex);
        }

        public int GetSelectedCharacterIndex(CharacterType characterType)
        {
            return characterType == CharacterType.Catcher ? SelectedCatcherCharacterIndex : SelectedEscapeeCharacterIndex;
        }

        public SelectedCharacterSave GetCharacterSave(int characterIndex)
        {
            if (EntityRepository.Instance.FindCharacter(characterIndex) == null) // disabled or error
            {
                var characterType = CharacterTypeHelper.IsEscapee(characterIndex) ? CharacterType.Escapee : CharacterType.Catcher;
                characterIndex = EntityRepository.Instance.GetDefaultCharacter(characterType).Index;
            }
            
            if (SelectedCharacterSaveByCharacterIndex.TryGetValue(characterIndex, out var save))
            {
                if (EntityRepository.Instance.FindSkin(save.SkinEntityId) != null)
                {
                    return save;
                }
            }

            var characterEntity = EntityRepository.Instance.FindCharacter(characterIndex);

            var newSave = new SelectedCharacterSave
            {
                Index = characterEntity.Index,
                SkinEntityId = characterEntity.DefaultSkin.Id,
            };

            SelectedCharacterSaveByCharacterIndex[characterIndex] = newSave;
            return newSave;
        }

        public bool IsEntityShown(string entityId)
        {
            if (string.IsNullOrEmpty(entityId))
            {
                return true;
            }

            return WatchedEntities.ContainsKey(entityId);
        }

        public void MarkEntityShown(string entityId)
        {
            if (!string.IsNullOrEmpty(entityId))
            {
                WatchedEntities[entityId] = default;
            }
        }

        public void IncrementSessionNumber()
        {
            ++SessionNumber;
        }

        public void UpdateLaunchDay()
        {
            var currentDay = DateTime.Now.GetDaysSinceEpoch();
            if (currentDay != LastLaunchDay)
            {
                var dayDiff = currentDay - LastLaunchDay;
                if (dayDiff > 0)
                {
                    ++LaunchDayCount;
                }
            }
        }

        public void SetAgeGroup(string ageGroup)
        {
            AgeGroup = ageGroup;
        }


        public void SetAge(int age)
        {
            Age = age;
        }

        public bool TryUpdateCharacterSelection(int concreteCharacterIndex, string skinEntityId)
        {
            if (SelectedCharacterSaveByCharacterIndex.TryGetValue(concreteCharacterIndex, out var selectedCharacterSave) &&
                selectedCharacterSave.SkinEntityId == skinEntityId)
            {
                return false;
            }

            SelectedCharacterSaveByCharacterIndex[concreteCharacterIndex] = new SelectedCharacterSave
                { Index = concreteCharacterIndex, SkinEntityId = skinEntityId, };

            return true;
        }

        public void SaveSelectedCharacterIndex(int characterIndex)
        {
            if (CharacterTypeHelper.IsCatcher(characterIndex))
                SelectedCatcherCharacterIndex = characterIndex;
            else if (CharacterTypeHelper.IsEscapee(characterIndex))
                SelectedEscapeeCharacterIndex = characterIndex;
        }

        public static LocalSaveModel New()
        {
            return new LocalSaveModel(
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default,
                default
            );
        }
    }
}