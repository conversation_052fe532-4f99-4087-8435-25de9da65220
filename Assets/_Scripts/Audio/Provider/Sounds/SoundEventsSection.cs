using Audio.Provider.Sounds.Characters;
using Audio.Provider.Sounds.Folders;
using Audio.Provider.Sounds.Folders.Gameplay;
using Audio.Provider.Sounds.Folders.Screens;

namespace Audio.Provider.Sounds
{
    public class SoundEventsSection
    {
        public SoundEventsSection()
        {
            Shared = new SharedSoundEventsFolder();
            CharacterUpgrade = new CharacterUpgradeSoundEventsFolder();
            Reward = new RewardSoundEventsFolder();
            MatchFinish = new MatchFinishSoundEventsFolder();
            ScreenOpenings = new ScreenOpeningsSoundEventsFolder();
            Gameplay = new GameplaySoundEventsFolder();
            Ambience = new AmbienceSoundEventsFolder();
            Characters = new CharacterSoundEventsFolder();
            TrophyPath = new TrophyPathSoundEventsFolder();
        }
        
        public SharedSoundEventsFolder Shared { get; }
        public CharacterUpgradeSoundEventsFolder CharacterUpgrade { get; }
        public RewardSoundEventsFolder Reward { get; }
        public MatchFinishSoundEventsFolder MatchFinish { get; }
        public ScreenOpeningsSoundEventsFolder ScreenOpenings { get; }
        public GameplaySoundEventsFolder Gameplay { get; }
        public AmbienceSoundEventsFolder Ambience { get; }
        public CharacterSoundEventsFolder Characters { get; }
        public TrophyPathSoundEventsFolder TrophyPath { get; }
    }
}