using Audio.Provider.Sounds.Folders;

namespace Audio.Provider.Sounds
{
    public class AmbienceSoundEventsFolder : SoundEventsFolder
    {
        public AmbienceSoundEventsFolder()
        {
            CampfireLoop = Create("sfx_ambience_environment_ campfire");
            SeaWavesLoop = Create("sfx_ambience_waterfront_loop");
            LobbyNightLoop = Create("sfx_ambience_night_lobby_loop");
        }
        
        public SoundEventIdentifier CampfireLoop { get; }
        public SoundEventIdentifier SeaWavesLoop { get; }
        public SoundEventIdentifier LobbyNightLoop { get; }
    }
}