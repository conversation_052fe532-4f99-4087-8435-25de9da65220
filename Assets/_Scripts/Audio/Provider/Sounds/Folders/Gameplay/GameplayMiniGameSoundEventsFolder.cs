namespace Audio.Provider.Sounds.Folders.Gameplay
{
    public class GameplayMiniGameSoundEventsFolder : SoundEventsFolder
    {
        public GameplayMiniGameSoundEventsFolder()
        {
            Appear = Create("sfx_gameplay_ui_fader_appear");
            TouchSuccessZone = Create("sfx_gameplay_ui_fader_succes");
            TouchFailZone = Create("sfx_gameplay_ui_fader_miss");
            TouchPerfectZone = Create("sfx_gameplay_ui_fader_succes_reward");
            CageSwing = Create("sfx_character_interaction_cage_selfrescue");
            LockStart = Create("sfx_gameplay_ui_selfrescue_lock_animation");
            LockOpen = Create("sfx_gameplay_ui_selfrescue_lock_open");
        }
        
        public SoundEventIdentifier Appear { get; }
        public SoundEventIdentifier CageSwing { get; }
        public SoundEventIdentifier TouchSuccessZone { get; }
        public SoundEventIdentifier TouchPerfectZone { get; }
        public SoundEventIdentifier TouchFailZone { get; }
        public SoundEventIdentifier LockStart { get; }
        public SoundEventIdentifier LockOpen { get; }
    }
}