namespace Audio.Provider.Sounds.Folders.Gameplay
{
    public class GameplayNotificationSoundEventsFolder : SoundEventsFolder
    {
        public GameplayNotificationSoundEventsFolder()
        {
            CagedEscapee = Create("sfx_gameplay_ui_cage_notification");
            EscapeeInjured = Create("sfx_gameplay_ui_wound_notification");
            CampfireLit = Create("sfx_gameplay_ui_campfire_notification");
            GateOpen = Create("sfx_gameplay_ui_gate_notification");
            EscapeeDead = Create("sfx_gameplay_ui_dead_notification");
        }
        
        public SoundEventIdentifier CagedEscapee { get; }
        public SoundEventIdentifier EscapeeInjured { get; }
        public SoundEventIdentifier CampfireLit { get; }
        public SoundEventIdentifier GateOpen { get; }
        public SoundEventIdentifier EscapeeDead { get; }
    }
}