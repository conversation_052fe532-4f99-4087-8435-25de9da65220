namespace Audio.Provider.Sounds.Folders.Gameplay
{
    public class GameplayTrapObjectSoundEventsFolder : SoundEventsFolder
    {
        public GameplayTrapObjectSoundEventsFolder()
        {
            SharedInstallLoop = Create("sfx_character_interaction_install_trap_loop");
            SharedInstalled = Create("sfx_character_interaction_trap_installed");
            
            ExplosionExplode = Create("sfx_int_object_trap_fire_burst");

            CatchExplode = Create("sfx_int_object_trap_fire_zap");

            SlowDownExplode = Create("sfx_int_object_trap_freeze");

            DisclosureExplode = Create("sfx_int_object_trap_radar");
        }
        
        public SoundEventIdentifier SharedInstallLoop { get; }
        public SoundEventIdentifier SharedInstalled { get; }
        
        public SoundEventIdentifier ExplosionExplode { get; }
        public SoundEventIdentifier CatchExplode { get; }
        public SoundEventIdentifier SlowDownExplode { get; }
        public SoundEventIdentifier DisclosureExplode { get; }
    }
}