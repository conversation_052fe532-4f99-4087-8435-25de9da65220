using Audio.Debug;
using Core.Helpers;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Logging;
using UnityEngine;
using UnityEngine.Scripting;

namespace Audio.ListenerManager
{
    [Preserve]
    public class AudioListenerController : IAudioListenerController
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(AudioListenerController));
        
        private FMODUnity.StudioListener _listener = null!;

        [Preserve]
        public AudioListenerController()
        {
        }

        public bool IsAttenuationTargetSpecified { get; private set; } = false;

        public void Initialize()
        {
            var listener = new GameObject(name: "FmodAudioListener");
            _listener = listener.AddComponent<FMODUnity.StudioListener>();
            Object.DontDestroyOnLoad(_listener);
        }

        public void SetAttenuationTarget(GameObject? gameObject)
        {
            if (_listener == null)
            {
                _logger.LogError($"The listener isn't initialized. Call {nameof(Initialize)} on the app initialization.");
                return;
            }
        
            _listener.SetAttenuationTarget(gameObject);
            IsAttenuationTargetSpecified = gameObject != null;

#if DEV
            SendChangeDebugAnalytics(gameObject);
#endif
        }

#if DEV
        private static void SendChangeDebugAnalytics(GameObject? gameObject)
        {
            if (gameObject.IsNullObj())
            {
                Emit("none");
            }
            else if (gameObject!.TryGetComponent<NetGamePlayer>(out var player))
            {
                Emit($"(Player) {player.BaseSyncData.PublicInfo.Value.Nickname}");
            }
            else
            {
                Emit($"(Object) {gameObject.name}");
            }

            void Emit(string objectName)
            {
                DebugAudioAnalytics.Instance.Emit($"LISTENER: {objectName}");
            }
        }
#endif
    }
}