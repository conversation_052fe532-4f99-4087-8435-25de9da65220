using System;
using Jx.Utils.Observable;

namespace Audio.Debug
{
    public class DebugAudioAnalytics
    {
#if DEV
        public static DebugAudioAnalytics Instance { get; } = new DebugAudioAnalytics();
        
        private readonly JxObservable<string> _onEmitted = new JxObservable<string>();

        public void Emit(string message)
        {
            _onEmitted.Invoke(message);
        }

        public IDisposable SubscribeToLog(Action<string> callback)
        {
            return _onEmitted.Subscribe(callback);
        }
#endif
    }
}