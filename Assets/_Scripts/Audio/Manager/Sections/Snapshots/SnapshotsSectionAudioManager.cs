using Audio.Manager.Handler;
using Audio.Provider.Snapshots;
using Audio.Sections.Sounds;
using Configs.Audio;

namespace Audio.Sections.Snapshots
{
    public class SnapshotsSectionAudioManager : SectionAudioManager,
                                                ISnapshotsSectionAudioManager
    {
        public SnapshotsSectionAudioManager(IAudioRepository audioRepository) : base(audioRepository)
        {
        }

        public IClientAudioHandler GetHandler(SnapshotAudioIdentifier identifier)
        {
            var instance = CreateInstanceInternal(identifier.Id);

            if (!instance.HasValue)
            {
#if DEV
                DebugSendAnalyticsError(identifier.Id);
#endif
                return ClientAudioInstanceHandler.Empty;
            }
            
            var handler = new ClientAudioInstanceHandler(instance.Value);

#if DEV
            DebugSendAnalytics($"SNAPSHOT | {identifier.Id}");
            handler.DebugInitialize(() => DebugSendAnalytics($"SNAPSHOT | REM | {identifier.Id}"));
#endif
            return handler;
        }
    }
}