using System;
using Audio.Debug;
using Audio.Manager.Handler;
using Configs.Audio;
using Core.Helpers;
using FMOD;
using FMOD.Studio;
using FMODUnity;
using Jx.Utils.Logging;
using Jx.Utils.MainThread;

namespace Audio.Sections.Sounds
{
    public class SectionAudioManager : ISectionAudioManager
    {
        protected SectionAudioManager(
            IAudioRepository audioRepository)
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
            AudioRepository = audioRepository;
        }
        
        protected IJxLogger Logger { get; }
        protected IAudioRepository AudioRepository { get; }

        protected EventInstance? CreateInstanceInternal(string id)
        {
            if (!TryGetGuidOrError(id, out var guid))
            {
                return null;
            }

            try
            {
                var instance = RuntimeManager.CreateInstance(guid);
                return instance;
            }
            catch (Exception exception)
            {
                Logger.LogError("Create audio instance exception. ENSURE, bank with this event is LOADED!", exception);
                return null;
            }
        }

        private bool TryGetGuidOrError(string id, out GUID guid)
        {
            guid = new GUID(Guid.Empty);

            try
            {
                var temp = AudioRepository.Find(id);

                if (temp.HasValue)
                {
                    guid = temp.Value;
                    return true;
                }
            }
            catch (Exception exception)
            {
                Logger.LogError("Find audio in repository exception", exception);
            }
            
            Logger.LogError($"Unknown audio: '{id}'");
            return false;
        }

        #region DEBUG

#if DEV
        protected static void DebugSendAnalytics(string message)
        {
            DebugAudioAnalytics.Instance.Emit(message);
        }

        protected static void DebugSendAnalyticsError(string id)
        {
            DebugSendAnalytics($"ERROR | {id}");
        }
#endif

        #endregion
    }
}