using System;
using System.IO;

namespace Api.Discovery
{
    public static class MonsterLandDiscovery
    {
        public static MonsterLandDiscoveryStacks Stack => MonsterLandDiscoveryStacks.Prod;
         // public static MonsterLandDiscoveryStacks Stack => MonsterLandDiscoveryStacks.Dev;

        public static string MatchmakingScope => "prod-tea";
        //public static string MatchmakingScope => "prod-coffee";
        // public static string MatchmakingScope => "dev-tea";
        // public static string MatchmakingScope => "dev-coffee";

        // todo: remove after gameplayconfig refactoring done
        public static Uri BuildDevOnlyEndpointUrl(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
                throw new ArgumentNullException(nameof(relativePath));

            var stack = Stack;
            stack = stack switch
            {
                MonsterLandDiscoveryStacks.Prod => MonsterLandDiscoveryStacks.Dev,
                _ => stack
            };

            var domain = GetStackedDomain(stack);
            return new Uri(Path.Combine(domain, relativePath));
        }

        public static string GetMnemonicStack(MonsterLandDiscoveryStacks stack) => stack.ToString().ToLowerInvariant();

        private static string GetStackedDomain(MonsterLandDiscoveryStacks stack)
        {
            switch (stack)
            {
                case MonsterLandDiscoveryStacks.Dev:
                    return $"http://{GetMnemonicStack(stack)}.edtechdev.net/";
                case MonsterLandDiscoveryStacks.Prod:
                    return $"http://{GetMnemonicStack(stack)}.woodroom-tech.com/";

                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }
}