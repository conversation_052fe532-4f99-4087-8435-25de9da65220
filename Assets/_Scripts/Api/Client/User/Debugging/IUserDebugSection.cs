using System;
using Cysharp.Threading.Tasks;
using LoM.Characters.ClientIntegration;
using LoM.Messaging.Requests.Debug;

namespace Api.Client.User.Debugging
{
    public interface IUserDebugSection
    {
        UniTask<bool> SkipTimeAsync(TimeSpan timeToSkip);

        UniTask<bool> ProcessSimpleActionAsync(DebugSimpleActionType actionType);

        UniTask<bool> GiveMatchAsync(TeamKind team, int characterIndex, bool isWinner, bool isMvp);

        UniTask<(bool success, int requestCount)> RunLoadTestAsync();
    }
}