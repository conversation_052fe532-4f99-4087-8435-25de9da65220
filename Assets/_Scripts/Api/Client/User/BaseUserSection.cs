using System.Threading;
using Cysharp.Threading.Tasks;
using Jx.Utils.Logging;
using MonsterLand.Meta.Api;

namespace Api.Client.User
{
    public abstract class BaseUserSection : IUserSection
    {
        protected BaseUserSection(UserContext userContext, IJxMonsterLandMetaApi api)
        {
            UserContext = userContext;
            Api = api;
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }
        
        protected UserContext UserContext { get; }
        protected IJxMonsterLandMetaApi Api { get; }
        protected IJxLogger Logger { get; }

        public virtual UniTask LoadAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask InitializeAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask ConfigureAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask OnConfiguredAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask UpdateAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask SaveAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
        
        public virtual UniTask ResetAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }
    }
}