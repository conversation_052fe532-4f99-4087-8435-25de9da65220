using Api.Client.User.Prices;
using Cysharp.Threading.Tasks;

namespace Api.Client.User.SeasonPass
{
    // infinite node doesn't have previous or next nodes
    public interface ISeasonPassNode
    {
        int Index { get; }
        ISeasonPassNodeRewardInteraction Default { get; }
        ISeasonPassNodeRewardInteraction Premium { get; }
        
        int Threshold { get; }
        bool IsImportant { get; }
        IJxUserPrice UnlockPrice { get; }

        UniTask<bool> UnlockThresholdAsync();
    }
}