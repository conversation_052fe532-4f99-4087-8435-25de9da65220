using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User.Billing;
using Api.Client.User.Prices;
using Api.Client.User.Rewards.Adapters;
using Cysharp.Threading.Tasks;
using Jx.Utils.Extensions;
using LoM.Messaging.ClientIntegrations.SeasonPass;
using Api.Client.User.SeasonPass;

namespace Api.Client.User.SeasonPass.Builder
{
    internal class SeasonPassInteractionBuildHelper
    {
        private readonly IUserBillingSection _billing;
        private readonly Func<int, bool, UniTask<bool>> _claimRewardAsync;
        private readonly Func<int, UniTask<bool>> _unlockThresholdAsync;
        private readonly Func<int, bool, bool> _canRewardBeClaimed;

        internal SeasonPassInteractionBuildHelper(
            IUserBillingSection billing,
            Func<int, bool, UniTask<bool>> claimRewardAsync,
            Func<int, UniTask<bool>> unlockThresholdAsync,
            Func<int, bool, bool> canRewardBeClaimed)
        {
            _billing = billing;
            _claimRewardAsync = claimRewardAsync;
            _unlockThresholdAsync = unlockThresholdAsync;
            _canRewardBeClaimed = canRewardBeClaimed;
        }

        public SeasonPassNodeRewardStatus GetNodeStatus(SeasonPassUserRewardProgressClientIntegration progress, SeasonPassNode node, int currentSeasonProgress)
        {
            if (currentSeasonProgress < node.Threshold)
                return SeasonPassNodeRewardStatus.Locked;

            return progress.LastRewardedThreshold >= node.Threshold ? SeasonPassNodeRewardStatus.Claimed : SeasonPassNodeRewardStatus.AvailableToClaim;
        }

        public SeasonPassNode? FindInfiniteNode(SeasonPassEventSettingsClientIntegration settings)
        {
            var dto = settings.Reward.InfiniteReward;
            if (dto == null)
                return null;

            return BuildNode(dto, settings.Reward.Rewards.Count);
        }

        public int GetMaxProgress(SeasonPassEventSettingsClientIntegration settings)
        {
            return settings.Reward.Rewards.Max(r => r.Threshold);
        }

        public IEnumerable<IJxUserReward> EnumerateAllRewards(SeasonPassEventSettingsClientIntegration settings)
        {
            foreach (var reward in settings.Reward.Rewards)
            {
                yield return GetReward(reward.RewardId);
                yield return GetReward(reward.PremiumRewardId);
            }

            var infiniteReward = settings.Reward.InfiniteReward;
            if (infiniteReward == null)
                yield break;
            
            yield return GetReward(infiniteReward.RewardId);
            yield return GetReward(infiniteReward.PremiumRewardId);
        }

        public IEnumerable<SeasonPassNode> EnumerateNodes(SeasonPassEventSettingsClientIntegration settings)
        {
            return settings.Reward.Rewards.Select(BuildNode);
        }
        
        private SeasonPassNode BuildNode(
            SeasonPassEventSettingsRewardItemClientIntegration dto, 
            int index)
        {
            var defaultRewardInteraction = BuildNodeRewardInteraction(dto.RewardId, dto.Threshold, false);
            var premiumRewardInteraction = BuildNodeRewardInteraction(dto.PremiumRewardId, dto.Threshold, true);
            
            return new SeasonPassNode(
                index,
                defaultInteraction: defaultRewardInteraction,
                premiumInteraction: premiumRewardInteraction,
                dto.Threshold,
                dto.HasImportantReward,
                GetPrice(dto.UnlockThresholdPriceId),
                () => _unlockThresholdAsync(dto.Threshold)
            );
        }
        
        private SeasonPassNodeRewardInteraction BuildNodeRewardInteraction(string rewardId, int threshold, bool isPremiumReward)
        {
            return new SeasonPassNodeRewardInteraction(
                GetReward(rewardId), 
                () => _claimRewardAsync(threshold, isPremiumReward),
                () => _canRewardBeClaimed(threshold, isPremiumReward)
            );
        }
        
        private IJxUserPrice GetPrice(string id)
        {
            if (!_billing.TryGetPrice(id, out var price))
                throw new Exception($"Failed to find price '{id}'");

            return price;
        }

        private IJxUserReward GetReward(string? id)
        {
            if (string.IsNullOrEmpty(id))
                throw new Exception("Given empty id for reward");
            
            return _billing.TryGetReward(id, out var reward) ? reward : throw new Exception("Failed to find reward").SetData("RewardId", id);
        }
    }
}