using Jx.Utils.ChangeableObjects;
using System;
using System.Collections.Generic;
using Api.Client.User.Rewards.Adapters;

namespace Api.Client.User.SeasonPass
{
    public interface ISeasonPassInteraction
    {
        IJxChangeableObject<bool> IsAvailable { get; }
        IJxChangeableObject<bool> IsPremiumUser { get; }
        IReadOnlyList<ISeasonPassNode> Nodes { get; }
        LinkedList<ISeasonPassNode> NodesLinkedList { get; }
        IReadOnlyList<IJxUserReward> Rewards { get; }
        ISeasonPassNode? InfiniteNode { get; }
        IJxChangeableObject<int> Progress { get; }
        DateTime EndDate { get; }
    }
}