using System;
using System.Collections.Generic;
using Api.Client.User.Rewards.Adapters;
using Core.Loading.Cover;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;
using UnityEngine;

namespace Api.Client.User.ExternalServices
{
    internal class ExternalService : IExternalService
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(ExternalService));

        private readonly Func<string, UniTask<bool>> _rewardReceiver;
        private readonly JxChangeableObject<IJxUserReward?, IJxUserReward?> _openUrlPendingReward;
        private readonly Uri _url;
        private readonly IJxUserReward? _reward;

        public ExternalService(
            string id,
            IExternalServiceMetadata metadata,
            Func<string, UniTask<bool>> rewardReceiver,
            Uri url,
            IJxUserReward? reward
        )
        {
            if (string.IsNullOrEmpty(id))
                throw new ArgumentException("Value cannot be null or empty.", nameof(id));
            
            Id = id;
            Metadata = metadata ?? throw new ArgumentNullException(nameof(metadata));
            _rewardReceiver = rewardReceiver ?? throw new ArgumentNullException(nameof(rewardReceiver));
            _url = url ?? throw new ArgumentNullException(nameof(url));
            _reward = reward;

            _openUrlPendingReward = new JxChangeableObject<IJxUserReward?, IJxUserReward?>();
        }

        public string Id { get; }

        public IExternalServiceMetadata Metadata { get; }

        public IJxChangeableObject<IJxUserReward?> OpenUrlPendingReward => _openUrlPendingReward;

        public void SetOpenUrlRewardIsPending(bool hasPendingReward)
        {
            if (_reward == null)
                return;

            _openUrlPendingReward.TrySet(
                old =>
                {
                    var rewardToSet = hasPendingReward ? _reward : null;
                    return (rewardToSet, !Equals(old, rewardToSet));
                }
            );
        }

        public async UniTask<bool> OpenUrlAndTryCollectRewardAsync(ILoadingCoverController loadingCoverController)
        {
            try
            {
                Application.OpenURL(_url.ToString());

                if (OpenUrlPendingReward.Value == null)
                    return false;

                using (loadingCoverController.Show())
                    return await _rewardReceiver(Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    $"{nameof(OpenUrlAndTryCollectRewardAsync)} failed",
                    ex,
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Id"] = Id,
                        ["Url"] = _url.ToString(),
                    }
                );
            }

            return false;
        }
    }
}