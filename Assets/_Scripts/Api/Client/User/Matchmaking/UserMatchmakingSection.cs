using System;
using System.Collections.Generic;
using MonsterLand.Meta.Api;

namespace Api.Client.User.Matchmaking
{
    public class UserMatchmakingSection : BaseUserSection, IUserMatchmakingSection
    {
        public UserMatchmakingSection(
            UserContext userContext,
            IJxMonsterLandMetaApi api
        )
            : base(userContext, api)
        {
        }

        public IReadOnlyList<int> GetLastPlayedCharacters()
        {
            var user = Api.User.Value;
            if (user != null)
            {
                return user.MatchmakingProgress.PlayedCharacterHistory;
            }
            
            return Array.Empty<int>();
        }
    }
}