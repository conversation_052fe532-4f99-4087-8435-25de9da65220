using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Api.Client.User.Billing.GoldenPass;
using Api.Client.User.Billing.Products;
using Api.Client.User.Prices;
using Api.Client.User.Prices.Adapters;
using Api.Client.User.Rewards.Adapters;
using Cysharp.Threading.Tasks;

namespace Api.Client.User.Billing
{
    public interface IUserBillingSection
    {
        IGoldenPassState GoldenPassState { get; }

        UniTask<PurchaseStatus> PurchaseAsync(IJxUserProduct product);

        IReadOnlyList<IJxUserProduct> GetProducts();
        IReadOnlyList<string> GetPriceIds();
        IEnumerable<SubscriptionUserPrice> EnumerateSubscriptionPrices();

        bool TryGetProduct(string productId, [MaybeNullWhen(false)] out IJxUserProduct product);
        bool TryGetPrice(string priceId, [MaybeNullWhen(false)] out IJxUserPrice price, bool ignoreInAppInit = false);
        bool TryGetReward(string rewardId, [MaybeNullWhen(false)] out IJxUserReward reward);

        bool IsPriceAvailable(IJxUserPrice price, bool instantly = false);
    }
}