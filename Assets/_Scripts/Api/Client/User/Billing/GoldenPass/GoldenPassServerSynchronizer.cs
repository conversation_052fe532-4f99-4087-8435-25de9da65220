using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using ExternalServices.InApp;
using Jx.ApiGateway;
using Jx.Utils;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using LoM.Messaging.Requests.Billing;
using LoM.Messaging.Responses;
using LoM.Messaging.Responses.Billing;
using MonsterLand.Meta.Api;
using UnityEngine.Scripting;

namespace Api.Client.User.Billing.GoldenPass
{
    internal class GoldenPassServerSynchronizer : IGoldenPassServerSynchronizer,
                                                  IJxInitializer,
                                                  IDisposable
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(GoldenPassServerSynchronizer));

        private readonly IUserContext _userContext;
        private readonly IJxMonsterLandMetaApi _api;
        private readonly IBillingIntegration _billing;

        private JxTimer? _synchronizationTimer;

        [Preserve]
        public GoldenPassServerSynchronizer(
            IUserContext userContext,
            IJxMonsterLandMetaApi api,
            IBillingIntegration billing
        )
        {
            _userContext = userContext;
            _api = api;
            _billing = billing;
        }

        IJxInitializer IGoldenPassServerSynchronizer.Initializer => this;

        private async Task SynchronizeAsync(CancellationToken cancellationToken)
        {
            if (!_billing.Initialized)
                return;
            
            var goldenPassIsActive = _userContext.Billing.GoldenPassState.IsActive.Value;
            // will be expired on server
            if (goldenPassIsActive)
                return;

            var subscriptionInfo = _billing.GetSubscriptionInfo();
            // synchronization is not required
            if (!subscriptionInfo.IsActive)
                return;

            var response = await _api.SendRequestAsync<UpdateGoldenPassStateRequest, UpdateGoldenPassStateResponse>(
                new UpdateGoldenPassStateRequest(subscriptionInfo.IsActive, subscriptionInfo.RemainingTime ?? TimeSpan.Zero),
                JxApiGatewayCommandImportance.Low
            );

            if (response.Status == JxMonsterLandResponseStatus.Success)
            {
                _logger.LogDebug(
                    "GoldenPassState changed",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["IsActive"] = subscriptionInfo,
                    }
                );
            }
        }
        
        private TimeSpan GetSynchronizationDelay() => _billing.Initialized ? TimeSpan.FromMinutes(3f) : TimeSpan.FromSeconds(30f);

        UniTask IJxInitializer.LoadAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;

        UniTask IJxInitializer.InitializeAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;

        UniTask IJxInitializer.ConfigureAsync(CancellationToken cancellationToken)
        {
            _synchronizationTimer = new JxTimer(
                "golden-pass-sync",
                SynchronizeAsync,
                GetSynchronizationDelay,
                GetSynchronizationDelay()
            );

            return UniTask.CompletedTask;
        }

        UniTask IJxInitializer.OnConfiguredAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;
        UniTask IJxInitializer.ResetAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;

        void IDisposable.Dispose() => _synchronizationTimer?.Dispose();
    }
}