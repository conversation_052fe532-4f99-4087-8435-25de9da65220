using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Api.Client.User.CharacterPath;
using Api.Client.User.Upgrades.Interactions;
using Api.Entities.Characters;
using Configs.Entities;
using Configs.Perks;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using Jx.Telemetry;
using Jx.Utils.Benchmarks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Collections;
using LoM.Messaging.ClientIntegrations.Upgrades;
using MonsterLand.Meta.Api;
using Savings;

namespace Api.Client.User.Upgrades
{
    public class UserUpgradesSection : BaseUserSection, IUserUpgradesSection
    {
        private readonly JxChangeableObject<UpgradesProfileClientIntegration, UpgradesProfileClientIntegration> _upgradeProfile;
        private readonly ILocalSave _localSave;

        private readonly IDictionary<int, CharacterUpgradeInteraction> _upgradeInteractions;
        private readonly CharacterUpgradeInteraction.ContextContainer _characterCreationContext;

        public UserUpgradesSection(
            UserContext userContext,
            IJxMonsterLandMetaApi api,
            IPerkIntegration perkIntegration,
            ICharacterPathProvider characterPathProvider,
            ILocalSave localSave
        )
            : base(userContext, api)
        {
            _upgradeInteractions = new ConcurrentDictionary<int, CharacterUpgradeInteraction>();
            _upgradeProfile = new JxChangeableObject<UpgradesProfileClientIntegration, UpgradesProfileClientIntegration>();
            _localSave = localSave;

            _characterCreationContext = new CharacterUpgradeInteraction.ContextContainer(
                UserContext,
                Api,
                _upgradeProfile,
                perkIntegration,
                characterPathProvider
            );
        }

        public IJxChangeableObject<UpgradesProfileClientIntegration> Profile => _upgradeProfile;

        public override UniTask LoadAsync(CancellationToken cancellationToken)
        {
            _upgradeProfile.Set(Api.Configuration.Upgrades);
            return UniTask.CompletedTask;
        }

        public override async UniTask OnConfiguredAsync(CancellationToken cancellationToken)
        {
            await base.OnConfiguredAsync(cancellationToken);

            await UniTask.RunOnThreadPool(() => PreloadInteractionsAsync(cancellationToken), cancellationToken: cancellationToken);
        }

        public ICharacterUpgradeInteraction GetCharacterUpgradeInteraction(int characterIndex)
        {
            return _upgradeInteractions.GetOrAdd(characterIndex, () => new CharacterUpgradeInteraction(_characterCreationContext, characterIndex));
        }

        private UniTask PreloadInteractionsAsync(CancellationToken cancellationToken)
        {
            using (JxMetrics.CreateTimeLogger("CharacterUpgrade.PreloadInteractions"))
            {
                var catcherIndex = _localSave.Value.SelectedCatcherCharacterIndex;
                var escapeeIndex = _localSave.Value.SelectedEscapeeCharacterIndex;
                var catcherCached = false;
                var escapeeCached = false;

                foreach (var character in EntityRepository.Instance.GetAllCharacters())
                {
                    if (!catcherCached && ShouldCacheIndex(catcherIndex, character))
                    {
                        GetCharacterUpgradeInteraction(catcherIndex);
                        catcherCached = true;
                    }

                    if (!escapeeCached && ShouldCacheIndex(escapeeIndex, character))
                    {
                        GetCharacterUpgradeInteraction(catcherIndex);
                        escapeeCached = true;
                    }

                    if (escapeeCached && catcherCached)
                        return UniTask.CompletedTask;
                }
                
                return UniTask.CompletedTask;

                static bool ShouldCacheIndex(int characterIndex, CharacterGameEntity character) =>
                    characterIndex == 0 && character.IsDefault || characterIndex != 0 && characterIndex == character.Index;
            }
        }
    }
}