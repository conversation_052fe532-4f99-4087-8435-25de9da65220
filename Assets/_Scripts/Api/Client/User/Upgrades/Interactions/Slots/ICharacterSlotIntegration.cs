using Api.Client.User.Upgrades.Interactions.Perks;
using Api.Client.User.Upgrades.Interactions.Slots.Availability;
using Jx.Utils.ChangeableObjects;
using LoM.Characters.ClientIntegration.Perks;

namespace Api.Client.User.Upgrades.Interactions.Slots
{
    public interface ICharacterSlotIntegration
    {
        int Id { get; }
        ICharacterSlotAvailability Availability { get; }
        bool IsEmpty { get; }
        IJxChangeableObject<ICharacterPerkIntegration> EquippedPerk { get; }


        void EquipPerk(PerkType? type);
        bool RarityIsSupported(PerkRarity perkRarity);
    }
}