using Jx.Utils.ChangeableObjects;

namespace Api.Client.User.Upgrades.Interactions.Slots.Availability
{
    internal class CharacterSlotAvailability : ICharacterSlotAvailability
    {
        public CharacterSlotAvailability(
            int availableSinceLevel,
            IJxChangeableObject<bool> isUnlocked
        )
        {
            AvailableSinceLevel = availableSinceLevel;
            IsUnlocked = isUnlocked;
        }

        public IJxChangeableObject<bool> IsUnlocked { get; }
        public int AvailableSinceLevel { get; }
    }
}