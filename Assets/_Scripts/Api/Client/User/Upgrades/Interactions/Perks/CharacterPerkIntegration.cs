using System;
using Api.Client.User.Upgrades.Interactions.Perks.Availability;
using Api.Client.User.Upgrades.Interactions.Perks.Content;
using Api.Client.User.Upgrades.Interactions.Perks.Progress;
using Jx.Utils.ChangeableObjects;
using LoM.Characters.ClientIntegration.Perks;

namespace Api.Client.User.Upgrades.Interactions.Perks
{
    internal class CharacterPerkIntegration : ICharacterPerkIntegration
    {
        private readonly Func<bool> _isEquipped;
        private readonly Func<bool> _canBeEquipped;
        
        public CharacterPerkIntegration(
            PerkType type,
            PerkRarity rarity,
            ICharacterPerkContent content,
            ICharacterPerkProgressLevelUp levelUp,
            ICharacterPerkAvailability availability,
            IJxChangeableObject<ICharacterPerkProgress> progress,
            Func<bool> isEquipped,
            Func<bool> canBeEquipped
        )
        {
            Type = type;
            Rarity = rarity;
            Content = content;
            LevelUp = levelUp;
            Availability = availability;
            Progress = progress;

            _isEquipped = isEquipped;
            _canBeEquipped = canBeEquipped;
        }

        public PerkType Type { get; }
        public PerkRarity Rarity { get; }
        
        public ICharacterPerkContent Content { get; }
        public ICharacterPerkProgressLevelUp LevelUp { get; }
        public ICharacterPerkAvailability Availability { get; }
        public IJxChangeableObject<ICharacterPerkProgress> Progress { get; }

        public bool IsEquipped()
        {
            return _isEquipped();
        }

        public bool CanBeEquipped()
        {
            return _canBeEquipped();
        }

        public bool IsUnlocked()
        {
            return Progress?.Value is { IsUnlocked: true };
        }

        public bool CanBeUpgraded()
        {
            return IsUnlocked() && Progress.Value.Level.CanBeUpgraded;
        }
    }
}