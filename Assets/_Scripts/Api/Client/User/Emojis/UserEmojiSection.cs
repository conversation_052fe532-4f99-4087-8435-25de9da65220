using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Api.Client.User.Rewards;
using Api.Client.User.Rewards.Adapters;
using Core.Helpers;
using Cysharp.Threading.Tasks;
using Jx.ApiGateway;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Extensions;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using LoM.Billing.ClientIntegration;
using LoM.Emojis.ClientIntegration;
using LoM.Messaging.ClientIntegrations;
using LoM.Messaging.Model.Emojis;
using LoM.Messaging.Requests.Emojis;
using LoM.Messaging.Responses.Emojis;
using MonsterLand.Meta.Api;

namespace Api.Client.User.Emojis
{
    public class UserEmojiSection : BaseUserSection, IUserEmojiSection, IDisposable
    {
        private const int EmojiSlotCount = 6;

        private readonly List<EmojiCollectionItem> _all;
        private readonly List<EmojiCollectionSlot> _slots;
        private readonly EquipmentNotifier _equipmentNotifier;

        private bool _equipmentSyncRequired;
        private bool _setupEquipmentFromServer;
        private IDisposable? _userSubscription;

        public UserEmojiSection(UserContext userContext, IJxMonsterLandMetaApi api)
            : base(userContext, api)
        {
            _all = new List<EmojiCollectionItem>();
            _slots = new List<EmojiCollectionSlot>();
            _equipmentNotifier = new EquipmentNotifier(this);
        }

        public IReadOnlyList<IEmojiCollectionItem> All => _all;
        public IReadOnlyList<IEmojiCollectionSlot> Slots => _slots;

        public UniTask<bool> EquipAsync(EmojiType emojiToEquip, int slotIndex, CancellationToken cancellationToken = default)
        {
            var slot = _slots.FirstOrDefault(s => s.SlotIndex == slotIndex);
            if (slot == null)
                return UniTask.FromResult(false);
            if (!slot.TrySetEquipped(emojiToEquip))
                return UniTask.FromResult(false);
            
            _equipmentNotifier.Notify(_slots.Select(s => s.EquippedEmoji.Value).ToList());
            return UniTask.FromResult(true);
        }

        public override UniTask ConfigureAsync(CancellationToken cancellationToken)
        {
            _setupEquipmentFromServer = true;
            
            var productByEmojiType = UserContext.Billing
                .GetProducts()
                .Where(p => p.Category == ProductCategory.Emoji && p.Reward.Kind == RewardKind.Emoji)
                .ToDictionary(p => p.Reward.Cast<EmojiUserReward>().EmojiType, p => p);
            
            foreach (var emojiType in JxEnumExtensions.GetEnumValues<EmojiType>())
            {
                productByEmojiType.TryGetValue(emojiType, out var product);
                _all.Add(new EmojiCollectionItem(emojiType, product));
            }

            for (var i = 0; i < EmojiSlotCount; ++i)
                _slots.Add(new EmojiCollectionSlot(i));
            
            _userSubscription = Api.User.SubscribeAndFire(OnUserChanged);

            return base.ConfigureAsync(cancellationToken);
        }

        private void OnUserChanged(UserDataClientIntegration? user)
        {
            var emojiState = user?.Emoji;
            if (emojiState == null)
                return;

            foreach (var item in _all)
                item.SetOwned(emojiState.Purchased.Contains(item.Type));

            SetEquipmentFromServer(emojiState);
        }

        private void SetEquipmentFromServer(UserEmojiStateClientIntegration emojiState)
        {
            if (!_setupEquipmentFromServer)
                return;

            _setupEquipmentFromServer = false;

            for (var i = 0; i < emojiState.Equipped.Count && i < _slots.Count; ++i)
                _slots[i].TrySetEquipped(emojiState.Equipped[i]);
        }

        void IDisposable.Dispose()
        {
            _userSubscription?.Dispose();
        }
        
        #region CLASSES

        private class EquipmentNotifier
        {
            private readonly TimeSpan _syncDelay;
            private readonly UserEmojiSection _section;

            private int _requestId;

            public EquipmentNotifier(UserEmojiSection section)
            {
                _syncDelay = TimeSpan.FromSeconds(3f);
                _section = section;
            }

            public void Notify(IReadOnlyList<EmojiType> equipped)
            {
                var requestId =Interlocked.Increment(ref _requestId);
                UniTask.RunOnThreadPool(SendSnapshotDelayedAsync).Forget();
                return;

                async UniTask SendSnapshotDelayedAsync()
                {
                    await UniTask.Delay(_syncDelay);

                    if (requestId != _requestId)
                        return;
                    
                    await SendSnapshotAsync(equipped);
                }
            }
            
            private UniTask SendSnapshotAsync(IReadOnlyList<EmojiType> equipped)
            {
                return _section.Api.SendRequestAsync<EmojiSyncEquipmentRequest, EmojiSyncEquipmentResponse>(
                    new EmojiSyncEquipmentRequest(equipped),
                    JxApiGatewayCommandImportance.Low
                );
            }
        }
        
        #endregion
    }
}