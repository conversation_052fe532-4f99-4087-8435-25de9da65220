using System;
using System.Collections.Generic;
using Api.Client.User.Billing;
using Api.Client.User.CharacterPath;
using Api.Client.User.Chests;
using Api.Client.User.Compensations;
using Api.Client.User.DailyBonus;
using Api.Client.User.Debugging;
using Api.Client.User.Emojis;
using Api.Client.User.Experiments;
using Api.Client.User.ExternalServices;
using Api.Client.User.FeatureAccess;
using Api.Client.User.Matchmaking;
using Api.Client.User.Missions;
using Api.Client.User.Missions.Definitions;
using Api.Client.User.Preset;
using Api.Client.User.Profile;
using Api.Client.User.Progress;
using Api.Client.User.Quests;
using Api.Client.User.Rewards;
using Api.Client.User.SeasonPass;
using Api.Client.User.Stats;
using Api.Client.User.TrophyPath;
using Api.Client.User.Tutorials;
using Api.Client.User.Upgrades;
using Configs.Perks;
using Cysharp.Threading.Tasks;
using Jx.User.Reporting.Api;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Extensions;
using MonsterLand.Matchmaking.Providers;
using MonsterLand.Meta.Api;
using Savings;
using UnityEngine.Purchasing;
using UnityEngine.Scripting;
using Zenject;

namespace Api.Client.User
{
    public class UserContext : IUserContext, IJxUserReportingPublicUserIdProvider, IJxMatchmakingPublicIdProvider, IDisposable
    {
        private readonly IJxMonsterLandMetaApi _api;

        [Preserve]
        public UserContext(
            IJxMonsterLandMetaApi api,
            DiContainer diScope,
            ILocalSave localSave,
            IPerkIntegration perkIntegration,
            IUserMissionDefinitionParser definitionParser,
            ICharacterPathProvider characterPathProvider
        )
        {
            _api = api;

            Preset = new UserPresetSection(this, api);
            Billing = new UserBillingSection(this, api, diScope);
            DailyBonus = new UserDailyBonusSection(this, api);
            Profile = new UserProfileSection(this, api);
            Progress = new UserProgressSection(this, api);
            DailyQuests = new UserDailyQuestsSection(this, api);
            Rewards = new UserRewardsSection(this, api);
            Tutorials = new UserTutorialsSection(this, api);
            Missions = new UserMissionsSection(this, api, definitionParser);
            Stats = new UserStatsSection(this, api);
            Chests = new UserChestsSection(this, api);
            Debug = new UserDebugSection(this, api);
            Upgrades = new UserUpgradesSection(this, api, perkIntegration, characterPathProvider, localSave);
            Matchmaking = new UserMatchmakingSection(this, api);
            ExternalIntegration = new ExternalIntegrationUserSection(this, api);
            TrophyPath = new UserTrophySection(this, api, diScope);
            FeatureAccess = new UserFeatureAccessSection(this, api);
            Compensation = new UserCompensationSection(this, api);
            ExperimentalFeature = new UserExperimentalFeatureSection(this, api);
            Emoji = new UserEmojiSection(this, api);
            SeasonPass = new UserSeasonPassSection(this, api);

            IsDebugUser = _api.User.CreateConverted(u => u?.IsDebugUser ?? false);
        }

        public UserBillingSection Billing { get; }
        public UserDailyBonusSection DailyBonus { get; }
        public UserProfileSection Profile { get; }
        public UserProgressSection Progress { get; }
        public UserDailyQuestsSection DailyQuests { get; }
        public UserRewardsSection Rewards { get; }
        public UserTutorialsSection Tutorials { get; }
        public UserMissionsSection Missions { get; }
        public UserPresetSection Preset { get; }
        public UserStatsSection Stats { get; }
        public UserChestsSection Chests { get; }
        public UserDebugSection Debug { get; }
        public UserUpgradesSection Upgrades { get; }
        public UserMatchmakingSection Matchmaking { get; }
        public ExternalIntegrationUserSection ExternalIntegration { get; }
        public UserTrophySection TrophyPath { get; }
        public UserFeatureAccessSection FeatureAccess { get; }
        public UserCompensationSection Compensation { get; }
        public UserExperimentalFeatureSection ExperimentalFeature { get; }
        public UserEmojiSection Emoji { get; }
        public UserSeasonPassSection SeasonPass { get; }

        public IJxChangeableObject<bool> IsDebugUser { get; }
        public bool IsInitialized { get; private set; }

        IUserBillingSection IUserContext.Billing => Billing;
        IUserDailyBonusSection IUserContext.DailyBonus => DailyBonus;
        IUserProfileSection IUserContext.Profile => Profile;
        IUserProgressSection IUserContext.Progress => Progress;
        IUserDailyQuestsSection IUserContext.DailyQuests => DailyQuests;
        IUserRewardsSection IUserContext.Rewards => Rewards;
        IUserTutorialsSection IUserContext.Tutorials => Tutorials;
        IUserMissionsSection IUserContext.Missions => Missions;
        IUserPresetSection IUserContext.Preset => Preset;
        IUserStatsSection IUserContext.Stats => Stats;
        IUserChestsSection IUserContext.Chests => Chests;
        IUserDebugSection IUserContext.Debug => Debug;
        IUserUpgradesSection IUserContext.Upgrades => Upgrades;
        IUserMatchmakingSection IUserContext.Matchmaking => Matchmaking;
        IExternalIntegrationUserSection IUserContext.ExternalIntegration => ExternalIntegration;
        IUserTrophySection IUserContext.TrophyPath => TrophyPath;
        IUserFeatureAccessSection IUserContext.FeatureAccess => FeatureAccess;
        IUserCompensationSection IUserContext.Compensation => Compensation;
        IUserExperimentalFeatureSection IUserContext.ExperimentalFeature => ExperimentalFeature;
        IUserEmojiSection IUserContext.Emoji => Emoji;
        IUserSeasonPassSection IUserContext.SeasonPass => SeasonPass;

        public void MarkInitialized()
        {
            IsInitialized = true;
        }

        public IEnumerable<IUserSection> EnumerateSections()
        {
            yield return Preset;
            yield return Chests;
            yield return Billing;
            yield return Progress;
            yield return Upgrades;
            yield return Profile;
            yield return Rewards;
            yield return DailyBonus;
            yield return DailyQuests;
            yield return Tutorials;
            yield return Missions;
            yield return Stats;
            yield return Debug;
            yield return ExternalIntegration;
            yield return TrophyPath;
            yield return FeatureAccess;
            yield return Compensation;
            yield return ExperimentalFeature;
            yield return Emoji;
            yield return SeasonPass;
        }

        public DateTime ServerTimestampToLocalDateTime(long serverTimestamp) => _api.ServerTimestampToLocalDateTime(serverTimestamp);

        public async UniTask<bool> ResetAsync()
        {
            try
            {
                await _api.ResetUserAsync();

                foreach (var section in EnumerateSections())
                {
                    await section.ResetAsync();
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public UniTask<PurchaseStatus> OnInAppPurchasedAsync(Product product) => Billing.OnInAppPurchasedAsync(product);

        string IJxUserReportingPublicUserIdProvider.Get() => Profile.Model.Value.PublicId;
        string? IJxMatchmakingPublicIdProvider.Get() => Profile.Model.Value?.PublicId;
        
        public void Dispose()
        {
            foreach (var section in EnumerateSections())
                section.TryDispose();
        }
    }
}