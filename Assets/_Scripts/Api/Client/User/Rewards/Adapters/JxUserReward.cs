using System;

namespace Api.Client.User.Rewards.Adapters
{
    public abstract class JxUserReward : IJxUserReward
    {
        private readonly Func<IJxUserReward, bool> _isAvailable;

        protected JxUserReward(
            string id,
            string? contentPath,
            Func<IJxUserReward, bool> isAvailable
        )
        {
            Id = id;
            ContentPath = contentPath;
            _isAvailable = isAvailable;
        }

        public string Id { get; }
        public abstract RewardKind Kind { get; }
        public string? ContentPath { get; }

        public bool IsAvailable()
        {
            return _isAvailable(this);
        }
    }
}