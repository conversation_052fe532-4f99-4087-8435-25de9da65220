using System;

namespace Api.Client.User.Rewards.Adapters
{
    public class GoldUserReward : JxUserReward,
                                  IResourceReward
    {
        public GoldUserReward(
            string id,
            int value,
            string? contentPath,
            Func<IJxUserReward, bool> isAvailable
        )
            : base(id, contentPath, isAvailable)
        {
            Value = value;
        }

        public override RewardKind Kind => RewardKind.Gold;
        public int Value { get; }
    }
}