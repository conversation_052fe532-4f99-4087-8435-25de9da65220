using System;
using System.Collections.Generic;
using Jx.Utils.Collections;
using LoM.Characters.ClientIntegration;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public class CharacterPathTreeCollector : ICharacterPathTreeCollector,
                                              IDisposable
    {
        private readonly IReadOnlyList<ICharacterUpgradeTreeNode> _allNodes;
        private readonly bool _isEscapee;
        private readonly IDictionary<CharacterStatKind, CharacterPathCollector> _kindCache;
        private readonly IDictionary<CharacterStatType, CharacterPathCollector> _statCache;

        public CharacterPathTreeCollector(IReadOnlyList<ICharacterUpgradeTreeNode> allNodes, bool isEscapee)
        {
            _allNodes = allNodes;
            _isEscapee = isEscapee;
            _kindCache = new Dictionary<CharacterStatKind, CharacterPathCollector>();
            _statCache = new Dictionary<CharacterStatType, CharacterPathCollector>();
        }

        public ICharacterPathCollector GetForKind(CharacterStatKind kind)
        {
            return _kindCache.GetOrAdd(kind, () => CharacterPathCollector.NewFromKind(kind, _allNodes, _isEscapee));
        }
        
        public ICharacterPathCollector GetForStat(CharacterStatType stat)
        {
            return _statCache.GetOrAdd(stat, () => CharacterPathCollector.NewFromStat(stat, _allNodes));
        }

        public void Dispose()
        {
            foreach (var pair in _kindCache)
                pair.Value.Dispose();
            foreach (var pair in _statCache)
                pair.Value.Dispose();
        }
    }
}