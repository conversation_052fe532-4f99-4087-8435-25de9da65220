using System;
using System.Collections.Generic;
using System.Linq;
using Core.Helpers;
using Core.Utils;
using Jx.Utils.Objects;
using LoM.Characters.ClientIntegration;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.CharacterPath.Rewards;
using UnityEngine.Playables;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public static class CharacterPathHelper
    {
        public static bool IsAbility(this CharacterStatType type)
            => GetKind(type) == CharacterStatKind.Ability;
        
        public static ushort GetAbilityIndex(this CharacterStatType type)
        {
            if (type.GetKind() != CharacterStatKind.Ability)
                return 0;
            var index = (ushort)(type - 50);
            if (index < 0)
                return 0;
            return index;
        }

        public static IReadOnlyList<CharacterStatType> GetStatsByKind(CharacterStatKind kind)
        {
            return Enum.GetValues(typeof(CharacterStatType)).Cast<CharacterStatType>().Where(stat => stat.GetKind() == kind).ToList();
        }
        
        public static CharacterStatKind GetKind(this CharacterStatType type)
        {
            if (IsInRangeInclusive(type, new(50, 54)))
                return CharacterStatKind.Ability;
            
            if (type == CharacterStatType.IncreaseMovementSpeed)
                return CharacterStatKind.Movement;
            
            if (IsInRangeInclusive(type, new (10_000, 10_499)) || IsInRangeInclusive(type, new (20_000, 20_499)))
                return CharacterStatKind.Agility;
            
            if (IsInRangeInclusive(type, new(10_500, 10_999)))
                return CharacterStatKind.Endurance;
            
            if (IsInRangeInclusive(type, new(11_000, 11_499)))
                return CharacterStatKind.Potion;
            
            if (IsInRangeInclusive(type, new(21_000, 21_499)))
                return CharacterStatKind.Trap;
            
            if (IsInRangeInclusive(type, new(20_500, 20_999)))
                return CharacterStatKind.Deterrence;
            
            return CharacterStatKind.None;
        }

        private static bool IsInRangeInclusive(CharacterStatType type, MinMax<ushort> minmax)
        {
            var index = GetIndex(type);
            return index >= minmax.Min && index <= minmax.Max;
        }

        private static bool IsGreater(CharacterStatType type, ushort than)
        {
            var index = GetIndex(type);
            return index > than;
        }
        
        private static ushort GetIndex(CharacterStatType upgrade) => (ushort)upgrade;
    }
}