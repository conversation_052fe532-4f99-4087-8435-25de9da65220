using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.CharacterPath.Rewards;

namespace Api.Client.User.CharacterPath.Rewards
{
    public class CharacterUpgradeNodeRewardStats : ICharacterUpgradeNodeReward
    {
        private readonly Func<CancellationToken, UniTask<bool>> _claimAsync;

        public CharacterUpgradeNodeRewardStats(
            ICharacterPathRewardDefinitionClientIntegration definition,
            Func<CancellationToken, UniTask<bool>> claimAsync
        )
        {
            if (definition == null)
                throw new ArgumentNullException(nameof(definition));
            if (claimAsync == null)
                throw new ArgumentNullException(nameof(claimAsync));
            
            Definition = definition;
            _claimAsync = claimAsync;
        }

        public ICharacterPathRewardDefinitionClientIntegration Definition { get; }

        public UniTask<bool> ClaimAsync(CancellationToken cancellationToken = default)
        {
            return _claimAsync(cancellationToken);
        }
    }
}