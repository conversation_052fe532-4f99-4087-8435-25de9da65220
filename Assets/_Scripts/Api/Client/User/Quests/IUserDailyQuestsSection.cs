using System;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using LoM.Messaging.ClientIntegrations.Quests;
using LoM.Messaging.ClientIntegrations.Quests.Preset;

namespace Api.Client.User.Quests
{
    public interface IUserDailyQuestsSection
    {
        IJxChangeableObject<UserQuestStorageClientIntegration> UserProgress { get; }
        IJxChangeableObject<PresetQuestsModel> Profile { get; }

        UniTask<bool> ClaimSlotRewardAsync(int slotIndex);
        UniTask<bool> RefreshSlotAsync(int slotIndex);
        TimeSpan GetRestTimeQuestsUpdated();
        bool IsThereAnyAvailableToPurchaseSlot();
    }
}