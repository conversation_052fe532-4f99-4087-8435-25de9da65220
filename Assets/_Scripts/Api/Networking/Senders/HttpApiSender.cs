using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BestHTTP;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using Jx.ApiGateway.Web.Http;
using Jx.ApiGateway.Web.Http.Contents;
using Jx.ApiGateway.Web.Http.Responses;
using Jx.ApiGateway.Web.UrlSwitching;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.IdGeneration;
using Jx.Utils.Logging;
using Jx.Utils.Serialization.Binary;

namespace Api.Networking.Senders
{
    internal abstract class HttpApiSender<TBaseRequest, TBaseResponse>
        where TBaseRequest : class
        where TBaseResponse : class
    {
        private const int MaxInvalidResponseAttemptCount = 5;

        private readonly JxIdGenerator _idGenerator;
        private readonly IJxBinarySerializer<object> _serializer;
        private readonly JxHttpAsyncClient _httpClient;
        private readonly IJxValueContainer<IJxSwitchableUrl> _url;

        protected HttpApiSender(
            IJxValueContainer<IJxSwitchableUrl> url,
            IJxBinarySerializer<object> serializer
        )
        {
            _url = url;
            _serializer = serializer;

            _idGenerator = new JxIdGenerator(200);
            _httpClient = new JxHttpAsyncClient(
                new JxHttpAsyncClientOptions
                {
                    Timeout = TimeSpan.FromSeconds(15f),
                    DefaultRequestHeaders = new Dictionary<string, string>
                    {
                        ["Cache-Control"] = "no-cache, no-store, must-revalidate",
                        ["Pragma"] = "no-cache",
                        ["Expires"] = "-1",
                        ["UserSessionIdentifier"] = Guid.NewGuid().ToString(),
                    },
                }
            );

            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        protected IJxLogger Logger { get; }

        protected virtual TimeSpan FailedRequestDelay => TimeSpan.FromSeconds(1.5f);
        protected virtual TimeSpan WaitConnectionRestorationDelay => TimeSpan.FromSeconds(1f);

        protected async UniTask<TResponse?> SendRequestAsyncInternal<TRequest, TResponse>(
            TRequest request,
            Func<TRequest, UniTask>? prepareRequestAsync,
            int attempts,
            CancellationToken cancellationToken
        )
            where TRequest : class, TBaseRequest
            where TResponse : class, TBaseResponse
        {
            var messageId = _idGenerator.Generate();
            var invalidResponseAttempts = MaxInvalidResponseAttemptCount;
            var requestName = request.GetType().Name;

            while (attempts > 0)
            {
                --attempts;

                try
                {
                    if (prepareRequestAsync != null)
                    {
                        await prepareRequestAsync(request);
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    var response = await SendHttpRequestAsync<TRequest, TResponse>(request, messageId);

                    cancellationToken.ThrowIfCancellationRequested();

                    if (ValidateResponse(requestName, response))
                    {
                        return response;
                    }

                    if (attempts <= 0)
                    {
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        throw;
                    }

                    if (ShouldRetryException(ref invalidResponseAttempts, requestName, ex))
                    {
                        await UniTask.Delay(WaitConnectionRestorationDelay, cancellationToken: cancellationToken);
                    }
                }

                await UniTask.Delay(FailedRequestDelay, cancellationToken: cancellationToken);
            }

            return null;
        }

        protected abstract bool ValidateResponse(string requestName, TBaseResponse? response);

        private bool ShouldRetryException(ref int invalidResponseAttempts, string requestName, Exception exception)
        {
            if (exception is AsyncHTTPException asyncHttpException)
            {
                if (asyncHttpException.StatusCode == 0)
                {
                    return true;
                }

#if UNITY_EDITOR
                Logger.LogEditorOnlyError($"SendHttpRequestAsync '{requestName}'", exception);
#endif

                return false;
            }

            if (exception is HttpInvalidResponseException invalidResponseException)
            {
                if (invalidResponseAttempts > 0)
                {
                    --invalidResponseAttempts;

#if UNITY_EDITOR
                    Logger.LogEditorOnlyError($"SendHttpRequestAsync '{requestName}'", exception);
#endif

                    return true;
                }

                Logger.LogError($"Max invalid response attempts exceeded '{MaxInvalidResponseAttemptCount}' at '{requestName}'", invalidResponseException);

                return false;
            }

            Logger.LogError(exception);
            return false;
        }

        private async Task<TResponse?> SendHttpRequestAsync<TRequest, TResponse>(
            TRequest request,
            int messageId
        )
            where TRequest : class
        {
            var payload = _serializer.Serialize(request);

            var sender = new JxHttpRequestSender<byte[]>(
                HTTPMethods.Put,
                _httpClient,
                _url.Value,
                JxHttpResponseParsers.Binary,
                new JxBinaryHttpContent(payload, new[] { new KeyValuePair<string, string>("Jx-MessageId", messageId.ToString()) })
            );

            using (var response = await sender.SendAsync(CancellationToken.None, null))
            {
                EnsureSuccessStatusCode(response.StatusCode);

                var responseData = response.Read().Parse(CancellationToken.None);

                ValidateResponse(responseData);
                
                var obj = _serializer.DeserializeChunk(responseData, 0, responseData.Length);

                return (TResponse)obj;
            }
        }

        private void ValidateResponse(byte[] value)
        {
            if (value == null || value.Length < 4)
            {
                throw new HttpInvalidResponseException($"Received '{value?.Length.ToString() ?? "null"}' response length");
            }
        }

        private void EnsureSuccessStatusCode(int statusCode)
        {
            if (statusCode != 200)
            {
                throw new AsyncHTTPException(statusCode, "Unhandled invalid status code exception", "Random content");
            }
        }
    }
}