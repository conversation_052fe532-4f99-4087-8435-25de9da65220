#if HEADLESS
using App.Headless;
using Jx.MatchDirector.Api;
using UnityEngine.Scripting;

namespace Api.Matchmaking.HeadlessOnly
{
    [Preserve]
    public class MatchDirectorEngineModule : JxMatchDirectorEngineModule<HeadlessMatchDirectorAllocator>
    {
        [Preserve]
        public MatchDirectorEngineModule() { }

        protected override JxMatchDirectorIntegrationOptions CreateOptions()
        {
            var parameters = HeadlessGameServerParameters.Instance;
            return new JxMatchDirectorIntegrationOptions(parameters.Geo, parameters.GameMode.ToString(), parameters.Scope, logEnabled: false);
        }
    }
}
#endif