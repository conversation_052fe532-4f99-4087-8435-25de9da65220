using System.Collections.Generic;
using App.Analytics;
using ExternalServices.Telemetry;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using MonsterLand.Matchmaking;

namespace Modules.Attribution
{
    internal static class JxTelemetryIntegrationExtensions
    {
        public static void TrackClick(this IJxTelemetryIntegration telemetry, Dictionary<string, string>? parameters)
        {
            telemetry.TrackEvent(
            TelemetryTrackingContexts.UserUIInteraction, 
                "click",
                parameters
            );
        }
    
        public static void TrackReference(this IJxTelemetryIntegration telemetry, JxAttributionTrackingReference trackingReference)
        {
            telemetry.TrackEvent(TelemetryTrackingContexts.Attribution, "reference",new Dictionary<string, string>
            {
                [TelemetryEventParameters.Provider] = trackingReference.Source.ToString(),
                [TelemetryEventParameters.Placement] = trackingReference.Url.Replace("://", "_").Replace(".", "_") ?? "Unknown",
            });
        }

        public static string ToTrackingName(this JxMatchmakingTeamKind team) => team == JxMatchmakingTeamKind.Catcher ? "catcher" : "escapee";
        
        public static string ToSimplifiedTrackingName(this JxMatchmakingTeamKind team)
        {
            return team switch
            {
                JxMatchmakingTeamKind.Catcher => "cat",
                JxMatchmakingTeamKind.Escapee => "esc",
                _ => "unkn"
            };
        }
    }
}