using System;
using System.Collections.Generic;
using System.Threading;
using App;
using Cysharp.Threading.Tasks;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils;
using Jx.Utils.Logging;
using Jx.Utils.MainThread;
using Modules.Attribution.InstallReferrers;
using UnityEngine;
using UnityEngine.Scripting;

namespace Modules.Attribution
{
    internal class JxAttributionIntegration : IJxAttributionIntegration, IJxInitializer, IDisposable
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(JxAttributionIntegration));

        private readonly IJxInstallReferrerProvider _installReferrerProvider;

        [Preserve]
        public JxAttributionIntegration(IJxInstallReferrerProvider installReferrerProvider)
        {
            _installReferrerProvider = installReferrerProvider;
        }

        IJxInitializer IJxAttributionIntegration.Initializer => this;

        public void TrackDeepLink(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                throw new ArgumentNullException(nameof(url));
            }

            JxTelemetryIntegration.Instance.TrackReference(
                new JxAttributionTrackingReference()
                {
                    Source = JxAttributionSource.DeepLink,
                    Url = url,
                }
            );
        }

        UniTask IJxInitializer.InitializeAsync(CancellationToken cancellationToken)
        {
            JxMainThread.Enqueue(
                () =>
                {
                    Application.deepLinkActivated += TryTrackDeepLink;

                    TryTrackDeepLink(Application.absoluteURL);
                }
            );

            UniTask.RunOnThreadPool(SendInstallReferrerAsync, cancellationToken: cancellationToken);

            return UniTask.CompletedTask;
        }

        UniTask IJxInitializer.ConfigureAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        UniTask IJxInitializer.OnConfiguredAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        UniTask IJxInitializer.ResetAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        UniTask IJxInitializer.LoadAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        private void TryTrackDeepLink(string url)
        {
            if (!string.IsNullOrWhiteSpace(url))
            {
                JxTelemetryIntegration.Instance.TrackReference(
                    new JxAttributionTrackingReference
                    {
                        Source = JxAttributionSource.DeepLink,
                        Url = url
                    }
                );
            }
        }

        private async UniTask SendInstallReferrerAsync()
        {
            JxInstallReferrer installReferrer;
            try
            {
                installReferrer = await _installReferrerProvider.GetAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Failed to get install referrer",
                    ex,
                    trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["AttributionSource"] = _installReferrerProvider.AttributionSource.ToString()
                    }
                );

                return;
            }

            if (installReferrer == null)
            {
                _logger.LogDebug(
                    "No install referrer found",
                    trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["AttributionSource"] = _installReferrerProvider.AttributionSource.ToString()
                    }
                );

                return;
            }

            JxTelemetryIntegration.Instance.TrackReference(
                new JxAttributionTrackingReference
                {
                    Source = _installReferrerProvider.AttributionSource,
                    Url = installReferrer.Url,
                }
            );
        }

        public void Dispose()
        {
            Application.deepLinkActivated -= TryTrackDeepLink;
        }
    }
}