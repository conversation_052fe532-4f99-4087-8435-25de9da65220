using System;
using System.Collections.Generic;
using System.Linq;
using Core.Attribution;
using Core.Helpers;
using Core.Helpers.Serializable;
using EditorCools;
using Jx.Utils.Logging;
using UnityEditor.Animations;
using UnityEngine;

namespace GameplayComponents.CharacterPresentation
{
    public class CharacterAnimatorMetaInfo : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(CharacterAnimatorMetaInfo));
        
        [SerializeField]
        private Animator _animator = null!;
        
        [SerializeField]
        [JxReadonly(false)]
        private List<ClipInfo> _stateDurations = null!;
        
        public float GetStateDuration(string stateName)
        {
            var state = _stateDurations.FirstOrDefault(s => s.StateEquals(stateName));
            if (state == null)
            {
                _logger.LogError("Failed to find state duration", 
                    trackingFactory: () => new Dictionary<string, object>()
                {
                    ["StateName"] = stateName
                });
                return 1f;
            }
            return state.Duration;
        }

#if UNITY_EDITOR
        [Button("Rebuild")]
        private void Rebuild()
        {
            if (Application.isPlaying)
                return;
            
            _animator ??= GetComponent<Animator>();
            
            var animatorController = _animator.runtimeAnimatorController;
            if (animatorController == null)
                return;
            
            var overrideController = animatorController as AnimatorOverrideController;
            if (overrideController == null)
                return;

            var baseController = overrideController.runtimeAnimatorController as AnimatorController;
            if (baseController == null)
                return;
            
            var layersOverride = new List<KeyValuePair<AnimationClip, AnimationClip>>();
            overrideController.GetOverrides(layersOverride);
            if (layersOverride.IsNullOrEmpty())
                return;
            
            // _stateDurations.Clear();

            for (var i = 0; i < _animator.layerCount; ++i)
            {
                var layer = baseController.layers[i];
                
                var states = layer.stateMachine.states;
                if (states.IsNullOrEmpty())
                    continue;
                
                foreach (var state in states)
                {
                    if (state.state.motion is AnimationClip clip)
                    {
                        _stateDurations.Add(new ClipInfo(state.state.name, GetOverridenClipLength(layersOverride, clip)));
                    }
                    else if (state.state.motion is BlendTree blendTree)
                    {
                        _stateDurations.Add(new ClipInfo(state.state.name, 1f));
                        ProcessBlendTree(layersOverride, blendTree);
                    }
                }
            }
        }
        
        private static float GetOverridenClipLength(List<KeyValuePair<AnimationClip, AnimationClip>> clipsOverride, AnimationClip clip)
        {
            var overrideClip = clipsOverride.Find(a => CompareClips(a.Key, clip));
            return overrideClip.Value?.length ?? clip.length;
        }

        private static bool CompareClips(AnimationClip first, AnimationClip second)
        {
            return string.Equals(first.name, second.name);
        }
        
        private void ProcessBlendTree(List<KeyValuePair<AnimationClip, AnimationClip>> clipsOverride, BlendTree blendTree)
        {
            foreach (var childMotion in blendTree.children)
            {
                if (childMotion.motion is AnimationClip childClip)
                {
                    _stateDurations.Add(new ClipInfo(childClip.name, GetOverridenClipLength(clipsOverride, childClip)));
                }
                else if (childMotion.motion is BlendTree childBlendTree)
                {
                    ProcessBlendTree(clipsOverride, childBlendTree);
                }
            }
        }

        private void OnValidate()
        {
            Rebuild();
        }
#endif
        
        #region CLASSES

        [Serializable]
        private class ClipInfo
        {
            [field: SerializeField]
            public string StateName { get; private set; }
            
            [field: SerializeField]
            public float Duration { get; private set;  }

            public ClipInfo(string stateName, float duration)
            {
                StateName = stateName;
                Duration = duration;
            }

            public bool StateEquals(string state)
            {
                return string.Equals(state, StateName, StringComparison.InvariantCultureIgnoreCase);
            }
        }

        #endregion
    }
}