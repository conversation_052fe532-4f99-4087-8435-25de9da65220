using System;

namespace GameplayComponents.CharacterPresentation
{
    // mirror serialized
    public struct CharacterViewIdentifier : IEquatable<CharacterViewIdentifier>
    {
        public int Index;
        public string SkinEntityId;
        
        public bool Equals(CharacterViewIdentifier other)
        {
            return Index == other.Index && SkinEntityId == other.SkinEntityId;
        }
        public override bool Equals(object? obj)
        {
            return obj is CharacterViewIdentifier other && Equals(other);
        }
        public override int GetHashCode()
        {
            return HashCode.Combine(Index, SkinEntityId);
        }
        public static bool operator ==(CharacterViewIdentifier left, CharacterViewIdentifier right)
        {
            return left.Equals(right);
        }
        public static bool operator !=(CharacterViewIdentifier left, CharacterViewIdentifier right)
        {
            return !left.Equals(right);
        }
    }
}