using System;
using GameplayComponents.Animations;
using UnityEngine;

namespace GameplayComponents.CharacterPresentation
{
    [DisallowMultipleComponent]
    public class CharacterAnimationEventListenerComponent : <PERSON>o<PERSON><PERSON><PERSON>our,
                                                            ICharacterAnimationEventListener
    {
        [SerializeField]
        private AnimationEventHandler _stepHandler = null!;
        
        [SerializeField]
        private AnimationEventHandler _hitHandler = null!;
        
        public IDisposable SubscribeOnStep(Action onStep)
        {
            return _stepHandler.Subscribe(onStep);
        }
        
        public IDisposable SubscribeOnHit(Action onHit)
        {
            return _hitHandler.Subscribe(onHit);
        }

        #region UNITY LINKED METHODS

        // linked in unity animation window
        private void EmitStep() => _stepHandler.TryRunHandler();
        private void EmitHit() => _hitHandler.TryRunHandler();
        
        #endregion
    }
}