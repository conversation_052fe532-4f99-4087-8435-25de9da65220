using System.Collections.Generic;
using Core.Extensions;
using GameplayNetworking.Gameplay.Components.Server.Visibility.Client;
using GameplayNetworking.Gameplay.Components.Server.Visibility.Client.Handlers;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Components.TeamVisualizers.Behaviours;
using Jx.Utils.ChangeableObjects;
using Mirror;
using UnityEngine;

namespace GameplayComponents.CharacterPresentation
{
    public class GameplayCharacterView : CharacterView
    {
        private CharacterAnimationEventListenerComponent? _animationEventListener;

        private readonly IJxChangeableObject<LocalVisibilityHandlerGroup?, LocalVisibilityHandlerGroup?> _activeVisibilityGroup =
            new JxChangeableObject<LocalVisibilityHandlerGroup?, LocalVisibilityHandlerGroup?>();
        public IJxChangeableObject<LocalVisibilityHandlerGroup?> ActiveVisibilityGroup => _activeVisibilityGroup;

        public ICharacterAnimationEventListener? FindActualAnimationEventListener()
        {
            // lazy
            _animationEventListener ??= GetComponentInChildren<CharacterAnimationEventListenerComponent>();
            return _animationEventListener;
        }

        protected override void OnAfterActivePresenterChanged(CharacterViewPresenter? presenter)
        {
            base.OnAfterActivePresenterChanged(presenter);

            // for lazy, not all characters need this component
            // every model has own listener attached
            _animationEventListener = null;

            if (presenter == null)
                return;

#if !HEADLESS
            Client_SetupModel(presenter.Model);
#endif
        }

        #region CLIENT

#if !HEADLESS

        [Client]
        private void Client_SetupModel(GameObject model)
        {
            var localVisibilityHandlerGroup = new LocalVisibilityHandlerGroup();

            foreach (var r in EnumerateRenderers(model))
            {
                var handler = r.gameObject.GetOrAddComponent<RendererClientVisibilityHandler>();
                handler.Initialize(r);
                localVisibilityHandlerGroup.Add(handler);

                if (r is SkinnedMeshRenderer sr)
                {
                    SetupOutline(r, localVisibilityHandlerGroup);
                    sr.updateWhenOffscreen = true;
                }
            }
            
            foreach (var vfx in EnumerateVfx(model))
            {
                var handler = vfx.gameObject.GetOrAddComponent<VfxClientVisibilityHandler>();
                handler.Initialize(vfx);
                localVisibilityHandlerGroup.Add(handler);
            }

            _activeVisibilityGroup.Set(localVisibilityHandlerGroup);
        }

        private IEnumerable<ParticleSystem> EnumerateVfx(GameObject model)
        {
            return model.GetComponentsInChildren<ParticleSystem>(true);
        }

        private IEnumerable<Renderer> EnumerateRenderers(GameObject model)
        {
            var modelRenderers = model.GetComponentsInChildren<Renderer>(true);
            if (modelRenderers != null)
            {
                foreach (var r in modelRenderers)
                    yield return r;
            }

            if (transform.parent.TryGetComponent<NetGamePlayer>(out var player))
            {
                yield return player.Collision.Visualization.Renderer;

                if (player is NetCatcherPlayer catcher)
                    foreach (var r in catcher.ClientRageController.ProgressBar.SpriteRenderers)
                        yield return r;
            }
        }

        private void SetupOutline(Renderer r, LocalVisibilityHandlerGroup visibilityHandlerGroup)
        {
            var rendererGo = r.gameObject;

            var outline = rendererGo.GetOrAddComponent<OUTLINE.Outline>();
            rendererGo.GetOrAddComponent<VisualizerOutlineTeamBehaviour>().Initialize(outline);

            var outlineVisibility = rendererGo.GetOrAddComponent<ComponentClientVisibilityHandler>();
            outlineVisibility.Initialize(outline, disabledOnTransparent: true);

            visibilityHandlerGroup.Add(outlineVisibility);
        }

#endif

        #endregion
    }
}