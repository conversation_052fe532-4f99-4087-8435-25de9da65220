using GameplayComponents.Movement;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Movement.Modification;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace GameplayComponents.MovementModifications.Processors
{
    public class RepulsionMovementProcessor : MovementProcessor
    {
        private GameplayMovementController.IAutoMovementHandler? _autoMovement;
        private Vector3 _moveDirection;
        private Vector3 _targetPosition;

        public RepulsionMovementProcessor(NetGamePlayer player)
            : base(player) { }

        public override MovementModificationType Type => MovementModificationType.Repulsion;

        protected override void OnEnabled()
        {
            _moveDirection = (PlayerTransform.position - Modification.Target.transform.position).normalized;
            _autoMovement?.Dispose();
            _autoMovement = Player.NetTransform.Movement.GetAutoMovement();
            _targetPosition = TargetTransform.position;
        }

        public override void OnDisabled() => _autoMovement?.Dispose();

        public override void OnTick()
        {
            if (_autoMovement == null || _autoMovement.IsDisposed)
                return;
            if (Modification.Target.IsDestroyed())
                return;
            if (!InActiveRadius())
                return;

            if (Modification.ListenTargetTransformChange)
                _targetPosition = Modification.Target.transform.position;

            _autoMovement.TranslateIn(_moveDirection * Modification.Strength);
        }

        private bool InActiveRadius() => Vector3.SqrMagnitude(PlayerTransform.position - _targetPosition) < Modification.SqrRadius;
    }
}