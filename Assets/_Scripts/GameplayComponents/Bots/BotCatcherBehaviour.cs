using System;
using System.Collections.Generic;
using System.Linq;
using GameplayComponents.Bots.Configuration;
using GameplayComponents.Bots.Constraints;
using GameplayComponents.Bots.Constraints.Catcher.Parameters;
using GameplayComponents.Bots.Memory;
using GameplayComponents.Bots.Mood.Catcher;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.States.Catcher;
using GameplayComponents.Bots.States.Catcher.UseInventory;
using GameplayComponents.Bots.States.SharedStates;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles;
using Graph;
using UnityEngine;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
using Core.Helpers.DebugScreen;
using Core.UnityUtils.Debug.Spatial;
#endif

namespace GameplayComponents.Bots
{
    public class BotCatcherBehaviour : BotBehaviour<
        NetCatcherPlayer, 
        CatcherBotVision, 
        CatcherBotMemory, 
        CatcherBotParameters,
        CatcherBotConfiguration, 
        CatcherBotDifficultyProfileDto,
        CatcherBotMoodController>
    {
#if !HEADLESS && !PRODUCTION_BUILD && DEV
        TextDebugSpatialHandler? _textDebugDrawer;
#endif
        
        protected override ISet<ActionType> ActiveActions { get; } = new HashSet<ActionType>()
        {
            ActionType.Idle,
            ActionType.Interaction,
            ActionType.Disarmed,
            ActionType.AbilityDisabled,
            ActionType.AbilityPreparation,
            ActionType.AbilityInProgress
        };
        
        protected virtual IReadOnlyList<IGraphNode> PrependSeekEscapeeStates { get; } = Array.Empty<IGraphNode>();
        protected virtual IReadOnlyList<IGraphNode> AppendSeekEscapeeStates { get; } = Array.Empty<IGraphNode>();
        
        private IReadOnlyList<IGraphNode> SeekEscapeeBranch { get; } = new List<IGraphNode>()
        {
            new ChaseEscapeeBotState(),
        };
        
        protected override void Initialize()
        {
            base.Initialize();

            Player.PopulateBotBehaviour(this);
        }

#if !HEADLESS && DEV && !PRODUCTION_BUILD
        protected override void OnRefresh()
        {
            base.OnRefresh();
            DrawDebug();
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            _textDebugDrawer?.Dispose();
        }
#endif

        protected sealed override RootNode BuildRootNode()
        {
            var rootNodeChildren = new List<IGraphNode>();
            
            foreach (var n in PrependStates)
                rootNodeChildren.Add(n);
            
            rootNodeChildren.Add(new EmojiCatcherBotState());
            rootNodeChildren.Add(new DestroyObstacleBotState());
            rootNodeChildren.Add(new CageEscapeeBotState());
            rootNodeChildren.Add(new BrokeCampFireBotState());
            rootNodeChildren.Add(new OpenChestCatcherBotState());
            rootNodeChildren.Add(new UseTrapCatcherBotState());
            rootNodeChildren.Add(new SeekEscapeeBotState(PrependSeekEscapeeStates, SeekEscapeeBranch, AppendSeekEscapeeStates));
            
            return new RootNode(rootNodeChildren);
        }

        protected override IReadOnlyList<BotConstraint<NetCatcherPlayer, CatcherBotVision, CatcherBotMemory, CatcherBotParameters, CatcherBotConfiguration, CatcherBotMoodController>>
            CreateConstraints(BotBehaviourParametersContainer<CatcherBotDifficultyProfileDto> parameters)
        {
            var constraintParameters = parameters.Difficulty.Constraints ?? new List<CatcherBotConstraintParameters>();
            return constraintParameters.Select(c => c.CreateConstraint()).ToList();
        }

        protected override CatcherBotMoodController CreateMoodController(NetCatcherPlayer player, CatcherBotVision vision, CatcherBotParameters parameters)
        {
            return new CatcherBotMoodController(player, vision, parameters);
        }

        protected override CatcherBotMemory CreateMemory(CatcherBotVision vision, BotStateContainer stateContainer)
        {
            return new CatcherBotMemory(vision, stateContainer);
        }

        protected override CatcherBotVision CreateVision(
            IGameplaySpawnedServerContext spawnedContext,
            BotBehaviourParametersContainer<CatcherBotDifficultyProfileDto> behaviourParameters
        )
        {
            return new CatcherBotVision(Player, spawnedContext, behaviourParameters.Difficulty);
        }

        protected override CatcherBotParameters CreateParameters(BotBehaviourParametersContainer<CatcherBotDifficultyProfileDto> parameters)
        {
            return new CatcherBotParameters(parameters.Difficulty);
        }

        protected override CatcherBotConfiguration CreateConfiguration()
        {
            return new CatcherBotConfiguration();
        }

#if !HEADLESS && !PRODUCTION_BUILD && DEV
        private void DrawDebug()
        {
            if (!JxDebugTemporaryConstants.UseGameplayDebugDrawings)
                return;
            
            if (_textDebugDrawer == null)
            {
                _textDebugDrawer = JxDebugSpatial.Instance.GetText();
                _textDebugDrawer.Transform.SetParent(Player.NetTransform.UnityTransform, false);
            }

            var header = $"Mood: {Mood.Current} ({Mood.Debug_GetRestMoodDuration():0.0} s)";
            _textDebugDrawer.Drawer.Text = header;
            _textDebugDrawer.Drawer.Color = Mood.Current == CatcherBotMood.ForceAttack ? Color.red : Color.white;
            _textDebugDrawer.Drawer.Size = 3f;
            _textDebugDrawer.Transform.localPosition = Vector3.up * 3.5f;
        }
#endif
    }
}