using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles;
using Jx.Utils.MainThread;
using UnityEngine;

namespace GameplayComponents.Bots.Parameters
{
    public class CatcherBotParameters : BotParameters
    {
        private readonly float _intuitionFactor01;

        public CatcherBotParameters(
            CatcherBotDifficultyProfileDto difficulty
        )
        {
            _intuitionFactor01 = difficulty.IntuitionFactor01;
            HuntingDuration = difficulty.HuntingEnabledDuration;
            RoamingDuration = difficulty.HuntingDisabledDuration;
            DestroyObstacleSqrRadius = difficulty.DestroyObstacleRadius * difficulty.DestroyObstacleRadius;
            OpenChestStartDelayInSeconds = difficulty.OpenChestStartDelayInSeconds;
            OpenChestReloadInSeconds = difficulty.OpenChestReloadInSeconds;
            MaxUsedTrapCount = difficulty.MaxUsedTrapCount;
            MaxBreakCampfires = difficulty.MaxBreakCampfires;
            MaxOpenChestCount = difficulty.MaxOpenChestCount;
            AbilityDisabled = difficulty.AbilityDisabled;
            RealPlayerChaseIgnoranceFactor = difficulty.RealPlayerChaseIgnoranceFactor;
            InteractionMultiplier = difficulty.InteractionMultiplier01 > 0f ? difficulty.InteractionMultiplier01 : 1f;
            EscapingDuration = difficulty.EscapingDurationInSeconds;
            ForceAttackDuration = difficulty.ForceAttackDuration;

            NextMaxIntuitionTime = 0f;
        }

        public float DestroyObstacleSqrRadius { get; }
        public float NextMaxIntuitionTime { get; private set; }
        public override float OpenChestStartDelayInSeconds { get; }
        public override float OpenChestReloadInSeconds { get; }
        public override bool AbilityDisabled { get; }
        public override float InteractionMultiplier { get; }
        public override void Refresh() { }

        public int MaxUsedTrapCount { get; }
        public int MaxBreakCampfires { get; }
        public int MaxOpenChestCount { get; }
        public float RealPlayerChaseIgnoranceFactor { get; }
        public float IntuitionFactor01 => _intuitionFactor01;
        public float HuntingDuration { get; }
        public float RoamingDuration { get; }
        public float EscapingDuration { get; }
        public float ForceAttackDuration { get; }

        public void UpdateMaxIntuitionTime(float time) => NextMaxIntuitionTime = time;
    }
}