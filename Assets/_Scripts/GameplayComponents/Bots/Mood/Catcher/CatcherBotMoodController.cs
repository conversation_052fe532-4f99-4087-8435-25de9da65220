using System;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player.Catcher;
using Mirror;

namespace GameplayComponents.Bots.Mood.Catcher
{
    public class CatcherBotMoodController : BotMoodController<NetCatcherPlayer, CatcherBotVision, CatcherBotParameters>
    {
        private const CatcherBotMood Default = CatcherBotMood.Roaming;
        
        private CatcherBotMood _current;
        private CatcherBotMood _previous;

        private double _roamingEndTimestamp;
        private double _huntingEndTimestamp;
        private double _escapeEndTimestamp;
        private double _forceAttackEndTimestamp;

        public CatcherBotMoodController(NetCatcherPlayer player, CatcherBotVision vision, CatcherBotParameters parameters) : base(player, vision, parameters)
        {
            _previous = CatcherBotMood.Roaming;
            _current = CatcherBotMood.Roaming;
            _roamingEndTimestamp = parameters.RoamingDuration;
        }

        public CatcherBotMood Current => _current;
        
        public void RestorePrevious()
        {
            if (_previous == _current || _previous == CatcherBotMood.ForceAttack)
            {
                Select(Default);
                return;
            }
            
            Select(_previous);
        }

        public void TryStop(CatcherBotMood mood)
        {
            if (_current != mood)
                return;
            
            RestorePrevious();
        }

        public void Select(CatcherBotMood mood)
        {
            if (_current == mood)
                return;
            
            _previous = _current;
            _current = mood;

            var time = NetworkTime.time;
            switch (mood)
            {
                case CatcherBotMood.Roaming:
                    _roamingEndTimestamp = time + Parameters.RoamingDuration;
                    break;
                case CatcherBotMood.Hunting:
                    _huntingEndTimestamp = time + Parameters.HuntingDuration;
                    break;
                case CatcherBotMood.Escaping:
                    _escapeEndTimestamp = time + Parameters.EscapingDuration;
                    break;
                case CatcherBotMood.ForceAttack:
                    _forceAttackEndTimestamp = time + Parameters.ForceAttackDuration;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(mood), mood, null);
            }
        }
        
        public override void Refresh()
        {
            var time = NetworkTime.time;
            switch (_current)
            {
                case CatcherBotMood.ForceAttack:
                    if (_forceAttackEndTimestamp < time)
                        RestorePrevious();
                    break;
                
                case CatcherBotMood.Roaming:
                    if (_roamingEndTimestamp < time)
                        Select(CatcherBotMood.Hunting);
                    break;
                
                case CatcherBotMood.Hunting:
                    if (_huntingEndTimestamp < time)
                        Select(CatcherBotMood.Roaming);
                    break;
                
                case CatcherBotMood.Escaping:
#if HEADLESS
                    if (_escapeEndTimestamp < time)
                        Select(_previous);
#else
                    if (!Vision.RealPlayer)
                    {
                        RestorePrevious();
                        break;
                    }
                    
                    if (_escapeEndTimestamp < time)
                    {
                        var realPlayerPosition = Vision.RealPlayer!.NetTransform.Position;
                        var realPlayerInVision = Vision.EnemyVisionChecker.IsVisible(realPlayerPosition, false);
                        Select(realPlayerInVision ? CatcherBotMood.Hunting : CatcherBotMood.Roaming);
                    }
#endif
                    break;
                
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

#if UNITY_EDITOR && !HEADLESS
        public double Debug_GetRestMoodDuration()
        {
            return _current switch
            {
                CatcherBotMood.Roaming => Get(in _roamingEndTimestamp),
                CatcherBotMood.Hunting => Get(in _huntingEndTimestamp),
                CatcherBotMood.Escaping => Get(in _escapeEndTimestamp),
                CatcherBotMood.ForceAttack => Get(in _forceAttackEndTimestamp),
                _ => throw new ArgumentOutOfRangeException()
            };

            double Get(in double timestamp) => timestamp - NetworkTime.time;
        }
#endif
    }
}