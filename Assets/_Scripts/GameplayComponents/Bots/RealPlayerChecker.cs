using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player;
using UnityEngine;

namespace GameplayComponents.Bots
{
    public class RealPlayerChecker
    {
        private readonly BotVision _vision;

        public RealPlayerChecker(BotVision vision)
        {
            _vision = vision;
        }

        public bool HasRealPlayer => !ReferenceEquals(_vision.RealPlayer, null);

        public NetGamePlayer? RealPlayer => _vision.RealPlayer;

        public float RealPlayerSqrRadius => _vision.RealPlayerSqrRadius;

        public bool IsPositionNearRealPlayer(Vector3 position)
        {
            if (!HasRealPlayer)
                return false;

            var realPlayerPosition = _vision.RealPlayer!.NetTransform.Position;
            var sqrDistance = Vector3.SqrMagnitude(position - realPlayerPosition);
            return sqrDistance < _vision.RealPlayerSqrRadius;
        }

        public bool IsPositionNearRealPlayer(Vector3 position, float customSqrRadius)
        {
            if (!HasRealPlayer)
                return false;

            var realPlayerPosition = _vision.RealPlayer!.NetTransform.Position;
            var sqrDistance = Vector3.SqrMagnitude(position - realPlayerPosition);
            return sqrDistance < customSqrRadius;
        }

        public float GetSqrDistanceToRealPlayer(Vector3 position)
        {
            if (!HasRealPlayer)
                return float.MaxValue;

            var realPlayerPosition = _vision.RealPlayer!.NetTransform.Position;
            return Vector3.SqrMagnitude(position - realPlayerPosition);
        }

        public float GetDistanceToRealPlayer(Vector3 position)
        {
            if (!HasRealPlayer)
                return float.MaxValue;

            var realPlayerPosition = _vision.RealPlayer!.NetTransform.Position;
            return Vector3.Distance(position, realPlayerPosition);
        }

        public Vector3? GetRealPlayerPosition()
        {
            return HasRealPlayer ? _vision.RealPlayer!.NetTransform.Position : null;
        }

        public bool IsPlayerRealPlayer(NetGamePlayer player)
        {
            if (!HasRealPlayer || player == null)
                return false;

            return ReferenceEquals(player, _vision.RealPlayer);
        }
    }
}
