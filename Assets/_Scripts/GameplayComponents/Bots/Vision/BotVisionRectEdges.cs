using UnityEngine;

namespace GameplayComponents.Bots.Vision
{
    public class BotVisionRectEdges
    {
        private readonly BotVisionRect _rect;
        private Vector2 _leftTop;
        private Vector2 _rightBottom;
        
        public BotVisionRectEdges(in BotVisionRect rect)
        {
            _rect = rect;
        }

        public void UpdateGlobalCenter(in Vector3 center)
        {
            _leftTop = new(center.x - _rect.HalfWidth, center.z + _rect.HalfHeight);
            _rightBottom = new(center.x + _rect.HalfWidth, center.z - _rect.HalfHeight);
        }

        public void SetScaleMultiplier(float multiplier)
        {
            _rect.SetMultiplier(multiplier);
        }

        public bool IsInside(in Vector3 point)
        {
            return _leftTop.x < point.x && _rightBottom.x > point.x && _leftTop.y > point.z && _rightBottom.y < point.z;
        }
    }
}