using System.Collections.Generic;
using System.Linq;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using UnityEngine;

namespace GameplayComponents.Bots.Vision.Contexts
{
    public class BotVisionCampfireContext : BotVisionContext
    {
        private readonly List<NetCampfire> _visible;
        private readonly List<NetCampfire> _all;

        public BotVisionCampfireContext(BotVision vision)
            : base(vision)
        {
            _visible = new List<NetCampfire>(3);
            _all = new List<NetCampfire>(10);
        }

        public IReadOnlyList<NetCampfire> All => _all;
        public IEnumerable<NetCampfire> AllNotFired => _all.Where(campFire => campFire.State.Value != CampfireState.InFire);

        public IEnumerable<NetCampfire> VisibleInProgress => _visible.Where(campFire => campFire.State.Value == CampfireState.FireInProgress);
        public IEnumerable<NetCampfire> VisibleNotFired => _visible.Where(campFire => campFire.State.Value != CampfireState.InFire);

        public override void Refresh()
        {
            _visible.Clear();
            _all.Clear();

            foreach (var campfire in Vision.SpawnedContext.Campfires)
            {
                _all.Add(campfire);

                if (Vision.ObjectVisionChecker.IsVisible(campfire.transform.position, ignoreRealPlayerZone: false))
                {
                    _visible.Add(campfire);
                }
            }
        }
    }
}