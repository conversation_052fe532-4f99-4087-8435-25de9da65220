using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using GameplayNetworking.Gameplay.Components.Server.Visibility;
using GameplayNetworking.Gameplay.Visibility;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Share.Character;
using UnityEngine;

namespace GameplayComponents.Bots.Vision.Contexts
{
    public class BotVisionCatcherContext : BotVisionContext
    {
        private readonly List<NetCatcherPlayer> _all;
        private readonly List<NetCatcherPlayer> _visible;

        public BotVisionCatcherContext(BotVision vision)
            : base(vision)
        {
            _all = new List<NetCatcherPlayer>();
            _visible = new List<NetCatcherPlayer>(2);
        }

        public IReadOnlyList<NetCatcherPlayer> Visible => _visible;
        public IReadOnlyList<NetCatcherPlayer> All => _all;

        public NetCatcherPlayer? FirstVisible => Visible.FirstOrDefault();

        public bool IsCatcherTooNear(
            float sqrMinDistanceToCatcher,
            [MaybeNull<PERSON>hen(false)] out NetCatcherPlayer catcher
        )
        {
            catcher = FirstVisible;

            if (catcher == null)
                return false;

            return SqrDistanceProvider(catcher) < sqrMinDistanceToCatcher;
        }

        public override void Refresh()
        {
            _all.Clear();
            _visible.Clear();

            foreach (var player in Vision.SpawnedContext.Players)
            {
                if (player.Equals(Vision.Owner))
                    continue;

                if (player is NetCatcherPlayer catcherPlayer)
                {
                    // например, способность mimicry позволяет ловцам быть в форме убегающих
                    if (CharacterTypeHelper.IsCatcher(catcherPlayer.SyncData.CharacterView.Value.Index))
                    {
                        _all.Add(catcherPlayer);

                        if (IsVisible(catcherPlayer))
                        {
                            _visible.Add(catcherPlayer);
                        }
                    }
                }
            }
        }
    }
}