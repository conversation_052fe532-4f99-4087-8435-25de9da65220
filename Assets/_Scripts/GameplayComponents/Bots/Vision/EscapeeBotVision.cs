using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles;

namespace GameplayComponents.Bots.Vision
{
    public class EscapeeBotVision : BotVision
    {
        public EscapeeBotVision(
            NetEscapeePlayer owner,
            IGameplaySpawnedServerContext spawned<PERSON>ontext,
            EscapeeBotDifficultyProfileDto difficultyProfile
        )
            : base(owner, spawnedContext, difficultyProfile)
        {
        }
    }
}