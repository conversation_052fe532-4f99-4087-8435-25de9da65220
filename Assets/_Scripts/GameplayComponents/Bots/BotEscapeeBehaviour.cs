using System;
using System.Collections.Generic;
using System.Linq;
using GameplayComponents.Bots.Configuration;
using GameplayComponents.Bots.Constraints;
using GameplayComponents.Bots.Constraints.Escapee.Parameters;
using GameplayComponents.Bots.Memory;
using GameplayComponents.Bots.Mood;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.States;
using GameplayComponents.Bots.States.Escapee;
using GameplayComponents.Bots.States.Escapee.UseInventory;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles;
using Graph;

namespace GameplayComponents.Bots
{
    public class BotEscapeeBehaviour : BotBehaviour<
        NetEscapeePlayer, 
        EscapeeBotVision, 
        EscapeeBotMemory,
        EscapeeBotParameters, 
        EscapeeBotConfiguration,
        EscapeeBotDifficultyProfileDto,
        EscapeeBotMoodController>
    {
        protected override ISet<ActionType> ActiveActions { get; } = new HashSet<ActionType>()
        {
            ActionType.Idle,
            ActionType.Interaction,
            ActionType.AbilityPreparation,
            ActionType.AbilityInProgress
        };

        protected virtual IReadOnlyList<IGraphNode> PrependEscapeFromCatcherStates { get; } = Array.Empty<IGraphNode>();
        protected virtual IReadOnlyList<IGraphNode> AppendEscapeFromCatcherStates { get; } = Array.Empty<IGraphNode>();

        private IReadOnlyList<IGraphNode> SeekCampfireBranch { get; } = new IGraphNode[]
        {
            new HealTeammateBotState(),
            new ProcessCampfireBotState(),
        };

        private IReadOnlyList<IGraphNode> EscapeFromCatcherBranch { get; } = new IGraphNode[]
        {
            new FallbackEscapeFromCatcherBotState(),
            new PutObstacleBotState(),
            new TeleportBotState(),
        };

        protected virtual IReadOnlyList<IGraphNode> NoDangerPrependStates { get; } = Array.Empty<IGraphNode>();

        protected override void Initialize()
        {
            base.Initialize();

            Player.PopulateBotBehaviour(this);
        }

        protected sealed override RootNode BuildRootNode()
        {
            var rootNodeChildren = new List<IGraphNode>();
            
            rootNodeChildren.Add(new SuicideEscapeeBotState());

            foreach (var n in PrependStates)
                rootNodeChildren.Add(n);

            rootNodeChildren.Add(new EmojiEscapeeBotState());
            rootNodeChildren.Add(new OpenGateBotState());
            rootNodeChildren.Add(new GoToOpenedGateBotState());
            rootNodeChildren.Add(new UseInventoryEscapeeBotState());
            rootNodeChildren.Add(new CageSelfReleaseEscapeeBotState());
            rootNodeChildren.Add(new EscapeFromCatcherBotState(PrependEscapeFromCatcherStates, EscapeFromCatcherBranch, AppendEscapeFromCatcherStates));

            foreach (var n in NoDangerPrependStates)
                rootNodeChildren.Add(n);

            rootNodeChildren.Add(new GoToAngelBotState());
            rootNodeChildren.Add(new StayOnHealingBotState());
            rootNodeChildren.Add(new HelpAnyEscapeeWithCageBotState());
            rootNodeChildren.Add(new SelfHealBotState());
            rootNodeChildren.Add(new OpenChestEscapeeBotState());
            rootNodeChildren.Add(new SeekGateBotState());
            rootNodeChildren.Add(new SeekCampfireBotState(SeekCampfireBranch));

            return new RootNode(rootNodeChildren);
        }
        
        protected override IReadOnlyList<BotConstraint<NetEscapeePlayer, EscapeeBotVision, EscapeeBotMemory, EscapeeBotParameters, EscapeeBotConfiguration, EscapeeBotMoodController>>
            CreateConstraints(BotBehaviourParametersContainer<EscapeeBotDifficultyProfileDto> parameters)
        {
            var constraintParameters = parameters.Difficulty.Constraints ?? new List<EscapeeBotConstraintParameters>();
            return constraintParameters.Select(c => c.CreateConstraint()).ToList();
        }

        protected override EscapeeBotMoodController CreateMoodController(NetEscapeePlayer player, EscapeeBotVision vision, EscapeeBotParameters parameters)
        {
            return new EscapeeBotMoodController(player, vision, parameters);
        }

        protected override EscapeeBotMemory CreateMemory(EscapeeBotVision vision, BotStateContainer stateContainer)
        {
            return new EscapeeBotMemory(vision, stateContainer);
        }

        protected override EscapeeBotVision CreateVision(
            IGameplaySpawnedServerContext spawnedContext,
            BotBehaviourParametersContainer<EscapeeBotDifficultyProfileDto> behaviourParameters
        )
        {
            return new EscapeeBotVision(Player, spawnedContext, behaviourParameters.Difficulty);
        }

        protected override EscapeeBotParameters CreateParameters(BotBehaviourParametersContainer<EscapeeBotDifficultyProfileDto> parameters)
        {
            return new EscapeeBotParameters(parameters.Difficulty);
        }

        protected override EscapeeBotConfiguration CreateConfiguration()
        {
            return new EscapeeBotConfiguration();
        }
    }
}