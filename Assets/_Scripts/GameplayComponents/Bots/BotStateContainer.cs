using System;
using System.Collections.Generic;
using System.Diagnostics;
using GameplayComponents.Bots.States;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using UnityEngine;

namespace GameplayComponents.Bots
{
    public class BotStateContainer
    {
        private const int LengthPreviousStates = 0;
        
        private readonly ISet<ActionType> _activeStateActions;
        private readonly NetGamePlayer _player;
        private readonly Func<IBotState?> _findState;
        
        private readonly (IBotState?, float, Vector3?)[] _previousStates;
        
        private IBotState? _current;

        public BotStateContainer(
            ISet<ActionType> activeStateActions,
            NetGamePlayer player,
            Func<IBotState?> findState
        )
        {
            _activeStateActions = activeStateActions ?? throw new ArgumentNullException(nameof(activeStateActions));
            _player = player ? player : throw new ArgumentNullException(nameof(player));
            _findState = findState ?? throw new ArgumentNullException(nameof(findState));

            if (LengthPreviousStates <= 0)
                _previousStates = Array.Empty<(IBotState?, float, Vector3?)>();
            else
                _previousStates = new (IBotState?, float, Vector3?)[LengthPreviousStates];
        }

        public IBotState? Current
        {
            get => _current;
            private set
            {
                RememberPreviousState();
                _current = value;
            }
        }
        public (IBotState?, float, Vector3?)[] PreviousStates => _previousStates;

        public void Refresh()
        {
            if (Current != null && !_activeStateActions.Contains(_player.BaseSyncData.Action.Value))
                SetState(null);
            else
                SetState(_findState());

            Current?.Update();
        }

        public void Discard()
        {
            if (Current == null)
                return;
            
            Current.Disable();
            Current = null;
        }

        private void SetState(IBotState? state) 
        {
            if (Equals(Current?.GetType(), state?.GetType()))
                return;
            
            Current?.Disable();

            Current = state;
            Current?.Enable();
        }
        
        [Conditional("UNITY_EDITOR")]
        private void RememberPreviousState()
        {
            if (LengthPreviousStates <= 0)
                return;
            
            for (var i = _previousStates.Length -1 ; i > 0; i--)
                _previousStates[i] = _previousStates[i - 1];

            Vector3? destination = null;
            if (_current is EscapeeBotState escapeeBotState)
                destination = escapeeBotState.CurrentAgentDestination;
                
            _previousStates[0] = (_current, Time.time, destination);
        }
    }
}