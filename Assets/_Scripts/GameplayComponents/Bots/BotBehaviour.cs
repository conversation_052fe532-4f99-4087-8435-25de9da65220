using System;
using System.Collections.Generic;
using Core.Extensions;
using Core.Helpers.NavMesh;
using GameplayComponents.Bots.Configuration;
using GameplayComponents.Bots.Constraints;
using GameplayComponents.Bots.Memory;
using GameplayComponents.Bots.Mood;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.States;
using GameplayComponents.Bots.States.Escapee;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player.Components.Movement;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Control;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Spawning;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Systems.Implementation.Bots.Profiles;
using Graph;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using Mirror;
using UnityEditor;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.XR;
using Zenject;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
using Core.UnityUtils.Debug.Spatial;
using Core.Helpers.DebugScreen;
#endif

namespace GameplayComponents.Bots
{
    public abstract class BotBehaviour<TPlayer, TVision, TMemory, TParameters, TConfiguration, TDifficulty, TMood> : MonoBehaviour, IBotBehaviour
        where TPlayer : NetGamePlayer
        where TMemory : BotMemory<TPlayer, TVision>
        where TVision : BotVision
        where TParameters : BotParameters
        where TConfiguration : BotConfiguration
        where TDifficulty : BotDifficultyProfileDto
        where TMood : BotMoodController<TPlayer, TVision, TParameters>
    {
        private const int UpdateFps = 5;
        private const float UpdateDeltaTime = 1f / UpdateFps;

        private readonly JxAtomicFlag _initialized = false;

        private IGameplayServerContext _gameplayContext = null!;
        private IGraph _graph = null!;
        private bool _isActive = true;
        private IDisposable? _speedSubscription;

        private IJxThrottler _throttler = null!;
        private BotBehaviourParametersContainer<TDifficulty> _behaviourParameters = null!;
        private IGameplayServerContextGetter _gameplayContextGetter = null!;

        private BotStateContainer _stateContainer = null!;
        private TMemory _memory = null!;
        private TConfiguration _configuration = null!;
        private IReadOnlyList<BotConstraint<TPlayer, TVision, TMemory, TParameters, TConfiguration, TMood>> _constraints = null!;

        private IDisposable? _navMeshAgentStopHandler;
        private IDisposable? _runnerTask;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
        private WireCubeDebugSpatialHandler? _objectVisionDrawer;
        private WireCubeDebugSpatialHandler? _teamMateVisionDrawer;
        private WireCubeDebugSpatialHandler? _enemyVisionDrawer;
        private TextDebugSpatialHandler? _stateTextDrawer;
#endif

        [Inject]
        private void Inject(
            IGameplayServerContextGetter gameplayContextGetter,
            BotBehaviourParameters parameters
        )
        {
            _gameplayContextGetter = gameplayContextGetter;

            Logger = JxLoggerFactory.CreateLogger(GetType().Name);

            _throttler = new JxThrottler(() => Time.time, UpdateDeltaTime);
            _behaviourParameters = new BotBehaviourParametersContainer<TDifficulty>(parameters);
        }

        #region BRANCHES

        protected virtual IReadOnlyList<IGraphNode> PrependStates => Array.Empty<IGraphNode>();

        #endregion

        public BotBehaviourParametersContainer<TDifficulty> BehaviourParameters => _behaviourParameters;

        protected IJxLogger Logger { get; private set; } = null!;
        protected JxNavMeshAgentComponent Agent { get; private set; } = null!;
        protected TPlayer Player { get; private set; } = null!;

        protected abstract ISet<ActionType> ActiveActions { get; }
        protected TVision Vision { get; private set; } = null!;
        protected TParameters Parameters { get; private set; } = null!;
        protected TMood Mood { get; private set; } = null!;
        protected RealPlayerChecker RealPlayerChecker { get; private set; } = null!;

        BotParameters IBotBehaviour.Parameters => Parameters;

        protected virtual void Start() => Initialize();
        
        public bool TryUpdate()
        {
            if (this.IsDestroyed())
                return true;
            
            if (!IsRunning() || Player.BaseSyncData.Action.Value.IsFinished())
            {
                _stateContainer?.Discard();
                return false;
            }

            try
            {
                if (_throttler.TryRequestAccess())
                {
                    OnRefresh();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(
                    "Unknown bot update exception",
                    ex,
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Type"] = GetType().Name,
                    }
                );
            }
            
            return false;
        }

        private void LateUpdate() => RefreshWalkingState();

        protected virtual void OnDisable()
        {
            _runnerTask?.Dispose();
            _speedSubscription?.Dispose();

            Destroy(this);
        }

        public void SetActive(bool isActive)
        {
            _isActive = isActive;

            _navMeshAgentStopHandler?.Dispose();
            _navMeshAgentStopHandler = null;
            
            if (isActive)
                return;

            _navMeshAgentStopHandler = Agent.Stop();
            Player.Interaction.Request(new InteractionRequest(isStarted: false));
        }

        public void Destroy()
        {
            if (this.IsDestroyed())
                return;
            
            foreach (var constraint in _constraints)
                constraint.DeInitialize();
            
#if !HEADLESS && !PRODUCTION_BUILD && DEV
            _enemyVisionDrawer?.Dispose();
            _teamMateVisionDrawer?.Dispose();
            _objectVisionDrawer?.Dispose();
            _stateTextDrawer?.Dispose();
#endif
            Destroy(this);
        }

        public string? FindDifficultyName()
        {
            return BehaviourParameters?.Difficulty?.Name;
        }

        protected virtual void Initialize()
        {
            if (!_initialized.TrySet())
                throw new InvalidOperationException("Attempt to initialize twice");

            Agent = this.GetComponentOrThrow<JxNavMeshAgentComponent>();
            Player = GetComponent<TPlayer>();

            _gameplayContext = _gameplayContextGetter.Get(Player.MatchId);

            Parameters = CreateParameters(_behaviourParameters);
            Vision = CreateVision(_gameplayContext.Spawned, _behaviourParameters);
            RealPlayerChecker = new RealPlayerChecker(Vision);
            _configuration = CreateConfiguration();
            _stateContainer = new BotStateContainer(ActiveActions, Player, FindState);
            _memory = CreateMemory(Vision, _stateContainer);
            Mood = CreateMoodController(Player, Vision, Parameters);

            _speedSubscription = Player.BaseSyncData.Speed.SubscribeAndFire(
                (s) => { Agent.SetSpeed(new NetTransformSpeedDto(s.Movement, s.Rotation * Mathf.Rad2Deg * 5f)); }
            );

            var context = new BotStateContext<TPlayer, TVision, TMemory, TParameters, TConfiguration, TMood>(
                Player,
                Agent,
                Vision,
                _behaviourParameters.PointsOfInterest,
                _memory,
                Parameters,
                _configuration,
                _behaviourParameters.SharedContext,
                Mood,
                RealPlayerChecker
            );

            _constraints = CreateConstraints(_behaviourParameters);
            foreach (var constraint in _constraints)
            {
                constraint.SetupContext(context);
                constraint.Initialize();
            }

            _graph = new GraphInstance(SetupContextForChildren(context, BuildRootNode()));

            _runnerTask = BotTaskRunner.Instance.Register(this);
        }

        protected abstract TMemory CreateMemory(TVision vision, BotStateContainer stateContainer);

        protected abstract TVision CreateVision(
            IGameplaySpawnedServerContext spawnedContext,
            BotBehaviourParametersContainer<TDifficulty> behaviourParameters
        );

        protected abstract TParameters CreateParameters(BotBehaviourParametersContainer<TDifficulty> parameters);
        protected abstract TConfiguration CreateConfiguration();
        protected abstract RootNode BuildRootNode();

        protected abstract IReadOnlyList<BotConstraint<TPlayer, TVision, TMemory, TParameters, TConfiguration, TMood>> CreateConstraints(
            BotBehaviourParametersContainer<TDifficulty> parameters
        );

        protected abstract TMood CreateMoodController(TPlayer player, TVision vision, TParameters parameters);

        private void RefreshWalkingState()
        {
            var canWalk = Player.BaseSyncData.Action.Value.HasPermission(ActionPermissions.Walk);

            if (_navMeshAgentStopHandler != null && canWalk)
            {
                _navMeshAgentStopHandler.Dispose();
                _navMeshAgentStopHandler = null;
            }
            else if (_navMeshAgentStopHandler == null && !canWalk)
            {
                _navMeshAgentStopHandler = Agent.Stop();
            }
        }

        protected virtual void OnRefresh()
        {
            foreach (var constraint in _constraints)
                constraint.Refresh();

            Vision.Refresh();

            _memory.Refresh(_behaviourParameters.PointsOfInterest);
            Parameters.Refresh();

            _behaviourParameters.SharedContext.Refresh();

            _stateContainer.Refresh();
            Mood.Refresh();

#if !HEADLESS && !PRODUCTION_BUILD && DEV
            if (JxDebugTemporaryConstants.UseGameplayDebugDrawings)
            {
                DrawRectVision();
                DrawState();
            }
#endif
        }

        private IBotState? FindState()
        {
            var node = _graph.GetLastSuitableNodeInFirstSuitableBranch();
            try
            {
                return node as IBotState;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex);
                return null;
            }
        }

        private bool IsRunning()
        {
            return _initialized.IsSet &&
                   _isActive &&
                   (Agent.IsOnNavMesh || Player.BaseSyncData.Action.Value == ActionType.InCage) &&
                   NetworkTime.time >= _gameplayContext.MatchSyncData.Timing.Value.StartTime;
        }

        private RootNode SetupContextForChildren(BotStateContext<TPlayer, TVision, TMemory, TParameters, TConfiguration, TMood> context, RootNode rootNode)
        {
            GraphWalker.VisitChildrenRecursively<BotState<TPlayer, TVision, TMemory, TParameters, TConfiguration, TMood>>(
                rootNode,
                node => node.Initialize(context)
            );

            return rootNode;
        }

        #region GIZMOS

#if UNITY_EDITOR && !HEADLESS && !PRODUCTION_BUILD && DEV
        protected virtual void OnDrawGizmos()
        {
            if (!Application.isPlaying)
                return;
            
            DrawPath(Agent, Agent.Destination);
            DrawRealPlayerVisionRadius();
        }
        
        private static bool DrawPath(
            JxNavMeshAgentComponent navMeshAgent,
            Vector3 destination
        )
        {
            var path = new NavMeshPath();
            navMeshAgent.CalculatePath(destination, path);
            var points = path.corners;
            if (path.status == NavMeshPathStatus.PathInvalid || points.Length < 2)
                return false;

            var originalColor = Gizmos.color;
            try
            {
                Gizmos.color = Color.yellow;
                for (var i = 1; i < points.Length; i++)
                    Gizmos.DrawLine(points[i - 1], points[i]);
            }
            finally
            {
                Gizmos.color = originalColor;
            }

            return true;
        }
#endif
        
#if !HEADLESS && !PRODUCTION_BUILD && DEV
        private void DrawRectVision()
        {
            const float y = 5f;
            const float colorAlpha = 0.3f;
            
            Draw(ref _objectVisionDrawer, Color.white, Vision.Debug_GetObjectVisionRect());
            Draw(ref _enemyVisionDrawer, Color.red, Vision.Debug_GetEnemyVisionRect());
            Draw(ref _teamMateVisionDrawer, Color.green, Vision.Debug_GetTeamMateVisionRect());

            return;
            
            void Draw(ref WireCubeDebugSpatialHandler? drawer, Color color, BotVisionRect rect)
            {
                if (drawer == null)
                {
                    drawer = JxDebugSpatial.Instance.GetWireCube();
                    drawer.Transform.SetParent(Player.NetTransform.UnityTransform, false);
                }
                
                color.a = colorAlpha;
                drawer.Drawer.Color = color;
                drawer.Drawer.Size = new Vector3(rect.Width, y, rect.Height);
            }
        }
        
        private void DrawState()
        {
            var state = _stateContainer.Current;
            if (state == null)
                return;

            if (_stateTextDrawer == null)
            {
                _stateTextDrawer = JxDebugSpatial.Instance.GetText();
                _stateTextDrawer.Transform.SetParent(Player.NetTransform.UnityTransform, false);
            }

            _stateTextDrawer.Drawer.Size = 3f;
            _stateTextDrawer.Drawer.Color = Color.yellow;
            _stateTextDrawer.Transform.localPosition = Vector3.up * 3f;
            _stateTextDrawer.Drawer.Text = $"State: {state.GetType().Name.Replace("BotState", "")}";
        }
        
        private void DrawRealPlayerVisionRadius()
        {
            if (!RealPlayerChecker.HasRealPlayer)
                return;

            var realPlayerPosition = RealPlayerChecker.GetRealPlayerPosition()!.Value;
            JxDebug.DrawSphere(
                realPlayerPosition,
                Quaternion.identity,
                JxMathf.SqrtOrDefault(RealPlayerChecker.RealPlayerSqrRadius),
                Color.yellow,
                Time.deltaTime // rendered every OnGUI frame
            );
        }
#endif

        #endregion
    }
}