using System.Collections.Generic;
using Jx.Utils.Objects;
using UnityEngine;

namespace GameplayComponents.Bots
{
    public class BotBehaviourParametersContainer<TDifficulty>
    {
        public BotBehaviourParametersContainer(BotBehaviourParameters parameters)
        {
            PointsOfInterest = parameters.PointsOfInterest;
            SharedContext = parameters.SharedContext;
            Difficulty = parameters.DifficultyProfile.Cast<TDifficulty>();
        }
        
        public IReadOnlyList<Vector3> PointsOfInterest { get; }
        public BotBehaviourSharedContext SharedContext { get; }
        public TDifficulty Difficulty { get; }
    }
}