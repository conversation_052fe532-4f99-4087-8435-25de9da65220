using Jx.Utils.Serialization.Json;
using Newtonsoft.Json;

namespace GameplayComponents.Bots.Constraints.Catcher.Parameters
{
    [JxJsonInherit("stopHuntingOnLastPlayer")]
    [JsonObject(MemberSerialization.OptIn)]
    public class StopHuntingOnLastPlayerCatcherConstraintParameters : CatcherBotConstraintParameters
    {
        public override CatcherBotConstraint CreateConstraint()
        {
            return new StopHuntingOnLastPlayerCatcherConstraint();
        }
    }
}