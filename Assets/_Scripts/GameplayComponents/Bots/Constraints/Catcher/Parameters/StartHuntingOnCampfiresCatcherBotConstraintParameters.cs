using Jx.Utils.Serialization.Json;
using Newtonsoft.Json;

namespace GameplayComponents.Bots.Constraints.Catcher.Parameters
{
    [JxJsonInherit("startHuntingOnCampfire")]
    [JsonObject(MemberSerialization.OptIn)]
    public class StartHuntingOnCampfiresCatcherBotConstraintParameters : CatcherBotConstraintParameters
    {
        [Json<PERSON>roperty("campfireThreshold")]
        public int CampfireThreshold { get; set; }
        
        [JsonProperty("MaxHuntingCount")]
        public int MaxHuntingCount { get; set; }
        
        public override CatcherBotConstraint CreateConstraint()
        {
            return new StartHuntingOnCampfiresCatcherBotConstraint(CampfireThreshold, MaxHuntingCount);
        }
    }
}