using Core.Extensions;
using Core.Helpers;
using GameplayComponents.Bots.Mood.Catcher;
using Jx.Utils.Helpers;
using UnityEngine;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
using Core.UnityUtils.Debug.Spatial;
using Core.Helpers.DebugScreen;
#endif


namespace GameplayComponents.Bots.Constraints
{
    public class ForceAttackCatcherCatcherBotConstraint : CatcherBotConstraint
    {
        private readonly IJxThrottler _throttler = new JxFrameThrottler(20);
        private readonly bool _valid;
        private readonly float _sqrRadius;
        private readonly float _radius;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
        private DefaultDebugSpatialHandler? _radiusSpatialHandler;
#endif
        
        public ForceAttackCatcherCatcherBotConstraint(
            float radius)
        {
            if (JxMathf.LessOrApproximately(radius, 0f))
                return;

            _radius = radius;
            _sqrRadius = radius * radius;
            _valid = true;
        }
        
        protected override void OnInitialize() { }

        protected override void OnDeInitialize()
        {
            base.OnDeInitialize();
            
#if !HEADLESS && !PRODUCTION_BUILD && DEV
            _radiusSpatialHandler?.Dispose();
#endif
        }

        protected override void OnUpdate()
        {
#if HEADLESS
            return;
#else
            if (!_valid)
                return;

#if !HEADLESS && !PRODUCTION_BUILD && DEV
            if (JxDebugTemporaryConstants.UseGameplayDebugDrawings)
                DebugDrawOnTick();
#endif

            if (!_throttler.TryRequestAccess())
                return;
            
            if (!Vision.RealPlayer || 
                !Vision.EnemyVisionChecker.IsVisible(Vision.RealPlayer!.NetTransform.Position, false) || 
                !Vision.IsThereActiveEscapeeNear(_sqrRadius))
            {
                if (Mood.Current == CatcherBotMood.ForceAttack)
                    Mood.RestorePrevious();
                return;
            }
            
            Mood.Select(CatcherBotMood.ForceAttack);
#endif
        }

#if !HEADLESS && !PRODUCTION_BUILD && DEV
        private void DebugDrawOnTick()
        {
            if (_radiusSpatialHandler == null)
            {
                _radiusSpatialHandler = JxDebugSpatial.Instance.GetSphere();
                _radiusSpatialHandler.Transform.SetParent(Player.NetTransform.UnityTransform, false);
                _radiusSpatialHandler.Color = new Color(0f, 255f, 0f, 0.25f);
            }
            
            _radiusSpatialHandler.Transform.localScale = Vector3.one * _radius;
        }
#endif
    }
}