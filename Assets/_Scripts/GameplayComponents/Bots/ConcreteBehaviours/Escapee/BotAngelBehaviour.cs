using System.Collections.Generic;
using System.Net.NetworkInformation;
using GameplayComponents.Bots.States.Escapee.AbilityStates;
using Graph;

namespace GameplayComponents.Bots.ConcreteBehaviours.Escapee
{
    public class BotAngelBehaviour : BotEscapeeBehaviour
    {
        protected override IReadOnlyList<IGraphNode> NoDangerPrependStates { get; } = new[] { new UseAbilityAngelHealBotState() };
    }
}