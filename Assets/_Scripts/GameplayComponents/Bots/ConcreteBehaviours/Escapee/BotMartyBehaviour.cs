using System.Collections.Generic;
using GameplayComponents.Bots.States.Escapee.AbilityStates;
using Graph;

namespace GameplayComponents.Bots.ConcreteBehaviours.Escapee
{
    public class BotMartyBehaviour : BotEscapeeBehaviour
    {
        protected override IReadOnlyList<IGraphNode> AppendEscapeFromCatcherStates { get; } = new IGraphNode[]
        {
            new UseAbilitySmokeCloudBotState(),
        };
    }
}