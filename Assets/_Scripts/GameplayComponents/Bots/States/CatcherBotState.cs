using System.Collections.Generic;
using System.Linq;
using GameplayComponents.Bots.Configuration;
using GameplayComponents.Bots.Memory;
using GameplayComponents.Bots.Mood.Catcher;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Data;
using Graph;
using Jx.Utils.Extensions;
using UnityEngine;

namespace GameplayComponents.Bots.States
{
    public abstract class CatcherBotState : BotState<NetCatcherPlayer, CatcherBotVision, CatcherBotMemory, CatcherBotParameters, CatcherBotConfiguration, CatcherBotMoodController>
    {
        protected CatcherBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children) { }

        protected virtual bool CanBeUsedAtObstacle => false;

        protected sealed override bool IsSuited()
        {
            var hasObstacleInteraction = Player.Interaction.IsAvailable(InteractionType.DestroyObstacle) ||
                                         Player.Interaction.IsInteracting(InteractionType.DestroyObstacle);

            if (CanBeUsedAtObstacle)
                return IsSuitedInternal(hasObstacleInteraction);

            if (hasObstacleInteraction)
                return false;

            return IsSuitedInternal(hasObstacleInteraction) && 
                   Player.BaseSyncData.Action.Value.IsPlaying() && 
                   Player.NetTransform.Movement.Agent.IsOnNavMesh;
        }

        protected abstract bool IsSuitedInternal(bool hasObstacle);

        protected bool IsThereObstacleOnSeekingEscapeePath(Vector3 destination)
        {
            if (!Vision.Obstacles.VisiblePut.Any())
                return false;

            var playerPosition = Player.NetTransform.Position;

            var movementDirectionNorm01 = (playerPosition - destination).normalized;

            var nearestObstaclePosition = Vision.Obstacles.VisiblePut.MinByDistance(
                                                     obstacle =>
                                                         Vector3.Distance(Player.NetTransform.Position, obstacle.transform.position)
                                                 )
                                                .transform
                                                .position;

            var directionToObstacleNorm01 = (playerPosition - nearestObstaclePosition).normalized;

            return Vector3.Dot(movementDirectionNorm01, directionToObstacleNorm01) > .5f; // ~ fov: 45 degrees
        }

        protected bool IsChaseAvailable()
        {
            if (Vision.Escapees.Visible.Count > 0)
                return true;
            if (Memory.TryGetLastVisibleEscapeeLocation(out _, out _))
                return true;

            return false;
        }
    }
}