using System;
using System.Collections.Generic;
using System.Linq;
using Graph;
using GameplayComponents.Bots.Configuration;
using GameplayComponents.Bots.Memory;
using GameplayComponents.Bots.Parameters;
using GameplayComponents.Bots.Vision;
using GameplayNetworking.Gameplay.Player;
using GameplayComponents.Services.NavigationServices;
using Jx.Utils.Collections;
using Core.Helpers;
using GameplayComponents.Bots.Mood;
using GameplayNetworking.Gameplay.Player.Data;
using UnityEngine;

namespace GameplayComponents.Bots.States
{
    public abstract class EscapeeBotState : BotState<
        NetEscapeePlayer, 
        EscapeeBotVision,
        EscapeeBotMemory, 
        EscapeeBotParameters, 
        EscapeeBotConfiguration,
        EscapeeBotMoodController>
    {
        private const int PATHS_COUNT_FOR_ANALIZE = 10;
        private const int MAX_GOOD_DESTINATIONS = 3;
        
        private readonly List<Vector3> _singleAvailableDestinationList;
        
        private readonly List<Vector3> _dangerPositions;
        private readonly List<Vector3> _pathPoints;
        private readonly List<Vector3> _targetToListPoints;
        private readonly List<KeyValuePair<float, int>> _weightByIndex;

        protected EscapeeBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children)
        {
            _singleAvailableDestinationList = new List<Vector3>();
            _dangerPositions = new List<Vector3>();
            _pathPoints = new List<Vector3>();
            _targetToListPoints = new List<Vector3>();
            _weightByIndex = new List<KeyValuePair<float, int>>();
        }

        protected Vector3 CurrentPosition => Player.NetTransform.Position;
        public Vector3 CurrentAgentDestination => Agent.Destination;

        protected override bool IsSuited() => !Memory.IsInFallbackEscapeFromCatcher && 
                                              Player.BaseSyncData.Action.Value.IsPlaying() && 
                                              Player.NetTransform.Movement.Agent.IsOnNavMesh;

        protected bool TrySelectBestTarget<T>(IEnumerable<T> availableTargets, out T? bestTarget)
            where T : Component
        {
            bestTarget = default;

            var destinations = new List<Vector3>();
            foreach (var it in availableTargets)
            {
                if (it.IsNullObj())
                    continue;

                destinations.Add(it.transform.position);
            }

            if (destinations.IsNullOrEmpty())
                return false;

            if (!TryGetDestinationAvoidingDangers(destinations, out var newDestination))
                return false;

            foreach (var it in availableTargets)
            {
                if (it.IsNullObj())
                    continue;

                if (newDestination != it.transform.position)
                    continue;

                bestTarget = it;
                break;
            }

            var result = !bestTarget.IsNullObj();
            return result;
        }

        protected bool TryGetDestinationAvoidingDangers(
            IReadOnlyList<Vector3>? possibleDestinations,
            out Vector3 newDestination,
            bool closerToDanger = false
        )
        {
            var currentPosition = CurrentPosition;
            newDestination = currentPosition;
            if (possibleDestinations.IsNullOrEmpty())
                return false;

            var minimumDistanceToDanger = Vision.Catchers.Visible.Count > 0 || Memory.InFear || closerToDanger
                ? NavigationService.MINIMUM_DISTANCE_TO_DANGER
                : NavigationService.DESIRED_MINIMUM_DISTANCE_TO_DANGER;

            var result = NavigationService.TryGetDestinationAvoidingDangers(
                Agent,
                currentPosition,
                possibleDestinations,
                MAX_GOOD_DESTINATIONS,
                GetDangerPositions(),
                out newDestination,
                minimumDistanceToDanger
            );

            return result;
        }

        protected bool CheckNewDestinationRequired()
        {
            if (Agent.IsDestinationAchieved())
                return true;

            var dirToDestination = Agent.Destination - CurrentPosition;
            if (IsBadDestinationDirection(dirToDestination))
                return true;

            return false;
        }

        protected virtual IReadOnlyList<Vector3> GetDestinationPoints() => PointsOfInterest;

        protected IReadOnlyList<Vector3> TargetToList(MonoBehaviour? mb)
        {
            _targetToListPoints.Clear();
            if (ReferenceEquals(mb, null))
                return Array.Empty<Vector3>();
            
            _targetToListPoints.Add(mb.transform.position);
            return _targetToListPoints;
        }

        private IReadOnlyList<Vector3> GetDangerPositions()
        {
            _dangerPositions.Clear();
            foreach (var catcherPlayer in Vision.Catchers.All)
                _dangerPositions.Add(catcherPlayer.NetTransform.Position);

            return _dangerPositions;
        }

        private static List<Vector3> TargetToList(Vector3 value)
        {
            var result = new List<Vector3> { value };
            return result;
        }

        protected bool TryUpdateDestination()
        {
            if (!TrySelectDestination(out var destination))
                return false;

            Agent.TrySetDestination(destination);
            return true;
        }

        protected bool IsAvailableDestination(Vector3 destination)
        {
            _singleAvailableDestinationList.Add(destination);

            var result = TryGetDestinationAvoidingDangers(_singleAvailableDestinationList, out _);
            _singleAvailableDestinationList.Clear();
            return result;
        }

        protected bool TrySelectDestination(out Vector3 newDestination, bool closerToDanger = false)
        {
            var currentPosition = CurrentPosition;
            newDestination = currentPosition;

            var destinations = GetDestinationPoints();
            if (destinations.Count == 0)
                return false;

            var result = TryGetDestinationAvoidingDangers(
                GetPathPoints(destinations),
                out newDestination,
                closerToDanger
            );

            return result;
        }

        private IReadOnlyList<Vector3> GetPathPoints(IReadOnlyList<Vector3> points)
        {
            _pathPoints.Clear();
            _weightByIndex.Clear();

            for (var index = 0; index < points.Count; index++)
            {
                var point = points[index];
                _weightByIndex.Add(new KeyValuePair<float, int>(GetPointWeight(point), index));
            }
            
            _weightByIndex.Sort(Comparison);

            for (var i = 0; i < _weightByIndex.Count && i < PATHS_COUNT_FOR_ANALIZE; ++i)
                _pathPoints.Add(points[_weightByIndex[i].Value]);

            return _pathPoints;
        }
        
        private static int Comparison(KeyValuePair<float, int> x, KeyValuePair<float, int> y) => x.Key.CompareTo(y.Key);

        private float GetPointWeight(Vector3 destination)
        {
            var currentPosition = CurrentPosition;
            var dirToPointOfInterest = destination - currentPosition;
            var weight = 1f;

            if (IsBadDestinationDirection(dirToPointOfInterest))
                weight *= 10_000;

            weight *= Vector3.SqrMagnitude(destination - currentPosition);

            if (Mathf.Abs(Vector3.Angle(dirToPointOfInterest, Player.NetTransform.Forward)) > 90f)
                weight *= 100f;

            return weight;
        }

        private bool IsBadDestinationDirection(Vector3 direction)
        {
            var danger = Memory.Danger;
            if (ReferenceEquals(danger, null))
                return false;
            
            return Mathf.Abs(Vector3.Angle(danger.NetTransform.Position - CurrentPosition, direction)) < 90f;
        }
    }
}