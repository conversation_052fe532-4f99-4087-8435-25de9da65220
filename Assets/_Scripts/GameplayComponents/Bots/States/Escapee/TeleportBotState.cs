using System.Collections.Generic;
using System.Linq;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using Graph;
using UnityEngine;

namespace GameplayComponents.Bots.States.Escapee
{
    public class TeleportBotState : EscapeeBotState
    {
        private const float TeleportDelayInSec = 5f;
        
        private NetTeleport? _targetTeleport;

        private Vector3? _teleportationStartPoint;
        private bool _teleported;
        private float _lastTeleportTime;

        public TeleportBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children) { }

        protected override bool IsSuited()
        {
            // base state ignored

            if (!Parameters.TeleportCanBeUsed)
                return false;
            if (Time.time - _lastTeleportTime < TeleportDelayInSec)
                return false;
            if (Player.BaseSyncData.Action.Value == ActionType.AbilityInProgress)
                return false;
            if (!Memory.InFear)
                return false;
            if (_teleported)
                return false;

            var targetTeleport = Vision.Teleports.VisibleAvailable.FirstOrDefault();
            if (ReferenceEquals(targetTeleport, null))
                return false;
            if (!SharedContext.TryMarkAsTeleportUser(targetTeleport, Player))
                return false;

            _targetTeleport = targetTeleport;
            return true;
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            FlushTeleportationProgress();
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            if (Player.BaseSyncData.Action.Value == ActionType.Interaction)
                Player.Interaction.Request(new InteractionRequest(false));

            if (_teleported)
                _lastTeleportTime = Time.time;

            FlushTeleportationProgress();

            SharedContext.UnMarkAsTeleportUser(Player);
            Parameters.ResetTeleportTimer();
        }

        protected override void OnUpdate()
        {
            if (ReferenceEquals(_targetTeleport, null))
                return;
            if (_teleported)
                return;

            if (_teleportationStartPoint != null)
                _teleported = Vector3.SqrMagnitude(_teleportationStartPoint.Value - Player.NetTransform.Position) >= 9f;

            if (_teleported)
            {
                Agent.TrySetDestination(_targetTeleport.BoundTeleportationPoint);
                return;
            }
            
            if (TryStartInteraction(_targetTeleport.transform, InteractionType.Teleport))
                _teleportationStartPoint = Player.NetTransform.Position;
        }

        private void FlushTeleportationProgress()
        {
            _teleported = false;
            _teleportationStartPoint = null;
            _targetTeleport = null;
        }
    }
}