using System.Collections.Generic;
using System.Linq;
using Core.Helpers;
using GameplayNetworking.Gameplay.SceneObjects.Static;
using Graph;
using Jx.Utils.Collections;
using UnityEngine;

namespace GameplayComponents.Bots.States.Escapee
{
    public class SeekGateBotState : EscapeeBotState
    {
        private readonly List<Vector3> _destinations;
        
        private NetGate? _selectedGate;
        private Vector3? _destination;

        public SeekGateBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children)
        {
            _destinations = new List<Vector3>();
        }

        protected override bool IsSuited()
        {
            if (!base.IsSuited())
                return false;

            if (!Vision.Gates.IsAvailable)
                return false;

            if (_destination != null && !Agent.IsDestinationAchieved())
                return true;

            if (Vision.Gates.All.Count == 0)
                return false;
            
            _destinations.Clear();
            foreach (var g in Vision.Gates.All)
                _destinations.Add(g.transform.position + g.transform.forward);

            if (!TrySelectDestination(out var destination))
                return false;

            _destination = destination;

            return true;
        }

        protected override void OnUpdate()
        {
            if (_destination == null)
                return;

            Agent.TrySetDestination(_destination.Value);
        }

        protected override IReadOnlyList<Vector3> GetDestinationPoints() => _destinations;
    }
}