using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player.Data;
using Graph;
using UnityEngine;

namespace GameplayComponents.Bots.States.Escapee
{
    public class EscapeFromCatcherBotState : EscapeeBotState
    {
        private Vector3 _destination;
        private bool _isFoundDestination;

        public EscapeFromCatcherBotState(params IReadOnlyList<IGraphNode>?[]? childrenToConcat)
            : base(childrenToConcat) { }

        protected override bool IsSuited()
        {
            if (!Player.NetTransform.Movement.Agent.IsOnNavMesh || Player.SyncData.Action.Value == ActionType.InCage)
                return false;
            
            var isDanger = Vision.Catchers.Visible.Count > 0 || Memory.InFear;
            if (!isDanger)
                return false;

            var newDestinationRequired = !_isFoundDestination || _isFoundDestination && Agent.IsDestinationAchieved(stoppingDistanceFactor: 2f);
            if (newDestinationRequired)
            {
                _isFoundDestination = TrySelectDestination(out _destination);
                Memory.IsInFallbackEscapeFromCatcher = !_isFoundDestination; // there is no destination to escape
            }

            return _isFoundDestination;
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            _isFoundDestination = false;
        }

        protected override void OnUpdate()
        {
            if (!_isFoundDestination)
                return;

            Agent.TrySetDestination(_destination);
        }
    }
}