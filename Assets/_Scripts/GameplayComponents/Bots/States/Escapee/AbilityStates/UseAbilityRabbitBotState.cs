using System.Collections.Generic;
using System.Linq;
using Graph;

namespace GameplayComponents.Bots.States.Escapee.AbilityStates
{
    public class UseAbilityRabbitBotState : AbstractEscapeeUseAbilityBotState
    {
        public UseAbilityRabbitBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children)
        {
        }

        protected override float UseAbilityProbability01 => 0.85f;

        protected override bool IsSuited()
        {
            return base.IsSuited() && Vision.Catchers.Visible.Any();
        }
    }
}