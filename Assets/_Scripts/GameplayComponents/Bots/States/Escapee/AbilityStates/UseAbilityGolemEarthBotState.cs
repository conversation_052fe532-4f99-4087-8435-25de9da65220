using System.Collections.Generic;
using System.Linq;
using Graph;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;

namespace GameplayComponents.Bots.States.Escapee.AbilityStates
{
    public class UseAbilityGolemEarthBotState : AbstractEscapeeUseAbilityBotState
    {
        private const float MaxDeviation = 2f;
        private const float SqrNoDeviationDistanceToCatcher = 4f;

        public UseAbilityGolemEarthBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children)
        {
        }

        protected override float UseAbilityProbability01 => 0.7f;

        protected override bool IsSuited()
        {
            return base.IsSuited() &&
                   Vision.Catchers.Visible.Any() &&
                   !IsThereObstacleBetweenPlayerAndTarget(Vision.Catchers.FirstVisible.transform.position);
        }

        protected override Vector3? GetAbilityTargetPoint()
        {
            return Vision.Catchers.IsCatcherTooNear(SqrNoDeviationDistanceToCatcher, out var catcher)
                ? catcher.transform.position
                : GetDeviationAroundTarget(Vision.Catchers.FirstVisible.transform, MaxDeviation);
        }
    }
}