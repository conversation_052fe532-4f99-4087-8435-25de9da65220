using System.Collections.Generic;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Gameplay.SceneObjects.Static.Campfire;
using Graph;

namespace GameplayComponents.Bots.States.Escapee
{
    public class ProcessCampfireBotState : TargetedEscapeeBotState<NetCampfire>
    {
        protected override IEnumerable<NetCampfire> AvailableTargets => Vision.Campfires.VisibleNotFired;

        public ProcessCampfireBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children) { }

        protected override void OnDisable()
        {
            if (Player.BaseSyncData.Action.Value == ActionType.Interaction)
                Player.Interaction.Request(new InteractionRequest(false));

            base.OnDisable();
        }

        protected override bool IsActualTarget(NetCampfire target) => target.State.Value != CampfireState.InFire;
        
        protected override void OnMovingToTarget(NetCampfire target)
        {
            TryStartInteraction(target.transform, InteractionType.LightCampfire);
        }
    }
}