using System.Collections.Generic;
using System.Linq;
using Core.Extensions;
using Core.Helpers;
using GameplayComponents.Bots.Mood.Catcher;
using GameplayComponents.Helpers;
using Graph;
using Jx.Utils.Collections;
using Jx.Utils.Helpers;
using Jx.Utils.Random;
using UnityEngine;

namespace GameplayComponents.Bots.States.Catcher
{
    public class SeekEscapeeBotState : CatcherBotState
    {
        private const int RandomPointsBufferSize = 3;
        private const float MinIntuitionDelay = 1f;

        private const float UpdateDelay = 0.2f;

        private readonly IJxThrottler _updatePositionThrottler;

        private float _lastIntuitionTime;
        private Vector3? _destinationPoint;

        public SeekEscapeeBotState(params IReadOnlyList<IGraphNode>?[]? children)
            : base(children)
        {
            _updatePositionThrottler = new JxThrottler(() => Time.time, UpdateDelay);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            _destinationPoint = null;
        }
        
        protected override bool IsSuitedInternal(bool hasObstacleInteraction) => true;

        protected override void OnUpdate()
        {
            if (!_updatePositionThrottler.TryRequestAccess())
                return;

#if !HEADLESS
            switch (Mood.Current)
            {
                case CatcherBotMood.Escaping:
                    UpdateDestinationOnEscaping();
                    return;
                case CatcherBotMood.ForceAttack:
                    UpdateDestinationOnForceAttack();
                    return;
            }
#endif
            UpdateDestinationOnRoamingOrHunting();
        }

        // add intuition throttle for invisible players
        protected virtual bool CheckCanUseIntuition()
        {
            if (Mood.Current == CatcherBotMood.Hunting)
                return false;
            if (JxRandomGenerator.GenerateTrueWithProbability(Parameters.IntuitionFactor01))
                return false;

            var currentTime = Time.time;
            if (currentTime - _lastIntuitionTime < MinIntuitionDelay)
                return false;

            _lastIntuitionTime = currentTime;
            return true;
        }

        private void UpdateDestinationOnRoamingOrHunting()
        {
            var ownerPosition = Player.NetTransform.Position;

            if (Mood.Current == CatcherBotMood.Hunting)
            {
                var nearestEscapee = Vision.Escapees.VisiblePlaying.GetNearestToPosition(ownerPosition);
                if (nearestEscapee)
                {
                    Agent.TrySetDestination(nearestEscapee!.transform.position);
                    return;
                }
            }

            if (_destinationPoint.HasValue && !Agent.IsDestinationAchieved(stoppingDistanceFactor: 2f)) 
                return;
                
            if (!TryChooseDestinationPoint(out _destinationPoint) || !_destinationPoint.HasValue)
                return;

            VisitedDestinations.Add(_destinationPoint.Value);
            Agent.TrySetDestination(_destinationPoint.Value);
        }

        private void UpdateDestinationOnForceAttack()
        {
            var ownerPosition = Player.NetTransform.Position;
            var nearestEscapee = Vision.Escapees.VisiblePlaying.GetNearestToPosition(ownerPosition);
            if (!nearestEscapee)
            {
                Mood.RestorePrevious();
                return;
            }
            
            Agent.TrySetDestination(nearestEscapee!.transform.position);
        }

        private void UpdateDestinationOnEscaping()
        {
#if HEADLESS
            return;
#else
            if (!Agent.IsDestinationAchieved())
                return;

            var nearestEscapee = Vision.Escapees.Playing.GetNearestToPosition(Player.NetTransform.Position);
            if (ReferenceEquals(nearestEscapee, null))
            {
                Mood.RestorePrevious();
                return;
            }

            var notVisitedDestinations = EnumerateNotVisitedDestinations().ToList();
            var target = notVisitedDestinations.FirstOrDefault(FilterPoint);
            if (target.Equals(default))
                target = notVisitedDestinations.RandomOrDefault();
            
            VisitedDestinations.Add(target);
            Agent.TrySetDestination(target);
            
            return;

            bool FilterPoint(Vector3 point)
            {
                const float minDot = 0.25f;
                var directionToEscapee = JxMathf.GetDirection(Player.NetTransform.Position, nearestEscapee.NetTransform.Position);
                var directionToPoint = JxMathf.GetDirection(Player.NetTransform.Position, point);
                return Mathf.Abs(Vector3.Dot(directionToEscapee, directionToPoint)) > minDot; // ~30 degrees
            }
#endif
        }

        private bool TryChooseDestinationPoint(out Vector3? newDestination)
        {
            newDestination = default;

            var intuitivePosition = default(Vector3?);
            var ownerPosition = Player.NetTransform.Position;

            if (CheckCanUseIntuition())
            {
                var nearestEscapee = Vision.Escapees.HasActiveGameplay.GetNearestToPosition(ownerPosition);
                if (!nearestEscapee.IsNullObj())
                {
                    var nearestEscapeePosition = nearestEscapee.transform.position;
                    intuitivePosition = ownerPosition + (nearestEscapeePosition - ownerPosition) / 2f;
                }
            }

            var destination = EnumerateNotVisitedDestinations()
                             .OrderBy(p => GetPointWeight(p, intuitivePosition))
                             .Take(RandomPointsBufferSize)
                             .RandomOrDefault();

            if (IsThereObstacleOnSeekingEscapeePath(destination))
            {
                var nearest = Vision.Obstacles.VisiblePut.GetNearestToPosition(ownerPosition);
                if (!nearest.IsNullObj())
                {
                    destination = nearest.transform.position;
                }
            }
            
            newDestination = destination;
            return true;
        }

        private float GetPointWeight(Vector3 point, Vector3? intuitivePosition)
        {
            if (intuitivePosition.HasValue)
            {
                var resultIntuitiveWeight = Vector3.SqrMagnitude(point - intuitivePosition.Value);
                return resultIntuitiveWeight;
            }

            var playerPosition = Player.NetTransform.Position;
            var sqrDistance = Vector3.SqrMagnitude(point - playerPosition);
            var directionPlayerToPoint = (point - playerPosition).normalized;

            if (Mathf.Abs(Vector3.Angle(directionPlayerToPoint, Player.NetTransform.Forward)) > 90f)
                sqrDistance *= 5f;

            return sqrDistance;
        }
    }
}