using System;
using Core.Extensions;
using GameplayComponents.Bots.DestinationProvider;
using UnityEngine;
using UnityEngine.AI;

namespace GameplayComponents.Bots.States.Catcher.Strategies
{
    public abstract class SeekStrategy
    {
        private bool _waitDestination;

        protected SeekStrategy(SeekEscapeeBotState state)
        {
            State = state;
        }

        protected SeekEscapeeBotState State { get; }

        public void Reset()
        {
            _waitDestination = false;
        }

        public void Update()
        {
            if (_waitDestination)
            {
                if (IsStillActual(State.Agent.Destination))
                {
                    return;
                }

                _waitDestination = false;
            }

            OnUpdate();
        }

        protected void SetDestination(Vector3 destination)
        {
            if (State.Agent.TrySetDestination(destination))
            {
                _waitDestination = true;
            }
        }

        protected void SetDestination(IBotDestination destination)
        {
            if (destination.TryMove(State.Agent))
            {
                _waitDestination = true;
            }
        }

        protected Vector3 GetPathBasedDirection(Vector3 destination)
        {
            var path = new NavMeshPath();
            State.Agent.CalculatePath(destination, path);
            var points = path.corners;

            if (path.status == NavMeshPathStatus.PathInvalid || points.Length < 2)
            {
                return JxMathf.GetDirection(State.Player.NetTransform.Position, destination);
            }

            var currentPosition = State.Player.NetTransform.Position;
            var nextWaypoint = points[1];
            return JxMathf.GetDirection(currentPosition, nextWaypoint);
        }

        protected float GetAvoidingWeight(Vector3 destination, Vector3 directionToAvoid, float minAngleDegrees)
        {
            var path = new NavMeshPath();
            State.Agent.CalculatePath(destination, path);
            var pathPoints = path.corners;

            if (path.status == NavMeshPathStatus.PathInvalid || pathPoints.Length < 2)
            {
                // Fallback to direct direction check if no valid path
                var directDirection = JxMathf.GetDirection(State.Player.NetTransform.Position, destination);
                var angleToTarget = Vector3.Angle(directDirection, directionToAvoid);
                return minAngleDegrees - angleToTarget;
            }

            var currentPosition = State.Player.NetTransform.Position;
            var weight = 0f;
            var pointCount = Math.Min(pathPoints.Length, 2);

            // Check each segment of the path to ensure it moves away from escapee
            for (int i = 1; i < pointCount; i++)
            {
                var segmentDirection = JxMathf.GetDirection(
                    i == 1 ? currentPosition : pathPoints[i - 1],
                    pathPoints[i]
                );

                var segmentAngleToTarget = Vector3.Angle(segmentDirection, directionToAvoid);

                weight += segmentAngleToTarget * (pointCount - i);
            }

            return weight;
        }

        protected bool IsAvoidingDirection(Vector3 destination, Vector3 directionToAvoid, float minAngleDegrees)
        {
            var path = new NavMeshPath();
            State.Agent.CalculatePath(destination, path);
            var pathPoints = path.corners;

            if (path.status == NavMeshPathStatus.PathInvalid || pathPoints.Length < 2)
            {
                // Fallback to direct direction check if no valid path
                var directDirection = JxMathf.GetDirection(State.Player.NetTransform.Position, destination);
                var angleToTarget = Vector3.Angle(directDirection, directionToAvoid);
                return angleToTarget > minAngleDegrees;
            }

            var currentPosition = State.Player.NetTransform.Position;

            // Check each segment of the path to ensure it moves away from escapee
            for (int i = 1; i < pathPoints.Length && i < 4; i++)
            {
                var segmentDirection = JxMathf.GetDirection(
                    i == 1 ? currentPosition : pathPoints[i - 1],
                    pathPoints[i]
                );

                var segmentAngleToTarget = Vector3.Angle(segmentDirection, directionToAvoid);

                // Every segment must move away from the target
                if (segmentAngleToTarget <= minAngleDegrees)
                {
                    return false;
                }
            }

            return true;
        }

        protected static float AngleToMinDotProduct(float angleDegrees)
        {
            return Mathf.Cos(angleDegrees * Mathf.Deg2Rad);
        }

        protected static float DotProductToAngle(float dotProduct)
        {
            return Mathf.Acos(Mathf.Clamp(dotProduct, -1f, 1f)) * Mathf.Rad2Deg;
        }

        protected abstract void OnUpdate();

        protected virtual bool IsStillActual(Vector3 point)
        {
            return !State.Agent.IsDestinationAchieved(stoppingDistanceFactor: 3f);
        }
    }
}