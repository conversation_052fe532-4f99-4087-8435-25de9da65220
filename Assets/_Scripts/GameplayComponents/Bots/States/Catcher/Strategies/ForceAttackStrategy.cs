using GameplayComponents.Helpers;
using UnityEngine;

namespace GameplayComponents.Bots.States.Catcher.Strategies
{
    public class ForceAttackStrategy : SeekStrategy
    {
        public ForceAttackStrategy(SeekEscapeeBotState state) : base(state) { }

        protected override void OnUpdate()
        {
            var ownerPosition = State.Player.NetTransform.Position;
            var nearestEscapee = State.Vision.Escapees.VisiblePlaying.GetNearestToPosition(ownerPosition);
            if (ReferenceEquals(nearestEscapee, null))
            {
                State.Mood.RestorePrevious();
                return;
            }
            
            SetDestination(nearestEscapee!.transform.position);
        }

        protected override bool IsStillActual(Vector3 point)
        {
            return false;
        }
    }
}
