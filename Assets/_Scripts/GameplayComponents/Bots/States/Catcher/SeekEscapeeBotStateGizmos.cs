#if UNITY_EDITOR
using System.Collections.Generic;
using UnityEngine;

namespace GameplayComponents.Bots.States.Catcher
{
    public class SeekEscapeeBotStateGizmos
    {
        private readonly SeekEscapeeBotState _state;
        private Vector3 _botPosition;
        private List<(Vector3 direction, bool isRealPlayer, Vector3 escapeePosition)> _escapeeDirections = new();
        private Vector3 _weightedDirection;

        public SeekEscapeeBotStateGizmos(SeekEscapeeBotState state)
        {
            _state = state;
        }

        public void UpdateEscapeeDirectionData(Vector3 botPosition, List<(Vector3 direction, bool isRealPlayer, Vector3 escapeePosition)> escapeeDirections, Vector3 weightedDirection)
        {
            _botPosition = botPosition;
            _escapeeDirections.Clear();
            _escapeeDirections.AddRange(escapeeDirections);
            _weightedDirection = weightedDirection;
        }

        public void OnDrawGizmos()
        {
            if (_escapeeDirections.Count == 0)
                return;

            var originalColor = Gizmos.color;

            // Draw individual directions to each escapee
            foreach (var (direction, isRealPlayer, escapeePosition) in _escapeeDirections)
            {
                // Different colors for real players vs bots
                Gizmos.color = isRealPlayer ? Color.red : Color.yellow;

                // Draw line from bot to escapee
                Gizmos.DrawLine(_botPosition, escapeePosition);

                // Draw direction arrow
                var arrowEnd = _botPosition + direction * 3f;
                Gizmos.DrawLine(_botPosition, arrowEnd);

                // Draw small sphere at escapee position
                Gizmos.DrawWireSphere(escapeePosition, 0.5f);
            }

            // Draw weighted average direction
            if (_weightedDirection != Vector3.zero)
            {
                Gizmos.color = Color.green;
                var weightedEnd = _botPosition + _weightedDirection * 5f;
                Gizmos.DrawLine(_botPosition, weightedEnd);

                // Draw arrow head for weighted direction
                var arrowSize = 0.5f;
                var right = Vector3.Cross(_weightedDirection, Vector3.up).normalized * arrowSize;
                var arrowPoint1 = weightedEnd - _weightedDirection * arrowSize + right;
                var arrowPoint2 = weightedEnd - _weightedDirection * arrowSize - right;

                Gizmos.DrawLine(weightedEnd, arrowPoint1);
                Gizmos.DrawLine(weightedEnd, arrowPoint2);
            }

            // Draw bot position
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(_botPosition, 1f);

            Gizmos.color = originalColor;
        }
    }
}
#endif
