using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Collections;

namespace GameplayComponents.Bots.States.Emoji
{
    public class BotEmojiSender
    {
        private readonly NetGamePlayer _player;
        private readonly BotEmojiPreset _preset;

        public BotEmojiSender(NetGamePlayer player, BotEmojiPreset preset)
        {
            _player = player;
            _preset = preset;
        }

        public void SendEmojiOnState(BotEmojiState state)
        {
            if (!_preset.TryGet(state, out var data))
                return;
            if (data.IsEmpty())
                return;

            var emoji = data.RandomOrDefault();
            _player.Emoji.Send(emoji);
        }
    }
}