using Core.Helpers.NavMesh;
using UnityEngine;

namespace GameplayComponents.Movement
{
    public class LocalPlayerGameplayMovementController : GameplayMovementController, ILocalGameplayMovable
    {
        public void MarkServerControlled()
        {
            if (CharacterController != null)
                CharacterController.enabled = false;
            if (Agent != null)
                Agent.enabled = false;
        }

        protected override void InitializeNavMeshAgent(JxNavMeshAgentComponent agent)
        {
            base.InitializeNavMeshAgent(agent);

            agent.enabled = false;
        }

        void ILocalGameplayMovable.MoveIn(Vector2 direction, float? movementSpeed, float? rotationSpeed) => MoveIn(direction, movementSpeed, rotationSpeed);
    }
}