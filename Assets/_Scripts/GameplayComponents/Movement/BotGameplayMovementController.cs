using Core.Helpers.NavMesh;
using UnityEngine.AI;

namespace GameplayComponents.Movement
{
    public class BotGameplayMovementController : GameplayMovementController
    {
        protected override void SetAgentEnabled(bool isEnabled) {} // controlled by automovement

        protected override void InitializeNavMeshAgent(JxNavMeshAgentComponent agent)
        {
            agent.Initialize(
                new JxNavMeshAgentComponent.Parameters
                {
                    Speed = 2.5f,
                    StoppingDistance = 1f,
                    ObstacleAvoidanceType =
                        ObstacleAvoidanceType.NoObstacleAvoidance, // https://discussions.unity.com/t/disabling-collisions-between-nav-mesh-agents/74925/4
                    Radius = Player.Collision.ScaledRadius,
                }
            );
        }
    }
}