using System.Collections.Generic;
using Core.Helpers;
using Jx.Utils.Extensions;
using Mirror;
using UnityEngine;

namespace GameplayComponents.Helpers
{
    public static class NetworkBehaviourHelpers
    {
        public static T? GetNearestToPosition<T>(this IEnumerable<T>? self, Vector3 toPosition)
            where T : NetworkBehaviour
        {
            return self?.MinByDistance(it => Vector3.Distance(toPosition, it.transform.position));
        }
        
        public static T? GetNearestToPosition<T>(this IReadOnlyCollection<T>? self, Vector3 toPosition)
            where T : NetworkBehaviour
        {
            if (self == null || self.Count == 0)
            {
                return null;
            }
            
            return self.MinByDistance(it => Vector3.Distance(toPosition, it.transform.position));
        }
    }
}
