using System;
using GameplayNetworking.Types;
using UnityEngine;

namespace GameplayComponents.Bushes
{
    public readonly struct BushFieldOfViewConfigurationDto : IEquatable<BushFieldOfViewConfigurationDto>
    {
        public BushFieldOfViewConfigurationDto(JxByteFloat radius, JxByteFloat height)
        {
            Radius = radius;
            Height = height;
        }

        public readonly JxByteFloat Height;
        public readonly JxByteFloat Radius;

        public Vector3 RevealedScale => new Vector3(Radius, Height, Radius);
        public Vector3 HiddenScale => new Vector3(1f, Height, 1f);

        public bool Equals(BushFieldOfViewConfigurationDto other) => Height.Equals(other.Height) && Radius.Equals(other.Radius);

        public override bool Equals(object? obj) => obj is BushFieldOfViewConfigurationDto other && Equals(other);

        public override int GetHashCode() => HashCode.Combine(Height, Radius);

        public static bool operator ==(BushFieldOfViewConfigurationDto left, BushFieldOfViewConfigurationDto right) => left.Equals(right);

        public static bool operator !=(BushFieldOfViewConfigurationDto left, BushFieldOfViewConfigurationDto right) => !left.Equals(right);
    }
}