using System.Linq;
using Jx.Utils.Collections;
using UnityEngine;

namespace GameplayComponents.Fog
{
    public class FogCoverable : MonoBehaviour,
                                  IFogCoverable
    {
        Transform IFogCoverable.Transform => transform;

        [SerializeField]
        private Renderer[] _meshRenderersList;

        private VisionType _visionType;

        private void Awake()
        {
            EnableFullVisibility();
            UpdateVisibility();
        }

        void IFogCoverable.UpdateVisionState(VisionType type, bool add)
        {
            if (add)
            {
                AddVisionState(type);
            }
            else
            {
                RemoveVisionState(type);
            }
        }

        private void AddVisionState(VisionType type)
        {
            if (_visionType.HasFlag(type))
            {
                return;
            }

            _visionType |= type;
            UpdateVisibility();
        }

        private void RemoveVisionState(VisionType type)
        {
            if (!_visionType.HasFlag(type))
            {
                return;
            }

            _visionType &= ~type;
            UpdateVisibility();
        }

        private void EnableFullVisibility()
        {
            _visionType = VisionType.Visible;
        }

        private void UpdateVisibility()
        {
            var isVisible = _visionType == VisionType.Visible;
            _meshRenderersList.ForEach(meshRenderer =>
            {
                if (meshRenderer != null)
                {
                    meshRenderer.enabled = isVisible;
                }
            });
        }

        private void OnTransformChildrenChanged()
        {
            UpdateMeshRenderersList();
        }

        public void UpdateMeshRenderersList()
        {
            _meshRenderersList = GetComponentsInChildren<Renderer>().Where(rend => !rend.gameObject.CompareTag("Fov")).ToArray();
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (_meshRenderersList == null || _meshRenderersList.Length == 0 || _meshRenderersList.Any(rend => rend == null))
            {
                UpdateMeshRenderersList();
            }
        }
#endif
    }
}