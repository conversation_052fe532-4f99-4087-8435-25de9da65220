using System;
using System.Collections.Generic;
using Api.Entities.Skins;
using Core.Managers.AssetLoader;
using GameplayComponents.CharacterPresentation;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Observable;
using UnityEngine;

namespace GameplayComponents
{
    public class SkinChanger : MonoBehaviour
    {
        private const string CharactersPath = "Characters";
        
        private readonly JxObservable _onSkinChanged = new JxObservable();
        
        public Animator Animator { get; private set; } = null!;
        public SkinnedMeshRenderer Renderer { get; private set; } = null!;
        public CharacterHeadPointContainer Head { get; private set; } = null!;
        public CharacterAnimatorMetaInfo Meta { get; } = null!;
        public IJxObservable OnSkinChanged => _onSkinChanged;

        private readonly IDictionary<string, GameObject> _cache = new Dictionary<string, GameObject>();
        private Transform _skinsContainer = null!;
        
        private string? _setSkinId;

        public void Initialize(Transform skinsContainer)
        {
            _skinsContainer = skinsContainer;
        }

        public bool IsAlreadySet(string skinId)
        {
            return string.Equals(_setSkinId, skinId, StringComparison.Ordinal);
        }

        public void SetSkin(SkinGameEntity skin)
        {
            if (string.Equals(_setSkinId, skin.Id, StringComparison.InvariantCultureIgnoreCase))
                return;
            
            foreach (var cached in _cache.Values)
                cached.gameObject.SetActive(false);

            if (_cache.TryGetValue(skin.Id, out var model))
            {
                SelectSkinInternal(skin.Id, model);
                return;
            }

            var prefab = JxResourceLoader.Instance.LoadPrefab(CharactersPath, skin.Character.ContentPath, skin.ContentPath);
            model = Instantiate(prefab, _skinsContainer, false);
            _cache.Add(skin.Id, model);
            SelectSkinInternal(skin.Id, model);
        }

        private void SelectSkinInternal(string id, GameObject model)
        {
            _setSkinId = id;
            model.SetActive(true);
            Renderer = model.GetComponent<SkinnedMeshRenderer>();
            Animator = model.GetComponent<Animator>();
            Head = model.GetComponent<CharacterHeadPointContainer>();
            
            _onSkinChanged.Invoke();
        }
    }
}