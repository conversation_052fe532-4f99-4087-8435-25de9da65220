#if !HEADLESS
using System;
using System.Collections.Generic;
using System.Threading;
using Api.Client.User;
using Api.Client.User.Billing.GoldenPass;
using Api.Client.User.CharacterPath;
using App;
using App.AppUpdate;
using App.Gdpr;
using App.Localization;
using App.Offers;
using App.ReviewManager;
using Audio.BusesInteraction;
using Audio.ListenerManager;
using Audio.Loading;
using Configs.EntityRenderer;
using Configs.Game;
using Configs.Perks;
using Core.Helpers.Navigation;
using Core.UI.LoadingScreen;
using Cysharp.Threading.Tasks;
using ExternalServices.Advertisement;
using ExternalServices.Auth;
using ExternalServices.Auth.Manager;
using ExternalServices.InApp;
using ExternalServices.Integrations.Firebase;
using ExternalServices.Integrations.UnityService;
using Jx.Leaderboard;
using Jx.Telemetry.Actions;
using Jx.Unity.NativePermissions;
using Jx.Unity.Notifications;
using Jx.User.Analytics.Api;
using Jx.User.Reporting.Api;
using Jx.UserRelationship;
using Jx.Utils;
using Jx.Utils.Benchmarks;
using Jx.Utils.Logging;
using Jx.Utils.Threading;
using Jx.Utils.UnityContext;
using Managers.Rewards;
using Modules.Attribution;
using MonsterLand.Matchmaking;
using MonsterLand.Meta.Api;
using Savings;
using UI.Components.Keyboard;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;

namespace SceneLogics.EntryPoints.InitScene
{
    public class InitSceneClientInitializer : IInitSceneInitializer
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(InitSceneClientInitializer));

        private IJxTelemetryActionTracker _actionTracker = null!;

        private readonly LazyInject<INavigation> _navigation;
        private readonly LazyInject<ILocalSave> _localSave;
        private readonly LazyInject<IJxMonsterLandMetaApi> _clientApi;
        private readonly LazyInject<IAuthIntegration> _authIntegration;
        private readonly LazyInject<IAuthManager> _authManager;
        private readonly LazyInject<IGameConfigProvider> _gameConfigProvider;
        private readonly LazyInject<IUserContext> _userContext;
        private readonly LazyInject<IBillingIntegration> _billingIntegration;
        private readonly LazyInject<IJxGoogleAppUpdateIntegration> _appUpdateIntegration;
        private readonly LazyInject<IAdvertisementIntegration> _advertisementIntegration;
        private readonly LazyInject<IJxGdprIntegration> _gdprIntegration;
        private readonly LazyInject<IRewardPresenter> _rewardPresenter;
        private readonly LazyInject<IPerkIntegration> _perkIntegration;
        private readonly LazyInject<IJxOfferIntegration> _offers;
        private readonly LazyInject<IJxNotificationIntegration> _notifications;
        private readonly LazyInject<IJxNativePermissionManager> _nativePermissionManager;
        private readonly LazyInject<IJxReviewManagerIntegration> _reviewManager;
        private readonly LazyInject<IJxAttributionIntegration> _attribution;
        private readonly LazyInject<ILoadingScreenManager> _loadingScreenManager;
        private readonly LazyInject<IGoldenPassServerSynchronizer> _goldenPassSynchronizer;
        private readonly LazyInject<IAudioListenerController> _audioListenerController;
        private readonly LazyInject<IAudioBankLoader> _audioBanksLoader;
        private readonly LazyInject<IEntityRendererRepositoryInitializer> _entityRendererRepositoryInitializer;
        private readonly LazyInject<IAudioVcaInteraction> _vcaInteraction;
        private readonly LazyInject<IJxUserLoggingApi> _loggingApi;
        private readonly LazyInject<IJxLeaderboardIntegration> _leaderboard;
        private readonly LazyInject<IJxUserRelationshipIntegration> _relationship;
        private readonly LazyInject<IJxMatchmakingIntegration> _matchmaking;
        private readonly LazyInject<ICharacterPathProvider> _characterPathProvider;
        private readonly LazyInject<IJxUserAnalyticsApi> _analytics;
        private readonly LazyInject<IJxUnityContext> _unityContext;

        [Preserve]
        public InitSceneClientInitializer(
            LazyInject<INavigation> navigation,
            LazyInject<IGameConfigProvider> gameConfigProvider,
            LazyInject<ILocalSave> localSave,
            LazyInject<IJxMonsterLandMetaApi> clientApi,
            LazyInject<IAuthIntegration> authIntegration,
            LazyInject<IAuthManager> authManager,
            LazyInject<IUserContext> userContext,
            LazyInject<IBillingIntegration> billingIntegration,
            LazyInject<IJxGoogleAppUpdateIntegration> appUpdateIntegration,
            LazyInject<IAdvertisementIntegration> advertisementIntegration,
            LazyInject<IJxGdprIntegration> gdprIntegration,
            LazyInject<IRewardPresenter> rewardPresenter,
            LazyInject<IPerkIntegration> perkIntegration,
            LazyInject<IJxOfferIntegration> offers,
            LazyInject<IJxNotificationIntegration> notifications,
            LazyInject<IJxNativePermissionManager> nativePermissionManager,
            LazyInject<IJxReviewManagerIntegration> reviewManager,
            LazyInject<IJxAttributionIntegration> attribution,
            LazyInject<IJxUnityContext> unityContext,
            LazyInject<ILoadingScreenManager> loadingScreenManager,
            LazyInject<IGoldenPassServerSynchronizer> goldenPassSynchronizer,
            LazyInject<IAudioListenerController> audioListenerController,
            LazyInject<IAudioBankLoader> audioBanksLoader,
            LazyInject<IEntityRendererRepositoryInitializer> entityRendererRepositoryInitializer,
            LazyInject<IAudioVcaInteraction> vcaInteraction,
            LazyInject<IJxUserLoggingApi> loggingApi,
            LazyInject<IJxLeaderboardIntegration> leaderboard,
            LazyInject<IJxUserRelationshipIntegration> relationship,
            LazyInject<IJxMatchmakingIntegration> matchmaking,
            LazyInject<ICharacterPathProvider> characterPathProvider,
            LazyInject<IJxUserAnalyticsApi> analytics
        )
        {
            _navigation = navigation;
            _localSave = localSave;
            _clientApi = clientApi;
            _authIntegration = authIntegration;
            _authManager = authManager;
            _gameConfigProvider = gameConfigProvider;
            _userContext = userContext;
            _billingIntegration = billingIntegration;
            _appUpdateIntegration = appUpdateIntegration;
            _advertisementIntegration = advertisementIntegration;
            _gdprIntegration = gdprIntegration;
            _rewardPresenter = rewardPresenter;
            _perkIntegration = perkIntegration;
            _offers = offers;
            _notifications = notifications;
            _nativePermissionManager = nativePermissionManager;
            _reviewManager = reviewManager;
            _attribution = attribution;
            _unityContext = unityContext;
            _loadingScreenManager = loadingScreenManager;
            _goldenPassSynchronizer = goldenPassSynchronizer;
            _audioListenerController = audioListenerController;
            _audioBanksLoader = audioBanksLoader;
            _entityRendererRepositoryInitializer = entityRendererRepositoryInitializer;
            _vcaInteraction = vcaInteraction;
            _loggingApi = loggingApi;
            _leaderboard = leaderboard;
            _relationship = relationship;
            _matchmaking = matchmaking;
            _characterPathProvider = characterPathProvider;
            _analytics = analytics;
        }

        public async UniTask InitializeAsync()
        {
            _actionTracker = JxTelemetryIntegration.Instance
                .ActionTracker
                .With(new JxTelemetryCallbackActionPopulator(b => { b.Category("app").Object("launch"); }));

            JxGoogleAppUpdateTask? appUpdateTask = null;

            try
            {
                _actionTracker.Track("start");

                var localizationLoadingTask = JxLocalizationIntegration.Instance.LoadSettingsAsync();
                appUpdateTask = _appUpdateIntegration.Value.CreateTask(CancellationToken.None);

                var loadingScreenHandler = await InitializeAsyncInternal(localizationLoadingTask, appUpdateTask.UpdateRequiredCancellationToken);

                _actionTracker.Track("initialized");

                loadingScreenHandler.SetProgress(0.95f);
                await appUpdateTask.WaitReadyTask.TimeoutWithoutException(TimeSpan.FromSeconds(10));

                _actionTracker.Track("updatechecked");
            }
            catch (Exception ex)
            {
                var appUpdateNotRequired = appUpdateTask?.UpdateRequiredCancellationToken.IsCancellationRequested == false;

                _actionTracker.Track("fatal", b => b.Addition(appUpdateNotRequired ? "error" : "update"));

                if (appUpdateNotRequired)
                {
                    _logger.LogError(ex);
                }

                if (appUpdateTask != null)
                {
                    await appUpdateTask.WaitReadyTask;
                }

#if !HEADLESS
                while (true)
                {
                    await _navigation.Value.NavigateToDialogAndWait("InitFail", importantLayer: true);
                }
#endif
            }
        }

        private async UniTask<ILoadingScreenHandler> InitializeAsyncInternal(UniTask localizationLoadingTask, CancellationToken cancellationToken)
        {
            JxUnityServicesCore.Instance.Initialize();
            JxFirebaseCore.Instance.Initialize();
            _audioListenerController.Value.Initialize();

            using (TrackBlock("initialization", "full"))
            {
                _actionTracker.Track("welcome");

                var serverGameplayLoadingTask = _gameConfigProvider.Value.InitializeAsync(cancellationToken);

                using (TrackBlock("localization", "load"))
                {
                    await localizationLoadingTask;
                }

                ILoadingScreenHandler loadingHandler;

                using (TrackBlock("loading", "init"))
                {
                    _loadingScreenManager.Value.Initialize();

                    loadingHandler = _localSave.Value.Value.SessionNumber > 0
                        ? _loadingScreenManager.Value.MainSection.ShowInitial()
                        : _loadingScreenManager.Value.MainSection.ShowFirstLaunch();
                }

                loadingHandler.SetProgress(0.05f);

                using (TrackBlock("audio", "load"))
                {
                    await _audioBanksLoader.Value.LoadAllAsync();
                    _vcaInteraction.Value.Initialize();
                }

                loadingHandler.SetProgress(0.15f);

                using (TrackBlock("auth", "init"))
                {
                    await _authIntegration.Value.InitializeAsync();
                }

#if REMOTE_CONFIG
                _gameConfigProvider.Value.SynchronizeInBackground(cancellationToken: cancellationToken);
#endif
                loadingHandler.SetProgress(0.25f);

                using (TrackBlock("gdpr", "process"))
                {
                    await _gdprIntegration.Value.ProcessAsync(_navigation.Value);
                }

                _unityContext.Value.GdprAccepted();

                loadingHandler.SetProgress(0.5f);

                _localSave.Value.Change(
                    m =>
                    {
                        m.IncrementSessionNumber();
                        m.UpdateLaunchDay();
                    }
                );

                ISignInHandler signInHandler;

                using (TrackBlock("auth", "sign_in"))
                {
                    signInHandler = await _authManager.Value.AuthenticateAsync();
                    await _analytics.Value.RunAsync();
                }

                using (TrackBlock("meta", "load"))
                {
                    await InitializeUserContextAsync(signInHandler);
                }

                var errorTrackingEnabled = _clientApi.Value.Settings.ErrorTrackingEnabled;
                using (TrackBlock("logging", errorTrackingEnabled ? "run" : "interrupt"))
                {
                    if (errorTrackingEnabled)
                    {
                        await _loggingApi.Value.RunAsync();
                    }
                    else
                    {
                        _loggingApi.Value.Interrupt();
                    }
                }

                using (TrackBlock("permission", "access"))
                {
                    _nativePermissionManager.Value.TryActivate();
                    RegisterNotifications();
                }

                loadingHandler.SetProgress(0.6f);

                IJxInitializer initializer;

                using (TrackBlock("initializers", "create"))
                {
                    initializer = JxAppInitializer.Create(EnumerateInitializers());
                }

                using (TrackBlock("initializers", "load"))
                {
                    await initializer.LoadAsync(cancellationToken);
                }

                using (TrackBlock("initializers", "initialize"))
                {
                    await initializer.InitializeAsync(cancellationToken);
                }

                using (TrackBlock("initializers", "configure"))
                {
                    await initializer.ConfigureAsync(cancellationToken);
                }

                using (TrackBlock("initializers", "onconfigured"))
                {
                    await initializer.OnConfiguredAsync(cancellationToken);
                }

                ((UserContext)_userContext.Value).MarkInitialized();
                loadingHandler.SetProgress(0.7f);

                using (TrackBlock("gameplayconfig", "load"))
                {
                    await serverGameplayLoadingTask;
                }

                loadingHandler.SetProgress(0.75f);

                using (TrackBlock("finish", "done"))
                {
                    _entityRendererRepositoryInitializer.Value.Initialize();
                    
                    _matchmaking.Value.RefreshServerListAsync().Forget();

                    JxTelemetryIntegration.Instance.TrackAudioVolume("init");
                    MobileKeyboardPresenter.Initialize();
                }

                return loadingHandler;
            }
        }

        private async UniTask InitializeUserContextAsync(ISignInHandler signInHandler)
        {
            _actionTracker.Track("start", b => b.Addition("usercontext"), immediately: true);

            await UniTask.RunOnThreadPool(() => _clientApi.Value.InitializeAsync(new JxMonsterLandMetaApiOptions(signInHandler.Result.IsNew)));

            if (_clientApi.Value.User?.Value != null)
            {
                signInHandler.Approve();
            }
            
            _actionTracker.Track("completed", b => b.Addition("usercontext"), immediately: true);

            var presetName = _clientApi.Value.User?.Value?.Preset?.Name;
            
            _actionTracker.Track("presetset", b => b.Addition(presetName));
        }

        private IEnumerable<IJxInitializer> EnumerateInitializers()
        {
            foreach (var section in ((UserContext)_userContext.Value).EnumerateSections())
            {
                yield return section;
            }

            yield return _attribution.Value.Initializer;
            yield return _rewardPresenter.Value.Initializer;
            yield return _perkIntegration.Value.Initializer;
            yield return _billingIntegration.Value.Initializer;
            yield return _advertisementIntegration.Value.Initializer;
            yield return _offers.Value.Initializer;
            yield return _reviewManager.Value.Initializer;
            yield return _goldenPassSynchronizer.Value.Initializer;

            yield return _relationship.Value.Initializer;
            yield return _matchmaking.Value.Initializer;
            yield return _leaderboard.Value.Initializer;
            yield return _characterPathProvider.Value.Initializer;
        }

        private IDisposable TrackBlock(string name, string addition)
        {
            return JxMetrics.CreateTimeLogger(
                name,
                onStepTracked: (s, t) => { _actionTracker.Track(s, b => b.Addition(addition).Duration(t)); }
            );
        }

        #region NOTIFICATIONS

        private void RegisterNotifications()
        {
            var launchTracked = new JxAtomicFlag(false);

            var notificationActionTracker = JxTelemetryIntegration.Instance
                .ActionTracker
                .With(new JxTelemetryCallbackActionPopulator(b => b.Category("notification").Object("notification")));

            notificationActionTracker.Track("init");

            _notifications.Value.Initialize(OnNotificationGranted, OnNotificationMessageReceived);
            return;

            void OnNotificationGranted(JxGrantedNotificationState state)
            {
                notificationActionTracker.Track("grant");

                if (_userContext.Value.IsDebugUser.Value)
                {
                    GUIUtility.systemCopyBuffer = state.DeviceToken;
                }

                UniTask.RunOnThreadPool(
                    async () =>
                    {
                        await UniTask.Delay(TimeSpan.FromSeconds(8f));
                        await UniTask.SwitchToMainThread();
                        OnNotificationMessageReceived(null);
                    }
                ).Forget();
            }

            void OnNotificationMessageReceived(JxNotificationMessage? message)
            {
                if (!launchTracked.TrySet())
                {
                    return;
                }

                var launched = false;
                var placement = "unknown";

                if (message != null)
                {
                    launched = message.NotificationOpened;
                    placement = PrepareForTracking(message.Title);
                }

                notificationActionTracker.Track(
                    "launch",
                    b =>
                    {
                        b.SetExtras("from", launched.ToString().ToLowerInvariant())
                            .SetExtras("placement", placement);
                    }
                );
            }

            string PrepareForTracking(string s)
            {
                try
                {
                    if (!string.IsNullOrEmpty(s))
                    {
                        var arr = s.ToCharArray();

                        arr = Array.FindAll(arr, char.IsLetterOrDigit);
                        return new string(arr);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex);
                }

                return "unknown";
            }
        }

        #endregion
    }
}
#endif