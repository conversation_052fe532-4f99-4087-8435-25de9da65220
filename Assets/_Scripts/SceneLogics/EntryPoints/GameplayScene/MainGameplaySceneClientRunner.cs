#if !HEADLESS
using System;
using System.Collections.Generic;
using App.Analytics;
using Core.Helpers.Navigation;
using Core.Helpers.Systems.Controller;
using Core.UI.LoadingScreen;
using Cysharp.Threading.Tasks;
using ExternalServices.Telemetry;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Manager.Network.Client.Connector;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.UserRelationship;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using Mirror;
using Modules.Attribution;
using SceneLogics.GameplayScene.Client.ConnectionFactory;
using SceneLogics.GameplayScene.Client.MatchDisconnectionManager;
using SceneLogics.GameplayScene.Systems.Client.AbilitiesProcessing;
using SceneLogics.GameplayScene.Systems.Client.AbilityControllers;
using SceneLogics.GameplayScene.Systems.Client.Animations;
using SceneLogics.GameplayScene.Systems.Client.Audio;
using SceneLogics.GameplayScene.Systems.Client.Bushes;
using SceneLogics.GameplayScene.Systems.Client.CameraChase;
using SceneLogics.GameplayScene.Systems.Client.ChestsAccent;
using SceneLogics.GameplayScene.Systems.Client.FlyingTexts;
using SceneLogics.GameplayScene.Systems.Client.GhostMode;
using SceneLogics.GameplayScene.Systems.Client.IndicationGameplayGoal;
using SceneLogics.GameplayScene.Systems.Client.Indicators;
using SceneLogics.GameplayScene.Systems.Client.InputModificatorHandler;
using SceneLogics.GameplayScene.Systems.Client.Interaction;
using SceneLogics.GameplayScene.Systems.Client.InteractionMarking;
using SceneLogics.GameplayScene.Systems.Client.InventoryItem;
using SceneLogics.GameplayScene.Systems.Client.Movement;
using SceneLogics.GameplayScene.Systems.Client.NearestPlayersIndicator;
using SceneLogics.GameplayScene.Systems.Client.Perks;
using SceneLogics.GameplayScene.Systems.Client.Score;
using SceneLogics.GameplayScene.Systems.Client.SelfHealing;
using SceneLogics.GameplayScene.Systems.Client.Spawn;
using SceneLogics.GameplayScene.Systems.Client.TeamVisualization;
using SceneLogics.GameplayScene.Systems.Client.Toasts;
using SceneLogics.GameplayScene.Systems.Client.Trackings;
using SceneLogics.GameplayScene.Systems.Client.Tutorial;
using SceneLogics.GameplayScene.Systems.Client.Vfx;
using SceneLogics.GameplayScene.Systems.Client.Vignette;
using SceneLogics.GameplayScene.Systems.Client.Visibility;
using UI.Screens.GameplayScene.Gameplay;
using UI.Screens.GameplayScene.MatchFinish.Providers.UserModelCacheController;
using UI.Screens.GameplayScene.MatchGoal;
using UI.Screens.MainMenuScene.Lobby;
using Zenject;

namespace SceneLogics.EntryPoints.GameplayScene
{
    public class MainGameplayClientRunner : GameplayClientRunner
    {
        private readonly IPreMatchUserSnapshotBuilder _preMatchUserSnapshotBuilder;
        private readonly DiContainer _diScope;
        private readonly INavigation _navigation;
        private readonly IJxUserRelationshipIntegration _relationship;

        public MainGameplayClientRunner(
            INetworkManagerConnector serverConnector,
            ILoadingScreenManager loadingScreen,
            IMatchDisconnectionManager matchDisconnectionManager,
            ILocalGameplayContext localGameplayContext,
            IPreMatchUserSnapshotBuilder preMatchUserSnapshotBuilder,
            IGameplayMatchConnectionParametersFactory connectionParametersFactory,
            DiContainer diScope,
            INavigation navigation,
            IJxUserRelationshipIntegration relationship
        )
            : base(
                serverConnector,
                loadingScreen,
                matchDisconnectionManager,
                localGameplayContext,
                connectionParametersFactory
            )
        {
            _preMatchUserSnapshotBuilder = preMatchUserSnapshotBuilder;
            _diScope = diScope;
            _navigation = navigation;
            _relationship = relationship;
        }

        protected override bool LocalHostOnly => false;

        protected override async UniTask<ILogicSystemsController> CreateSystemsAsync()
        {
            var systems = _diScope.Instantiate<LogicSystemsController>().Cast<ILogicSystemsController>();

            await systems.CreateAsync<MatchInitClientSystem>();
            await systems.CreateAsync<VignetteClientSystem>();
            await systems.CreateAsync<CameraChaseClientSystem>();
            await systems.CreateAsync<MovementClientSystem>();
            await systems.CreateAsync<AnimationClientSystem>();
            await systems.CreateAsync<InteractionClientSystem>();
            await systems.CreateAsync<AbilitiesProcessingClientSystem>();
            await systems.CreateAsync<IndicatorsClientSystem>();
            await systems.CreateAsync<InputModificatorHandlerClientSystem>();
            await systems.CreateAsync<SelfHealingClientSystem>();
            await systems.CreateAsync<VfxClientSystem>();
            await systems.CreateAsync<ToastsClientSystem>();
            await systems.CreateAsync<FlyingTextClientSystem>();
            await systems.CreateAsync<TeamVisualizationClientSystem>();
            await systems.CreateAsync<ScoreControllerClientSystem>();
            await systems.CreateAsync<InteractionVisualizationClientSystem>();
            await systems.CreateAsync<NearPlayersIndicatorsClientSystem>();
            await systems.CreateAsync<IndicationGameplayGoalClientSystem>();
            await systems.CreateAsync<TelemetryGameplayClientSystem>();
            await systems.CreateAsync<GhostClientSystem>();
            await systems.CreateAsync<TutorialClientSystem>();
            await systems.CreateAsync<BushFieldOfViewClientSystem>();
            await systems.CreateAsync<InventoryItemClientSystem>();
            await systems.CreateAsync<VisibilityClientSystem>();
            await systems.CreateAsync<ChestAccentClientSystem>();
            await systems.CreateAsync<AudioClientSystem>(new AudioClientSystemParameters(isTutorial: false));
            await systems.CreateAsync<ClientAbilityControllersSystem>();
            await systems.CreateAsync<PerkClientSystem>();

            return systems;
        }

        protected override UniTask PreloadScreensAsync() => _navigation.PreloadScreenAsync<GameplayScreen>();

        protected override UniTask NavigateGameplayScreenAsync() => _navigation.NavigateAsync<GameplayScreen>();

        protected override UniTask WaitGameplayReadyAsync()
        {
            if (LocalGameplayContext.MatchSyncData.Timing.Value.StartTime + MatchGoalScreenController.TotalDuration < NetworkTime.time)
                return UniTask.CompletedTask;
            
            return _navigation.NavigateAsync<MatchGoalScreen>();
        }

        protected override void PublishPlacement() => _relationship.OnlinePlacements.SetPlacement(PlayerOnlinePlacements.InMatch);

        protected override void TrackStart(GameplayMatchConnectionParameters? connectionParameters)
        {
            try
            {
                var snapshot = _preMatchUserSnapshotBuilder.SaveSnapshot();

                JxTelemetryIntegration.Instance.TrackEvent(
                    TelemetryTrackingContexts.Gameplay,
                    "start",
                    new Dictionary<string, string>
                    {
                        [TelemetryEventParameters.Step] = snapshot.PlayedMatchIndex > 30 ? "30plus" : snapshot.PlayedMatchIndex.ToString(),
                        [TelemetryEventParameters.Team] = connectionParameters?.AuthRequest.Team.ToTrackingName() ?? "null",
                        [TelemetryEventParameters.Name] = snapshot.EscapeeBotBehaviour.DifficultyName,
                    }
                );
            }
            catch (Exception ex)
            {
                Logger.LogError(ex);
            }
        }

        protected override void TrackState(string stateName, GameplayMatchConnectionParameters? connectionParameters)
        {
            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.Gameplay,
                "state",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.Name] = stateName,
                    [TelemetryEventParameters.Team] = connectionParameters?.AuthRequest.Team.ToString() ?? "null",
                    [TelemetryEventParameters.Type] = connectionParameters?.IsLoopback() == false ? "online" : "offline",
                }
            );
            
            JxTelemetryIntegration.Instance.TrackEvent("gameplay", "connection", stateName, connectionParameters?.IsLoopback() == false ? "online" : "offline");
        }
    }
}
#endif