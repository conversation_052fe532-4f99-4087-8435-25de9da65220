#if !HEADLESS
using System.Threading;
using Audio.Manager;
using Audio.Provider;
using Configs.Game;
using Configs.ServerGameplay;
using Core.Helpers.Navigation;
using Core.Helpers.Systems.Controller;
using Core.UI.LoadingScreen;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Manager.Network.Client.Connector;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Objects;
using MonsterLand.Matchmaking.Dto;
using SceneLogics.GameplayScene.Client.ConnectionFactory;
using SceneLogics.GameplayScene.Client.MatchDisconnectionManager;
using SceneLogics.GameplayScene.Systems.Client.AbilitiesProcessing;
using SceneLogics.GameplayScene.Systems.Client.AbilityControllers;
using SceneLogics.GameplayScene.Systems.Client.Animations;
using SceneLogics.GameplayScene.Systems.Client.Audio;
using SceneLogics.GameplayScene.Systems.Client.CameraChase;
using SceneLogics.GameplayScene.Systems.Client.FlyingTexts;
using SceneLogics.GameplayScene.Systems.Client.IndicationGameplayGoal;
using SceneLogics.GameplayScene.Systems.Client.Indicators;
using SceneLogics.GameplayScene.Systems.Client.InputModificatorHandler;
using SceneLogics.GameplayScene.Systems.Client.Interaction;
using SceneLogics.GameplayScene.Systems.Client.InteractionMarking;
using SceneLogics.GameplayScene.Systems.Client.InventoryItem;
using SceneLogics.GameplayScene.Systems.Client.Movement;
using SceneLogics.GameplayScene.Systems.Client.Perks;
using SceneLogics.GameplayScene.Systems.Client.Score;
using SceneLogics.GameplayScene.Systems.Client.SelfHealing;
using SceneLogics.GameplayScene.Systems.Client.Spawn;
using SceneLogics.GameplayScene.Systems.Client.TeamVisualization;
using SceneLogics.GameplayScene.Systems.Client.Toasts;
using SceneLogics.GameplayScene.Systems.Client.Vfx;
using SceneLogics.GameplayScene.Systems.Client.Vignette;
using Tutorial.Schemas.Gameplay.RequirementsProvider;
using UI.Screens.TutorialScene.Gameplay;
using UnityEngine.Scripting;
using Zenject;

namespace SceneLogics.EntryPoints.GameplayScene
{
    public class TutorialGameplayClientRunner : GameplayClientRunner
    {
        private readonly DiContainer _diScope;
        private readonly INavigation _navigation;
        private readonly IGameplayTutorialRequirementsValidator _tutorialRequirementsValidator;
        private readonly IGameConfigProvider _gameConfigProvider;
        private readonly IJxChangeableObject<IServerGameplayConfig, IServerGameplayConfig> _gameplayConfig;

        [Preserve]
        public TutorialGameplayClientRunner(
            INetworkManagerConnector serverConnector,
            ILoadingScreenManager loadingScreen,
            IMatchDisconnectionManager matchDisconnectionManager,
            ILocalGameplayContext localGameplayContext,
            IGameplayMatchConnectionParametersFactory connectionParametersFactory,
            DiContainer diScope,
            IGameplayTutorialRequirementsValidator tutorialRequirementsValidator,
            INavigation navigation,
            IGameConfigProvider gameConfigProvider,
            IJxChangeableObject<IServerGameplayConfig, IServerGameplayConfig> gameplayConfig
        )
            : base(
                serverConnector,
                loadingScreen,
                matchDisconnectionManager,
                localGameplayContext,
                connectionParametersFactory
            )
        {
            _navigation = navigation;
            _gameConfigProvider = gameConfigProvider;
            _gameplayConfig = gameplayConfig;
            _diScope = diScope;
            _tutorialRequirementsValidator = tutorialRequirementsValidator;
        }

        protected override bool LocalHostOnly => true;

        protected override async UniTask<GameplayMatchConnectionParameters> ConnectAsync()
        {
            var config = await _gameConfigProvider.Gameplay.GetAsync(JxMonsterLandGameModeKind.Tutorial);
            _gameplayConfig.Set(config.Value!);
            
            return await base.ConnectAsync();
        }

        protected override UniTask PreloadScreensAsync() => _navigation.PreloadScreenAsync<TutorialGameplayScreen>();

        protected override UniTask NavigateGameplayScreenAsync() => _navigation.NavigateAsync<TutorialGameplayScreen>(new TutorialGameplayScreenParameters());

        protected override async UniTask WaitGameplayReadyAsync()
        {
            await _tutorialRequirementsValidator.IsRequirementsMet.AwaitConcreteResult(true);
            await LocalGameplayContext.WaitReadyAsync(CancellationToken.None);
            
            JxAudioManager.Instance.Music.GetHandler(AudioLibrary.Instance.Music.MainTheme).Play();
        }

        protected override void TrackStart(GameplayMatchConnectionParameters? connectionParameters) { }

        protected override void TrackState(string stateName, GameplayMatchConnectionParameters? connectionParameters) { }

        protected override async UniTask<ILogicSystemsController> CreateSystemsAsync()
        {
            var systems = _diScope.Instantiate<LogicSystemsController>().Cast<ILogicSystemsController>();

            await systems.CreateAsync<MatchInitClientSystem>();
            await systems.CreateAsync<VignetteClientSystem>();
            await systems.CreateAsync<CameraChaseClientSystem>();
            await systems.CreateAsync<MovementClientSystem>();
            await systems.CreateAsync<AnimationClientSystem>();
            await systems.CreateAsync<InteractionClientSystem>();
            await systems.CreateAsync<AbilitiesProcessingClientSystem>();
            await systems.CreateAsync<IndicatorsClientSystem>();
            await systems.CreateAsync<InputModificatorHandlerClientSystem>();
            await systems.CreateAsync<SelfHealingClientSystem>();
            await systems.CreateAsync<VfxClientSystem>();
            await systems.CreateAsync<ToastsClientSystem>();
            await systems.CreateAsync<FlyingTextClientSystem>();
            await systems.CreateAsync<TeamVisualizationClientSystem>();
            await systems.CreateAsync<ScoreControllerClientSystem>();
            await systems.CreateAsync<InteractionVisualizationClientSystem>();
            await systems.CreateAsync<IndicationGameplayGoalClientSystem>();
            await systems.CreateAsync<InventoryItemClientSystem>();
            await systems.CreateAsync<AudioClientSystem>(new AudioClientSystemParameters(isTutorial: true));
            await systems.CreateAsync<ClientAbilityControllersSystem>();
            await systems.CreateAsync<PerkClientSystem>();

            return systems;
        }

        protected override void PublishPlacement() { }
    }
}
#endif