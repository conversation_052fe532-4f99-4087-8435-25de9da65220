#if HEADLESS
using System;
using System.Threading;
using Agones;
using Configs.Game;
using Configs.Perks.Loaders;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Manager.Network.Server.Runner;
using Jx.MatchDirector.Api;
using Jx.Utils.Logging;
using Mirror;
using UnityEngine.Scripting;

namespace SceneLogics.EntryPoints.GameplayScene
{
    public class GameplaySceneHeadlessInitializer : IGameplaySceneInitializer
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(GameplaySceneHeadlessInitializer));

        private readonly INetworkManagerServerRunner _serverRunner;
        private readonly IGameConfigProvider _gameConfigProvider;
        private readonly IPerkConfigLoader _perkConfigLoader;
        private readonly IJxAgonesIntegration _agones;
        private readonly IJxMatchDirectorIntegration _matchDirectorIntegration;

        [Preserve]
        public GameplaySceneHeadlessInitializer(
            INetworkManagerServerRunner serverRunner,
            IGameConfigProvider gameConfigProvider,
            IPerkConfigLoader perkConfigLoader,
            IJxAgonesIntegration agones,
            IJxMatchDirectorIntegration matchDirectorIntegration
        )
        {
            _serverRunner = serverRunner;
            _gameConfigProvider = gameConfigProvider;
            _perkConfigLoader = perkConfigLoader;
            _agones = agones;
            _matchDirectorIntegration = matchDirectorIntegration;
        }

        public async UniTask ExecuteAsync()
        {
            await _serverRunner.RunAsync(NetworkManagerMode.ServerOnly);

            // _gameConfigProvider.StartReloadingInBackground(TimeSpan.FromMinutes(5), CancellationToken.None);
            // _perkConfigLoader.StartReloadingInBackground(TimeSpan.FromMinutes(5), CancellationToken.None);

            var agonesSuccess = await _agones.RegisterAsync();

            await _matchDirectorIntegration.InitializeAsync(8000, agonesSuccess);
        }

        public UniTask StopAsync() => UniTask.CompletedTask;
    }
}
#endif