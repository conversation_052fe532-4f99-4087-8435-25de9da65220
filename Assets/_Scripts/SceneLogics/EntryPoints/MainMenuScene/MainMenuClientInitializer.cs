using Core.Helpers.Navigation;
using Cysharp.Threading.Tasks;
using Jx.Utils.Benchmarks;
using UnityEngine.Scripting;

namespace SceneLogics.EntryPoints.MainMenuScene
{
    [Preserve]
    public class MainMenuClientInitializer
    {
        private readonly INavigation _navigation;

        [Preserve]
        public MainMenuClientInitializer(INavigation navigation)
        {
            _navigation = navigation;
        }

        public async UniTask InitializeAsync()
        {
            await PreloadScreensAsync();
        }

        public UniTask CleanupAsync()
        {
            return UniTask.CompletedTask;
        }

        private async UniTask PreloadScreensAsync()
        {
            using (var logger = JxMetrics.CreateTimeLogger(nameof(MainMenuClientInitializer) + " " + nameof(PreloadScreensAsync)))
            {
                // await _navigation.PreloadScreenAsync<LobbyScreen>();
                // logger.TrackStep(nameof(LobbyScreen));
                
                // await _navigation.PreloadScreenAsync<MainMenuScreen>();
                // logger.TrackStep(nameof(MainMenuScreen));
                //
                // await _navigation.PreloadScreenAsync<CharactersScreen>();
                // logger.TrackStep(nameof(CharactersScreen));
                //
                // await _navigation.PreloadScreenAsync<ShopScreen>();
                // logger.TrackStep(nameof(ShopScreen));
                //
                // await _navigation.PreloadScreenAsync<MainMenuSettingsScreen>();
                // logger.TrackStep(nameof(MainMenuSettingsScreen));
                //
                // await _navigation.PreloadScreenAsync<QuestsScreen>();
                // logger.TrackStep(nameof(QuestsScreen));
                //
                // await _navigation.PreloadScreenAsync<TeamSelectorScreen>();
                // logger.TrackStep(nameof(TeamSelectorScreen));
                //
                // await _navigation.PreloadScreenAsync<ProfileScreen>();
                // logger.TrackStep(nameof(ProfileScreen));
                //
                // await _navigation.PreloadScreenAsync<RewardScreen>();
                // logger.TrackStep(nameof(RewardScreen));
                //
                // await _navigation.PreloadScreenAsync<OfferScreen>();
                // logger.TrackStep(nameof(OfferScreen));
            }
        }
    }
}