using GameplayNetworking.Gameplay.Player.Components;
using GameplayNetworking.Gameplay.Player.Data;
using SceneLogics.GameplayScene.Client.Vfx;

namespace SceneLogics.GameplayScene.Systems.Client.Vfx.Processors
{
    public class PlayerActionVfxClientProcessor : VfxClientProcessor
    {
        public PlayerActionVfxClientProcessor(VfxClientProcessorContext context) : base(context)
        {
        }

        protected override void OnExecuted()
        {
            DisposeOnExit(Context.Player.BaseSyncData.Action.Subscribe(OnActionChange));
        }

        private void OnActionChange(ActionType action)
        {
            switch (action)
            {
                case ActionType.Lose:
                    Context.VfxManager.ShowAndForget(
                        VfxBuilder
                            .Create(ParticleType.Death)
                            .SetOwner(Context.Player)
                            .Build()
                    );
                    break;
            }
        }

        protected override void OnExit()
        {
        }
    }
}