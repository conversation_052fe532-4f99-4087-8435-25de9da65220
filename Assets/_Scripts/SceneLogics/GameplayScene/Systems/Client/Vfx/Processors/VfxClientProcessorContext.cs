using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using SceneLogics.GameplayScene.Client.Vfx;

namespace SceneLogics.GameplayScene.Systems.Client.Vfx.Processors
{
    public class VfxClientProcessorContext
    {
        public VfxClientProcessorContext(ILocalGameplayContext localGameplayContext, NetGamePlayer player, IClientVfxManager vfxManager)
        {
            LocalGameplayContext = localGameplayContext;
            Player = player;
            VfxManager = vfxManager;
        }

        public ILocalGameplayContext LocalGameplayContext { get; }
        public NetGamePlayer Player { get; }
        public IClientVfxManager VfxManager { get; }
    }
}