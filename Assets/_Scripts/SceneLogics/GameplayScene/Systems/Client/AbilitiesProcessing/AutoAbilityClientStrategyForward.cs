using SceneLogics.GameplayScene.Abilities.Parameters;

namespace SceneLogics.GameplayScene.Systems.Client.AbilitiesProcessing.Strategies
{
    public class AutoAbilityClientStrategyForward : AutoAbilityClientStrategy
    {
        public static AutoAbilityClientStrategy Instance { get; } = new AutoAbilityClientStrategyForward();

        protected AutoAbilityClientStrategyForward() { }

        public override GenericAbilityInputParameters CreateParameters(AutoAbilityClientStrategyContext context)
        {
            var ownerTransform = context.GameplayContext.Player.NetTransform;
            
            var startPoint = ownerTransform.Position + ownerTransform.Forward * context.GameplayContext.Player.Collision.Radius;
            var endPoint = startPoint + ownerTransform.Forward * context.ProjectileCollisionService.GetTargetDistance(ownerTransform.Forward);
            
            return AbilityParametersFactory.CreatePointTarget(startPoint, endPoint);
        }
    }
}