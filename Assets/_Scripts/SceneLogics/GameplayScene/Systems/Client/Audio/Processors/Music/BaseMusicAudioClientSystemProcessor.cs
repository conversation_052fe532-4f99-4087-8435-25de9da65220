using Audio.Manager;
using Audio.Manager.Handler;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace SceneLogics.GameplayScene.Systems.Client.Audio.Processors.Music
{
    public class BaseMusicAudioClientSystemProcessor : AudioClientSystemProcessor
    {
        protected const float TickDurationInSec = 0.15f;
        
        private const string LitCampfiresParameter = "LitCampfires";

        private int _startCampfireGoal;
        
        public BaseMusicAudioClientSystemProcessor(ClientAudioSystemProcessorContext context) : base(context)
        {
        }
        
        protected IClientAudioInstanceHandler MainThemeAudioHandler { get; private set; } = null!;

        protected override void OnExecute()
        {
            _startCampfireGoal = Context.Gameplay.MatchSyncData.GameplayInfo.Value.CampfireGoal;
            MainThemeAudioHandler = JxAudioManager.Instance.Music.GetHandler(Music.MainTheme);
            MainThemeAudioHandler.Play();
            
            DisposeOnExit(Context.Gameplay.MatchSyncData.GameplayInfo.SubscribeAndFire(OnCampfireGoalChange));
        }

        protected override void OnExit()
        {
            base.OnExit();
            MainThemeAudioHandler?.Dispose();
        }

        private void OnCampfireGoalChange(NetGameplayInfoDto gameplayInfoDto)
        {
            var goal = gameplayInfoDto.RestCampfires;
            MainThemeAudioHandler.ChangeParameter(LitCampfiresParameter, Mathf.Clamp(_startCampfireGoal - goal, 0, _startCampfireGoal));
        }
    }
}