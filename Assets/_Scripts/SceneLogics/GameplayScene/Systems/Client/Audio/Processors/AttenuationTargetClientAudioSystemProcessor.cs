using Audio.ListenerManager;
using Jx.Utils.ChangeableObjects;
using Mirror;

namespace SceneLogics.GameplayScene.Systems.Client.Audio.Processors
{
    public class AttenuationTargetClientAudioSystemProcessor : AudioClientSystemProcessor
    {
        private readonly IAudioListenerController _audioListenerController;

        public AttenuationTargetClientAudioSystemProcessor(
            ClientAudioSystemProcessorContext context,
            IAudioListenerController audioListenerController
        )
            : base(context)
        {
            _audioListenerController = audioListenerController;
        }

        protected override void OnExecute()
        {
            DisposeOnExit(Context.Gameplay.Spectating.SpectatingIdentity.SubscribeAndFire(OnSpectatedIdentityChange));
        }

        protected override void OnExit()
        {
            _audioListenerController.SetAttenuationTarget(null);
        }

        private void OnSpectatedIdentityChange(NetworkIdentity? identity)
        {
            if (ReferenceEquals(identity, null))
                return;

            _audioListenerController.SetAttenuationTarget(identity.gameObject);
        }
    }
}