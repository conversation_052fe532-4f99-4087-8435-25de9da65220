using GameplayNetworking.Gameplay.Player.Shared.Ability;

namespace SceneLogics.GameplayScene.Systems.Client.AbilityControllers.Controllers
{
    public class FayWallAbilityClientController : AbilityClientController
    {
        protected override void OnExecuted() { }
        protected override void OnExit() { }

        protected override void OnStateChangeInternal(GenericClientAbilityStateType state)
        {
            switch (state)
            {
                case GenericClientAbilityStateType.Processing:
                    SoundManager.PlayOneShot(AbilitySounds.Activation.Fay, Player);
                    break;
            }
        }
    }
}