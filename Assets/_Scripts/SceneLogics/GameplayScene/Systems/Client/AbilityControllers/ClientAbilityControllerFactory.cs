using System;
using System.Collections.Generic;
using Jx.Utils.Objects;
using SceneLogics.GameplayScene.Abilities;
using SceneLogics.GameplayScene.Systems.Client.AbilityControllers.Controllers;

namespace SceneLogics.GameplayScene.Systems.Client.AbilityControllers
{
    public class ClientAbilityControllerFactory
    {
        private static readonly IReadOnlyDictionary<AbilityType, Type> _controllerTypeByAbilityType = new Dictionary<AbilityType, Type>()
        {
            // register here new ones
            [AbilityType.AngelHelp] = typeof(AngelHelpAbilityClientController),
            [AbilityType.EchoLocation] = typeof(EchoAbilityClientController),
            [AbilityType.ElectraField] = typeof(ElectraFieldAbilityClientController),
            [AbilityType.EscapeeMimicry] = typeof(EscapeeMimicryAbilityClientController),
            [AbilityType.FayWall] = typeof(FayWallAbilityClientController),
            [AbilityType.Fear] = typeof(FearAbilityClientController),
            [AbilityType.Fireball] = typeof(FireballAbilityClientController),
            [AbilityType.Hook] = typeof(HookAbilityClientController),
            [AbilityType.Invisibility] = typeof(InvisibilityAbilityClientController),
            [AbilityType.JumpShake] = typeof(JumpShakeAbilityClientController),
            [AbilityType.KnockingDash] = typeof(KnockingDashAbilityClientController),
            [AbilityType.LuckyBag] = typeof(LuckyBagAbilityClientController),
            [AbilityType.Roll] = typeof(RollAbilityClientController),
            [AbilityType.SmokeCloud] = typeof(SmokeCloudAbilityClientController),
            [AbilityType.SpiderWeb] = typeof(SpiderWebAbilityClientController),
            [AbilityType.Teleport] = typeof(TeleportAbilityClientController),
            [AbilityType.TeleportInMark] = typeof(TeleportInMarkAbilityClientController),
            [AbilityType.ThrowRock] = typeof(ThrowRockAbilityClientController),
            [AbilityType.TimeLaps] = typeof(TimelapsAbilityClientController),
            [AbilityType.LazyRegeneration] = typeof(LazyRegenerationAbilityClientController),
        };

        public ClientAbilityControllerFactory()
        { }
        
        public AbilityClientController? CreateOrNull(AbilityType abilityType, AbilityClientControllerContext context)
        {
            if (abilityType == AbilityType.None)
                return null;
            
            if (!_controllerTypeByAbilityType.TryGetValue(abilityType, out var controller))
                return null;
            
            var instance = Activator.CreateInstance(controller);
            var casted = instance.Cast<AbilityClientController>();
            casted.Initialize(context);
            return casted;
        }
    }
}