using System;
using System.Collections.Generic;
using Core.Extensions;
using Core.Helpers.Systems;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player.Components.GameplayIndication;
using Jx.Utils.Helpers;
using SceneLogics.GameplayScene.Components.Indicators;
using UnityEngine.Scripting;

namespace SceneLogics.GameplayScene.Systems.Client.Indicators
{
    public class IndicatorsClientSystem : BaseLogicSystem
    {
        private readonly ILocalGameplayContext _localGameplayContext;
        private readonly IIndicatorsController _indicatorsController;

        private readonly IDictionary<uint, IDisposable> _indicationCancellationId
            = new Dictionary<uint, IDisposable>();

        private IDisposable? _indicationRequestSubscription;

        [Preserve]
        public IndicatorsClientSystem(
            ILocalGameplayContext localGameplayContext,
            IIndicatorsController indicatorsController
        )
        {
            _localGameplayContext = localGameplayContext;
            _indicatorsController = indicatorsController;
        }

        protected override UniTask OnExecuteAsync()
        {
            _indicationRequestSubscription = _localGameplayContext.Player.Indicator.OnClientRequested.Subscribe(OnIndicationRequested);
            return base.OnExecuteAsync();
        }

        protected override UniTask OnDeInitializeAsync()
        {
            _indicationRequestSubscription?.Dispose();
            return UniTask.CompletedTask;
        }

        private void OnIndicationRequested(GameplayIndicationRequest request)
        {
            var id = request.GetId();
            var isStarted = request.IsStarted();

            if (!isStarted)
            {
                HideIndication(id);
                return;
            }

            // don't show double indication
            if (_indicationCancellationId.ContainsKey(id))
                return;
            
            var cancellation = _indicatorsController.Indicate(request.Type, request.Target.transform);
            _indicationCancellationId.Add(id, cancellation);
            
            var duration = request.GetDuration();
            if (duration != null)
            {
                UniTask.Delay(duration.Value, cancellationToken: _localGameplayContext.Player.NetIdentity.GetCancellationTokenOnDestroy())
                       .ContinueWith(() => HideIndication(id))
                       .SuppressCancellationThrow()
                       .Forget();
            }
        }

        private void HideIndication(uint id)
        {
            if (!_indicationCancellationId.Remove(id, out var cancellation)) 
                return;
            
            cancellation.Dispose();
        }
    }
}