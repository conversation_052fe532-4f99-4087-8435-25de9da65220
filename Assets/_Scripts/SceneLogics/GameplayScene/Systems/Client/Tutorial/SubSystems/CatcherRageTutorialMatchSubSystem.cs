using GameplayNetworking.Gameplay.Player.Catcher;
using LoM.Characters.ClientIntegration;

namespace SceneLogics.GameplayScene.Systems.Client.Tutorial.SubSystems
{
    public class CatcherRageTutorialMatchSubSystem : TutorialMatchSubSystem
    {
        private const string SpeedUpFirstCampfireId = "SpeedUpFirstCampfire.Catcher";
        private const string SpeedUpLastCampfireId = "SpeedUpLastCampfire.Catcher";
        private const float CloudDurationInSec = 5f;
        private const int MaxCatcherMatchCount = 1;

        public CatcherRageTutorialMatchSubSystem(TutorialMatchSubSystemContext context)
            : base(context) { }

        protected override void OnExecuted()
        {
            if (Context.Player is not NetCatcherPlayer catcher)
                return;

            if (GetPlayedMatchCount(TeamKind.Catcher) >= MaxCatcherMatchCount)
                return;

            DisposeOnExit(catcher.ClientRageController.RageStacks.Subscribe(OnRageChange));
        }

        private void OnRageChange(int rage)
        {
            switch (rage)
            {
                case 1:
                    ShowCloudPopup(SpeedUpFirstCampfireId);
                    break;

                case 5:
                    ShowCloudPopup(SpeedUpLastCampfireId);
                    break;
            }
        }

        private void ShowCloudPopup(string localization) => Context.TutorialMatchContext.TutorialCloudPopupController.Show(localization, CloudDurationInSec);
    }
}