using System;
using Core.UI.Toasts;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Share.Character;

namespace SceneLogics.GameplayScene.Systems.Client.Tutorial.SubSystems
{
    public class TutorialHealMatchSubSystem : TutorialMatchSubSystem
    {
        public TutorialHealMatchSubSystem(TutorialMatchSubSystemContext context)
            : base(context) { }

        protected override void OnExecuted()
        {
            if (!Context.Player.IsEscapee())
                return;

            if (!CheckMustShowSelfHealingTips() && !CheckMustShowHealingTips())
                return;

            DisposeOnExit(Context.Player.SyncData.AvailableInteraction.Subscribe(HandleInteractionDataUpdated));
        }

        private void HandleInteractionDataUpdated(NetPlayerAvailableInteractionDto data)
        {
            if (CheckMustShowHealingTips() &&
                Context.Player.SyncData.Action.Value == ActionType.Interaction &&
                data.InteractionType == InteractionType.HealEscapeeTeammate)
            {
                ToastManager.Instance.Show(
                    "GameplayTutorialMedkit",
                    duration: TimeSpan.FromSeconds(Context.Config.HealingMessageDurationInSeconds)
                );
            }

            if (CheckMustShowSelfHealingTips() && data.InteractionType == InteractionType.SelfHealing)
            {
                ToastManager.Instance.Show(
                    "GameplayTutorialSelfHeal",
                    duration: TimeSpan.FromSeconds(Context.Config.SelfHealingMessageDurationInSeconds)
                );
            }
        }

        private static bool CheckMustShowSelfHealingTips() => true;
        private static bool CheckMustShowHealingTips() => true;
    }
}