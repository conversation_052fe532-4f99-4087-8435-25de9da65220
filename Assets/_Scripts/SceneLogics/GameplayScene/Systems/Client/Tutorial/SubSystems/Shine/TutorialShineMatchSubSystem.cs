using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Share.Character;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Collections;

namespace SceneLogics.GameplayScene.Systems.Client.Tutorial.SubSystems.Shine
{
    public class TutorialShineMatchSubSystem : TutorialMatchSubSystem
    {
        private readonly IList<TutorialShineProcessor> _processors;

        public TutorialShineMatchSubSystem(TutorialMatchSubSystemContext context) : base(context)
        {
            _processors = new List<TutorialShineProcessor>()
            {
                //new ChestTutorialShineProcessor()
            };
            
            if (context.Player.IsEscapee())
            {
                _processors.Add(new CampfireTutorialShineProcessor());
                _processors.Add(new CageTutorialShineProcessor());
            }
        }

        protected override void OnExecuted()
        {
            var playedMatchCount = GetPlayedMatchCount();
            _processors.ForEach(p => p.Initialize(Context.GameplayContext, playedMatchCount));
            
            DisposeOnExit(Context.Player.SyncData.Action.SubscribeAndFire(OnActionChange));
        }

        public override void Exit()
        {
            base.Exit();
            
            _processors.ForEach(p => p.DeInitialize());
        }

        private void OnActionChange()
        {
            _processors.ForEach(UpdateProcessor);
        }

        private void UpdateProcessor(TutorialShineProcessor processor)
        {
            processor.OnPlayerActionChange();
        }
    }
}