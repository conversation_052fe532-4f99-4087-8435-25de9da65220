using Core.Helpers.Systems;
using Cysharp.Threading.Tasks;
using UnityEngine.Scripting;

namespace SceneLogics.GameplayScene.Systems.Client.FlyingTexts
{
    public class FlyingTextClientSystem : BaseLogicSystem
    {
        [Preserve]
        public FlyingTextClientSystem()
        {
        }
        
        protected override UniTask OnInitializeAsync()
        {
            return UniTask.CompletedTask;
        }

        protected override UniTask OnDeInitializeAsync()
        {
            return UniTask.CompletedTask;
        }
    }
}