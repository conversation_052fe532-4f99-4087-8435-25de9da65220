using System.Collections.Generic;
using Core.Haptic;
using GameplayNetworking.Gameplay.Player.Components.GameplayIndication;
using SceneLogics.GameplayScene.Components.Indicators;

namespace SceneLogics.GameplayScene.Systems.Client.Haptic.Processors.Shared
{
    public class IndicationHapticClientSystemProcessor : HapticClientSystemProcessor
    {
        private static readonly ISet<IndicationType> _excludedIndications = new HashSet<IndicationType>()
        {
            
        };
        
        public IndicationHapticClientSystemProcessor(HapticClientSystemProcessorContext context) : base(context)
        {
        }

        protected override void OnExecuted()
        {
            DisposeOnExit(Context.LocalGameplayContext.Player.Indicator.OnClientRequested.Subscribe(OnIndicationRequest));
        }
        
        protected override void OnExit()
        {
        }

        private static void OnIndicationRequest(GameplayIndicationRequest request)
        {
            var type = request.Type;
            if (_excludedIndications.Contains(type))
                return;
            
            JxHapticManager.Instance.Play(Haptics.Instance.IndicatorAppeared);
        }
    }
}