using Core.Haptic;

namespace SceneLogics.GameplayScene.Systems.Client.Haptic.Processors.Escapee
{
    public class GateOpenEscapeeHapticClientSystemProcessor : HapticClientSystemProcessor
    {
        public GateOpenEscapeeHapticClientSystemProcessor(HapticClientSystemProcessorContext context) : base(context)
        {
        }

        protected override void OnExecuted()
        {
            foreach (var gate in Context.LocalGameplayContext.Gates)
            {
                DisposeOnExit(gate.IsOpened.Subscribe(OnAnyGateOpenedStatusChange));
            }
        }
        
        protected override void OnExit()
        {
        }

        private static void OnAnyGateOpenedStatusChange(bool isOpened)
        {
            if (!isOpened)
                return;
            
            JxHapticManager.Instance.Play(Haptics.Instance.GatesFullyOpened);
        }
    }
}