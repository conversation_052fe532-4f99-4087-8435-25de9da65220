using System;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Components.Animations;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Share.Character;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace SceneLogics.GameplayScene.Systems.Client.Animations
{
    public class CatcherClientAnimationHandler : PlayerClientAnimationHandler<NetCatcherPlayer>
    {
        private IDisposable? _configSubscription;

        public CatcherClientAnimationHandler(NetCatcherPlayer player)
            : base(player) { }

        public override void Initialize()
        {
            base.Initialize();

            // if (Player.SyncData.PublicInfo.Value.CharacterIdentifier.IsCatcher(CatcherType.Goliath))
            //     OverrideDuration(CharacterAnimationType.AbilityInProgress, CharacterAnimationSpeedType.AbilityInProgressSpeed, 0.65f);

            _configSubscription = Player.SyncData.SharedConfig.SubscribeAndFire(OnConfigChanged);
        }

        public override void Dispose()
        {
            base.Dispose();

            _configSubscription?.Dispose();
        }
        
        public void OnSuccessfulAttack()
        {
            Animator!.SetTrigger(Mapper.AttackSuccessfulHash);
        }

        private void OnConfigChanged(NetSharedConfigDto config)
        {
            var attackDuration = config.AttackDurationInMills / 1000f;
            var successfulAttackDuration = config.SuccessfulAttackDurationInMills / 1000f;
            
            OverrideDuration(CharacterAnimationType.Attack, CharacterAnimationSpeedType.Attack, attackDuration);
            OverrideDuration(CharacterAnimationType.SuccessfulAttack, CharacterAnimationSpeedType.SuccessfulAttack, Mathf.Abs(successfulAttackDuration - attackDuration));
        }
    }
}