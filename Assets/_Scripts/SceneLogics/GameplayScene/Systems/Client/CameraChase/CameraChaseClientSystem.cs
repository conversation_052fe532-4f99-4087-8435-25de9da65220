using Core.Helpers.Systems;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Local;
using Jx.Utils.ChangeableObjects;
using Mirror;
using SceneLogics.GameplayScene.Components;
using UnityEngine.Scripting;

namespace SceneLogics.GameplayScene.Systems.Client.CameraChase
{
    public class CameraChaseClientSystem : BaseLogicSystem
    {
        private readonly ILocalGameplayContext _localGameplayContext;

        [Preserve]
        public CameraChaseClientSystem(ILocalGameplayContext localGameplayContext)
        {
            _localGameplayContext = localGameplayContext;
        }

        private CameraChaserComponent CameraChaser => _localGameplayContext.CameraController.Chaser;

        protected override UniTask OnInitializeAsync()
        {
            return UniTask.CompletedTask;
        }

        protected override UniTask OnExecuteAsync()
        {
            CameraChaser.Setup(_localGameplayContext.Player.SyncData.SharedConfig.Value.Camera);
            
            DisposeOnExit(_localGameplayContext.Spectating.SpectatingIdentity.SubscribeAndFire(OnSpectatingTargetChanged));
            return base.OnExecuteAsync();
        }

        private void OnSpectatingTargetChanged(NetworkIdentity? target)
        {
            if (target == null)
                return;
            
            if (target.TryGetComponent<ILocalGameplayPlayer>(out var player))
                CameraChaser.UpdateTarget(target.transform, player.SyncData.SharedConfig.Value.Camera);
            else
                CameraChaser.UpdateTarget(target.transform, null);
        }
    }
}