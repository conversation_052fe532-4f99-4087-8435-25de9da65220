using System;
using System.Collections.Generic;
using App.Analytics;
using App.Inventory.Item;
using Core.Helpers.Systems;
using Cysharp.Threading.Tasks;
using ExternalServices.Telemetry;
using GameplayNetworking.Gameplay.Local.Input;
using GameplayNetworking.Gameplay.Inventory;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player.Data;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils.Extensions;
using Modules.Attribution;
using UnityEngine.Scripting;

namespace SceneLogics.GameplayScene.Systems.Client.InventoryItem
{
    [Preserve]
    public class InventoryItemClientSystem : BaseLogicSystem
    {
        private readonly ILocalGameplayContext _localGameplayContext;
        private readonly ILocalInputContext _localInputContext;

        [Preserve]
        public InventoryItemClientSystem(
            ILocalGameplayContext localPlayerContext,
            ILocalInputContext localInputContext
        )
        {
            _localGameplayContext = localPlayerContext;
            _localInputContext = localInputContext;
        }

        protected override UniTask OnExecuteAsync()
        {
            DisposeOnExit(_localGameplayContext.Player.SyncData.InventoryItemSlot.Subscribe(OnInventoryChange));
            DisposeOnExit(_localInputContext.InventoryView.SubscribeToUse(OnUseInventoryItemClick));
            DisposeOnExit(_localGameplayContext.Player.SyncData.Action.SubscribeAndFireOnMainThread(OnPlayerActionChange));

            var slot = _localGameplayContext.Player.SyncData.InventoryItemSlot.Value;
            OnInventoryChange(slot, slot);

            return base.OnExecuteAsync();
        }

        private void OnPlayerActionChange(ActionType actionType)
        {
            _localInputContext.InventoryView.SetInteractable(actionType.HasPermission(ActionPermissions.UseInventoryItem));
        }

        private void OnInventoryChange(InventoryItemSlot old, InventoryItemSlot @new)
        {
            _localInputContext.InventoryView.Render(@new.IsEmpty ? Array.Empty<InventoryItemInfo>() : new[] { new InventoryItemInfo(@new) });
            Track(old, @new);
        }

        private void OnUseInventoryItemClick(InventoryItemSlot identifier)
        {
            _localGameplayContext.Player.Inventory.Cmd_ConsumeItem(identifier);
        }
        
        private void Track(InventoryItemSlot old, InventoryItemSlot @new)
        {
            if (!old.IsEmpty && @new.IsEmpty)
            {
                TrackItem("consume", old.ItemType);
            }

            if (old.IsEmpty && !@new.IsEmpty)
            {
                TrackItem("pickup", @new.ItemType);
            }

            return;

            void TrackItem(string action, InventoryItemType type)
            {
                _localGameplayContext.ActionTracker.Track(action,
                    b =>
                    {
                        b.Object("item");
                        b.Addition(type.ToString().ToLowerInvariant());
                    });
            }
        }
    }
}