using UI.SceneScreens.Gameplay.Components.CatcherRage;

namespace SceneLogics.GameplayScene.Systems.Client.CatcherRage
{
    public class CatcherRageClientSystemParameters
    {
        public CatcherRageClientSystemParameters(
            CatcherRageIndicatorView indicator
        )
        {
            Indicator = indicator;
        }

        public CatcherRageIndicatorView Indicator { get; }
    }
}