using GameplayNetworking.Gameplay.Player;
using Jx.Utils.ChangeableObjects;
using Mirror;

namespace SceneLogics.GameplayScene.Context
{
    public interface ISpectatingClientHandler
    {
        IJxChangeableObject<NetworkIdentity?> SpectatingIdentity { get; }
        IJxChangeableObject<NetGamePlayer?> SpectatingPlayer { get; }

        void SpectateMainPlayer();
        void UpdateSpectatedObject(NetworkIdentity identity);
    }
}
