using System;
using System.Linq;
using System.Net;
using Configs.Entities;
using Core.Helpers.SceneManagement;
using Core.Helpers.SceneManagement.TransitionParameters;
using Cysharp.Threading.Tasks;
using ExternalServices.Auth;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Authentication;
using GameplayNetworking.Manager.Network.Server.Runner.Configuration;
using GameplayNetworking.Share.Character;
using Jx.Utils.Ensurance;
using UnityEngine.Scripting;

namespace SceneLogics.GameplayScene.Client.ConnectionFactory
{
    public class TutorialMatchConnectionParametersFactory : IGameplayMatchConnectionParametersFactory
    {
        private readonly IAuthIntegration _authIntegration;
        private readonly IMonsterLandSceneManager _sceneManager;
        private readonly IGameServerHostConfigurationProvider _hostConfigurationProvider;

        [Preserve]
        public TutorialMatchConnectionParametersFactory(
            IAuthIntegration authIntegration,
            IMonsterLandSceneManager sceneManager,
            IGameServerHostConfigurationProvider hostConfigurationProvider
        )
        {
            _authIntegration = authIntegration;
            _sceneManager = sceneManager;
            _hostConfigurationProvider = hostConfigurationProvider;
        }

        public async UniTask<GameplayMatchConnectionParameters> CreateAsync()
        {
            var transitionParameters = _sceneManager.GetTransitionParameters<GameplaySceneTransitionParameters>();
            if (transitionParameters == null)
            {
                throw new InvalidOperationException("Transition parameters are not set");
            }

            var authRequest = await CreateAuthRequestAsync(transitionParameters);
            var connectionEndPoint = await GetConnectionEndPointAsync();

            return new GameplayMatchConnectionParameters(authRequest, connectionEndPoint);
        }

        private async UniTask<MonsterLandAuthRequestDto> CreateAuthRequestAsync(GameplaySceneTransitionParameters transitionParameters)
        {
            var userToken = await _authIntegration.TryGetTokenAsync(forceRefresh: true);

            var characterEntity = EntityRepository.Instance.GetCharacters(transitionParameters.Team).First(
                c => c.Index == CatcherType.Goliath.ToIndex() ||
                     c.Index == EscapeeType.Golem.ToIndex()
            );

            return new MonsterLandAuthRequestDto(
                userToken,
                transitionParameters.Team,
                new CharacterViewIdentifier
                {
                    Index = characterEntity.Index,
                    SkinEntityId = characterEntity.DefaultSkin.Id,
                },
                1,
                0,
                1,
                transitionParameters.TicketId,
                characterStats: null,
                equippedEmojis: null
            );
        }

        private async UniTask<IPEndPoint> GetConnectionEndPointAsync() => new(IPAddress.Loopback, (await _hostConfigurationProvider.GetAsync()).ListeningPort);
    }
}