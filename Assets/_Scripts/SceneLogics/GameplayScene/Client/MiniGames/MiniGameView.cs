using System;
using System.Collections.Generic;
using App.Analytics;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.MiniGames;
using ExternalServices.Telemetry;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using UnityEngine;

namespace GameplayNetworking.Gameplay.Minigames.Processors
{
    public abstract class MiniGameView<TConfig, TPlayer> : MonoBehaviour,
                                                           IClientMiniGameView
        where TConfig : MiniGameConfigPresentation
        where TPlayer : NetGamePlayer
    {
        private Action<GenericMiniGameResult> _sendResultToServer = null!;
        private IDisposable? _dataSubscription;

        public abstract MiniGameType MiniGameType { get; }
        
        protected TConfig Config { get; private set; } = null!;
        protected TPlayer Player { get; private set; } = null!;
        protected IJxLogger Logger { get; private set; } = null!;

        public virtual void Initialize(
            ILocalGameplayPlayer player,
            IServerGameplayConfig serverGameplayConfig,
            Action<GenericMiniGameResult> sendResultToServer
        )
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
            Player = player.Cast<TPlayer>();
            Config = serverGameplayConfig.GetMiniGameConfig<TConfig>(MiniGameType) ?? throw new NullReferenceException("Can't find config");
            _sendResultToServer = sendResultToServer;
        }

        public abstract void Launch();
        public abstract void Cleanup();

        protected void SendResultToServer(GenericMiniGameResult result)
        {
            TrackResult(result);
            _sendResultToServer(result);
        }

        private void TrackResult(GenericMiniGameResult result)
        {
            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.MiniGame,
                "result",
                new Dictionary<string, string>()
                {
                    [TelemetryEventParameters.Placement] = GetPlacement(MiniGameType),
                    [TelemetryEventParameters.Status] = result.ToString().ToLowerInvariant(),
                }
            );

            return;

            static string GetPlacement(MiniGameType type)
            {
                return type switch
                {
                    MiniGameType.Campfire => "campfire",
                    MiniGameType.Cage => "cage",
                    _ => "unknown"
                };
            }
        }
    }
}