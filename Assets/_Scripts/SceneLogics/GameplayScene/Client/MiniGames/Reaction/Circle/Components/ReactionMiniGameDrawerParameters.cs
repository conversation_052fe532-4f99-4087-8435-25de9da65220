using GameplayNetworking.Gameplay.Minigames.Processors.Reaction.Circle.Zones;

namespace GameplayNetworking.Gameplay.Minigames.Processors.Reaction.Circle
{
    public class ReactionMiniGameDrawerParameters
    {
        public ReactionMiniGameDrawerParameters(ZoneReactionParameters success, ZoneReactionParameters perfectSuccess)
        {
            Success = success;
            PerfectSuccess = perfectSuccess;
        }

        public ZoneReactionParameters Success { get; }
        public ZoneReactionParameters PerfectSuccess { get; }
    }
}