using System;
using System.Collections;
using App.Components.Contexts;
using App.Localization.Components;
using Configs.ServerGameplay.Presentation.MiniGames;
using Configs.ServerGameplay.Presentation.MiniGames.Concrete;
using Core.Extensions;
using Core.Helpers.TweenAnimation;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameplayNetworking.Gameplay.Minigames.Processors.Reaction.Circle.Zones;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.UnityComponentExtensions;
using SceneLogics.GameplayScene.Client.MinigameUI;
using UnityEngine;
using UnityEngine.UI;

namespace GameplayNetworking.Gameplay.Minigames.Processors.Reaction.Circle
{
    [RequireComponent(typeof(CircleReactionMiniGameAnimationController))]
    [RequireComponent(typeof(JxContextBoundMonoBehaviour))]
    public class CampfireMiniGameView : MiniGameView<CampfireMiniGameConfigPresentation, NetEscapeePlayer>,
                                        IJxLocalizationContext,
                                        ICampfireMiniGameTutorialHandler
    {
        private static readonly TimeSpan _setVisibleDuration = TimeSpan.FromSeconds(0.3f);
        private static readonly TimeSpan _delayBeforeHide = TimeSpan.FromSeconds(0.1f);

        private static readonly IMinigameAudioController _audioController = new DefaultMinigameAudioController();
        
        [SerializeField]
        private CircleReactionMiniGameDrawerComponent _drawer = null!;

        [SerializeField]
        private MiniGameAnnouncerComponent _announcer = null!;

        [SerializeField]
        private Button _clickArea = null!;

        [SerializeField]
        private CircleReactionMiniGameAnimationController _animationController = null!;

        [SerializeField]
        private MiniGameResultStatusTextView _resultText = null!;

        [SerializeField]
        private RectTransform _pointerRectTransform = null!;
        
        [SerializeField]
        private ParticleSystem _perfectSuccessEffect = null!;
        
        private readonly JxChangeableObject<bool, bool> _isShown = new JxChangeableObject<bool, bool>();
        private readonly IAnimationSlot _appearanceAnimationSlot = new AnimationSlot("appearance");

        private IDisposable? _playCancellation;
        private IDisposable? _clickAreaSubscription;
        private IDisposable? _moveCancellation;

        public override MiniGameType MiniGameType { get; } = MiniGameType.Campfire;

        #region LOCALIZATION

        bool IJxLocalizationContext.IsRoot { get; } = true;
        string IJxLocalizationContext.Key { get; } = "Minigame";
        string IJxLocalizationContext.DefaultText { get; } = string.Empty;

        #endregion
        
        #region TUTORIAL
        
        void ICampfireMiniGameTutorialHandler.SetInteractable(bool interactable) => _clickArea.interactable = interactable;
        bool ICampfireMiniGameTutorialHandler.IsPerfectZoneMiddle() => _drawer.IsPointerInTheMiddlePerfect();

        #endregion

        private void OnEnable()
        {
            _clickAreaSubscription = _clickArea.OnClick(OnClick);
        }

        private void OnDisable()
        {
            _clickAreaSubscription?.Dispose();
        }

        private void OnDestroy()
        {
            _appearanceAnimationSlot?.Destroy();
        }

        public override void Launch()
        {
            _playCancellation?.Dispose();
            _playCancellation = StartCoroutine(LaunchRoutine()).BuildCancellation(this);
        }

        public override void Cleanup()
        {
            CleanupInternal();
            DoHide();
            _drawer.Cleanup();
        }

        private IEnumerator LaunchRoutine()
        {
            Announce();
            yield return new WaitForSeconds((float)Config.AnnounceDuration.TotalSeconds);
            
            Prepare();
            _moveCancellation?.Dispose();
            _moveCancellation = StartCoroutine(MoveRoutine()).BuildCancellation(this);
        }

        private void Announce()
        {
            _announcer.gameObject.SetActive(true);
            _announcer.SetPrepareState();
        }

        private void Prepare()
        {
            _audioController.PlayAppear();
            _isShown.Set(true);
            _drawer.gameObject.SetActive(true);
            _clickArea.gameObject.SetActive(true);
            
            _appearanceAnimationSlot.PlayNew(
                _animationController.DoSetVisibleMinigame(true, setVisibleDuration: TimeSpan.FromSeconds(0.2), delayDuration: TimeSpan.Zero)
            );
            
            _announcer.SetInProgressState();
            _drawer.Render(BuildZoneParameters());
        }

        private IEnumerator MoveRoutine()
        {
            var pointerPosition = 0f;
            var tick = UniTask.Yield(PlayerLoopTiming.Update);
            var speed = Config.Speed;
            
            while (true)
            {
                _drawer.SetPointerPosition(pointerPosition);
                pointerPosition += speed * Time.deltaTime;

                var acceleration = Config.Acceleration;
                if (acceleration > 0f)
                    speed += acceleration;
                
                yield return tick;
            }
        }

        private void CleanupInternal()
        {
            _playCancellation?.Dispose();
            _announcer.Hide();
            _clickArea.gameObject.SetActive(false);
            _appearanceAnimationSlot.CompleteAndStop();
        }

        private void OnClick()
        {
            var result = CalculateResult();
            _moveCancellation?.Dispose();
            _animationController.DoPunchPointer();
            ShowResult(result);
            SendResultToServer(result);
        }

        private GenericMiniGameResult CalculateResult()
        {
            if (_drawer.IsPointerInsidePerfect())
                return GenericMiniGameResult.PerfectSuccess;

            if (_drawer.IsPointerInsideSuccess())
                return GenericMiniGameResult.Success;

            return GenericMiniGameResult.Fail;
        }

        private ReactionMiniGameDrawerParameters BuildZoneParameters()
        {
            var success = ZoneReactionParameters.BuildRandomSuccess(
                Config.SuccessZoneSize01,
                Config.SuccessZoneOffset01
            );

            var perfect = ZoneReactionParameters.BuildRandomPerfect(
                success,
                Config.PerfectSuccessZoneSize01,
                Config.PerfectSuccessZoneOffset01
            );

            return new ReactionMiniGameDrawerParameters(success: success, perfectSuccess: perfect);
        }

        private void ShowResult(GenericMiniGameResult result)
        {
            _audioController.PlayResult(result);
            _resultText.gameObject.SetActive(true);
            _resultText.SetResult(result, _pointerRectTransform.position);
            
            if (result == GenericMiniGameResult.PerfectSuccess)
            {
                _perfectSuccessEffect.transform.position = _pointerRectTransform.position;
                _perfectSuccessEffect.Stop();
                _perfectSuccessEffect.Play();
            }
        }

        private void DoHide()
        {
            _appearanceAnimationSlot.PlayNew(
                _animationController
                    .DoSetVisibleMinigame(false, setVisibleDuration: _setVisibleDuration, delayDuration: _delayBeforeHide)
                    .OnComplete(CleanupInternal)
                    .OnKill(CleanupInternal)
            );

            _isShown.Set(false);
            _announcer.Hide();
        }
    }
}