using System.Collections.Generic;
using Configs.Perks.Model.Settings;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using SceneLogics.GameplayScene.Abilities;

namespace SceneLogics.GameplayScene.Perks.ConcreteControllers
{
    public abstract class UniquePerkController<TPlayer, TSettings, TAbilityController> : PerkController<TPlayer, TSettings>
        where TPlayer : NetGamePlayer
        where TSettings : PerkSettings
        where TAbilityController : class, IAbilityController
    {
        protected abstract int CharacterIndex { get; }
        protected TAbilityController Ability { get; private set; }

        protected sealed override void OnStart()
        {
            var characterIndex = Player.BaseSyncData.PublicInfo.Value.CharacterIdentifier.Index;

            if (characterIndex == CharacterIndex)
            {
                if (Player.Ability.Controller.TryCast<TAbilityController>(out var abilityController))
                {
                    Ability = abilityController;
                }
            }
            else
            {
                Logger.LogError(
                    "CharacterIndex mismatch",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Expected"] = CharacterIndex,
                        ["Actual"] = characterIndex,
                    }
                );
            }
            
            OnServerStartedInternal();
        }

        // todo: better name?
        protected abstract void OnServerStartedInternal();
    }
}