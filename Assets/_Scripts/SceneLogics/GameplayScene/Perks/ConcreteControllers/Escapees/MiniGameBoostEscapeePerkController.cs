using System;
using Configs.Perks.Model.Settings.Concrete.Escapee;
using GameplayNetworking.Gameplay.Components.Server.Interaction;
using GameplayNetworking.Gameplay.Components.Server.Interaction.Implementation.LightCampfire;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using Jx.Utils.ChangeableObjects;

namespace SceneLogics.GameplayScene.Perks.ConcreteControllers.Escapees
{
    /*
     * Ускорение прогресса розжига костра при попадании в зеленую зону мини-игры.
     *
     * Перк усиляет базовое ускорение прогресса при успешной мини игре.
     */
    public class MiniGameBoostEscapeePerkController : PerkController<NetEscapeePlayer, MiniGameBoostEscapeePerkSettings>
    {
        private static readonly TimeSpan _activeDuration = TimeSpan.FromSeconds(2);
        
        protected override void OnStart()
        {
            DisposeOnServerStop(Player.BaseSyncData.MiniGame.SubscribeAndFire(OnMiniGameDataChange));
        }

        private void OnMiniGameDataChange(NetPlayerMiniGameDto data)
        {
            if (data.IsRunning || !Player.Interaction.IsInteracting(InteractionType.LightCampfire))
                return;
            
            if (data.Result != GenericMiniGameResult.PerfectSuccess)
                return;

            var interaction = FindNearestInteraction<LightCampfireInteraction>(Player.NetTransform.UnityTransform);
            if (interaction != null)
            {
                interaction.ModifyProgressOnDelta(Settings.LightCampfireBoost, interaction.GetInteractionConfig().ProgressBuffDuration);
                SetIsActiveTemporary(_activeDuration);
            }
        }
    }
}