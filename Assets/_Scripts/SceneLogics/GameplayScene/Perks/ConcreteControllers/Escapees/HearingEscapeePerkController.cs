using Configs.Perks.Model.Settings.Concrete.Escapee;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Logging;

namespace SceneLogics.GameplayScene.Perks.ConcreteControllers.Escapees
{
    // TODO: FIX!
    /*
     * Усиление уха
     */
    public class HearingEscapeePerkController : PerkController<NetEscapeePlayer, HearingEscapeePerkSettings>
    {
        protected override void OnStart()
        {
            Logger.LogError($"Not implemented");
        }
    }
}