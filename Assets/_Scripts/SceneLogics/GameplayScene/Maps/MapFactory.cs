using System;
using Core.Managers.AssetLoader;
using GameplayNetworking.Gameplay.Components.Server.Map;
using Jx.Utils.Logging;
using MonsterLand.Matchmaking.Dto.Maps;
using Object = UnityEngine.Object;

namespace SceneLogics.GameplayScene.Maps
{
    public class MapFactory
    {
        private const string Path = "maps";
        private const JxMonsterLandMap FallbackMap = JxMonsterLandMap.SmallDarkIsland1;
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(MapFactory));
        
        public static MapFactory Instance { get; } = new MapFactory();

        private MapFactory() { }

        public Map Create(JxMonsterLandMap mapType)
        {
            var mapPrefab = InstantiateMap(mapType);
            if (mapPrefab == null)
            {
                _logger.LogError($"Can't find map prefab {mapType}. Loaded FALLBACK!");
                mapPrefab = InstantiateMap(FallbackMap);
            }

            if (mapPrefab == null)
                throw new InvalidOperationException($"Can't find map prefab `{mapType}`");
            
            return Object.Instantiate(mapPrefab);
        }

        private static Map? InstantiateMap(JxMonsterLandMap mapType) => JxResourceLoader.Instance.LoadPrefab<Map>(GetMapPath(mapType));
        private static string GetMapPath(JxMonsterLandMap mapType) => $"{Path}/{mapType}/{mapType}";
    }
}