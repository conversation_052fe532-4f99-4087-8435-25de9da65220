using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Components.Server.Map;
using MonsterLand.Matchmaking.Dto.Maps;
using Tutorial.Managers.Gameplay;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;

namespace SceneLogics.GameplayScene.Maps
{
    public class MapSpawner : IMapSpawner
    {
        private readonly IDictionary<JxMonsterLandMap, IMapInfo> _mapByInfo;
        
        // TODO: temp, we can't get tutorial type via anything else, but a real match doesn't have this injection -> tutor refactor is needed
        private readonly GameplaySchemeType _schemeType;

        [Preserve]
        public MapSpawner([InjectOptional] GameplaySchemeType schemeType)
        {
            _mapByInfo = new Dictionary<JxMonsterLandMap, IMapInfo>();
            _schemeType = schemeType;
        }

        public UniTask<IMapInfo> GetOrSpawnAsync(JxMonsterLandMap map)
        {
            if (_mapByInfo.TryGetValue(map, out var info))
                return UniTask.FromResult(info);
            
            var mapObject = MapFactory.Instance.Create(map);
            mapObject.transform.position = GetMapOffset(map);
            
            mapObject.gameObject.SetActive(true);
            
            _mapByInfo.Add(map, mapObject);
            mapObject.ApplyCustomizations();
            return UniTask.FromResult<IMapInfo>(mapObject);
        }

        // TODO: temp
        public async UniTask<IMapInfo> GetTutorialAsync()
        {
            await UniTask.WaitWhile(() => EscapeeTutorialMap.Instance == null && CatcherTutorialMap.Instance == null);
            Map map = _schemeType == GameplaySchemeType.Escapee ? EscapeeTutorialMap.Instance : CatcherTutorialMap.Instance;
            map.ApplyCustomizations();
            return map;
        }

        private static Vector3 GetMapOffset(JxMonsterLandMap map) => new Vector3(1f, 0f, 1f) * (150f * (int)map);
    }
}