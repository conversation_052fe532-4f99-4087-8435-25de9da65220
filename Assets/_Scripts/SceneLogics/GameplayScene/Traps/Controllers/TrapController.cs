using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Configs.ServerGameplay;
using Configs.ServerGameplay.Presentation.Traps;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics;
using GameplayNetworking.Gameplay.Inventory;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using Jx.Utils.Coroutines;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using UnityEngine;

namespace SceneLogics.GameplayScene.Traps.Controllers
{
    public abstract class TrapController<TConfig>  : ITrapController
        where TConfig : TrapConfigPresentation
    {
        private const float TickDurationInSeconds = 0.15f;
        private const float DelayBeforeDisposeInSeconds = 2f;

        private DynamicObjectHolder<NetTrap> _trap = null!;
        private IGameplayServerContext _serverContext = null!;
        private IServerGameplayConfig _gameplayConfig = null!;
        
        private IDisposable? _tickCancellation;

        protected TrapController()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        protected IJxLogger Logger { get; }

        public void Initialize(
            DynamicObjectHolder<NetTrap> trap,
            IGameplayServerContext serverContext,
            IServerGameplayConfig gameplayConfig,
            NetGamePlayer player)
        {
            _trap = trap;
            _serverContext = serverContext;
            _gameplayConfig = gameplayConfig;
            Modifier = player.Inventory.TrapModifier!.Get(trap.Item.TrapType);
            Config = _gameplayConfig.TrapsConfig.Get(_trap.Item.TrapType).Cast<TConfig>();
            Player = player;
            
#if DEBUG_GAMEPLAY_ANALYTICS
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(Player.netId,
                $"{_trap.Item.TrapType} radius",
                () => $"{GetRadius():0.00}");
#endif

            OnInitialized();
        }
        
        protected TrapModifier Modifier { get; private set; } = null!;
        protected TConfig Config { get; private set; } = null!;
        protected NetGamePlayer Player { get; private set; } = null!;
        
        protected virtual void OnInitialized(){}

        public void Launch()
        {
            if (_trap?.Item.IsDestroyed() ?? true)
                return;
            
            _tickCancellation?.Dispose();
            _tickCancellation = _trap.Item.StartCoroutine(TickRoutine()).BuildCancellation(_trap.Item);
        }

        public void Stop()
        {
            _tickCancellation?.Dispose();
        }

        protected abstract bool FilterTrapTarget(NetGamePlayer player);

        protected abstract void OnTargetCatch(NetGamePlayer player);

        private float GetRadius() => Config.Radius * Modifier.RadiusModifier;
        private float GetSqrRadius() => Mathf.Pow(GetRadius(), 2);

        private IEnumerator TickRoutine()
        {
            var installDuration = Config.InstallDuration;

            // wait for dynamic game object spawned -> else client rpc warning
            yield return null;
            var installInMills = (float)installDuration.TotalSeconds;
            
            _trap.Item.ClientRpc_Install(GetRadius(), installInMills);
            yield return new WaitForSeconds(installInMills);

            var activationDelay = new WaitForSeconds((float)Config.ActivationDelay.TotalSeconds);
            var tickDuration = new WaitForSeconds(TickDurationInSeconds);
            var players = _serverContext.Spawned.Players;
            
            while (!players.Any(IsEntered))
                yield return tickDuration;

            _trap.Item.ClientRpc_Activate();
            yield return activationDelay;

            // enemies could escape from the radius during activation -> search who's still in the radius and debuff them
            var enemies = players.Where(IsEntered);
            yield return ExplosionRoutine(enemies);
        }

        private IEnumerator ExplosionRoutine(IEnumerable<NetGamePlayer> targets)
        {
            foreach (var player in targets)
                OnTargetCatch(player);
            
            _trap.Item.ClientRpc_Explode();

            yield return new WaitForSeconds(DelayBeforeDisposeInSeconds);
            _trap.Dispose();
        }

        private bool IsEntered(NetGamePlayer player)
        {
            if (!player.BaseSyncData.Action.Value.IsPlaying())
                return false;

            if (!FilterTrapTarget(player))
                return false;

            if ((player.NetTransform.Position - _trap.Item.transform.position).sqrMagnitude > GetSqrRadius())
                return false;

            return true;
        }
    }
}