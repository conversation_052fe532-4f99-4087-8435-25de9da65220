using Core.Extensions;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using UnityEngine;

namespace SceneLogics.GameplayScene.Traps
{
    public class TrapAnimationController
    {
        private const float ShowDuration = 0.5f;
        private const float HideDuration = 0.5f;
        
        private readonly Transform _model;
        private readonly ParticleSystem _trapProgress;
        private readonly Vector3 _localScaleOnStart;
        
        private readonly IAnimationSlot _setSizeAnimationSlot = new AnimationSlot("size");
        private readonly IAnimationSlot _installAnimationSlot = new AnimationSlot("install");
        private readonly IAnimationSlot _setVisibleProgressBarAnimationSlot = new AnimationSlot("set-visible-progress-bar");

        public TrapAnimationController(ParticleSystem trapProgress, Transform model)
        {
            _model = model;
            _trapProgress = trapProgress;
            _localScaleOnStart = _model.localScale;
        }

        public void Destroy()
        {
            _setSizeAnimationSlot.Destroy();
            _installAnimationSlot.Destroy();
            _setVisibleProgressBarAnimationSlot.Destroy();
        }

        public void Clear()
        {
            _setSizeAnimationSlot.CompleteAndStop();
            _installAnimationSlot.CompleteAndStop();
            _setVisibleProgressBarAnimationSlot.CompleteAndStop();
            
            _model.localScale = _localScaleOnStart;
        }

        public void Init()
        {
            _model.localScale = Vector3.zero;
            _trapProgress.gameObject.SetActive(false);
        }
        
        public Tween DOInstall(float durationInSeconds, float radius)
        {
            return _installAnimationSlot.PlayNew(DOTween.Sequence()
                    .Join(DOSetVisibleProgressBarInternal(durationInSeconds, true))
                    .Join(DOSetSizeInternal(Vector3.zero, _localScaleOnStart * radius, ShowDuration))
                )
                .OnComplete(() => DOSetVisibleProgressBarInternal(durationInSeconds, false));
        }
        
        public Tween DOActivate()
        {
            _installAnimationSlot.CompleteAndStop();
            _setVisibleProgressBarAnimationSlot.CompleteAndStop();
            
            return DOSetSizeInternal(_model.transform.localScale, Vector3.zero, HideDuration);
        }
        
        private Tween DOSetVisibleProgressBarInternal(float durationInSeconds, bool isVisible)
        {
            var sequence = DOTween.Sequence();
            if (!isVisible)
                sequence.AppendInterval(durationInSeconds);
            sequence.AppendCallback(() => _trapProgress.gameObject.SetActive(isVisible));
            return _setVisibleProgressBarAnimationSlot.PlayNew(sequence);
        }
        
        private Tween DOSetSizeInternal(Vector3 scaleOnStart, Vector3 scale, float duration)
        {
            _setSizeAnimationSlot.CompleteAndStop();
            _model.localScale = scaleOnStart;
            return _setSizeAnimationSlot.PlayNew(_model.DOScale(scale, duration));
        }
    }
}