using System;
using System.Collections.Generic;
using GameplayNetworking.Share.Character;
using SceneLogics.GameplayScene.Abilities.Impl;
using SceneLogics.GameplayScene.Abilities.Impl.Base.ObjectSpawn;

namespace SceneLogics.GameplayScene.Abilities
{
    public class AbilitiesMapper
    {
        public static AbilitiesMapper Instance { get; } = new AbilitiesMapper();

        private AbilitiesMapper() { }

        private static readonly IDictionary<EscapeeType, AbilityInfo> _abilityTypeByEscapeeType = new Dictionary<EscapeeType, AbilityInfo>
        {
            [EscapeeType.Rab] = new(typeof(RollAbilityController), AbilityInputType.Button, AbilityType.Roll),
            [EscapeeType.Sup] = new(typeof(AngelHelpAbilityController), AbilityInputType.Button, AbilityType.AngelHelp),
            [EscapeeType.Spidi] = new(typeof(SpiderWebAbilityController), AbilityInputType.Direction, AbilityType.SpiderWeb),
            [EscapeeType.Golem] = new(typeof(ThrowRockAbilityController), AbilityInputType.Direction, AbilityType.ThrowRock),
            [EscapeeType.Chamie] = new(typeof(InvisibilityAbilityController), AbilityInputType.Button, AbilityType.Invisibility),
            [EscapeeType.Pango] = new(typeof(TeleportAbilityController), AbilityInputType.Direction, AbilityType.Teleport),
            [EscapeeType.Gor] = new(typeof(KnockingDashAbilityController), AbilityInputType.Button, AbilityType.KnockingDash),
            [EscapeeType.Fay] = new(typeof(FayWallAbilityController), AbilityInputType.Direction, AbilityType.FayWall),
            [EscapeeType.Electra] = new(typeof(ElectraFieldAbilityController), AbilityInputType.Button, AbilityType.ElectraField),
            [EscapeeType.Lucky] = new(typeof(LuckyBagAbilityController), AbilityInputType.Button, AbilityType.LuckyBag),
            [EscapeeType.Marty] = new(typeof(SmokeCloudAbilityController), AbilityInputType.Button, AbilityType.SmokeCloud),
            [EscapeeType.Robby] = new(typeof(TimeLapsAbilityController), AbilityInputType.Button, AbilityType.TimeLaps),
            [EscapeeType.Liz] = new(typeof(LazyRegenerationAbilityController), AbilityInputType.Button, AbilityType.LazyRegeneration),
            [EscapeeType.Trickie] = new(typeof(MicroDashAbilityController), AbilityInputType.Direction, AbilityType.MicroDash),
        };

        private static readonly IDictionary<CatcherType, AbilityInfo> _abilityTypeByCatcherType = new Dictionary<CatcherType, AbilityInfo>
        {
            [CatcherType.Ripp] = new(typeof(HookAbilityController), AbilityInputType.Direction, AbilityType.Hook),
            [CatcherType.Spec] = new(typeof(EscapeeMimicryAbilityController), AbilityInputType.Button, AbilityType.EscapeeMimicry),
            [CatcherType.Echo] = new(typeof(EchoLocationAbilityController), AbilityInputType.Button, AbilityType.EchoLocation),
            [CatcherType.Goliath] = new(typeof(JumpShakeAbilityController), AbilityInputType.Button, AbilityType.JumpShake),
            [CatcherType.Wiz] = new(typeof(FireballAbilityController), AbilityInputType.Direction, AbilityType.Fireball),
            [CatcherType.Phantom] = new (typeof(FearAbilityController), AbilityInputType.Button, AbilityType.Fear),
            [CatcherType.Witch] = new (typeof(TeleportationInMarkAbilityController), AbilityInputType.Button, AbilityType.TeleportInMark),
        };

        public AbilityInfo GetAbilityInfo(int characterIndex)
        {
            if (CharacterTypeHelper.IsCatcher(characterIndex))
                return _abilityTypeByCatcherType[CharacterTypeHelper.GetCatcherTypeFromIndex(characterIndex)];

            if (CharacterTypeHelper.IsEscapee(characterIndex))
                return _abilityTypeByEscapeeType[CharacterTypeHelper.GetEscapeeTypeFromIndex(characterIndex)];

            throw new ArgumentException($"Invalid character concrete index: {characterIndex}");
        }
    }
}