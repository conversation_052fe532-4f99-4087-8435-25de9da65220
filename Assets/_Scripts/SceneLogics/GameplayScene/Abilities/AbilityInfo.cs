using System;

namespace SceneLogics.GameplayScene.Abilities
{
    public struct AbilityInfo
    {
        public Type ControllerType;
        public AbilityInputType InputType;
        public AbilityType Type;

        public AbilityInfo(Type controllerType, AbilityInputType inputType, AbilityType type)
        {
            ControllerType = controllerType;
            InputType = inputType;
            Type = type;
        }
    }
}