using Audio.Manager;
using Audio.Provider;
using GameplayNetworking.Gameplay.Player;
using Mirror;
using SceneLogics.GameplayScene.Components.FlyingTexts;
using UnityEngine;
using Random = UnityEngine.Random;

namespace SceneLogics.GameplayScene.Abilities.Impl.Base.ProjectileControllers
{
    public class RockProjectile : DefaultProjectile
    {
        [SerializeField]
        private float _rotationSpeed = 200;
        
        [SerializeField]
        private ParticleSystem _explosionParticleSystem;

        [SerializeField]
        private GameObject _modelObject;
        
        private void OnEnable()
        {
            _modelObject.SetActive(true);
            
            // rotate random
            if (NetworkServer.active)
                _modelObject.transform.Rotate(Random.insideUnitSphere * Random.Range(0, 180));
        }

        private void Update()
        {
            if (NetworkServer.active)
                _modelObject.transform.Rotate(transform.forward, Time.deltaTime * _rotationSpeed);
        }

        [ClientRpc]
        public void ClientRpc_OnLifeEnd() => Client_OnLineEndInternal();

        [ClientRpc]
        public void ClientRpc_OnTouched(NetGamePlayer touchedPlayer)
        {
            FlyingTextClientManager.ShowAndForget(FlyingTextType.Hit, touchedPlayer.transform.position);
            Client_OnLineEndInternal();
        }

        [Client]
        private void Client_OnLineEndInternal()
        {
            _modelObject.SetActive(false);
            _explosionParticleSystem.Play();
            JxAudioManager.Instance.Sounds.PlayOneShot(AudioLibrary.Instance.Sounds.Gameplay.Characters.Abilities.Completion.Golem, ClientTransform.position);
        }
    }
}