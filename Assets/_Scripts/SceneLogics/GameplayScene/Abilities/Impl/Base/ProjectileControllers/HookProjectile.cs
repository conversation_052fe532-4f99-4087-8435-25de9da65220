using System;
using Audio.Manager;
using Audio.Provider;
using Core.Extensions;
using Core.Helpers;
using Core.Helpers.Serializable;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting;
using Jx.Utils.Ensurance;
using Jx.Utils.Logging;
using Mirror;
using SceneLogics.GameplayScene.Client.Vfx;
using UnityEngine;
using Zenject;

namespace SceneLogics.GameplayScene.Abilities.Impl.Base.ProjectileControllers
{
    public class HookProjectile : DefaultProjectile
    {
        #region SHARED

        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(HookProjectile));

        [field: SerializeField]
        public ParentComponent ParentComponent { get; private set; } = null!;
            
        private void OnEnable()
        {
            if (isClient)
            {
                CleanupTweens();
                _shakeTween = transform.DOShakeRotation(duration: 5f, Vector3.forward * 30f).SetLoops(loops: -1);
            }
        }

        private void OnDisable()
        {
            if (isClient)
            {
                CleanupTweens();
                _scaleAnimationSlot.CompleteAndStop();
            }
        }

        #endregion

        // todo: адекватную архитектуру под это
        #region Client
        
        [SerializeField]
        private MeshRenderer _meshRenderer = null!;

        [SerializeField]
        private MeshFilter _meshFilter = null!;

        [SerializeField]
        private SerializableDictionary<string, HookModelSettings> _meshWithMaterial = null!;

        [SerializeField]
        private Transform _modelTransform;

        private readonly IAnimationSlot _scaleAnimationSlot = new AnimationSlot("scale");
        private Tween? _shakeTween;
        
        private string? _lastSetSkinEntityId;
        private HookModelSettings? _lastModelSettings;

        public override void OnStartClient()
        {
            base.OnStartClient();
            Ensure.NotNull(_meshWithMaterial, "Model settings is not provided");
        }

        public override void OnStopClient()
        {
            base.OnStopClient();
            
            _scaleAnimationSlot.CompleteAndStop();
        }
        
        private void CleanupTweens()
        {
            _shakeTween?.CompletedLoops();
            _shakeTween?.KillSafe();
        }

        [ClientRpc]
        public void ClientRpc_OnTouchedTarget(NetEscapeePlayer target)
        {
            if (!_lastModelSettings.IsNullObj())
            {
                _scaleAnimationSlot.PlayNew(_modelTransform.DOScale(_lastModelSettings!.GetScale() * 0.6f, duration: 0.2f));
            }

            ClientVfxManager.ShowAndForget(VfxBuilder.Create(ParticleType.DustExplosion).SetOwner(target).Build());
            JxAudioManager.Instance.Sounds.PlayOneShot(AudioLibrary.Instance.Sounds.Gameplay.Characters.Abilities.Completion.Ripp, target.transform);
        }

        [ClientRpc]
        public void ClientRpc_SetSkin(string skinEntityId)
        {
            if (_lastSetSkinEntityId == skinEntityId)
                return;

            _lastSetSkinEntityId = skinEntityId;
            
            if (!_meshWithMaterial.TryGetValue(skinEntityId, out var modelSettings))
            {
                _logger.LogError($"Unknown skin entityId '{skinEntityId}'");
                return;
            }
            
            modelSettings.ApplyTo(_meshFilter, _meshRenderer, _modelTransform);
        }

        #region CLASSES

        [Serializable]
        private class HookModelSettings
        {
            [SerializeField]
            private Mesh _mesh = null!;
            
            [SerializeField]
            private Material _material = null!;

            [SerializeField]
            private Vector3 _rotation;
            
            [SerializeField]
            private Vector3 _position;
            
            [SerializeField]
            private Vector3 _scale;

            public void ApplyTo(MeshFilter filter, MeshRenderer renderer, Transform transform)
            {
                filter.mesh = _mesh;
                renderer.material = _material;
                transform.localRotation = Quaternion.Euler(_rotation);
                transform.localPosition = _position;
                transform.localScale = _scale;
            }

            public Vector3 GetScale()
            {
                return _scale;
            }
        }

        #endregion

        #endregion
    }
}