using System;
using System.Threading;
using Configs.ServerGameplay.Presentation.Abilities;
using Core.Extensions;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Shared.Ability;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using SceneLogics.GameplayScene.Abilities.Impl.Modifications.Mimicry;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;

namespace SceneLogics.GameplayScene.Abilities.Impl
{
    public class EscapeeMimicryAbilityController : BaseAbilityController<IEmptyAbilityParameters, MimicryEscapeeAbilityConfigPresentation>
    {
        private CancellationTokenSource? _autoStopMimicryCts;
        private IDisposable? _mimicryBuff;

        private bool IsInMimicry => Context.Player.BaseSyncData.Mimicry.Value.IsTurned;

        protected override AbilityType AbilityType => AbilityType.EscapeeMimicry;

        public override void OnServerStop()
        {
            base.OnServerStop();

            CleanupState();
        }

        public float GetSqrRadius() => Mathf.Pow(Config.Radius, 2);

        protected override TimeSpan GetAbilityCooldown() => IsInMimicry ? GetTurnToMimicryCooldown() : base.GetAbilityCooldown();
        protected override TimeSpan GetPreparationDuration() => IsInMimicry ? TimeSpan.Zero : base.GetPreparationDuration();

        protected override UniTask ProcessAsync(IEmptyAbilityParameters parameters)
        {
            if (IsInMimicry)
            {
                CleanupState();
                return UniTask.CompletedTask;
            }
            
            // do not wait it to prevent reload stopping
            _autoStopMimicryCts = new CancellationTokenSource();
            TurnIntoVictimAsync(_autoStopMimicryCts.Token).Forget();
            
            return UniTask.CompletedTask;
        }

        private void TurnIntoMyself()
        {
            foreach (var mod in EnumerateModifications<IMimicryAbilityModification>())
                mod.OnTurnedIntoMyself();
            
            SendState(GenericClientAbilityStateType.AdditionalProcessingState0);
        }

        private async UniTask TurnIntoVictimAsync(CancellationToken ct)
        {
            foreach (var mod in EnumerateModifications<IMimicryAbilityModification>()) 
                mod.OnTurnedIntoVictim();
            
            SendState(GenericClientAbilityStateType.AdditionalProcessingState1);

            var duration = GetDuration();
            _mimicryBuff = Context.Player.Buff.Activate(BuffType.EscapeeMimicry, duration, onComplete: TurnIntoMyself);
            
            var cancelled = await UniTask
                .Delay(duration, cancellationToken: ct, delayType: DelayType.DeltaTime)
                .SuppressCancellationThrow();
            
            if (cancelled)
                return;
            if (!Context.Player.Buff.IsActive(BuffType.EscapeeMimicry))
                return;
            
            _mimicryBuff.Dispose();
        }

        private void CleanupState()
        {
            _mimicryBuff?.Dispose();
            
            _autoStopMimicryCts?.CancelAndDispose();
            _autoStopMimicryCts = null;
        }

        private TimeSpan GetTurnToMimicryCooldown() => Config.TurnToMyselfCooldown;
    }
}