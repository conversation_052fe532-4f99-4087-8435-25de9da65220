using System;
using Configs.NetGameObjects;
using Configs.ServerGameplay.Presentation.Abilities;
using Core.Helpers;
using GameplayNetworking.Gameplay.Player.Catcher;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Player.Data;
using SceneLogics.GameplayScene.Abilities.Helper;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using SceneLogics.GameplayScene.Abilities.Impl.Base.ProjectileControllers;
using SceneLogics.GameplayScene.Abilities.Impl.Modifications;

namespace SceneLogics.GameplayScene.Abilities.Impl
{
    public class ThrowRockAbilityController : ProjectileAbilityController<RockProjectile, NetCatcherPlayer, ThrowRockAbilityConfigPresentation>
    {
        protected override AbilityType AbilityType => AbilityType.ThrowRock;

        protected override DynamicObjectType ProjectileType => DynamicObjectType.Rock;

        // wait for explosion particles
        protected override float WaitBeforeProjectileDispose => 1f;
        protected override int CollisionLayerMask => LayerType.StaticObstacle.ToMask();
        protected override bool WaitProjectileLiveBeforeCooldown => false;

        protected override void OnProjectileSpawned(NetObjectHolder<RockProjectile> projectile)
        {
        }

        protected override bool CheckCanMoveBack()
        {
            return false;
        }

        protected override void OnProjectileTouchedTarget(NetObjectHolder<RockProjectile> projectile, NetCatcherPlayer touchedPlayer)
        {
            touchedPlayer.Buff.Activate(BuffType.StunnedByRock, GetStunDuration());
            projectile.Item.ClientRpc_OnTouched(touchedPlayer);
        }

        protected override bool CheckTargetIsTouchable(NetCatcherPlayer targetPlayer)
        {
            return base.CheckTargetIsTouchable(targetPlayer) &&
                   !targetPlayer.Immunity.IsImmune &&
                   !targetPlayer.BaseSyncData.Action.Value.HasPermission(ActionPermissions.IgnoreStun);
        }

        protected override void OnProjectileLiveEnd(NetObjectHolder<RockProjectile> projectile, bool stoppedByObstacle)
        {
            projectile.Item.ClientRpc_OnLifeEnd();
        }

        private TimeSpan GetStunDuration() => GetGenericModifiedProperty(
            GenericAbilityModifiedProperty.ThrowRockStunDuration,
            Config.StunDuration
        );
    }
}