using System;
using Core.Extensions;
using Jx.Utils.Logging;
using Mirror;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using UnityEngine;

namespace SceneLogics.GameplayScene.Abilities.Impl.Strategies
{
    public class ProjectileAbilityMovementForwardStrategy : ProjectileAbilityMovementStrategy
    {
        public ProjectileAbilityMovementForwardStrategy(IProjectileAbilityController controller)
            : base(controller)
        {
        }

        public override void OnProjectileLiveTick()
        {
            try
            {
                ProjectileTransform.Translate(Controller.GetSpeed() * Time.deltaTime * ProjectileTransform.forward, Space.World);
            }
            catch (Exception ex)
            {
#if UNITY_EDITOR
                JxLoggerFactory.CreateLogger(nameof(ProjectileAbilityMovementForwardStrategy)).LogEditorOnlyError(ex.Message, ex);
#endif
            }
        }
    }
}