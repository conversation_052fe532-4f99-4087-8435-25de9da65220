using System;
using System.Collections.Generic;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Movement.Modification;
using GameplayNetworking.Gameplay.Player.Data;
using Mirror;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using UnityEngine;

namespace SceneLogics.GameplayScene.Abilities.Impl.Modifications.Projectile
{
    public class MagnetFireballProjectileLifetimeAbilityModification : ProjectileLifetimeAbilityModification
    {
        private readonly float _sqrRadius;
        private readonly float _strength;
        private readonly IDictionary<uint, IDisposable> _magnetDisposableByPlayerNetworkId;

        private NetworkIdentity? _projectileNetworkIdentity;

        public MagnetFireballProjectileLifetimeAbilityModification(
            IProjectileAbilityController controller,
            float radius,
            float strength
        )
            : base(controller)
        {
            _sqrRadius = radius * radius;
            _strength = strength;
            _magnetDisposableByPlayerNetworkId = new Dictionary<uint, IDisposable>();
        }

        public override void OnProjectileLiveStart(Transform projectileTransform)
        {
            base.OnProjectileLiveStart(projectileTransform);

            _projectileNetworkIdentity = projectileTransform.GetComponent<NetworkIdentity>();
        }

        public override void OnProjectileLiveTick()
        {
            foreach (var escapee in Controller.Context.GameplayContext.Spawned.Escapees)
            {
                if (IsPlayerTarget(ProjectileTransform, escapee))
                {
                    StartMagneticAttraction(escapee);
                }
                else
                {
                    StopMagneticAttraction(escapee);
                }
            }
        }

        public override void OnProjectileLiveEnd()
        {
            foreach (var cancellation in _magnetDisposableByPlayerNetworkId.Values)
            {
                cancellation.Dispose();
            }
            _magnetDisposableByPlayerNetworkId.Clear();
            
            base.OnProjectileLiveEnd();
        }

        private void StartMagneticAttraction(NetGamePlayer player)
        {
            if (_magnetDisposableByPlayerNetworkId.ContainsKey(player.netId))
            {
                return;
            }
            
            var modificationCancellation = player.NetTransform.ApplyModification(
                new GenericMovementModification(
                    MovementModificationType.ForceField,
                    _projectileNetworkIdentity,
                    _strength,
                    _sqrRadius,
                    listenTargetTransformChange: true
                )
            );

            _magnetDisposableByPlayerNetworkId.Add(player.netId, modificationCancellation);
        }

        private void StopMagneticAttraction(NetGamePlayer player)
        {
            if (!_magnetDisposableByPlayerNetworkId.TryGetValue(player.netId, out var magnetCancellation))
            {
                return;
            }
            
            magnetCancellation.Dispose();
            _magnetDisposableByPlayerNetworkId.Remove(player.netId);
        }

        private bool IsPlayerTarget(Transform? magnetSource, NetEscapeePlayer player)
        {
            if (ReferenceEquals(magnetSource, null))
                return false;
            
            return player != null &&
                   player.netId != Controller.Context.Player.netId &&
                   player.BaseSyncData.Action.Value.IsInMatchAndActive() &&
                   Vector3.SqrMagnitude(player.NetTransform.Position - magnetSource.position) < _sqrRadius;
        }
    }
}