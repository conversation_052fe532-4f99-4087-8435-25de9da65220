using System;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using SceneLogics.GameplayScene.Abilities.Impl.Base;

namespace SceneLogics.GameplayScene.Abilities.Impl.Modifications.Aura
{
    public class AngelSleepAbilityModification : StunWithAuraAbilityModification
    {
        private readonly TimeSpan _duration;

        public AngelSleepAbilityModification(
            TimeSpan stunDuration,
            NetGamePlayer owner,
            IAuraAbilityController controller
        )
            : base(stunDuration, owner, controller)
        {
            _duration = stunDuration;
        }

        protected override void ProcessStun(NetGamePlayer player)
        {
            player.Buff.Activate(BuffType.AngelSleep, _duration);
        }
    }
}