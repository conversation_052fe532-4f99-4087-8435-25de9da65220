using System;
using GameplayNetworking.Gameplay.Player;

namespace SceneLogics.GameplayScene.Abilities.Impl.Modifications
{
    public abstract class ChanneledStunEveryTickAbilityModification : StunEveryTickAbilityModification, IChanneledAbilityModification
    {
        protected ChanneledStunEveryTickAbilityModification(
            TimeSpan stunDuration, 
            TimeSpan stunCooldown,
            NetGamePlayer owner) 
            : base(stunDuration, stunCooldown, owner)
        {
        }

        public void OnChannelingStarted()
        {
            StartStunning();
        }

        public void OnChannelingInterrupted()
        {
            StopStunning();
        }

        public void OnChannelingCompleted()
        {
            StopStunning();
        }
    }
}