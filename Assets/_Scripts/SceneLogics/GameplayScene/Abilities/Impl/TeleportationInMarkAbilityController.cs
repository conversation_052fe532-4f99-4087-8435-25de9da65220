using System;
using Configs.ServerGameplay.Presentation.Abilities;
using Core.Extensions.DiExtensions;
using Core.Helpers.DebugScreen.SubScreens.GameplayAnalytics;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Shared.Ability;
using Mirror;
using SceneLogics.GameplayScene.Abilities.Impl.Base;
using SceneLogics.GameplayScene.Abilities.Impl.Modifications;
using SceneLogics.GameplayScene.Abilities.Parameters;
using UnityEngine;

namespace SceneLogics.GameplayScene.Abilities.Impl
{
    public class TeleportationInMarkAbilityController : BaseAbilityController<IEmptyAbilityParameters, TeleportationInMarkAbilityConfigPresentation>
    {
        private IDisposable? _markCancellation;
        private Mark? _mark;

        private double? _setTimestamp;

        private bool IsMarkAlive
        {
            get
            {
                if (_mark == null)
                    return false;
                if (!_setTimestamp.HasValue)
                    return false;
                if (_setTimestamp.Value + GetMarkLifespan().TotalSeconds <= NetworkTime.time)
                    return false;
                return true;
            }
        }
        protected override AbilityType AbilityType => AbilityType.TeleportInMark;

        public override void OnServerStart()
        {
            base.OnServerStart();

#if DEBUG_GAMEPLAY_ANALYTICS
            DebugGameplayAnalyticsRepository.Instance.RegisterPlayerStat(
                Context.Player.netId,
                "Witch mark lifespan",
                () =>
                {
                    var total = $"{GetMarkLifespan().TotalSeconds:0.00}s";
                    if (!IsMarkAlive)
                        return total;

                    return $"{Math.Max(0.0, _setTimestamp.GetValueOrDefault() + GetMarkLifespan().TotalSeconds - NetworkTime.time):0.00}s/{total}";
                }
                );
#endif
        }

        public override void OnServerStop()
        {
            base.OnServerStop();

            _markCancellation?.Dispose();
            _setTimestamp = null;
            _mark = null;
        }

        protected override void OnPreparation()
        {
            base.OnPreparation();
            Context.Player.BaseSyncData.AbilityAnimationIndex.SetFromServer(IsMarkAlive ? (byte)1 : (byte)0);
        }

        protected override UniTask ProcessAsync(IEmptyAbilityParameters parameters)
        {
            _markCancellation?.Dispose();

            if (IsMarkAlive)
                HandleTeleportationEnd();
            else
                HandleTeleportationStart();

            return UniTask.CompletedTask;
        }

        protected override TimeSpan GetAbilityCooldown()
        {
            // this method invoked after the ability is started and now we have saved point
            if (IsMarkAlive)
                return Config.TeleportMarkCooldown;
            
            return base.GetAbilityCooldown();
        }

        private TimeSpan GetMarkLifespan() 
            => GetGenericModifiedProperty(GenericAbilityModifiedProperty.WitchMarkLifespan, Config.MarkLifespan);

        private void HandleTeleportationStart()
        {
            SendState(GenericClientAbilityStateType.AdditionalProcessingState0);

            _mark = new Mark(Context.Player);

            var info = Context.AbilitySceneObjectFactory.Create(
                Context.Player.MatchContext,
                AbilityType,
                GetMarkSpatialParameter(),
                Context.Player,
                GetMarkLifespan()
            );

            _markCancellation = info.DynamicObject;
            _setTimestamp = NetworkTime.time;
        }

        private void HandleTeleportationEnd()
        {
            SendState(GenericClientAbilityStateType.AdditionalProcessingState1);
            TeleportToMark(_mark!);

            _mark = null;
            _setTimestamp = null;
        }

        private ISpatialParameter GetMarkSpatialParameter() => new SpatialParameter(Context.Player.NetTransform.Position, Quaternion.identity, Vector3.one);

        private void TeleportToMark(Mark mark) =>
            Context.Player.NetTransform.ServerTeleport(mark.Position, mark.Rotation);

        #region CLASSES

        private class Mark
        {
            public Mark(NetGamePlayer player)
            {
                var transform = player.NetTransform.UnityTransform;
                Position = transform.position;
                Rotation = transform.rotation;
            }

            public Vector3 Position { get; }
            public Quaternion Rotation { get; }
        }

        #endregion
    }
}