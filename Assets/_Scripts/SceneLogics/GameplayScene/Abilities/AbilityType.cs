namespace SceneLogics.GameplayScene.Abilities
{
    public enum AbilityType : ushort
    {
        None = 0,
        EchoLocation = 1,
        <PERSON><PERSON> = 2,
        <PERSON> = 3,
        <PERSON> = 4,
        <PERSON><PERSON>Mimicry = 5,
        <PERSON> = 6,
        <PERSON><PERSON><PERSON><PERSON> = 7,
        <PERSON>hrowRock = 8,
        JumpShake = 9,
        <PERSON><PERSON><PERSON> = 10,
        Invisibility = 11,
        KnockingDash = 12,
        <PERSON><PERSON>all = 13,
        Teleport = 14,
        TimeLaps = 15,
        ElectraField = 16,
        LuckyBag = 17,
        SmokeCloud = 18,
        Fear = 19,
        TeleportInMark = 20,
        LazyRegeneration = 21,
        MicroDash = 22,
    }
}