using UnityEngine;

namespace SceneLogics.GameplayScene.Abilities.Parameters
{
    public static class AbilityParametersFactory
    {
        public static GenericAbilityInputParameters CreateNoTarget()
        {
            return new GenericAbilityInputParameters();
        }

        public static GenericAbilityInputParameters CreatePointTarget(Vector3 ownerPosition, Vector3 targetPosition)
        {
            return new GenericAbilityInputParameters(ownerPosition, targetPosition);
        }
    }
}