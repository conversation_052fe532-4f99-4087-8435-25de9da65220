using System.Threading.Tasks;
using Configs.ServerGameplay.Presentation.Items;
using GameplayNetworking.Gameplay.Player.Data;

namespace SceneLogics.GameplayScene.Items.Impl.Base
{
    public abstract class ChannelingItemController<TConfig> : BaseItemController<TConfig>
        where TConfig : BaseChannelingItemConfigPresentation
    {
        protected override async Task<bool> TryConsumeAsync()
        {
            await Player.Action.SetNewTemporaryAsync(ActionType.ItemConsumption, Config.Value.ApplicationDuration);

            if (Player.BaseSyncData.Action.Value != ActionType.ItemConsumption)
            {
                return false;
            }

            Consume();

            return true;
        }

        protected abstract void Consume();
    }
}