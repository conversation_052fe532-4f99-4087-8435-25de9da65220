using System;
using UnityEngine.Scripting;
using Zenject;

namespace SceneLogics.GameplayScene.Components.InteractionVisualization
{
    public class TestVisualizationController : ITestVisualizationController
    {
        private InteractionVisualization.Factory _interactionVisualizationFactory;

        [Preserve]
        public TestVisualizationController()
        { }

        [Inject]
        private void Inject(InteractionVisualization.Factory interactionVisualizationFactory)
        {
            _interactionVisualizationFactory = interactionVisualizationFactory;
        }

        public IDisposable Show(InteractionVisualizationParameters parameters)
        {
            return _interactionVisualizationFactory.Create(parameters);
        }
    }
}