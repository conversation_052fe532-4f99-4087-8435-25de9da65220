using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Coroutines;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using System;
using GameplayNetworking.Gameplay.Player.Escapee;
using Jx.Utils.ChangeableObjects;
using UnityEngine;
using UnityEngine.UI;

namespace SceneLogics.GameplayScene.Components.PlayersStateIndication
{
    public class EscapeeStateEliminationVisualizer : MonoBehaviour
    {
        private static readonly IJxLogger _logger
            = JxLoggerFactory.CreateLogger(nameof(EscapeeStateEliminationVisualizer));

        [SerializeField]
        private Image _progressBarImage = null!;

        [SerializeField]
        private Image _eliminationCharacterIcon = null!;

        [SerializeField]
        private Material _eliminationCharacterMaterial = null!;

        private NetEscapeePlayer? _boundedEscapee;

        private void Awake() => Restore();

        public void SetPlayer(NetEscapeePlayer player) => _boundedEscapee = player;
        public void SetCharacterIcon(Sprite icon) => _eliminationCharacterIcon.sprite = icon;

        public void RenderDeath(bool isDead)
        {
            Restore();
            _eliminationCharacterIcon.material = isDead ? _eliminationCharacterMaterial : null;
        }

        public IDisposable BeginEliminationProgress()
        {
            if (_boundedEscapee == null)
            {
                _logger.LogError("Bounded player is null");
                return JxDisposableAction.Empty;
            }

            return JxDisposableAction.Build()
                .AppendDispose(_boundedEscapee.SyncData.Elimination.SubscribeAndFire(OnDtoChange))
                .AppendCallback(Restore);
        }

        private void OnDtoChange(NetEscapeeEliminationDto dto)
        {
            var progress = Mathf.Clamp01(dto.Progress / 100f);
            _progressBarImage.fillAmount = progress;
        }

        private void Restore()
        {
            _progressBarImage.fillAmount = 0f;
        }
    }
}