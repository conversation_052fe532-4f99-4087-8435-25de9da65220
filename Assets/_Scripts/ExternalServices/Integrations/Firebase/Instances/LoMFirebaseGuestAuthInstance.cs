using System;
using Cysharp.Threading.Tasks;
using Firebase.Auth;

namespace ExternalServices.Integrations.Firebase.Instances
{
    public class LoMFirebaseGuestAuthInstance : LoMFirebaseBaseAuthInstance
    {
        public LoMFirebaseGuestAuthInstance(Func<UniTask<FirebaseAuth>> auth) : base(auth)
        { }
        
        protected override async UniTask<ILomFirebaseUser> AuthenticateAsync()
        {
            var authResult = await (await AuthProvider()).SignInAnonymouslyAsync();
            var user = authResult.User;
            if (user != null)
            {
                return new LomFirebaseUserWrapper(user);
            }

            return default;
        }
    }
}