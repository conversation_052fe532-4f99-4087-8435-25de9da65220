using System;
using Cysharp.Threading.Tasks;
using Firebase.Auth;
using Jx.Utils.Logging;

namespace ExternalServices.Integrations.Firebase.Instances
{
    public abstract class LoMFirebaseBaseAuthInstance
    {
        protected LoMFirebaseBaseAuthInstance(Func<UniTask<FirebaseAuth>> authProvider)
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
            AuthProvider = authProvider;
        }
     
        protected IJxLogger Logger { get; }
        protected Func<UniTask<FirebaseAuth>> AuthProvider { get; }

        public async UniTask<ILomFirebaseUser> TryAuthenticateAsync()
        {
            try
            {
                return await AuthenticateAsync();
            }
            catch (Exception exception)
            {
#if UNITY_EDITOR || !PRODUCTION_BUILD
                Logger.LogError($"Authentication failed with exception", exception);
#endif
                return default;
            }
        }

        protected abstract UniTask<ILomFirebaseUser> AuthenticateAsync();
    }
}