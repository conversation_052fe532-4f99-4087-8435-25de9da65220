using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using ExternalServices.Auth;
using ExternalServices.Integrations.GooglePlayGames;
using Firebase.Auth;
using GooglePlayGames.BasicApi;
using Jx.Utils.Logging;

namespace ExternalServices.Integrations.Firebase.Instances
{
    public class LoMFirebaseGPGAuthInstance : LoMFirebaseBaseAuthInstance, IFirebaseAuthLinkableInstance
    {
        private readonly IGooglePlayGamesPlatform _gpgPlatform;
        private readonly bool _silentMode;

        public LoMFirebaseGPGAuthInstance(
            Func<UniTask<FirebaseAuth>> auth,
            IGooglePlayGamesPlatform gpgPlatform,
            bool silentMode
        ) : base(auth)
        {
            _gpgPlatform = gpgPlatform;
            _silentMode = silentMode;
        }

        protected override async UniTask<ILomFirebaseUser> AuthenticateAsync()
        {
            var credential = await SignInGPGAsync();

            if (credential == null)
            {
                return null;
            }

            var user = await (await AuthProvider()).SignInWithCredentialAsync(credential).ConfigureAwait(false);
            if (user != null)
            {
                return new LomFirebaseUserWrapper(user);
            }

            return default;
        }
        
        public async UniTask<AuthLinkProviderStatus> LinkAsync()
        {
            try
            {
                var credential = await SignInGPGAsync();

                if (credential == null)
                    return AuthLinkProviderStatus.Failed;

                var user = await FirebaseAuth.DefaultInstance.CurrentUser.LinkWithCredentialAsync(credential);

                if (user == null)
                    return AuthLinkProviderStatus.Failed;

                return AuthLinkProviderStatus.Success;
            }
            catch (Exception exception)
            {
                Logger.LogError("Linking failed", exception);
            }

            return AuthLinkProviderStatus.Failed;
        }

        private async UniTask<Credential> SignInGPGAsync()
        {
            var authResult = await _gpgPlatform.AuthenticateAsync(_silentMode ? GooglePlayGamesAuthMode.Silent : GooglePlayGamesAuthMode.UI);

            if (authResult == null)
            {
                Logger.LogError("GPG sign in returns null result");
                return null;
            }

            if (authResult.Status == SignInStatus.Canceled ||
                authResult.Status == SignInStatus.UiSignInRequired)
            {
                return null;
            }

            if (authResult.Status != SignInStatus.Success)
            {
                Logger.LogError("Bad status", trackingFactory: () => new Dictionary<string, object>
                {
                    ["Status"] = authResult.Status,
                    ["Code"] = authResult.ServerAuthCode
                });

                return null;
            }

            return PlayGamesAuthProvider.GetCredential(authResult.ServerAuthCode);
        }
    }
}