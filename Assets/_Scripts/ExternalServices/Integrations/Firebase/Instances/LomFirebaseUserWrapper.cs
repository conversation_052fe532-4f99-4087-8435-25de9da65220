using System;
using Cysharp.Threading.Tasks;
using Firebase.Auth;
using Jx.Utils.Logging;

namespace ExternalServices.Integrations.Firebase.Instances
{
    public class LomFirebaseUserWrapper : ILomFirebaseUser
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(LomFirebaseUserWrapper));
        
        private readonly FirebaseUser _user;
        
        public LomFirebaseUserWrapper(FirebaseUser user)
        {
            _user = user;
        }

        public string DisplayName => _user.DisplayName;
        public bool IsAnonymous => _user.IsAnonymous;
        
        public async UniTask<string> TokenAsync()
        {
            while (true) 
            {
                try
                {
                    await UniTask.SwitchToMainThread();
                    var token = await _user.TokenAsync(true);
                    await UniTask.SwitchToMainThread();

                    return token;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex);
                }

                await UniTask.Delay(TimeSpan.FromSeconds(1f));
            }
        }
    }
}