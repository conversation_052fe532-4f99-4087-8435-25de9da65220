using System;
using Jx.User.Analytics.Api;
using UnityEngine.Scripting;

namespace ExternalServices.Integrations.Telemetry
{
    [Preserve]
    public class UserAnalyticsEngineModule : JxUserAnalyticsEngineModule
    {
        [Preserve]
        public UserAnalyticsEngineModule() { }

        protected override JxUserAnalyticsConfiguration GetConfiguration()
        {
            return new JxUserAnalyticsConfiguration
            {
                SendDelay = TimeSpan.FromSeconds(10),
                FastSendDelay = TimeSpan.FromSeconds(5),
                BackupDelay = TimeSpan.FromSeconds(5)
            };
        }
    }
}