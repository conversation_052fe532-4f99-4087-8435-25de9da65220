using GooglePlayGames.BasicApi;

namespace ExternalServices.Integrations.GooglePlayGames
{
    public class GooglePlayGamesAuthResult
    {
        public GooglePlayGamesAuthResult(SignInStatus status, string serverAuthCode)
        {
            Status = status;
            ServerAuthCode = serverAuthCode;
        }
        
        public SignInStatus Status { get; }
        public string ServerAuthCode { get; }
    }
}