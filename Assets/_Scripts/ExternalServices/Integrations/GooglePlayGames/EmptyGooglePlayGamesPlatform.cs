using Cysharp.Threading.Tasks;
using GooglePlayGames.BasicApi;
using UnityEngine.Scripting;

namespace ExternalServices.Integrations.GooglePlayGames
{
    public class EmptyGooglePlayGamesPlatform : IGooglePlayGamesPlatform
    {
        [Preserve]
        public EmptyGooglePlayGamesPlatform()
        { }
        
        public UniTask<GooglePlayGamesAuthResult> AuthenticateAsync(GooglePlayGamesAuthMode mode)
        {
            return new UniTask<GooglePlayGamesAuthResult>(new GooglePlayGamesAuthResult(SignInStatus.InternalError, ""));
        }
    }
}