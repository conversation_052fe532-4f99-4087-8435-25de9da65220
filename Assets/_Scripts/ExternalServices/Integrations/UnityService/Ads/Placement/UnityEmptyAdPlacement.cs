using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;

namespace ExternalServices.Integrations.UnityService.Ads.Placement
{
    public class UnityEmptyAdPlacement : IUnityAdPlacement
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(UnityEmptyAdPlacement));
        
        public static IUnityAdPlacement Instance { get; } = new UnityEmptyAdPlacement();
        
        private UnityEmptyAdPlacement()
        {
            IsAvailable = false.AsChangeable();
        }
        
        public IJxChangeableObject<bool> IsAvailable { get; }
        
        public UniTask<RewardedAdShowResult> ShowAsync(string placement)
        {
            _logger.LogDebug("Empty.ShowAsync", trackingFactory: () => new Dictionary<string, object>
            {
                ["Placement"] = placement,
            });
            return UniTask.FromResult(RewardedAdShowResult.NotAvailable);
        }
    }
}