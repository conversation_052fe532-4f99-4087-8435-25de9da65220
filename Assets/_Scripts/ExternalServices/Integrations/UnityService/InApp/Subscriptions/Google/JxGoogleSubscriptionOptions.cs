using System;
using System.Xml;
using UnityEngine.Purchasing;

namespace ExternalServices.Integrations.UnityService.InApp.Subscriptions.Google
{
    internal class JxGoogleSubscriptionOptions : IJxUnityPurchasingInAppSubscriptionOptions
    {
        private readonly GoogleProductMetadata _googleProductMetadata;

        public JxGoogleSubscriptionOptions(GoogleProductMetadata googleProductMetadata)
        {
            _googleProductMetadata = googleProductMetadata;
        }

        public object GetSourceTrackingData()
        {
            return _googleProductMetadata;
        }

        public JxInAppSubscriptionOptions Parse()
        {
            var period = ParsePeriod(_googleProductMetadata.subscriptionPeriod);
            var freeTrialPeriod = string.IsNullOrEmpty(_googleProductMetadata.freeTrialPeriod) ? null : ParsePeriod(_googleProductMetadata.freeTrialPeriod);

            return new JxInAppSubscriptionOptions(period, freeTrialPeriod);
        }

        /// ISO 8601 format
        private static JxInAppSubscriptionPeriod ParsePeriod(string period)
        {
            switch (period)
            {
                case "P1W":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Week, 1);

                case "P2W":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Week, 2);

                case "P3W":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Week, 3);

                case "P1M":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Month, 1);

                case "P3M":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Month, 3);

                case "P6M":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Month, 6);

                case "P1Y":
                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Year, 1);

                default:
                    var time = XmlConvert.ToTimeSpan(period);

                    return new JxInAppSubscriptionPeriod(JxInAppSubscriptionPeriodUnit.Day, (int)Math.Ceiling(time.TotalDays));
            }
        }
    }
}