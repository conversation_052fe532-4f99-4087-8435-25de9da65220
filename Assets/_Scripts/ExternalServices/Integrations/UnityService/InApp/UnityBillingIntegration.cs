using System;
using System.Collections.Generic;
using System.Threading;
using Api.Client.User.Billing;
using App.Analytics;
using Cysharp.Threading.Tasks;
using ExternalServices.InApp;
using ExternalServices.Telemetry;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils;
using Jx.Utils.Logging;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;
using UnityEngine.Scripting;

namespace ExternalServices.Integrations.UnityService.InApp
{
    public class UnityBillingIntegration : IBillingIntegration,
                                           IDetailedStoreListener,
                                           IJxInitializer
    {
        private readonly IJxLogger _logger;
        private readonly IPurchasingProductsProvider _productsProvider;
        private readonly IPurchaseHandler _purchaseHandler;
        private readonly UniTaskCompletionSource<bool> _initializationTaskCompletionSource;
        
        private bool _initialized;
        private IStoreController? _storeController;
        private IExtensionProvider? _extensionProvider;
        private UniTaskCompletionSource<bool>? _purchaseTaskCompletionSource;
        
        private readonly UnitySubscriptionHandler _subscriptionHandler;
        private readonly UnityInAppHandler _inAppHandler;

        [Preserve]
        public UnityBillingIntegration(
            IPurchasingProductsProvider productsProvider,
            IPurchaseHandler purchaseHandler
        )
        {
            _logger = JxLoggerFactory.CreateLogger(GetType().Name);
            _productsProvider = productsProvider ?? throw new ArgumentNullException(nameof(productsProvider));
            _purchaseHandler = purchaseHandler ?? throw new ArgumentNullException(nameof(purchaseHandler));

            _initialized = false;
            _initializationTaskCompletionSource = new UniTaskCompletionSource<bool>();
            
            _subscriptionHandler = new UnitySubscriptionHandler();
            _inAppHandler = new UnityInAppHandler();
        }

        public bool Initialized => _initialized;
        public IJxInitializer Initializer => this;

        public SubscriptionInfo GetSubscriptionInfo() => _subscriptionHandler.GetSubscriptionInfo();
        
        public async UniTask<bool> PurchaseAsync(string productId)
        {
            if (!CanInitiatePurchase(productId, out var error))
            {
                _logger.LogError($"Cannot initiate purchase: {error}");
                return false;
            }

            _purchaseTaskCompletionSource = new UniTaskCompletionSource<bool>();
            _storeController!.InitiatePurchase(productId);

            _logger.LogDebug($"Purchase initiated [{productId}]");

            var purchased = await _purchaseTaskCompletionSource.Task;
            _purchaseTaskCompletionSource = null;
            return purchased;
        }

        public string GetLocalizedPrice(string productId) => _storeController?.products?.WithID(productId)?.metadata?.localizedPriceString ?? "Nan";
        public bool ProductIsAvailable(string? productId) => _inAppHandler.IsProductAvailable(productId);

        private bool CanInitiatePurchase(string productId, out string error)
        {
            if (!_initialized)
            {
                error = "Billing not initialized";
                return false;
            }

            if (_purchaseTaskCompletionSource != null)
            {
                error = "Another purchase in progress";
                return false;
            }

            if (_storeController == null)
            {
                error = "Store controller is null";
                return false;
            }

            error = string.Empty;
            return true;
        }

        UniTask IJxInitializer.LoadAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;

        UniTask IJxInitializer.InitializeAsync(CancellationToken cancellationToken)
        {
            InitializeInternalAsync().Forget();
            return UniTask.CompletedTask;

            async UniTask InitializeInternalAsync()
            {
                try
                {
                    if (!JxUnityServicesCore.Instance.Initialized)
                        await UniTask.WaitUntil(() => JxUnityServicesCore.Instance.Initialized, cancellationToken: cancellationToken);

                    var products = _productsProvider.GetProducts();

                    await UniTask.SwitchToMainThread();

                    var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());
                    foreach (var product in products)
                        builder.AddProduct(product.ProductId, product.ProductType);

                    UnityPurchasing.Initialize(this, builder);

                    TrackInitialization();
                }
                catch (Exception ex)
                {
                    _logger.LogError("Initialization failed", ex);
                }
            }
        }

        UniTask IJxInitializer.ConfigureAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;
        UniTask IJxInitializer.OnConfiguredAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;
        UniTask IJxInitializer.ResetAsync(CancellationToken cancellationToken) => UniTask.CompletedTask;

        void IStoreListener.OnInitialized(IStoreController storeController, IExtensionProvider extensionProvider)
        {
            _logger.LogDebug("OnInitialized");

            _storeController = storeController;
            _extensionProvider = extensionProvider;
            
            _subscriptionHandler.Initialize(storeController);
            _inAppHandler.Initialize(storeController);
            
            _initializationTaskCompletionSource.TrySetResult(true);
            _initialized = true;
        }

        void IStoreListener.OnInitializeFailed(InitializationFailureReason error) => HandleInitializationFailure(error, message: null);
        void IStoreListener.OnInitializeFailed(InitializationFailureReason error, string message) => HandleInitializationFailure(error, message);

        PurchaseProcessingResult IStoreListener.ProcessPurchase(PurchaseEventArgs purchaseEvent)
        {
            var purchasedProduct = purchaseEvent.purchasedProduct;
            var productId = purchasedProduct.definition.id;
            var productLocalizedDescription = purchasedProduct.metadata?.localizedDescription ?? string.Empty;

            _logger.LogDebug(
                $"ProcessPurchase `{productLocalizedDescription}`",
                trackingFactory: () => new Dictionary<string, object>
                {
                    ["ProductId"] = productId,
                    ["TransactionId"] = purchasedProduct.transactionID,
                }
            );

            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.Billing,
                "process",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.State] = "pending",
                    [TelemetryEventParameters.ProductId] = productId,
                }
            );

            _purchaseHandler.OnInAppPurchasedAsync(purchasedProduct)
                            .ContinueWith(status => TryConfirmPurchasedProduct(purchasedProduct, status))
                            .Forget();

            return PurchaseProcessingResult.Pending;
        }

        void IDetailedStoreListener.OnPurchaseFailed(Product product, PurchaseFailureDescription failureDescription)
        {
            OnPurchaseFailed(product, failureDescription.reason, failureDescription.message);
        }

        void IStoreListener.OnPurchaseFailed(Product product, PurchaseFailureReason failureReason)
        {
            OnPurchaseFailed(product, failureReason, failureMessage: null);
        }

        private async void TrackInitialization()
        {
            try
            {
                var (isTimedOut, initialized) = await _initializationTaskCompletionSource.Task.TimeoutWithoutException(TimeSpan.FromSeconds(10));

                JxTelemetryIntegration.Instance.TrackEvent(
                    TelemetryTrackingContexts.Billing,
                    "initialization",
                    new Dictionary<string, string>
                    {
                        [TelemetryEventParameters.State] = GetStatus(),
                    }
                );

                string GetStatus()
                {
                    if (isTimedOut)
                        return "too_long";

                    if (!initialized)
                        return "fail";

                    return "success";
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e);
            }
        }

        private void TryConfirmPurchasedProduct(Product product, PurchaseStatus status)
        {
            try
            {
                if (product == null)
                    throw new ArgumentNullException(nameof(product));
                if (_storeController == null)
                    throw new ArgumentNullException(nameof(_storeController));

                var productId = product.definition?.id;
                if (string.IsNullOrEmpty(productId))
                    throw new InvalidOperationException($"{nameof(productId)} can't be null or empty");
                
                _logger.LogDebug(
                    $"{nameof(TryConfirmPurchasedProduct)} `{productId}`",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["TransactionId"] = product.transactionID,
                        ["Status"] = status.ToString(),
                    }
                );

                switch (status)
                {
                    case PurchaseStatus.Success:
                        try
                        {
                            _storeController.ConfirmPendingPurchase(product);

                            _purchaseTaskCompletionSource?.TrySetResult(true);

                            TrackStatus("success");

                            _logger.LogDebug("Product purchase confirmed");
                        }
                        catch (Exception ex)
                        {
                            _purchaseTaskCompletionSource?.TrySetResult(false);

                            TrackStatus("apperror");
                            _logger.LogError("Purchase confirmation failed", ex);
                        }

                        break;

                    default:
                        _purchaseTaskCompletionSource?.TrySetResult(false);

                        TrackStatus("internalerror");
                        _logger.LogError($"{nameof(TryConfirmPurchasedProduct)} non success status [{status}]");

                        break;
                }

                RefreshProducts();

                void TrackStatus(string state)
                {
                    JxTelemetryIntegration.Instance.TrackEvent(
                        TelemetryTrackingContexts.Billing,
                        "process",
                        new Dictionary<string, string>
                        {
                            [TelemetryEventParameters.State] = state,
                            [TelemetryEventParameters.Status] = status.ToString(),
                            [TelemetryEventParameters.Provider] = productId,
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
        }
        
        private void RefreshProducts()
        {
            _inAppHandler.RefreshProducts();
            _subscriptionHandler.RefreshProducts();
        }

        private void OnPurchaseFailed(Product? product, PurchaseFailureReason failureReason, string? failureMessage)
        {
            _purchaseTaskCompletionSource?.TrySetResult(false);

            failureMessage ??= "empty";

            if (product == null)
            {
                _logger.LogError(
                    "Unknown purchase failed",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["Reason"] = failureReason.ToString(),
                        ["Message"] = failureMessage
                    }
                );
            }
            else if (failureReason == PurchaseFailureReason.UserCancelled)
            {
                _logger.LogDebug(
                    "Purchase cancelled",
                    trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["StoreProductId"] = product.definition.id,
                        ["Message"] = failureMessage,
                    }
                );
            }
            else
            {
                _logger.LogError(
                    "Purchase failed",
                    trackingFactory: () => new Dictionary<string, object>
                    {
                        ["StoreProductId"] = product.definition.id,
                        ["Reason"] = failureReason.ToString(),
                        ["Message"] = failureMessage
                    }
                );
            }

            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.Billing,
                "failed",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.Reason] = failureReason.ToString(),
                    [TelemetryEventParameters.Message] = failureMessage,
                    [TelemetryEventParameters.Provider] = product?.definition?.id ?? "null_product",
                }
            );
        }

        private void HandleInitializationFailure(InitializationFailureReason error, string? message)
        {
            _initializationTaskCompletionSource.TrySetResult(false);
            _initialized = false;

            message ??= string.Empty;

            _logger.LogError(
                "Initialization failed",
                trackingFactory: () => new Dictionary<string, object>
                {
                    ["Reason"] = error,
                    ["Message"] = message,
                }
            );
        }
    }
}