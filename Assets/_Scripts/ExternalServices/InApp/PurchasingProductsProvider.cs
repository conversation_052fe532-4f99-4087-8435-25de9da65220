using System.Collections.Generic;
using Api.Client.User;
using Api.Client.User.Prices.Adapters;
using UnityEngine.Purchasing;
using UnityEngine.Scripting;

namespace ExternalServices.InApp
{
    [Preserve]
    public class PurchasingProductsProvider : IPurchasingProductsProvider
    {
        private readonly IUserContext _userContext;

        [Preserve]
        public PurchasingProductsProvider(IUserContext userContext)
        {
            _userContext = userContext;
        }

        public IEnumerable<PurchasingProductConfiguration> GetProducts()
        {
            foreach (var priceId in _userContext.Billing.GetPriceIds())
            {
                if (!_userContext.Billing.TryGetPrice(priceId, out var price, ignoreInAppInit: true))
                    continue;

                switch (price)
                {
                    case SubscriptionUserPrice subscription:
                        yield return new PurchasingProductConfiguration(subscription.Id, ProductType.Subscription);
                        break;

                    case MoneyUserPrice money:
                        yield return new PurchasingProductConfiguration(money.Id, ProductType.Consumable);
                        break;
                }
            }
        }
    }
}