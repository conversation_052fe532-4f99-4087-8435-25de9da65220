using Cysharp.Threading.Tasks;
using ExternalServices.Auth.User;

namespace ExternalServices.Auth
{
    public interface IAuthIntegration
    {
        IAuthUser User { get; }

        UniTask InitializeAsync();
        UniTask<IAuthUser?> SignInAsync(AuthProviderType provider);
        UniTask<AuthLinkProviderStatus> LinkGuestToProviderAsync(AuthProviderType provider);

        UniTask<string> TryGetTokenAsync(bool forceRefresh);
    }
}