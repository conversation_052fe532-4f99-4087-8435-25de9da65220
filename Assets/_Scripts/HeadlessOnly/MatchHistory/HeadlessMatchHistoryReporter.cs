#if HEADLESS
using System;
using System.Collections.Generic;
using System.Linq;
using App.Headless;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context;
using Jx.Utils;
using Jx.Utils.Logging;
using Jx.Utils.UnityContext;
using LoM.Messaging.Model.MatchHistory;
using Mirror;
using MonsterLand.Matchmaking;
using MonsterLand.Meta.Api;

namespace HeadlessOnly.MatchHistory
{
    public class HeadlessMatchHistoryReporter
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(HeadlessMatchHistoryReporter));

        private readonly IJxMonsterLandMetaServerApi _serverApi;
        private readonly IJxUnityContext _unityContext;

        public HeadlessMatchHistoryReporter(
            IJxMonsterLandMetaServerApi serverApi,
            IJxUnityContext unityContext
        )
        {
            _serverApi = serverApi;
            _unityContext = unityContext;
        }

        public async UniTask ReportAsync(IGameplayServerContext gameplayContext, IMatchContext matchContext)
        {
            try
            {
                await _serverApi.ReportMatchHistoryAsync(CreateMatchHistory(gameplayContext, matchContext));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
        }

        private MatchHistoryClientIntegration CreateMatchHistory(IGameplayServerContext gameplayContext, IMatchContext matchContext)
        {
            var timing = gameplayContext.MatchSyncData.Timing.Value;
            var isTimedOut = NetworkTime.time > timing.EndTime;
            var winnerTeam = gameplayContext.Spawned.Catchers.FirstOrDefault()?.SyncData.State.Value == GameplayPlayerState.IsWinner
                ? JxMatchmakingTeamKind.Catcher
                : JxMatchmakingTeamKind.Escapee;

            return new MatchHistoryClientIntegration(
                isTimedOut: isTimedOut,
                winnerTeam: winnerTeam,
                durationInSec: (int)(timing.EndTime - timing.StartTime),
                playerStatistics: GetPlayersHistory(gameplayContext),
                gameMode: matchContext.GameMode,
                map: matchContext.Map,
                scope: HeadlessGameServerParameters.Instance.Scope,
                geo: HeadlessGameServerParameters.Instance.Geo,
                appVersion: JxUnityAppVersionParser.Parse(_unityContext.ApplicationInfo.Version)
            );
        }

        private IReadOnlyList<PlayerMatchHistoryClientIntegration> GetPlayersHistory(IGameplayServerContext gameplayContext)
        {
            var result = new List<PlayerMatchHistoryClientIntegration>(gameplayContext.Spawned.Players.Count);

            result.AddRange(gameplayContext.Spawned.Players.Select(player => CreatePlayerHistory(player, gameplayContext)));

            return result;
        }

        private PlayerMatchHistoryClientIntegration CreatePlayerHistory(NetGamePlayer player, IGameplayServerContext gameplayContext)
        {
            var syncData = player.BaseSyncData;

            var publicId = syncData.PublicInfo.Value.PublicId;
            var playTime = syncData.PlayTimeSeconds.Value;
            var isMvp = player.Score.IsMvp;
            var characterIndex = syncData.PublicInfo.Value.CharacterIdentifier.Index;
            var characterLevel = syncData.PublicInfo.Value.CharacterLevel;
            var characterTrophies = syncData.PublicInfo.Value.CharacterTrophies;
            var equippedPPerks = player.Perk.GetPerkDefinitions(player)?.Select(d => d.Type).ToArray();
            var diagnostics = gameplayContext.PingTracker.FindPlayerStatistics(player.Client.AuthInfo.TicketId);
            var playedMatchCount = 0;
            var mmr = syncData.PublicInfo.Value.Mmr;
            var leftInCage = false;
            var leftInGame = false;
            var score = player.Score.TotalValue.Value;

            return new PlayerMatchHistoryClientIntegration(
                publicId,
                playTime,
                isMvp,
                characterIndex,
                characterLevel,
                characterTrophies,
                equippedPPerks,
                diagnostics,
                playedMatchCount,
                mmr,
                leftInCage,
                leftInGame,
                score
            );
        }
    }
}
#endif