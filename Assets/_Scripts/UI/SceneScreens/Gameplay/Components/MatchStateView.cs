using System;
using Core.Inspector.ConditionalFieldInspector;
using Core.UI.Components.Buttons;
using Core.UIReusable.Timer;
using Core.UIReusable.Timer.TimeProviders;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace UI.SceneScreens.Gameplay.Components
{
    public class MatchStateView : MonoBehaviour
    {
        [SerializeField]
        private GoalMatchStateView _goal = null!;

        [SerializeField]
        private bool _timedEnabled = true;

        [SerializeField]
        private RectTransform _timerContainer = null!;

        [SerializeField]
        [ConditionalField(nameof(_timedEnabled))]
        private UIDurationTimer _timer = null!;

        [SerializeField]
        private RectTransform _highlightContainer = null!;

        [field: SerializeField]
        public ButtonComponent Button { get; private set; }

        private ILocalGameplayContext _gameplayContext = null!;

        private bool _enabled;
        private bool _initialized;
        private IDisposable? _campfireGoalSubscription;
        private IDisposable? _matchTimingSubscription;

        private void OnEnable()
        {
            _enabled = true;

            if (_initialized)
                Setup();

            _timerContainer.gameObject.SetActive(_timedEnabled);
        }

        private void OnDisable()
        {
            _enabled = false;
            Cleanup();
        }

        public void Initialize(ILocalGameplayContext gameplayContext)
        {
            _gameplayContext = gameplayContext;

            _goal.Initialize(_gameplayContext.MatchSyncData.GameplayInfo.Value.CampfireGoal, isPlayerEscapee: _gameplayContext.Player.IsEscapee());

            if (_enabled)
                Setup();

            _initialized = true;
        }

        public RectTransform GetHighlightContainer() => _highlightContainer;

        private void Setup()
        {
            Cleanup();

            if (_timedEnabled)
                _matchTimingSubscription = _gameplayContext.MatchSyncData.Timing.SubscribeAndFire(RenderMatchDuration);

            _campfireGoalSubscription = _gameplayContext.MatchSyncData.GameplayInfo.SubscribeAndFire(_goal.Render);
        }

        private void Cleanup()
        {
            _campfireGoalSubscription?.Dispose();
            _matchTimingSubscription?.Dispose();
        }

        private void RenderMatchDuration(NetMatchTimingDto timing) => _timer.Setup(timing.EndTime, NetworkTimeTimeProvider.Instance);
    }
}