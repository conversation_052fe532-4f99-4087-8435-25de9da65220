using Core.Managers.AssetLoader;
using UnityEngine;
using UnityEngine.UI;

namespace UI.SceneScreens.Gameplay.Components.CatcherRage
{
    public class CatcherRageIndicatorView : MonoBehaviour
    {
        private const string PathToIcon = "gameplay-icons/catcher-rage";
        private const string FormatIcon = @"rage-{0}";

        private const string PathToRage = "gameplay-icons/catcher-rage-progress";
        private const string RageFormatIcon = @"fill-{0}";

        [SerializeField]
        private Image _icon = null!;

        [SerializeField]
        private Image _fill = null!;

        public void Set(int rage)
        {
            rage = Mathf.Clamp(rage, 0, 5);
            _icon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(PathToIcon, string.Format(FormatIcon, rage));
            _fill.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(PathToRage, string.Format(RageFormatIcon, rage));
        }
    }
}