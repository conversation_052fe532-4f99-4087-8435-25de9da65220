using Core.Extensions;
using Core.Helpers;
using GameplayComponents.CharacterPresentation;

namespace UI.Screens.Common.Reward.CharacterPresenter
{
    public class EntityRendererCharacterView : CharacterView
    {
        protected override void OnAfterActivePresenterChanged(CharacterViewPresenter? presenter)
        {
            base.OnAfterActivePresenterChanged(presenter);
            
            gameObject.SetLayerRecursively(LayerType.EntityRenderer);
        }
    }
}