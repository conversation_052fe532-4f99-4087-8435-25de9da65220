using Api.Entities.Characters;
using UI.Screens.Common.Reward.Views.GroupViews.Identifiers;

namespace UI.Screens.Common.Reward.Parameters
{
    public class CharacterRewardParameters : IRewardParameters
    {
        public CharacterRewardParameters(CharacterGameEntity entity)
        {
            Entity = entity;
            
            Group = new CharacterRewardGroupIdentifier(Entity.Index);
        }
        
        public CharacterGameEntity Entity { get; }
        public int Weight => int.MaxValue;
        public IRewardGroupIdentifier Group { get; }
    }
}