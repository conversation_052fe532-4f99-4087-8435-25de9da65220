using Api.Client.User.Upgrades.Interactions;
using Api.Entities.Characters;
using UI.Screens.Common.Reward.Views.GroupViews.Identifiers;

namespace UI.Screens.Common.Reward.Parameters
{
    public class CharacterTokenRewardParameters : IRewardParameters
    {
        public CharacterTokenRewardParameters(
            CharacterGameEntity characterEntity,
            ICharacterUpgradeInteraction upgradeInteraction,
            int tokens,
            bool synchronized
        )
        {
            CharacterEntity = characterEntity;
            UpgradeInteraction = upgradeInteraction;
            Tokens = tokens;
            Synchronized = synchronized;

            Group = new CharacterRewardGroupIdentifier(characterEntity.Index);
        }

        public CharacterGameEntity CharacterEntity { get; }
        public ICharacterUpgradeInteraction UpgradeInteraction { get; }
        public int Tokens { get; }
        public int Weight => int.MaxValue - 1;
        public bool Synchronized { get; }
        public IRewardGroupIdentifier Group { get; }
    }
}