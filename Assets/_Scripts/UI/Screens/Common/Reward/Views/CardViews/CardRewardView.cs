using System;
using System.Threading;
using Core.Helpers;
using Core.Managers.AssetLoader;
using Jx.Utils.Coroutines;
using Jx.Utils.Logging;
using UI.Screens.Common.Reward.Parameters;
using UnityEngine;
using UnityEngine.Scripting;
using Zenject;

namespace UI.Screens.Common.Reward.Views.CardViews
{
    public abstract class CardRewardView<TView, TReward> : MonoBehaviour,
                                                           ICardRewardView,
                                                           IPoolable<TReward, IMemoryPool>
        where TReward : IRewardParameters
        where TView : ICardRewardView
    {
        // [SerializeField]
        // private TextComponent _kindText;

        private IMemoryPool _pool;
        private TReward _parameters;

        protected IJxLogger Logger { get; private set; }

        public GameObject GameObject => gameObject;
        public RectTransform Transform => transform as RectTransform;

        private void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        public void OnSpawned(TReward parameters, IMemoryPool pool)
        {
            _pool = pool;
            _parameters = parameters;
        }

        public void OnDespawned()
        {
            gameObject.SetActive(false);
        }

        public void Render()
        {
            Render(_parameters);
        }

        public void Dispose()
        {
            if (!this.IsDestroyed())
            {
                Interlocked.Exchange(ref _pool, null)?.Despawn(this);
            }
        }
        
        protected abstract void OnRender(TReward parameters);
        protected abstract string GetKind(TReward parameters);
        
        private void Render(TReward parameters)
        {
            try
            {
                // SetupKindLabel(parameters);
                
                OnRender(parameters);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex);

                if (gameObject.IsNullObj())
                    return;

                gameObject.SetActive(false);
            }
        }

        // private void SetupKindLabel(TReward parameters)
        // {
        //     _kindText.Text = GetKind(parameters);
        // }
        
        #region Factory

        [Preserve]
        public class Factory : PlaceholderFactory<TReward, TView>
        {
        }

        #endregion
    }
}