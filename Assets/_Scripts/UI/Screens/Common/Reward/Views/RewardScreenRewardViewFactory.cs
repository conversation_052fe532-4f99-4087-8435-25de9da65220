using System;
using Jx.Utils.Extensions;
using UI.Screens.Common.Reward.Parameters;
using UI.Screens.Common.Reward.Views.CardViews;
using UI.Screens.Common.Reward.Views.CarouselViews;
using UI.Screens.Common.Reward.Views.GroupViews;
using UnityEngine.Scripting;

namespace UI.Screens.Common.Reward.Views
{
    [Preserve]
    public class RewardScreenRewardViewFactory : IRewardScreenRewardViewFactory
    {
        // todo говнокод сделать общий подход для рендера ревардов, общий менеджер (сейчас 1 класс для квестов, другой класс для окна ревардов)
        
        private readonly ResourceCardRewardView.Factory _resourceCardViewFactory;
        private readonly CharacterTokenCardRewardView.Factory _characterCardTokenViewFactory;
        private readonly PerkCardRewardView.Factory _perkCardViewFactory;
        private readonly SeasonPassXpCardRewardView.Factory _seasonPassXpCardViewFactory;

        private readonly ResourceCarouselRewardView.Factory _resourceCarouselViewFactory;
        private readonly CharacterTokenCarouselRewardView.Factory _characterCarouselTokenViewFactory;
        private readonly PerkCarouselRewardView.Factory _perkCarouselViewFactory;
        private readonly CharacterCarouselRewardView.Factory _characterCarouselViewFactory;
        private readonly SkinCarouselRewardView.Factory _skinCarouselViewFactory;
        private readonly SeasonPassXpCarouselRewardView.Factory _seasonPassXpCarouselViewFactory;

        private readonly GroupCardRewardView.Factory _groupFactory;

        [Preserve]
        public RewardScreenRewardViewFactory(
            ResourceCardRewardView.Factory resourceCardViewFactory,
            CharacterTokenCardRewardView.Factory characterCardTokenViewFactory,
            PerkCardRewardView.Factory perkCardViewFactory,
            SeasonPassXpCardRewardView.Factory seasonPassXpCardViewFactory,

            ResourceCarouselRewardView.Factory resourceCarouselViewFactory,
            CharacterTokenCarouselRewardView.Factory characterCarouselTokenViewFactory,
            PerkCarouselRewardView.Factory perkCarouselViewFactory,
            CharacterCarouselRewardView.Factory characterCarouselViewFactory,
            SkinCarouselRewardView.Factory skinCarouselViewFactory,
            SeasonPassXpCarouselRewardView.Factory seasonPassXpCarouselViewFactory,
            
            GroupCardRewardView.Factory groupFactory
        )
        {
            _resourceCardViewFactory = resourceCardViewFactory;
            _characterCardTokenViewFactory = characterCardTokenViewFactory;
            _perkCardViewFactory = perkCardViewFactory;
            _seasonPassXpCardViewFactory = seasonPassXpCardViewFactory;
            
            _resourceCarouselViewFactory = resourceCarouselViewFactory;
            _characterCarouselTokenViewFactory = characterCarouselTokenViewFactory;
            _perkCarouselViewFactory = perkCarouselViewFactory;
            _characterCarouselViewFactory = characterCarouselViewFactory;
            _skinCarouselViewFactory = skinCarouselViewFactory;
            _seasonPassXpCarouselViewFactory = seasonPassXpCarouselViewFactory;

            _groupFactory = groupFactory;
        }

        public ICardRewardView CreateCardView(IRewardParameters parameters)
        {
            switch (parameters)
            {
                case SeasonPassXpRewardParameters seasonPassXpRewardParameters:
                    return _seasonPassXpCardViewFactory.Create(seasonPassXpRewardParameters);
                case IResourceRewardParameters resourceParameters:
                    return _resourceCardViewFactory.Create(resourceParameters);
                case CharacterTokenRewardParameters characterTokenParameters:
                    return _characterCardTokenViewFactory.Create(characterTokenParameters);
                case PerkRewardParameters perkParameters:
                    return _perkCardViewFactory.Create(perkParameters);
                default:
                    throw new ArgumentException(nameof(parameters))
                       .SetData("Type", parameters.GetType().Name);
            }
        }

        public ICarouselRewardView CreateCarouselView(IRewardParameters parameters)
        {
            switch (parameters)
            {
                case SeasonPassXpRewardParameters seasonPassXpRewardParameters:
                    return _seasonPassXpCarouselViewFactory.Create(seasonPassXpRewardParameters);
                case IResourceRewardParameters resourceParameters:
                    return _resourceCarouselViewFactory.Create(resourceParameters);
                case CharacterTokenRewardParameters characterTokenParameters:
                    return _characterCarouselTokenViewFactory.Create(characterTokenParameters);
                case PerkRewardParameters perkParameters:
                    return _perkCarouselViewFactory.Create(perkParameters);
                case SkinRewardParameters skinParameters:
                    return _skinCarouselViewFactory.Create(skinParameters);
                case CharacterRewardParameters characterParameters:
                    return _characterCarouselViewFactory.Create(characterParameters);
                default:
                    throw new ArgumentException(nameof(parameters))
                       .SetData("Type", parameters.GetType().Name);
            }
        }

        public IGroupCardRewardView CreateGroupView(GroupCardRewardViewParameters parameters)
        {
            return _groupFactory.Create(parameters);
        }
    }
}