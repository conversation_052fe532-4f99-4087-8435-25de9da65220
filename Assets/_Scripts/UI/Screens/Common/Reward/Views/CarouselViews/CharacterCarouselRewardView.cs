using Api.Entities;
using App.Localization.Providers;
using Configs.EntityRenderer;
using Core.UI.Components.RenderTexture.Factory;
using Core.UI.Components.RenderTexture.Renderers;
using Core.UI.Components.Text;
using UI.Screens.Common.Reward.Parameters;
using UnityEngine;

namespace UI.Screens.Common.Reward.Views.CarouselViews
{
    public class CharacterCarouselRewardView : CarouselRewardView<CharacterRewardParameters, CharacterCarouselRewardView>
    {
        [SerializeField]
        private CharacterImage _characterImage = null!;

        [SerializeField]
        private JxTextComponent _characterNameText = null!;

        protected override void OnRender(CharacterRewardParameters parameters)
        {
            var localization = LocalizationKeyProvider.Instance.GetForCharacter(parameters.Entity.Index);
            
            _characterNameText.SetLocalizationKey(localization.NameKey);
            
            _characterImage.Render(
                CharacterImageBuilder
                    .Create(parameters.Entity.ToViewIdentifier())
                    .SetPlacement(EntityRendererPlacement.Reward)
                    .SetQuality(RenderTextureQuality.FullHD)
                    .Build()
                );
        }
    }
}