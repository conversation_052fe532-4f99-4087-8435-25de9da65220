using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Containers;
using Core.Helpers.Systems.Controller;
using Cysharp.Threading.Tasks;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Local.Input;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using MonsterLand.Matchmaking;
using SceneLogics.GameplayScene.Components.Indicators;
using SceneLogics.GameplayScene.Systems.Client.Heartbeat;
using System;
using UnityEngine.Scripting;
using Zenject;

namespace UI.Screens.Common.Gameplay.InGame
{
    public abstract class InGameSubScreenController<TView> : NavigationScreenContainerSubController<TView>
        where TView : InGameSubScreenView
    {
        private readonly DiContainer _diScope;
        private readonly ILocalInputRegistrar _localInputRegistrar;
        private readonly IInticatorsRegistrar _indicatorsRegistrar;
        private readonly ILocalGameplayContext _gameplayContext;
        private readonly INavigation _navigation;
        private readonly ILocalInputContext _localInputContext;

        private LogicSystemsController _subSystems = null!;
        private ILocalGameplayPlayer _player = null!;

        [Preserve]
        protected InGameSubScreenController(
            DiContainer diScope,
            ILocalInputRegistrar localInputRegistrar,
            IInticatorsRegistrar indicatorsRegistrar,
            ILocalGameplayContext gameplayContext,
            INavigation navigation,
            ILocalInputContext localInputContext,
            TView view
        )
            : base(view)
        {
            _diScope = diScope;
            _localInputRegistrar = localInputRegistrar;
            _indicatorsRegistrar = indicatorsRegistrar;
            _gameplayContext = gameplayContext;
            _navigation = navigation;
            _localInputContext = localInputContext;
        }

        protected ILocalGameplayPlayer Player => _player;
        protected INavigation Navigation => _navigation;
        protected ILocalGameplayContext GameplayContext => _gameplayContext;

        protected override async UniTask OnInitializeAsync()
        {
            await base.OnInitializeAsync();

            _subSystems = _diScope.Instantiate<LogicSystemsController>();
            _player = _gameplayContext.Player;

            await CreateSubsystemsAsync(_subSystems);

            RegisterInput(_localInputRegistrar);
            RegisterIndications(_indicatorsRegistrar);

            InitializeGoal();
            InitializePlayerStates();

            View.UseAbilityButton.Initialize(
                _gameplayContext,
                _localInputContext
            );
        }

        protected virtual void InitializePlayerStates()
        {
            View.PlayersStateViewsIndicator.Initialize(_gameplayContext.SpawnedPlayers, OnPlayerStateClick);
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();
            View.PlayersStateViewsIndicator.Setup();

            // spectator screen can change spectated object by default
            _gameplayContext.Spectating.SpectateMainPlayer();

            // spectator screen registers joystick, we need re-register it here
            _localInputRegistrar.RegisterJoystick(View.Joystick);


            _subSystems.ExecuteAllAsync().Forget();
        }

        protected override void OnViewDetached()
        {
            View.PlayersStateViewsIndicator.Cleanup();
            _subSystems.DeInitializeAllAsync().Forget();
        }

        protected virtual void RegisterInput(ILocalInputRegistrar inputRegistrar)
        {
            inputRegistrar.RegisterJoystick(View.Joystick);
            inputRegistrar.RegisterInteractionButton(View.InteractButton);
            inputRegistrar.RegisterGoalContainerButton(View.GameplayGoalContainer.Button);
            inputRegistrar.RegisterMedkitButton(View.MedkitButton);
            inputRegistrar.RegisterInventoryView(View.InventoryView);
            inputRegistrar.RegisterAbilityInput(View.UseAbilityButton);
        }

        protected virtual void RegisterIndications(IInticatorsRegistrar indicationRegistrar)
        {
            indicationRegistrar.SetParentContainer(View.IndicatorsContainer);
        }

        protected virtual async UniTask CreateSubsystemsAsync(LogicSystemsController subSystemsController)
        {
            await subSystemsController.CreateAsync<HeartbeatClientSystem>(new HeartbeatClientSystemParameters(View.HeartbeatConfiguration));
        }
        
        private void InitializeGoal() => View.GameplayGoalContainer.Initialize(GameplayContext);
        
        // todo SRP: refactor: move it into a separate controller
        #region PLAYER STATE CLICK

        protected void OnPlayerStateClick(NetGamePlayer player)
        {
            if (Player.NetIdentity.netId == player.netId)
                return;
            if (Player.IsEscapee())
                OnPlayerStateClickAsEscapee(player);
            if (Player.IsCatcher())
                OnPlayerStateClickAsCatcher(player);
        }

        private void OnPlayerStateClickAsEscapee(NetGamePlayer player)
        {
            if (player.Team == JxMatchmakingTeamKind.Catcher)
                return;
            
            var targetAction = player.BaseSyncData.Action.Value;
            if (targetAction.IsFinished())
                return;

            var indication = targetAction == ActionType.InCage ? IndicationType.EscapeeCaged : IndicationType.EchoLocation;
            IndicatePlayer(player, indication);
        }

        private void OnPlayerStateClickAsCatcher(NetGamePlayer player)
        {
            if (player.BaseSyncData.Action.Value != ActionType.InCage) 
                return;
            
            IndicatePlayer(player, IndicationType.EscapeeCaged);
        }

        private void IndicatePlayer(NetGamePlayer player, IndicationType indicationType)
        {
            var indicationDuration = TimeSpan.FromSeconds(4f);
            Player.Indicator.ShowTemporary(indicationType, player.netIdentity, indicationDuration);
        }

        #endregion
    }
}