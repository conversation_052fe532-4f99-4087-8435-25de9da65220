using System;
using DG.Tweening;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace UI.Screens.Common.Gameplay.InGame.Components
{
    // todo TEMPORARY, UNTIL TUTORIAL REFACTOR
    // a very fast implementation, single use only, no factory, we need the tutorial refactor first and make the shared manager for all the tutorial popups (now it's impossible)
    public class TutorialCloudPopupController : MonoBehaviour
    {
        [SerializeField]
        private TutorialCloudPopup _tutorialCloudPopup = null!;
        
        private string? _lastLocalization;
        private float? _lastRequestEndTime;
        private IDisposable? _disposeAfterDelayCancellation;

        private void OnEnable()
        {
            if (!IsShowing())
            {
                Cleanup();
                return;
            }
            
            ShowInternal(_lastLocalization!, _lastRequestEndTime!.Value - Time.time);
        }

        private void OnDisable()
        {
            _tutorialCloudPopup.gameObject.SetActive(false);
        }

        public void Show(string localization, float durationInSec)
        {
            if (IsShowing())
                return;
            
            _lastLocalization = localization;
            _lastRequestEndTime = Time.time + durationInSec;

            if (isActiveAndEnabled)
            {
                ShowInternal(localization, durationInSec);
            }
        }

        private void ShowInternal(string localization, float durationInSec)
        {
            if (durationInSec <= 0f)
            {
                Cleanup();
                return;
            }
            
            _tutorialCloudPopup.gameObject.SetActive(true);
            _tutorialCloudPopup.DoShow(localization);
            
            this.InvokeAfterDelay(() =>
                {
                    _tutorialCloudPopup.DoHide().OnComplete(Cleanup);
                    _lastRequestEndTime = null;
                },
                durationInSec
            );
        }
        
        private void Cleanup()
        {
            _tutorialCloudPopup.gameObject.SetActive(false);
            _lastRequestEndTime = null;
        }

        private bool IsShowing()
        {
            return _lastRequestEndTime > Time.time;
        }
    }
}