using System.Collections.Generic;
using System.Linq;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.Managers.AssetLoader;
using ExternalServices.Auth;
using UnityEngine.Scripting;

namespace UI.Screens.Common.AuthenticationProviderRequest
{
    public class AuthenticationProviderRequestScreenController
        : BaseNavigationScreenController<AuthenticationProviderRequestScreen, AuthenticationProviderRequestScreenParameters>
    {
        [Preserve]
        public AuthenticationProviderRequestScreenController() { }

        public override NavigationLayer Layer => NavigationLayer.Loading;

        protected override void OnViewAttached()
        {
            View.Title.SetLocalizationKey($"{Parameters.Scope}.Title");
            View.BackButton.gameObject.SetActive(Parameters.EnableCloseButton);

            View.Draw(CreateInfoList(Parameters.Providers));
            View.ProviderClickedEvent += HandleProviderSelection;
        }

        protected override void OnViewDetached()
        {
            View.ProviderClickedEvent -= HandleProviderSelection;
        }

        private void HandleProviderSelection(AuthProviderType providerType)
        {
            Exit(providerType);
        }

        private IReadOnlyList<AuthenticationProviderVariantInfo> CreateInfoList(IReadOnlyList<AuthProviderType> providerTypes)
        {
            return providerTypes.Select(
                                     providerType => new AuthenticationProviderVariantInfo(
                                         providerType,
                                         JxResourceLoader.Instance.LoadAuthProviderIcon(providerType),
                                         GetMapProviderName(providerType)
                                     )
                                 )
                                .ToList();
        }

        private string GetMapProviderName(AuthProviderType providerType)
        {
            return providerType switch
            {
                AuthProviderType.Google => "Google",
                AuthProviderType.Guest => "Guest",
                AuthProviderType.GooglePlayGamesWithUI => "GooglePlayGames",
                _ => "Unknown"
            };
        }
    }
}