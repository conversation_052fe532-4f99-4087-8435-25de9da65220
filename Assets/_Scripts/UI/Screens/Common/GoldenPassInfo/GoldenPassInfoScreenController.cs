using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.UI.Components.Text;
using Jx.Utils.Templating;
using UnityEngine;
using UnityEngine.Scripting;

namespace UI.Screens.Common.GoldenPassInfo
{
    [Preserve]
    public class GoldenPassInfoScreenController : BaseNavigationScreenController<GoldenPassInfoScreen>
    {
        private static readonly Color _benefitSpecialColor = Color.yellow;
        
        public override Navigation.NavigationMode NavigationMode => Navigation.NavigationMode.Additive;

        protected override void OnViewAttached()
        {
            View.Scroll.verticalNormalizedPosition = 1f;
            
            SetupTextPart(View.AfterBenefitsText);
            SetupTextPart(View.BenefitsText);
            SetupTextPart(View.BeforeBenefitsText);
            
            base.OnViewAttached();
        }

        private void SetupTextPart(JxTextComponent textPart)
        {
            textPart.SetTextInterpolationContext(new JxStringTemplateParameters()
                .Set("SPECIAL_COLOR", ColorUtility.ToHtmlStringRGBA(_benefitSpecialColor)));
        }
    }
}