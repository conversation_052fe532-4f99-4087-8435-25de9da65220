using Core.Helpers.Navigation.View;
using Core.UI.Components.Text;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.Common.GoldenPassInfo
{
    [ScreenController(typeof(GoldenPassInfoScreenController))]
    public class GoldenPassInfoScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public ScrollRect Scroll { get; private set; }

        [field: SerializeField]
        public JxTextComponent BeforeBenefitsText { get; private set; }
        
        [field: SerializeField]
        public JxTextComponent BenefitsText { get; private set; }
        
        [field: SerializeField]
        public JxTextComponent AfterBenefitsText { get; private set; }
    }
}