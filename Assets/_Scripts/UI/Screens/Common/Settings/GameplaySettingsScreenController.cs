using Audio.BusesInteraction;
using Core.Helpers.Navigation;
using UnityEngine.Scripting;

namespace UI.Screens.Common.Settings
{
    [Preserve]
    public class GameplaySettingsScreenController : SettingsScreenController<GameplaySettingsScreen>
    {
        [Preserve]
        public GameplaySettingsScreenController(
            IAudioVcaInteraction vcaInteraction,
            INavigation navigation) 
            : base(vcaInteraction, navigation)
        {
        }
        
        public override NavigationLayer Layer => NavigationLayer.Dialog;
        
        // to ensure exchange mode to prevent bugs
        public override Navigation.NavigationMode NavigationMode { get; } = Navigation.NavigationMode.Exchange;
    }
}