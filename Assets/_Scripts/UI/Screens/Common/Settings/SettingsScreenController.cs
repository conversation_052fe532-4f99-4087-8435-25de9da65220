using Audio.BusesInteraction;
using Core.Extensions.Buttons;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Jx.Utils.UnityComponentExtensions;
using UI.Components.Toggle;
using UI.Screens.Common.ChangeLanguage;
using UI.Screens.Common.Report;

namespace UI.Screens.Common.Settings
{
    public abstract class SettingsScreenController<TScreen> : BaseNavigationScreenController<TScreen>
        where TScreen : BaseSettingsScreen
    {
        private readonly IAudioVcaInteraction _vcaInteraction; 
        private readonly INavigation _navigation;

        protected SettingsScreenController(
            IAudioVcaInteraction vcaInteraction,
            INavigation navigation)
        {
            _vcaInteraction = vcaInteraction;
            _navigation = navigation;
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();
            
            SetupAudioToggle(View.MusicAudioToggle, _vcaInteraction.GetMusicVolume01());
            SetupAudioToggle(View.SoundAudioToggle, _vcaInteraction.GetSoundVolume01());
            
            DisposeOnDetach(View.ChangeLanguageButton.NavigateOnClick<ChangeLanguageScreen>(_navigation));
            DisposeOnDetach(View.ReportProblemButton.OnClick(OnReportProblemButtonClick));
            DisposeOnDetach(View.MusicAudioToggle.Subscribe(OnMusicToggled));
            DisposeOnDetach(View.SoundAudioToggle.Subscribe(OnSoundsToggled));
        }
        
        private static void SetupAudioToggle(ToggleComponent toggle, float volume01)
        {
            toggle.SetIsOnSilently(volume01 > 0f);
        }
        
        private void OnSoundsToggled(bool isToggled)
        {
            _vcaInteraction.SetSoundVolume01(isToggled ? 1f : 0f);
        }

        private void OnMusicToggled(bool isToggled)
        {
            _vcaInteraction.SetMusicVolume01(isToggled ? 1f : 0f);
        }
        
        private void OnReportProblemButtonClick()
        {
            _navigation.Navigate<ReportScreen>(new ReportScreenParameters(ReportCategory.Problem));
        }
    }
}