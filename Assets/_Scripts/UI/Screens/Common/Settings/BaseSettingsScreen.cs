using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UI.Components.Toggle;
using UnityEngine;

namespace UI.Screens.Common.Settings
{
    public abstract class BaseSettingsScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public ButtonComponent ReportProblemButton { get; private set; } = null!;
        
        [field: SerializeField]
        public ButtonComponent ChangeLanguageButton { get; private set; } = null!;
        
        [field: SerializeField]
        public ToggleComponent SoundAudioToggle { get; private set; } = null!;
        
        [field: SerializeField]
        public ToggleComponent MusicAudioToggle { get; private set; } = null!;
    }
}