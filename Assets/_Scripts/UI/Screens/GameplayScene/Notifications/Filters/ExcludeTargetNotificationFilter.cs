using System.Collections.Generic;
using System.Linq;
using GameplayNetworking.Gameplay.Player;
using JetBrains.Annotations;

namespace UI.Screens.GameplayScene.Notifications.Filters
{
    public class ExcludeTargetNotificationFilter : BaseNotificationFilter
    {
        private readonly uint _targetPlayerNetId;

        private ExcludeTargetNotificationFilter(uint targetPlayerNetId)
        {
            _targetPlayerNetId = targetPlayerNetId;
        }
        
        public override IEnumerable<NetGamePlayer> Process([NotNull] IEnumerable<NetGamePlayer> players)
        {
            return players.Where(player => player.netId != _targetPlayerNetId);
        }

        public static INotificationFilter Create(uint targetPlayerNetId)
        {
            return new ExcludeTargetNotificationFilter(targetPlayerNetId);
        }
    }
}