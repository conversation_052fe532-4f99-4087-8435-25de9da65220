using System;
using System.Collections.Generic;
using System.Linq;
using GameplayNetworking;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Data;
using GameplayNetworking.Manager.Network.Share.Matchmaking.Handlers.Gameplay.Context.Control;
using Mirror;
using UI.Screens.GameplayScene.Notifications.Filters;
using UI.Screens.GameplayScene.Notifications.Request;
using UnityEngine.Scripting;
using Zenject;

namespace UI.Screens.GameplayScene.Notifications
{
    [Preserve]
    public class ServerNotificationController : IServerNotificationController
    {
        private IGameplayServerContextGetter _serverContextGetter;

        [Inject]
        private void Inject(IGameplayServerContextGetter serverContext)
        {
            _serverContextGetter = serverContext;
        }

        [Server]
        public void ShowNotification(Guid matchId, NotificationType notificationType, INotificationFilter filter)
        {
            ShowNotificationInternal(matchId, new ShowNotificationRequest(notificationType), filter);
        }
        
        [Server]
        public void ShowNotificationToPlayersInteractingWith(Guid matchId, NotificationType notificationType, uint targetInteractableObjectNetId)
        {
            var players = GetAllNotBotEscapees(matchId)
                .Where(a => a.SyncData.Action.Value == ActionType.Interaction &&
                            a.SyncData.AvailableInteraction.Value.NetId == targetInteractableObjectNetId);

            foreach (var player in players)
            {
                player.connectionToClient.Send(new ShowNotificationRequest(notificationType), Channels.Reliable);
            }
        }

        private void ShowNotificationInternal(Guid matchId, ShowNotificationRequest request, INotificationFilter filter)
        {
            SendRequestForMatch(matchId, request, filter);
        }
        
        private void SendRequestForMatch(Guid matchId, ShowNotificationRequest request, INotificationFilter filter)
        {
            filter ??= NoneNotificationFilter.Instance;
            
            foreach (var player in filter.Process(GetAllNotBotPlayers(matchId)))
            {
                player.connectionToClient.Send(request, Channels.Reliable);
            }
        }

        [Server]
        private IEnumerable<NetGamePlayer> GetAllNotBotPlayers(Guid matchId)
        {
            return _serverContextGetter.Get(matchId).Spawned.Players.Where(p => !p.IsBot());
        }
        
        [Server]
        private IEnumerable<NetEscapeePlayer> GetAllNotBotEscapees(Guid matchId)
        {
            return GetAllNotBotPlayers(matchId).OfType<NetEscapeePlayer>();
        }
    }
}