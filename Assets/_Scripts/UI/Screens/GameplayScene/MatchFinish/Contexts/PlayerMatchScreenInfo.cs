using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using GameplayComponents.CharacterPresentation;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Components.Animations;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using Jx.Utils.ChangeableObjects;
using LoM.Characters.ClientIntegration.Perks;

namespace UI.Screens.GameplayScene.MatchFinish.Contexts
{
    public abstract class PlayerMatchScreenInfo
    {
        private readonly PlayerMatchScreenInfoContext _context;

        protected PlayerMatchScreenInfo(PlayerMatchScreenInfoContext context)
        {
            _context = context;
        }

        public bool IsLocal => _context.IsLocal;
        public bool IsMvp => _context.IsMvp;
        public string Nickname => _context.Nickname;
        public IJxChangeableObject<int> Score => _context.Score;
        public IJxChangeableObject<IReadOnlyDictionary<int, int>> Stats => _context.Stats;
        public CharacterViewIdentifier CharacterIdentifier => _context.CharacterViewIdentifier;
        public int CharacterLevel => _context.CharacterLevel;
        public int ProfileLevel => _context.ProfileLevel;
        public int CharacterTrophies => _context.CharacterTrophies;
        public IJxChangeableObject<GameplayPlayerState> GameplayState => _context.GameplayState;
        public TimeSpan PlayedTime => _context is LocalPlayerMatchScreenInfoContext localContext ? localContext.PlayedTime : throw new NotSupportedException();
        public UniTask<NetGameplayMatchRewardDto> GetRewardAsync() => _context.GetRewardAsync();
        public IReadOnlyList<PerkType> PerkSlots => _context.Perks;
        public string? PublicId => _context.PublicId;

        protected abstract IReadOnlyList<GameplayStatisticsType> RenderStats { get; }

        public CharacterAnimationType ConvertMatchResultToAnimation()
        {
            var animationType = CharacterAnimationType.Idle;
            if (GameplayState.Value != GameplayPlayerState.InGame)
                animationType = GameplayState.Value == GameplayPlayerState.IsWinner ? CharacterAnimationType.Happy : CharacterAnimationType.Sad;
            
            return animationType;
        }

        public IReadOnlyList<KeyValuePair<GameplayStatisticsType, int>> GetSortedStats()
        {
            var stats = Stats.Value;

            return RenderStats
                  .Select(statType => new KeyValuePair<GameplayStatisticsType, int>(statType, stats.GetValueOrDefault((int)statType, 0)))
                  .ToList();
        }
    }
}