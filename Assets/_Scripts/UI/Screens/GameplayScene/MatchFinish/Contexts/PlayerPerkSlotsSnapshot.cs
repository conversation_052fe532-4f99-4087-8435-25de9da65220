using System;
using LoM.Characters.ClientIntegration.Perks;

namespace UI.Screens.GameplayScene.MatchFinish.Contexts
{
    public readonly struct PlayerPerkSlotsSnapshot
    {
        public readonly PerkType[] Perks;

        public PlayerPerkSlotsSnapshot(PerkType[] perks)
        {
            Perks = perks;
        }

        public static PlayerPerkSlotsSnapshot Default()
        {
            return new PlayerPerkSlotsSnapshot(Array.Empty<PerkType>());
        }
    }
}