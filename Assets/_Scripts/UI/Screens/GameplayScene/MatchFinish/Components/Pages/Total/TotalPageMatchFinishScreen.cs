using System;
using Core.UI.Components.Buttons;
using Core.UIReusable.Timer;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Ensurance;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Total.Components.PlayerResult;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Total.Components.TeamResult;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Total
{
    public class TotalPageMatchFinishScreen : MatchFinishScreenPage
    {
        [SerializeField]
        private UITimerComponent _timer = null!;

        [SerializeField]
        private TeamResultMatchFinishScreen _matchTeamResult = null!;

        [SerializeField]
        private PlayerMatchResultsContainer _matchStatsViewsContainer = null!;

        [SerializeField]
        private ButtonComponent _spectateButton = null!;

        public override string Key => "Total";
        public Button.ButtonClickedEvent SpectateButtonClickedEvent => _spectateButton.onClick;

        private IJxChangeableObject<MatchResultType> _matchResult = null!;
        private IJxChangeableObject<MatchStateType> _matchState = null!;

        public void Initialize(
            IJxChangeableObject<MatchResultType> matchResult,
            IJxChangeableObject<MatchStateType> matchState,
            IMatchFinishPopupPresenter popupPresenter
        )
        {
            _matchResult = matchResult;
            _matchState = matchState;

            _matchStatsViewsContainer.Initialize(popupPresenter);
            MarkInitialized();
        }

        protected override void OnAttached()
        {
            Ensure.NotNull(_matchResult, nameof(_matchResult));
            Ensure.NotNull(_matchState, nameof(_matchState));

            _matchStatsViewsContainer.Setup(Container);
            _matchTeamResult.Initialize(Container, _matchResult);

            DisposeOnDisable(_matchState.SubscribeAndFire(OnGameplayStateChange));
            InitializeTimer(Container.GetLocal().PlayedTime);
            _timer.SetFrozenTime(Container.GetLocal().PlayedTime);
        }

        protected override void OnDeAttached()
        {
            _matchStatsViewsContainer.Cleanup();

            base.OnDeAttached();
        }

        private void OnGameplayStateChange(MatchStateType state) =>
            _spectateButton.gameObject.SetActive(state == MatchStateType.InGame);

        private void InitializeTimer(TimeSpan playedTime)
        {
            _timer.SetFrozenTime(playedTime);
        }
    }
}