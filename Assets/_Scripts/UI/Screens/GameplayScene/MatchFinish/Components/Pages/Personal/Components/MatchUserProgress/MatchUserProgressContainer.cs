using System;
using Api.Client.User;
using Api.Client.User.TrophyPath;
using Core.UI.Components.Text;
using Core.UIReusable.LevelIndicator;
using Core.UIReusable.Progress.Double.UserExperience;
using DG.Tweening;
using Jx.Utils.ChangeableObjects;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.MatchUserProgress.Rating;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.MatchUserProgress.WinStreak;
using UI.Screens.GameplayScene.MatchFinish.Contexts;
using UI.Screens.GameplayScene.MatchFinish.Providers.UserModelCacheController;
using UnityEngine;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.MatchUserProgress
{
    public class MatchUserProgressContainer : MonoBehaviour
    {
        [SerializeField]
        private WinStreakMatchUserProgressView _winStreak = null!;

        private IDisposable? _trophyPathStateSubscription;
        private IUserContext _userContext = null!;

        public void Initialize(
            IUserContext userContext)
        {
            _userContext = userContext;
        }

        public void Setup(PlayerMatchScreenInfo player)
        {
            RenderWinStreak(player);
        }

        private void RenderWinStreak(PlayerMatchScreenInfo player)
        {
            var isEscapee = player is EscapeePlayerMatchScreenInfo;
            var stats = _userContext.Stats.Value.Value;
            var teamWinStreak = isEscapee ? stats.Escapee.CurrentWinStreak : stats.Catcher.CurrentWinStreak;
            
            var canRenderWinStreak = teamWinStreak > 0;
            _winStreak.gameObject.SetActive(canRenderWinStreak);
            if (canRenderWinStreak)
            {
                _winStreak.Render(teamWinStreak, isEscapee);
            }
        }
    }
}