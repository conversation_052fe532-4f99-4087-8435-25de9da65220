using System;
using App.Components.Contexts;
using App.Localization.Components;
using Core.Extensions;
using Core.Extensions.Buttons;
using Core.Helpers.Navigation;
using Core.UI.Components.Buttons;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using Jx.Utils.UnityComponentExtensions;
using UI.Screens.GameplayScene.MatchFinish.Contexts;
using UI.Screens.GameplayScene.MatchResultSummary;
using UnityEngine;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages
{
    [RequireComponent(typeof(JxContextBoundMonoBehaviour))]
    public abstract class MatchFinishScreenPage : MonoBehaviour,
                                                  IJxLocalizationContext
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(MatchFinishScreenPage));
        
        [field: SerializeField]
        public ButtonComponent ContinueButton { get; private set; } = null!;

        [field: SerializeField]
        public ButtonComponent GoToSummaryButton { get; private set; } = null!;
        
        private JxDisposableAction? _disposeOnExit;
        private bool _initialized;

        private INavigation _navigation = null!;
        private Action _showSummary = null!;
        
        protected PlayerMatchScreenInfoContainer Container { get; private set; } = null!;

        #region LOCALIZATION

        bool IJxLocalizationContext.IsRoot => false;
        public abstract string Key { get; }
        string IJxLocalizationContext.DefaultText => string.Empty;

        #endregion

        public void BaseInitialize(
            INavigation navigation,
            PlayerMatchScreenInfoContainer container,
            Action showSummary)
        {
            _navigation = navigation;
            Container = container;
            _showSummary = showSummary;
        }

        private void OnEnable()
        {
            _disposeOnExit = JxDisposableAction.Build();
            _disposeOnExit.AppendDispose(GoToSummaryButton.OnClick(_showSummary));
            
            _logger.LogIfFalse(_initialized, "Page is not initialized to be attached.");
            
            if (_initialized)
            {
                OnAttached();
            }
        }

        private void OnDisable()
        {
            _disposeOnExit?.Dispose();

            if (_initialized)
            {
                OnDeAttached();   
            }
        }

        protected void MarkInitialized()
        {
            _initialized = true;
        }

        protected abstract void OnAttached();

        protected virtual void OnDeAttached()
        {
        }

        protected void DisposeOnDisable(IDisposable disposable)
        {
            _disposeOnExit ??= JxDisposableAction.Build();
            _disposeOnExit.AppendDispose(disposable);
        }
    }
}