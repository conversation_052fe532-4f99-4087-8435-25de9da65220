using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using Core.Extensions;
using Jx.Utils.Collections;
using Jx.Utils.Coroutines;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.Tasks.Container.Parameters;
using UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.Tasks.Container.ParametersFactories;
using UI.Screens.GameplayScene.MatchFinish.Providers.UserModelCacheController;
using UI.Screens.MainMenuScene.Quests.Components.DailyQuests;
using UnityEngine;
using Zenject;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.Tasks.Container
{
    [RequireComponent(typeof(TaskViewsContainerMatchFinishAnimationController))]
    public class TaskViewsContainerMatchFinish : MonoBehaviour
    {
        [SerializeField]
        private RectTransform _container = null!;

        [SerializeField]
        private TaskViewsContainerMatchFinishAnimationController _animationController = null!;

        private readonly IList<TaskViewMatchFinish> _views = new List<TaskViewMatchFinish>();
        private readonly List<TaskViewMatchFinishParameters> _parameters = new List<TaskViewMatchFinishParameters>();

        private MissionParametersFactoryTaskView _missionParametersFactory = null!;
        private QuestParametersFactoryTaskView _questParametersFactory = null!;
        private TaskViewMatchFinish.Factory _viewFactory = null!;

        private IDisposable? _carouselCancellation;

        [Inject]
        private void Inject(
            TaskViewMatchFinish.Factory viewFactory,
            IUserContext userContext,
            IQuestSlotContextFactory contextFactory
        )
        {
            _viewFactory = viewFactory;
            _missionParametersFactory = new MissionParametersFactoryTaskView(userContext);
            _questParametersFactory = new QuestParametersFactoryTaskView(userContext, contextFactory);
        }

        public bool TryBuildDiffs(PreMatchUserSnapshot preMatchSnapshot)
        {
            if (_questParametersFactory.TryBuild(preMatchSnapshot, out var questParameters))
            {
                _parameters.AddRange(questParameters);
            }

            if (_missionParametersFactory.TryBuild(preMatchSnapshot, out var missionParameters))
            {
                _parameters.AddRange(missionParameters);
            }

            return _parameters.Any();
        }

        public void Setup()
        {
            if (_parameters.IsEmpty())
            {
                return;
            }

            _parameters.ForEach(CreateTask);

            var animationControllers = _views
                                      .Select(t => t.AnimationController)
                                      .ToList();

            _animationController.Setup(animationControllers);
            StartCarousel();
        }

        public void Clear()
        {
            _views.DisposeAndClear();
            _parameters.Clear();
            _animationController.Clear();

            _carouselCancellation?.Dispose();
            _carouselCancellation = null;
        }

        private void CreateTask(TaskViewMatchFinishParameters parameters)
        {
            var view = _viewFactory.Create(_container, parameters);
            _views.Add(view);
        }

        private void StartCarousel()
        {
            _carouselCancellation?.Dispose();
            _carouselCancellation = StartCoroutine(_animationController.CarouselRoutine()).BuildCancellation(this);
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            _animationController = GetComponent<TaskViewsContainerMatchFinishAnimationController>();
        }
#endif
    }
}