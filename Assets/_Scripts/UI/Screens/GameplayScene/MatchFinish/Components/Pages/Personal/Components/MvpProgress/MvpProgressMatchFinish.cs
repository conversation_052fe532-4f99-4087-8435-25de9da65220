using System;
using Api.Client.User;
using Core.Helpers.TweenAnimation;
using Core.Managers.AssetLoader;
using Core.UI.Components.Animation;
using Core.UIReusable.Progress.Cellular.Mvp;
using DG.Tweening;
using Savings;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.MvpProgress
{
    public class MvpProgressMatchFinish : MonoBehaviour
    {
        private const float ShowDurationInSeconds = 0.4f;
        
        [SerializeField]
        private RectTransform _rectTransform = null!;
        
        [SerializeField]
        private MvpProgressBarView _progressBar = null!;

        [SerializeField]
        private Image _chestIcon = null!;

        [SerializeField]
        private FadeAndAnchoredMoveUIAnimationComponent _textMoveAnimation;

        [Header("Animation")]
        [SerializeField]
        private float _anchorPosXOffset = -58.4f;

        private IUserContext _userContext = null!;
        private ILocalSave _localSave = null!;
        private Vector2 _startAnchoredPosition;

        private readonly IAnimationSlot _showAnimationSlot = new AnimationSlot("show");

        private void Awake()
        {
            _startAnchoredPosition = new Vector2(500f, _rectTransform.anchoredPosition.y);
            _rectTransform.anchoredPosition = _startAnchoredPosition;
        }

        public void Initialize(IUserContext userContext, ILocalSave localSave)
        {
            _userContext = userContext;
            _localSave = localSave;
            
            _progressBar.Setup(userContext.Chests.MvpChest.Value.RequiredMvpValue);
            _chestIcon.sprite = JxResourceLoader.Instance.LoadChestSprite(userContext.Chests.MvpChest.Value.ChestDefinition.ContentPath);
        }

        public void Cleanup()
        {
            _progressBar.Cleanup();
            _rectTransform.anchoredPosition = _startAnchoredPosition;
        }
        
        public Tween DoShow()
        {
            var integration = _userContext.Chests.MvpChest.Value;
            _rectTransform.anchoredPosition = _startAnchoredPosition;
            
            if (integration.IsOnCooldown() || integration.CurrentMvpValue <= _localSave.Value.LastMvpValue)
                return DOTween.Sequence();

            _progressBar.SetProgress(_localSave.Value.LastMvpValue);
            
            return _showAnimationSlot.PlayNew(
                DOTween.Sequence()
                    .Append(_rectTransform.DOAnchorPosX(_anchorPosXOffset, ShowDurationInSeconds))
                    .Append(
                        DOTween.Sequence()
                            .Append(_progressBar.DoSetProgress(_userContext.Chests.MvpChest.Value.CurrentMvpValue))
                            .Join(_textMoveAnimation.DoFadeAndMove()
                        )
                    )
            );
        }
    }
}