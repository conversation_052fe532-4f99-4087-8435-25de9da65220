using Core.Extensions;
using Core.Helpers.TweenAnimation;
using Core.Managers.AssetLoader;
using Core.UI.Components.Animation;
using Core.UI.Components.Text;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Personal.Components.MatchUserProgress.WinStreak
{
    public class WinStreakMatchUserProgressView : MonoBehaviour
    {
        private const string IconPathFormat = "profile/win-streaks/{0}"; // {0} - catcher/escapee

        #region SETTINGS

        private readonly Color _escapeeColor = Color.white;
        private readonly Color _catcherColor = new Color(255f, 203f, 0f, 1f).NormalizeRGB();

        #endregion

        [SerializeField]
        private Image _winStreakIcon = null!;

        [SerializeField]
        private JxTextComponent _countText = null!;

        [SerializeField]
        private JxTextComponent _diffText = null!;

        [Header("Animation")]
        [SerializeField]
        private FadeAndAnchoredMoveUIAnimationComponent _diffFadeAndAnchoredMoveAnimation = null!;

        [SerializeField]
        [Min(0f)]
        private float _countCurrentWinStreakAnimationDurationInSeconds = 1f;

        private readonly IAnimationSlot _countCurrentWinStreakAnimationSlot = new AnimationSlot("count-current-win-streak");

        private void OnDisable()
        {
            _diffFadeAndAnchoredMoveAnimation.StopAndReset();
            _countCurrentWinStreakAnimationSlot.CompleteAndStop();
        }

        private void OnDestroy()
        {
            _countCurrentWinStreakAnimationSlot.Destroy();
        }

        public void Render(int winStreak, bool isEscapee)
        {
            RenderIcon(isEscapee);
            RenderValue(winStreak, isEscapee);
            RenderDiff();
        }

        private void RenderIcon(bool isEscapee)
        {
            _winStreakIcon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(string.Format(IconPathFormat, isEscapee ? "escapee" : "catcher"));
        }

        private void RenderValue(int winStreak, bool isEscapee)
        {
            _countText.Color = isEscapee ? _escapeeColor : _catcherColor;

            if (winStreak > 0)
            {
                _countCurrentWinStreakAnimationSlot
                   .PlayNew(
                        _countText
                           .DoCount(winStreak, winStreak - 1, _countCurrentWinStreakAnimationDurationInSeconds)
                           .OnKill(() => _countText.Text = winStreak.ToString())
                    );
            }
            else
            {
                _countText.Text = winStreak.ToString();
            }
        }

        private void RenderDiff()
        {
            _diffText.Text = "+1";
            _diffFadeAndAnchoredMoveAnimation.DoFadeAndMove();
        }
    }
}