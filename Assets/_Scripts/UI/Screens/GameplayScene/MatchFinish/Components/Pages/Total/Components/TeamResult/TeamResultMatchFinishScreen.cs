using System;
using System.Linq;
using Core.Extensions;
using Core.UI.Components.Text;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using UI.Screens.GameplayScene.MatchFinish.Contexts;
using UnityEngine;

namespace UI.Screens.GameplayScene.MatchFinish.Components.Pages.Total.Components.TeamResult
{
    public class TeamResultMatchFinishScreen : MonoBehaviour
    {
        #region SETTINGS

        private static readonly Color _resultColorOnWin = new Color(255f, 224f, 144f, 1).NormalizeRGB();
        private static readonly Color _resultColorOnLose = new Color(255f, 135f, 84f, 1f).NormalizeRGB();
        private static readonly Color _resultColorOnDraw = new Color(197f, 184f, 233f, 1f).NormalizeRGB();
        private static readonly Color _inGameColor = Color.white;

        #endregion

        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(TeamResultMatchFinishScreen));

        [SerializeField]
        private JxTextComponent _escapedCount = null!;

        [SerializeField]
        private JxTextComponent _killedCount = null!;

        [SerializeField]
        private JxTextComponent _resultText = null!;

        private bool _isEnabled;
        private IDisposable? _matchResultSubscription;
        private IDisposable? _teamScoreSubscription;
        private PlayerMatchScreenInfoContainer? _players;
        private IJxChangeableObject<MatchResultType>? _matchResult;

        private void OnEnable()
        {
            _isEnabled = true;

            Render();
        }

        private void OnDisable()
        {
            _isEnabled = false;

            Cleanup();
        }

        public void Initialize(PlayerMatchScreenInfoContainer players, IJxChangeableObject<MatchResultType> result)
        {
            _players = players;
            _matchResult = result;

            if (_isEnabled)
                Render();
        }

        private void Render()
        {
            if (_players == null || _matchResult == null)
                return;

            Cleanup();

            _teamScoreSubscription = InitializeTeamScore(_players);
            _matchResultSubscription = _matchResult.SubscribeAndFire(OnMatchStateChanged);
        }

        private IDisposable InitializeTeamScore(PlayerMatchScreenInfoContainer players)
        {
            var subscriptions = JxDisposableAction.Build();

            foreach (var escapee in players.Escapees)
            {
                subscriptions.AppendDispose(escapee.GameplayState.Subscribe(RenderTeamScore));
            }

            RenderTeamScore();

            return subscriptions;

            void RenderTeamScore()
            {
                var survivedEscapeeCount = players.Escapees.Count(e => e.GameplayState.Value == GameplayPlayerState.IsWinner);
                var killedEscapeeCount = players.Escapees.Count(e => e.GameplayState.Value == GameplayPlayerState.IsLoser);

                _escapedCount.Text = survivedEscapeeCount.ToString();
                _killedCount.Text = killedEscapeeCount.ToString();
            }
        }

        private void OnMatchStateChanged(MatchResultType result)
        {
            switch (result)
            {
                case MatchResultType.Draw:
                    RenderResultText("Draw", _resultColorOnDraw);
                    break;
                case MatchResultType.Victory:
                    RenderResultText("Win", _resultColorOnWin);
                    break;
                case MatchResultType.Defeat:
                    RenderResultText("Lose", _resultColorOnLose);
                    break;
                case MatchResultType.InGame:
                    RenderResultText("InGame", _inGameColor);
                    break;
                default:
                    _logger.LogError($"Unknown client match result type '{result}'");
                    _resultText.DisableLocalization();
                    _resultText.Text = $"ERROR MATCH RESULT: {(byte)result}";
                    break;
            }
        }

        private void RenderResultText(string localizationPostfix, Color color)
        {
            _resultText.EnableLocalization();
            _resultText.SetLocalizationPostfix(localizationPostfix);
            _resultText.Color = color;
        }

        private void Cleanup()
        {
            _matchResultSubscription?.Dispose();
            _teamScoreSubscription?.Dispose();
        }
    }
}