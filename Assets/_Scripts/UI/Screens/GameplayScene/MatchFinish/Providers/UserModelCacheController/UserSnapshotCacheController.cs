using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Missions;
using Api.Client.User.Stats;
using GameplayNetworking.Gameplay.Local;
using LoM.DailyQuests.ClientIntegration;
using LoM.Messaging.ClientIntegrations.Missions;
using LoM.Messaging.ClientIntegrations.Quests;
using MonsterLand.Matchmaking;
using UnityEngine.Scripting;

namespace UI.Screens.GameplayScene.MatchFinish.Providers.UserModelCacheController
{
    [Preserve]
    public class UserSnapshotCacheController : IPreMatchUserSnapshotProvider,
                                               IPreMatchUserSnapshotBuilder
    {
        private readonly IUserContext _userContext;
        private readonly ILocalGameplayContext _localGameplayContext;

        private PreMatchUserSnapshot? _snapshot;

        [Preserve]
        public UserSnapshotCacheController(
            IUserContext userContext,
            ILocalGameplayContext localGameplayContext
        )
        {
            _userContext = userContext;
            _localGameplayContext = localGameplayContext;
        }

        public PreMatchUserSnapshot SaveSnapshot()
        {
            _snapshot = TakeSnapshot();
            return _snapshot;
        }

        public PreMatchUserSnapshot GetSnapshot()
        {
            if (_snapshot == null)
            {
                throw new InvalidOperationException("Snapshot is not found");
            }

            return _snapshot;
        }

        private PreMatchUserSnapshot TakeSnapshot()
        {
            var quests = GetQuests(_userContext.DailyQuests?.UserProgress?.Value);
            var missions = GetMissions(_userContext.Missions.Interaction?.CurrentPack?.Value);

            var bots = _localGameplayContext.SpawnedPlayers;
            
            var trophyPathInteraction = _userContext.TrophyPath.Interaction;
            var trophyPathState = trophyPathInteraction.State.Value;

            return new PreMatchUserSnapshot(
                trophyPathStateCache: new TrophyPathStateCache(
                    currentTrophy: trophyPathState.Current,
                    currentLocalTrophy: trophyPathState.GetCurrentLocalTrophy(),
                    lastPassedLocalThreshold: trophyPathState.GetPassedLevelLocalThreshold(),
                    nextLocalThreshold: trophyPathState.GetNextLevelLocalThreshold(),
                    maxTrophy: trophyPathState.Max,
                    level: trophyPathState.Level
                ),
                quests: quests, 
                missions: missions, 
                escapeeBotBehaviour: GetBotBehaviourSnapshot(JxMatchmakingTeamKind.Escapee),
                playedMatchIndex: _userContext.Stats.Value.Value.GetPlayedMatchCount()
            );

            BotBehaviourSnapshot GetBotBehaviourSnapshot(JxMatchmakingTeamKind team)
            {
                // "not_set" if error or bot behaviour is disabled via debug
                var difficulty = bots.FirstOrDefault(b => b.Team == team && b.BaseSyncData.SharedConfig.Value.HasBotBehaviour())?
                    .BaseSyncData
                    .SharedConfig
                    .Value
                    .BotDifficultyName ?? "not_set";
                
                return new BotBehaviourSnapshot(difficulty);
            }
        }

        private IReadOnlyList<QuestCachedProgress> GetQuests(UserQuestStorageClientIntegration? questStorage)
        {
            var quests = new List<QuestCachedProgress>();

            if (questStorage != null)
            {
                for (var i = 0; i < questStorage.Slots.Count; i++)
                {
                    var slot = questStorage.Slots[i];
                    var progress = questStorage.Progresses[i];

                    if (progress.State == QuestState.WaitingToCompletion)
                    {
                        quests.Add(new QuestCachedProgress(slot.QuestName, progress.CurrentValue));
                    }
                }
            }

            return quests;
        }

        private IReadOnlyList<MissionCachedProgress> GetMissions(IUserMissionPack? currentPack)
        {
            var missions = new List<MissionCachedProgress>();

            if (currentPack != null)
            {
                foreach (var mission in currentPack.Missions)
                {
                    var missionProgress = mission.Progress.Value;
                    if (missionProgress.State == MissionProgressState.RewardClaimed || missionProgress.State == MissionProgressState.Completed)
                    {
                        continue;
                    }

                    missions.Add(new MissionCachedProgress(mission.Definition.Name, missionProgress.Value));
                }
            }

            return missions;
        }
    }
}