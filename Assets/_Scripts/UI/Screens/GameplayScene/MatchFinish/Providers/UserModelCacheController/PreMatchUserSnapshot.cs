using System.Collections.Generic;
using Api.Client.User.Upgrades;
using Jx.Utils.Ensurance;

namespace UI.Screens.GameplayScene.MatchFinish.Providers.UserModelCacheController
{
    public class PreMatchUserSnapshot
    {
        public PreMatchUserSnapshot(
            TrophyPathStateCache trophyPathStateCache,
            IReadOnlyList<QuestCachedProgress> quests,
            IReadOnlyList<MissionCachedProgress> missions,
            BotBehaviourSnapshot escapeeBotBehaviour,
            int playedMatchIndex
        )
        {
            TrophyPathStateCache =  trophyPathStateCache;
            Quests = quests;
            Missions = missions;
            EscapeeBotBehaviour = escapeeBotBehaviour;
            PlayedMatchIndex = playedMatchIndex;
        }

        public TrophyPathStateCache TrophyPathStateCache { get; }
        public BotBehaviourSnapshot EscapeeBotBehaviour { get; }
        public IReadOnlyList<QuestCachedProgress> Quests { get; }
        public IReadOnlyList<MissionCachedProgress> Missions { get; }
        public int PlayedMatchIndex { get; }
    }
}