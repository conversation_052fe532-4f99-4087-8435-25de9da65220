using System.Collections.Generic;
using Core.Attribution;
using Core.UI.Components.Text;
using GameplayNetworking.Gameplay.Player.Components.Statistics;
using UnityEngine;

namespace UI.Screens.GameplayScene.MatchResultSummary.Table
{
    public class TitlesMatchSummaryTable : MonoBehaviour
    {
        [SerializeField]
        [JxComment("Do not provide here player, perks, status titles")]
        private JxTextComponent[] _statTitles = null!;

        public void Render(IReadOnlyList<GameplayStatisticsType> statistics)
        {
            Cleanup();

            for (var i = 0; i < statistics.Count && i < _statTitles.Length; ++i)
            {
                if (i >= _statTitles.Length)
                    return;
                
                var title = _statTitles[i];
                var stat = statistics[i];
                
                // located in global folder
                title.SetLocalizationKey(stat.ToString());
                title.gameObject.SetActive(true);
            }
        }

        private void Cleanup()
        {
            foreach (var title in _statTitles)
            {
                title.gameObject.SetActive(false);
            }
        }
    }
}