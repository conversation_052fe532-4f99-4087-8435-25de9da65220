using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using System.Linq;
using Core.Helpers;
using UnityEngine;
using MonsterLand.Matchmaking;

namespace UI.Screens.GameplayScene.MatchGoal
{
    public class MatchGoalScreenPlayersContainer : MonoBehaviour
    {
        [SerializeField]
        private MatchGoalScreenPlayerView[] _escapeePlayerCards = null!;

        [SerializeField]
        private MatchGoalScreenPlayerView _catcherPlayerCard = null!;
        
        private ILocalGameplayContext _localGameplayContext = null!;

        public void Initialize(
            ILocalGameplayContext localGameplayContext)
        {
            _localGameplayContext = localGameplayContext;

            foreach (var playerInfo in _escapeePlayerCards)
            {
                playerInfo.Initialize(_localGameplayContext);
            }

            _catcherPlayerCard.Initialize(_localGameplayContext);
        }

        public void Setup()
        {
            if (_localGameplayContext.SpawnedPlayers.IsNullOrEmpty())
                return;
            
            var escapees = _localGameplayContext.SpawnedPlayers
                .Where(p => p.IsEs<PERSON>())
                .ToArray();
            
            if (escapees.IsNullOrEmpty())
                return;

            for (var i = 0; i < _escapeePlayerCards.Length; ++i)
            {
                if (i >= escapees.Length) 
                    break;

                var view = _escapeePlayerCards[i];
                var dto = escapees.ElementAtOrDefault(i)?.BaseSyncData;

                RenderPlayer(dto, view, JxMatchmakingTeamKind.Escapee);
            }
            
            RenderPlayer(
                _localGameplayContext.SpawnedPlayers.FirstOrDefault(a => a.IsCatcher())?.BaseSyncData,
                _catcherPlayerCard,
                JxMatchmakingTeamKind.Catcher
            );
        }

        private static void RenderPlayer(NetGameplayPlayerSyncData? dto, MatchGoalScreenPlayerView view, JxMatchmakingTeamKind team)
        {
            var canRender = ReferenceEquals(dto, null) == false;
            view.gameObject.SetActive(canRender);
            if (canRender)
                view.Render(dto!, team);
        }
    }
}