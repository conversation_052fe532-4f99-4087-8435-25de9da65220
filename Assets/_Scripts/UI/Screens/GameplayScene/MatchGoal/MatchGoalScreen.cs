using Core.Helpers.Navigation.View;
using Core.UI.Components.Text;
using UI.Screens.GameplayScene.MatchGoal.Components;
using UI.Screens.MainMenuScene.MainMenu.Components.GameMode;
using UnityEngine;

namespace UI.Screens.GameplayScene.MatchGoal
{
    [ScreenController(typeof(MatchGoalScreenController))]
    public class MatchGoalScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public JxTextComponent HeaderText { get; private set; } = null!;

        [field: SerializeField]
        public JxTextComponent GoalText { get; private set; } = null!;
        
        [field: SerializeField]
        public JxTextComponent AlertText { get; private set; } = null!;

        [field: SerializeField]
        public CountdownMatchGoalView Countdown { get; private set; } = null!;

        [field: SerializeField]
        public MatchGoalGameModeView MatchGoalGameModeRenderer { get; private set; } = null!;

        [field: SerializeField]
        public MatchGoalScreenPlayerView[] EscapeePlayerCards { get; private set; } = null!;

        [field: SerializeField]
        public MatchGoalScreenPlayerView CatcherPlayerCard { get; private set; } = null!;

        [field: SerializeField]
        public MatchGoalScreenAnimationController ScreenAnimationController { get; private set; } = null!;

        [field: SerializeField]
        public MatchGoalScreenPlayersContainer PlayersContainer { get; private set; } = null!;
    }
}