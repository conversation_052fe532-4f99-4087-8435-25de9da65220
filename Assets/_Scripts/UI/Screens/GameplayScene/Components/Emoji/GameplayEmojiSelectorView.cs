using Core.Helpers;
using Core.Managers.AssetLoader;
using Core.UI.Components.Buttons;
using GameplayNetworking.Gameplay.Local;
using Jx.Utils.Coroutines;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using LoM.Emojis.ClientIntegration;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI.Screens.GameplayScene.Components.Emoji
{
    public class GameplayEmojiSelectorView : MonoBehaviour
    {
        private const string SectorPath = "Screens/GameplayScene/Gameplay/Components/EmojiSelectorSector";

        [SerializeField]
        private RoundSelectorComponent _selector = null!;

        [SerializeField]
        private RectTransform _sectorsContainer = null!;
        
        [SerializeField]
        private ButtonComponent _openButton = null!;

        private IDisposable? _clickSubscription;
        private IDisposable? _pointerDownRoutine;
        private EmojiType? _lastEmoji;
        
        private ILocalGameplayPlayer _player = null!;

        private bool _isCreated;

        public void Initialize(ILocalGameplayPlayer player)
        {
            _player = player;
        }

        private void TryCreateSelector()
        {
            if (_isCreated)
                return;

            var emojiDtos = _player.SyncData.EmojiSlotsReadOnly;
            if (emojiDtos.IsNullOrEmpty())
                return;

            var prefab = JxResourceLoader.Instance.LoadPrefab<EmojiRoundSelectorSectorComponent>(SectorPath)
                ?? throw new NullReferenceException(nameof(EmojiRoundSelectorSectorComponent));

            var availableSectors = new List<EmojiRoundSelectorSectorComponent>();

            foreach (var emoji in emojiDtos)
            {
                var view = Instantiate(prefab, _sectorsContainer, false);
                view.gameObject.SetActive(true);
                view.SetEmoji(emoji);
                availableSectors.Add(view);
            }

            _selector.Initialize(availableSectors);
            
            _isCreated = true;
        }

        public void Setup()
        {
            TryCreateSelector();
            _clickSubscription = _openButton.SubscribeToPointerDown(OnPointerDown);
        }

        public void Cleanup()
        {
            _pointerDownRoutine?.Dispose();
            _clickSubscription?.Dispose();
        }

        private void OnPointerDown(PointerEventData eventData)
        {
            var fingerId = -1;
            for (var i = 0; i < Input.touchCount; ++i)
            {
                var touch = Input.GetTouch(i);
                if (touch.phase == TouchPhase.Began)
                {
                    fingerId = touch.fingerId;
                }
            }

            if (fingerId < 0)
                return;
            
            _selector.SetVisible(true, fingerId);

            _pointerDownRoutine?.Dispose();
            _pointerDownRoutine = StartCoroutine(WhilePointerDown(fingerId)).BuildCancellation(this);
        }

        private IEnumerator WhilePointerDown(int fingerId)
        {
            while (true)
            {
                var fingerUp = fingerId >= Input.touchCount || Input.GetTouch(fingerId).phase == TouchPhase.Ended;
                if (fingerUp)
                {
                    var casted = _selector.SelectedSector as EmojiRoundSelectorSectorComponent;
                    SendEmoji(casted == null ? null : casted.Emoji);
                    
                    _selector.SetVisible(false);
                    yield break;
                }

                yield return null;
            }
        }

        private void SendEmoji(EmojiType? emojiType)
        {
            if (emojiType.HasValue)
            {
                _lastEmoji = emojiType.Value;
                SendEmojiInternal(emojiType.Value);
                return;
            }

            if (_lastEmoji.HasValue)
            {
                SendEmojiInternal(_lastEmoji.Value);
                return;
            }

            var slots = _player.SyncData.EmojiSlotsReadOnly;
            if (slots.IsNullOrEmpty())
                return;
            var firstEmoji = _player.SyncData.EmojiSlotsReadOnly.First();
            SendEmojiInternal(firstEmoji);
        }

        private void SendEmojiInternal(EmojiType emojiType) => _player.SyncData.Cmd_SendEmoji(emojiType);
    }
}