using System;
using System.Collections.Generic;
using App.Inventory.Item;
using GameplayNetworking.Gameplay.Inventory;

namespace UI.Screens.GameplayScene.Components.InventoryItem
{
    public interface IInventoryView
    {
        IDisposable SubscribeToUse(Action<InventoryItemSlot> onItemUse);
        void Render(IReadOnlyList<InventoryItemInfo> items);
        void SetInteractable(bool isInteractable);
    }
}