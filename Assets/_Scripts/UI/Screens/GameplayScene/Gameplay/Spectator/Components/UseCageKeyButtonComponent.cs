using Core.Extensions;
using Core.UI.Components.Buttons;
using UI.Components.Buttons.Components.Indicator;
using UnityEngine;

namespace UI.Screens.GameplayScene.Gameplay.Spectator.Components
{
    public class UseCageKeyButtonComponent : ButtonComponent
    {
        [SerializeField]
        private ButtonIndicatorComponent _countIndicator = null!;

        public void Render(int keyCount)
        {
            _countIndicator.SetBoundedCount(keyCount, 3);
        }
    }
}