using System;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using Jx.Utils.Helpers;
using Jx.Utils.Logging;
using UnityEngine;

namespace UI.Screens.GameplayScene.Gameplay.Spectator.ModeViews
{
    public abstract class SpectatorModeView : MonoBehaviour
    {
        private JxDisposableAction? _disposeOnExit;

        protected IJxLogger Logger { get; private set; } = null!;
        protected NetEscapeePlayer Player { get; private set; } = null!;
        protected ILocalGameplayContext GameplayContext { get; private set; } = null!;

        private void OnEnable()
        {
            _disposeOnExit = JxDisposableAction.Build();
            OnAttached();
        }

        private void OnDisable()
        {
            _disposeOnExit?.Dispose();
            _disposeOnExit = null;

            OnDeAttached();
        }

        protected void BaseInitialize(
            ILocalGameplayContext localGameplayContext
        )
        {
            GameplayContext = localGameplayContext;
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);

            if (GameplayContext.Player is NetEscapeePlayer player)
            {
                Player = player;
            }
        }

        protected void DisposeOnDeAttach(IDisposable disposable)
        {
            _disposeOnExit!.AppendDispose(disposable);
        }

        protected abstract void OnAttached();
        protected virtual void OnDeAttached() { }
    }
}