using System;
using System.Collections.Generic;
using Core.Helpers;
using Core.Helpers.TweenAnimation;
using Core.Managers.AssetLoader;
using DG.Tweening;
using GameplayNetworking.Gameplay.Player;
using GameplayNetworking.Gameplay.Player.Escapee;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Ensurance;
using UnityEngine;

namespace UI.Screens.GameplayScene.Gameplay.PlayerInfo
{
    public class EscapeeHealthProgressBarView : MonoBehaviour
    {
        private const string Path = "Screens/GameplayScene/Gameplay/Components/EscapeeHealthBar";
        
        [SerializeField]
        private RectTransform _container = null!;

        private EscapeeHealthBarView? _prefab;
        private NetEscapeePlayer? _target;

        private IList<EscapeeHealthBarView> _views = null!;

        private IDisposable? _healthSubscription;
        private bool _initialized;

        private byte _maxHealth;

        public void Initialize(byte maxHealth)
        {
            _maxHealth = maxHealth;
            
            _prefab ??= JxResourceLoader.Instance.LoadPrefab<EscapeeHealthBarView>(Path);
            Ensure.NotNull(_prefab, nameof(_prefab));
            
            _views = new List<EscapeeHealthBarView>(maxHealth);
            for (var i = 0; i < maxHealth; ++i)
            {
                var view = Create();
                _views.Add(view);
            }

            _initialized = true;
        }

        public void RenderMimicry()
        {
            if (!_initialized)
                return;
            
            Cleanup();
            gameObject.SetActive(true);
            RenderHealthWhenMimicry();
        }

        public void Setup(NetEscapeePlayer target)
        {
            if (!_initialized)
                return;
            if (ReferenceEquals(_target, target))
                return;
            
            Cleanup();
            
            gameObject.SetActive(true);
            _target = target;
            var health = _target.SyncData.Health;
            
            _healthSubscription = health.Subscribe(OnHealthChanged);
            RenderHealth(health.Value.Current, health.Value.Previous, instantly: true);
        }
        
        public void Cleanup()
        {
            _target = null;
            gameObject.SetActive(false);
            _healthSubscription?.Dispose();

            if (_views.IsNullOrEmpty())
                return;
            
            foreach (var view in _views)
                view.CleanupAnimation();
        }

        private void OnHealthChanged(NetEscapeeHealthDto dto) => RenderHealth(dto.Current, dto.Previous, instantly: false);
        private void RenderHealthWhenMimicry() => RenderHealth(_maxHealth, _maxHealth, true);
        
        private void RenderHealth(byte current, byte previous, bool instantly)
        {
            foreach (var view in _views)
                view.CleanupAnimation();
            
            if (instantly || current == previous)
            {
                for (var i = 0; i < _views.Count; ++i)
                    _views[i].SetIsFilledInstantly(current - 1 >= i);
                
                return;
            }
            
            var wasHealed = current > previous;
            
            for (var i = 0; i < _views.Count; ++i)
            {
                var view = _views[i];
                
                if (wasHealed)
                {
                    if (i == current - 1)
                        view.DoSetIsFilled(true);
                    else
                        view.SetIsFilledInstantly(i <= current - 1);
                }
                else
                {
                    if (i == previous - 1)
                        view.DoSetIsFilled(false);
                    else
                        view.SetIsFilledInstantly(i <= current - 1);
                }
            }
        }
        
        private EscapeeHealthBarView Create()
        {
            var instance = Instantiate(_prefab, _container, false)!;
            instance.gameObject.SetActive(true);
            return instance;
        }
    }
}