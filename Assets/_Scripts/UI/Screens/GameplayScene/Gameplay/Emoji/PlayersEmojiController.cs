using System;
using System.Collections.Generic;
using Core.Helpers.Camera;
using Core.Managers.AssetLoader;
using Core.UIReusable.Timer.TimeProviders;
using GameplayNetworking.Gameplay.Local;
using GameplayNetworking.Gameplay.Player;
using UnityEngine;

namespace UI.Screens.GameplayScene.Gameplay.Emoji
{
    public class PlayersEmojiController : MonoBehaviour
    {
        private const string PathToPrefab = "Screens/GameplayScene/Gameplay/Components/EmojiVisualizer";
        
        [SerializeField]
        private RectTransform _container = null!;
        
        private IReadOnlyList<PlayerEmojiVisualizer> _visualizers = null!;
        private IReadOnlyList<NetGamePlayer> _players = null!;
        private IGameplayCameraController _gameplayCameraController = null!;
        private bool _created;
        
        private void TryCreate()
        {
            if (_created)
                return;
            
            var views = new List<PlayerEmojiVisualizer>();
            var prefab = JxResourceLoader.Instance.LoadPrefab<PlayerEmojiVisualizer>(PathToPrefab) ?? throw new NullReferenceException();
            foreach (var player in _players)
            {
                var view = Instantiate(prefab, _container, false);
                view.Initialize(player, NetworkTimeTimeProvider.Instance, _gameplayCameraController);
                view.gameObject.SetActive(true);
                views.Add(view);
            }
            _visualizers = views;
            _created = true;
        }

        private void OnDestroy()
        {
            foreach (var visualizer in _visualizers)
            {
                visualizer.Cleanup();
                Destroy(visualizer.gameObject);
            }
        }

        public void Initialize(ILocalGameplayContext localGameplayContext)
        {
            _players = localGameplayContext.SpawnedPlayers;
            _gameplayCameraController = localGameplayContext.CameraController;
        }

        public void Setup()
        {
            TryCreate();
            
            foreach (var visualizer in _visualizers)
                visualizer.Setup();
        }
        
        public void Cleanup()
        {
            foreach (var visualizer in _visualizers)
                visualizer.Cleanup();
        }
    }
}