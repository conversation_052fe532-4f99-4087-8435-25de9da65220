using Core.UI.Components.Buttons;
using UI.SceneScreens.Gameplay.Components.BuffContainer;
using UI.SceneScreens.Gameplay.Components.CatcherRage;
using UI.Screens.Common.Gameplay.InGame;
using UI.Screens.GameplayScene.Components.Emoji;
using UI.Screens.GameplayScene.Components.PerksStates;
using UI.Screens.GameplayScene.Gameplay.Components.MatchAlert;
using UI.Screens.GameplayScene.Gameplay.Components.Score;
using UnityEngine;

namespace UI.Screens.GameplayScene.Gameplay.InGame
{
    public class GameplayInGameSubScreenView : InGameSubScreenView
    {
        [field: SerializeField]
        public ButtonComponent PauseButton { get; private set; } = null!;
        
        [field: SerializeField]
        public GameplayEmojiSelectorView EmojiSelector { get; private set; } = null!;
        
        [field: SerializeField]
        public PerksStatesContainer PerksStatesContainer { get; private set; } = null!;

        [field: SerializeField]
        public MatchAlertView Alert { get; private set; } = null!;

        [field: SerializeField]
        public ScoreContainer Score { get; private set; } = null!;

        [field: SerializeField]
        public CatcherRageIndicatorView CatcherRageIndicatorView { get; private set; } = null!;

        [field: SerializeField]
        public BuffViewsContainer BuffsContainer { get; private set; } = null!;
    }
}