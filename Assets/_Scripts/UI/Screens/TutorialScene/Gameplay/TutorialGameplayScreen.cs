using Core.Helpers.Navigation.Containers;
using Core.Helpers.Navigation.View;
using SceneLogics.GameplayScene.Client.VignetteController;
using UnityEngine;

namespace UI.Screens.TutorialScene.Gameplay
{
    [ScreenController(controller: typeof(TutorialGameplayScreenController))]
    public class TutorialGameplayScreen : NavigationScreenContainerScreen
    {
        [field: SerializeField]
        public VignetteController VignetteController { get; private set; } = null!;
        
        [SerializeField]
        private TutorialGameplayInGameSubScreenView _inGame = null!;

        public TutorialGameplayInGameSubScreenView InGame => _inGame;
    }
}