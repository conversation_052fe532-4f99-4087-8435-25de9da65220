using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using App.Localization.Providers;
using Configs.Entities;
using Core.Helpers;
using Core.UI.Toasts;
using GameplayNetworking.Share.Character;
using Jx.Utils.UnityComponentExtensions;
using UI.Components.TabsContainer;
using UI.Screens.MainMenuScene.UserProfile.Characters.Card;
using UnityEngine;

namespace UI.Screens.MainMenuScene.UserProfile.Characters
{
    public class CharactersNavigationUserProfile : MonoBehaviour
    {
        [SerializeField]
        private CharacterCollectionTabsContainer _tabs = null!;

        [SerializeField]
        private CharactersContainerUserProfile _charactersContainer = null!;
        
        private IReadOnlyList<UserProfileCharacterSnapshot> _characters = null!;
        
        private IDisposable? _escapeeTabSubscription;
        private IDisposable? _catcherTabSubscription;
        
        private bool _lastSelectedSectionCatcher;

        private void OnEnable()
        {
            _escapeeTabSubscription = _tabs.EscapeeTabButton.OnClick(() => SelectSection(CharacterType.Escapee));
            _catcherTabSubscription = _tabs.CatcherTabButton.OnClick(() => SelectSection(CharacterType.Catcher));
        }

        private void OnDisable()
        {
            _escapeeTabSubscription?.Dispose();
            _catcherTabSubscription?.Dispose();
        }

        public void Initialize(
            UnlockedCharacterCardUserProfile.Factory unlockedCharacterFactory,
            LockedCharacterCardUserProfile.Factory lockedCharacterFactory,
            IUserContext userContext)
        {
            _charactersContainer.Initialize(
                unlockedCharacterFactory, 
                lockedCharacterFactory, 
                userContext
            );
        }
        
        public void Setup(IReadOnlyList<UserProfileCharacterSnapshot> characters)
        {
            // network error
            if (characters.IsNullOrEmpty())
            {
                ToastManager.Instance.ShowError();
                CleanupRenderer();
                return;
            }

            _characters = characters;
            RenderCurrentSection();
        }

        public void Cleanup()
        {
            CleanupRenderer();
        }

        private void CleanupRenderer()
        {
            _charactersContainer.Cleanup();
        }

        private void RenderCurrentSection() => SelectSection(_lastSelectedSectionCatcher ? CharacterType.Catcher : CharacterType.Escapee);

        private void SelectSection(CharacterType characterType)
        {
            CleanupRenderer();
            
            var isEscapeeSection = characterType == CharacterType.Escapee;
            _lastSelectedSectionCatcher = !isEscapeeSection;
            
            var totalCount = isEscapeeSection ? EntityRepository.Instance.GetEscapees().Count : EntityRepository.Instance.GetCatchers().Count;
            _charactersContainer.Setup(_characters.Where(c => Filter(c.CharacterIndex)).ToList(), totalCount, isEscapeeSection);
            
            var tabToSelect = isEscapeeSection ? _tabs.EscapeeTabButton : _tabs.CatcherTabButton;
            _tabs.Select(tabToSelect);
            
            return;

            bool Filter(int characterIndex)
            {
                return isEscapeeSection ? 
                    CharacterTypeHelper.IsEscapee(characterIndex) :
                    CharacterTypeHelper.IsCatcher(characterIndex);
            }
        }
    }
}