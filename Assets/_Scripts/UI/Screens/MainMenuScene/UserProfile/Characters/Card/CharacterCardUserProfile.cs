using System;
using Core.Managers.AssetLoader;
using Jx.Utils.Logging;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace UI.Screens.MainMenuScene.UserProfile.Characters.Card
{
    public abstract class CharacterCardUserProfile : MonoBehaviour,
                                                     IPoolable<ICharacterCardUserProfileParameters, IMemoryPool>,
                                                     IDisposable
    {
        private const string IconPlacement = "rectangle";

        [SerializeField]
        private Image _characterIcon = null!;

        private IMemoryPool? _pool;

        protected IJxLogger Logger { get; private set; } = null!;

        protected virtual void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        public void Dispose()
        {
            _pool?.Despawn(this);
            _pool = null;
        }

        void IPoolable<ICharacterCardUserProfileParameters, IMemoryPool>.OnDespawned()
        {
            gameObject.SetActive(false);
            OnDeSpawnedInternal();
        }

        void IPoolable<ICharacterCardUserProfileParameters, IMemoryPool>.OnSpawned(ICharacterCardUserProfileParameters parameters, IMemoryPool pool)
        {
            _pool = pool;
            
            _characterIcon.sprite = JxResourceLoader.Instance.LoadCharacterIcon(IconPlacement, parameters.CharacterIndex);
            OnSpawnedInternal(parameters);
        }

        protected abstract void OnSpawnedInternal(ICharacterCardUserProfileParameters parameters);
        protected abstract void OnDeSpawnedInternal();
    }
}