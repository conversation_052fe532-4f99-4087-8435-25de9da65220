using System.Collections.Generic;

namespace UI.Screens.MainMenuScene.UserProfile
{
    public class UserProfileSnapshot
    {
        public UserProfileSnapshot(
            string publicId, 
            string nickname, 
            uint maxTrophy,
            int currentTrophy,
            int profileLevel, 
            uint mvpCount, 
            uint escapeeVictories,
            uint catcherVictories, 
            uint completedQuests,
            uint escapeeWinStreak, 
            uint catcherWinStreak, 
            IReadOnlyList<UserProfileCharacterSnapshot> unlockedCharacters)
        {
            PublicId = publicId;
            Nickname = nickname;
            MaxTrophy = maxTrophy;
            ProfileLevel = profileLevel;
            MvpCount = mvpCount;
            EscapeeVictories = escapeeVictories;
            CatcherVictories = catcherVictories;
            CompletedQuests = completedQuests;
            EscapeeWinStreak = escapeeWinStreak;
            CatcherWinStreak = catcherWinStreak;
            UnlockedCharacters = unlockedCharacters;
            CurrentTrophy = currentTrophy;
        }

        public string PublicId { get; }
        public string Nickname { get; }
        public uint MaxTrophy { get; }
        public int CurrentTrophy { get; }
        public int ProfileLevel { get; }

        public uint MvpCount { get; }
        public uint EscapeeVictories { get; }
        public uint CatcherVictories { get; }
        public uint CompletedQuests { get; }
        
        public uint EscapeeWinStreak { get; }
        public uint CatcherWinStreak { get; }
        
        public IReadOnlyList<UserProfileCharacterSnapshot> UnlockedCharacters { get; }
    }
}