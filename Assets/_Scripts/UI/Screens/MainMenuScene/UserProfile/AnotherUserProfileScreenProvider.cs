using System;
using System.Linq;
using Jx.MonsterLand.UserDatabase.Dto;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Observable;
using LoM.Characters.ClientIntegration.Perks;
using LoM.Messaging.Public;
using MonsterLand.Meta.Api;

namespace UI.Screens.MainMenuScene.UserProfile
{
    public class AnotherUserProfileScreenProvider : IUserProfileScreenProvider
    {
        private readonly JxChangeableObject<UserProfileSnapshot?, UserProfileSnapshot?> _userData = new();
        private readonly IDisposable _changeSubscription;

        public AnotherUserProfileScreenProvider(
            string publicId,
            IJxMonsterLandMetaApi metaApi,
            bool showAddFriendButton = true
        )
        {
            ShowAddFriendButton = showAddFriendButton;

            _changeSubscription = metaApi.ExtendedPublicInfo.GetRecord(publicId).Info.SubscribeAndFire(OnInfoChange);
        }

        public IJxChangeableObject<UserProfileSnapshot?> UserData => _userData;
        public bool ShowAddFriendButton { get; }

        public void Dispose()
        {
            _changeSubscription.Dispose();
        }

        private void OnInfoChange(JxUserExtendedPublicInfoClientIntegration? extendedPublicInfo)
        {
            if (extendedPublicInfo?.PublicInfo == null)
                return;

            _userData.Set(ToSnapshot(extendedPublicInfo.PublicInfo, extendedPublicInfo));
        }

        private UserProfileSnapshot ToSnapshot(JxUserPublicInfoDto publicInfo, JxUserExtendedPublicInfoClientIntegration extendedPublicInfo)
        {
            var id = publicInfo.Identifier.PublicId;
            var nickName = publicInfo.Nickname;
            var maxTrophy = extendedPublicInfo.Stats.MaxTrophy;
            var currentTrophy = publicInfo.Trophies;
            var profileLevel = publicInfo.ProfileLevel;
            var mvpCount = extendedPublicInfo.Stats.Escapee.MvpCount + extendedPublicInfo.Stats.Catcher.MvpCount;
            var escapeeVictories = extendedPublicInfo.Stats.Escapee.WinCount;
            var catcherVictories = extendedPublicInfo.Stats.Catcher.WinCount;
            var completedQuests = extendedPublicInfo.Stats.CompletedQuests;
            var escapeeWinStreak = extendedPublicInfo.Stats.Escapee.MaxWinStreak;
            var catcherWinStreak = extendedPublicInfo.Stats.Catcher.MaxWinStreak;

            var unlockedCharacters =
                extendedPublicInfo.Progress.Characters.List
                    .Select(
                        c => new UserProfileCharacterSnapshot(
                            characterIndex: c.CharacterIndex,
                            characterLevel: c.Level,
                            characterTrophy: c.Trophy,
                            attachedPerks: c.EquippedPerks.Select(p => (PerkType)p).ToList()
                        )
                    )
                    .ToList();

            return new UserProfileSnapshot(
                publicId: id,
                nickname: nickName,
                maxTrophy: maxTrophy,
                currentTrophy: currentTrophy,
                profileLevel: profileLevel,
                mvpCount: mvpCount,
                escapeeVictories: escapeeVictories,
                catcherVictories: catcherVictories,
                completedQuests: completedQuests,
                escapeeWinStreak: escapeeWinStreak,
                catcherWinStreak: catcherWinStreak,
                unlockedCharacters: unlockedCharacters
            );
        }
    }
}