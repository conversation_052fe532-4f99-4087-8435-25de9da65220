using System;
using Api.Client.User;
using Jx.Utils.ChangeableObjects;
using UI.Components.Buttons.Components.Indicator;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Shop
{
    public class ShopScreenIndicatorsController : MonoBehaviour
    {
        [SerializeField]
        private ButtonIndicatorsController _rewardsController;
        
        private IUserContext _userContext = null!;

        private IDisposable? _dailyBonusSubscription;
        
        [Inject]
        private void Inject(IUserContext userContext)
        {
            _userContext = userContext;
        }

        private void OnEnable()
        {
            SetupIndicators();
        }

        private void OnDisable()
        {
            _dailyBonusSubscription?.Dispose();
            _dailyBonusSubscription = null;
        }

        private void SetupIndicators()
        {
            _dailyBonusSubscription = _userContext.DailyBonus.Progress.SubscribeAndFire(RenderRewardsIndicator);
        }

        private void RenderRewardsIndicator()
        {
            var availableDailyBonus = _userContext.DailyBonus.Progress.Value.Available;
            
            _rewardsController.TextIndicator.SetActive(availableDailyBonus);

            if (availableDailyBonus)
            {
                _rewardsController.TextIndicator.StyleApplier.Apply(ButtonIndicatorStyle.RedAlert);
                _rewardsController.TextIndicator.SetLocalizationPostfix(ButtonIndicatorLocalizationKeys.New);
            }
        }
    }
}