using UI.Screens.MainMenuScene.Offer.Helpers;

namespace UI.Screens.MainMenuScene.Offer.Components.View.Concrete
{
    public class PackShopOfferView : ShopOfferView
    {
        protected override void OnSetup()
        {
            var rewardOfferInfos = OfferRewardHelper.GetRewardInfos(
                Parameters.Offer.Reward.Items);
            
            SetupRewards(rewardOfferInfos);
            Poster.Render(Parameters.Offer.Reward);
            SetupInfoButton();
        }
    }
}