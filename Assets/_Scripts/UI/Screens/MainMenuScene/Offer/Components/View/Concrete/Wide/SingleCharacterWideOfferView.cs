using System;
using System.Linq;
using Api.Client.User.Rewards.Adapters;
using Api.Entities.Characters;
using App.Localization.Providers;
using Core.Helpers.Navigation;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Jx.Utils.Logging;
using Jx.Utils.UnityComponentExtensions;
using UI.Screens.MainMenuScene.Characters;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Offer.Components.View.Concrete.Wide
{
    public class SingleCharacterWideOfferView : BaseCharacterWideOfferView
    {
        [SerializeField]
        private JxTextComponent _characterNameText = null!;

        [SerializeField]
        private ButtonComponent _infoButton = null!;
        
        private IDisposable? _infoButtonSubscription;
        private CharacterGameEntity? _characterEntity;

        public override void Dispose()
        {
            base.Dispose();
            Cleanup();
        }

        protected override void OnSetup()
        {
            var reward = Parameters.Offer.Reward.Items.FirstOrDefault();

            if (reward == null)
            {
                Logger.LogError("Offer reward's not found");
                return;
            }

            if (reward is not EntityUserReward entityReward)
            {
                Logger.LogError($"Not allowed reward type '{reward.GetType().Name}'");
                return;
            }

            if (entityReward.Entity is not CharacterGameEntity characterEntity)
            {
                Logger.LogError($"Not allowed entity reward type '{entityReward.Entity.GetType().Name}'");
                return;
            }

            _characterEntity = characterEntity;
            
            var characterIndex = _characterEntity.Index;
            
            var localization = LocalizationKeyProvider.Instance.GetForCharacter(characterIndex);
            _characterNameText.SetLocalizationKey(localization.NameKey);
            
            SetupCharacterRenderer(characterIndex);
            
            _infoButtonSubscription?.Dispose();
            _infoButtonSubscription = _infoButton.OnClick(OnInfoButtonClick);
            StyleApplier.RenderForCharacterOffer(characterIndex);
        }

        private void OnInfoButtonClick()
        {
            Navigation.Navigate<CharactersScreen>(CharacterScreenParametersBuilder.Upgrades(_characterEntity!.Index));
        }

        private void Cleanup()
        {
            _infoButtonSubscription?.Dispose();
            _characterEntity = null;
        }
    }
}