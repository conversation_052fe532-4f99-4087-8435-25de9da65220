using System;
using Core.Helpers;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Core.UIReusable.Timer;
using Jx.Utils.Helpers;
using Jx.Utils.Threading;
using UI.Screens.MainMenuScene.Offer.Components.View.Base;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Offer.Components.View.Concrete.Icon
{
    public class IconOfferView : MonoBehaviour,
                                 IOfferView
    {
        [SerializeField]
        private ButtonComponent _button = null!;
        
        [Header("Optional")]
        [SerializeField]
        private UITimerComponent? _availabilityTimer;
        
        [SerializeField]
        private JxTextComponent? _profitText;

        [field: SerializeField]
        public RectTransform Transform { get; private set; } = null!;

        public Button.ButtonClickedEvent ClickedEvent => _button.onClick;
        public bool IsReleased => _isReleased.IsSet;
        public ButtonComponent? ExitButton { get; } = null;
        public OfferViewParameters Parameters { get; private set; } = null!;

        private readonly JxAtomicFlag _isReleased = new JxAtomicFlag(true);

        private IDisposable? _timerCancellation;
        
        private void Awake()
        {
            if (!_availabilityTimer.IsNullObj())
            {
                _availabilityTimer!.SetExpiredTextLocalizationPostfix("LastChance");
            }
        }

        private void OnDestroy()
        {
            Dispose();
        }

        public void Dispose()
        {
            _isReleased.TrySet();
            _timerCancellation?.Dispose();
            gameObject.SetActive(false);
        }

        public void Setup(OfferViewParameters? parameters)
        {
            if (parameters.IsNullObj())
            {
                Dispose();
                return;
            }

            if (!_isReleased.TryReset())
            {
                return;
            }

            Parameters = parameters!;
            
            gameObject.SetActive(true);
            transform.SetParent(null, worldPositionStays: false);
            
            SetupTimer();

            if (!_profitText.IsNullObj())
            {
                _profitText!.Text = $"x{Parameters.Offer.Profit}";
            }
        }
        
        private void SetupTimer()
        {
            if (_availabilityTimer.IsNullObj())
            {
                return;
            }

            _availabilityTimer!.gameObject.SetActive(false);

            var expirationDate = Parameters.Offer.Expiration.GetExpirationDate();
            if (expirationDate == null)
            {
                return;
            }

            _availabilityTimer.gameObject.SetActive(true);

            _timerCancellation?.Dispose();
            _timerCancellation = _availabilityTimer.Begin(expirationDate.Value - DateTime.UtcNow);
        }

        public IDisposable SubscribeOnPurchase(Action _)
        {
            return JxDisposableAction.Empty;
        }
    }
}