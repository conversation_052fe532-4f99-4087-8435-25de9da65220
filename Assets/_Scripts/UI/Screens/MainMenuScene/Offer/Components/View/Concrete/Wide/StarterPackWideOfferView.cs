using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User.Rewards.Adapters;
using Api.Entities;
using Api.Entities.Skins;
using App.Localization.Providers;
using Configs.EntityRenderer;
using Core.Helpers;
using Core.UI.Components.RenderTexture.Factory;
using Core.UI.Components.RenderTexture.Renderers;
using Core.UI.Components.Text;
using Jx.Utils.Logging;
using UI.Screens.MainMenuScene.Offer.Components.View.RewardView;
using UI.Screens.MainMenuScene.Offer.Helpers;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Offer.Components.View.Concrete.Wide
{
    public class StarterPackWideOfferView : WideOfferView
    {
        [SerializeField]
        private CharacterImage _characterImage = null!;
        
        [SerializeField]
        private SkinNameContainer _skinNameContainer;

        protected override void OnSetup()
        {
            var entityRewards = Parameters.Offer.Reward.Items.OfType<EntityUserReward>().ToArray();

            if (entityRewards.IsNullOrEmpty())
            {
                Logger.LogError($"Offer '{Parameters.Offer.Identifier.OfferName}' does not support non-entity reward packs");
                return;
            }
            
            SkinGameEntity? skinEntityReward = null;

            foreach (var reward in entityRewards)
            {
                switch (reward.Entity)
                {
                    case SkinGameEntity skinGameEntity:
                        skinEntityReward = skinGameEntity;
                        break;
                }
            }

            RenderSkin(skinEntityReward);
            SetupRewards(GetRewardsInfosForCards());
        }

        private IReadOnlyList<OfferRewardDisplayInfo> GetRewardsInfosForCards()
        {
            var targetRewards = Parameters.Offer.Reward.Items.Where(FilterCardReward).ToArray();
            return OfferRewardHelper.GetRewardInfos(targetRewards);
        }
        
        private static bool FilterCardReward(IJxUserReward reward)
        {
            if (reward is EntityUserReward entityUserReward)
            {
                return entityUserReward.Entity is not SkinGameEntity;
            }

            return true;
        }

        private void RenderSkin(SkinGameEntity? skinEntityReward)
        {
            if (_skinNameContainer.IsNull())
            {
                return;
            }
            
            var canRenderSkin = !skinEntityReward.IsNullObj();
            _skinNameContainer.SetActive(canRenderSkin);

            if (!canRenderSkin)
            {
                return;
            }
            
            var localization = LocalizationKeyProvider.Instance
                .GetForCharacter(skinEntityReward!.Character.Index)
                .FindSkin(skinEntityReward.Id);

            if (localization == null)
            {
                Logger.LogError($"Can't find localization for skin-id '{skinEntityReward.Id}'");
            }
            else
            {
                _skinNameContainer.SetLocalizationKey(localization.NameKey);
            }
            
            _characterImage.Render(
                CharacterImageBuilder
                    .Create(skinEntityReward.ToViewIdentifier())
                    .SetPlacement(EntityRendererPlacement.Offer)
                    .SetQuality(RenderTextureQuality.FullHD)
                    .Build()
                );
        }

        #region CLASSES

        [Serializable]
        private class SkinNameContainer
        {
            [Tooltip("Optional")]
            [SerializeField]
            private JxTextComponent? _skinNameText;

            [SerializeField]
            private RectTransform _container = null!;

            public void SetActive(bool isActive)
            {
                _container.gameObject.SetActive(isActive);
            }

            public void SetLocalizationKey(string key)
            {
                if (IsNull())
                {
                    return;
                }
                
                _skinNameText!.SetLocalizationKey(key);
            }

            public bool IsNull()
            {
                return _skinNameText.IsNullObj();
            }
        }

        #endregion
    }
}