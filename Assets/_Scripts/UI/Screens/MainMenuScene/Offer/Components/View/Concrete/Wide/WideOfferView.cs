using System.Collections.Generic;
using System.Linq;
using UI.Screens.MainMenuScene.Offer.Components.View.Base;
using UI.Screens.MainMenuScene.Offer.Components.View.RewardContainer;
using UI.Screens.MainMenuScene.Offer.Components.View.RewardView;
using UI.Screens.MainMenuScene.Offer.Helpers;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Offer.Components.View.Concrete.Wide
{
    public class WideOfferView : BaseOfferView
    {
        [SerializeField]
        private WideOfferRewardContainer _rewardsContainer = null;

        protected override void OnSetup()
        {
            var rewardOfferInfos = OfferRewardHelper.GetRewardInfos(
                Parameters.Offer.Reward.Items);
            
            SetupRewards(rewardOfferInfos);
        }
        
        protected void SetupRewards(IReadOnlyList<OfferRewardDisplayInfo> offerRewardInfos)
        {
            if (_rewardsContainer != null && offerRewardInfos.Any())
            {
                _rewardsContainer.Setup(offerRewardInfos);
            }
        }
    }
}