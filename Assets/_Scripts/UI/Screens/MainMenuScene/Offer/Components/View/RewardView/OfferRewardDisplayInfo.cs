using Api.Client.User.Rewards.Adapters;

namespace UI.Screens.MainMenuScene.Offer.Components.View.RewardView
{
    public class OfferRewardDisplayInfo
    {
        public OfferRewardDisplayInfo(
            IJxUserReward reward,
            int count
        )
        {
            Reward = reward;
            Count = count;
        }

        public IJxUserReward Reward { get; }
        public int Count { get; }
    }
}