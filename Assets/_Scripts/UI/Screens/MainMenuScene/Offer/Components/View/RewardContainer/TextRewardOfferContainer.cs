using System.Collections.Generic;
using Core.Extensions;
using UI.Screens.MainMenuScene.Offer.Components.View.RewardView;
using UI.Screens.MainMenuScene.Offer.Components.View.RewardView.Text;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Offer.Components.View.RewardContainer
{
    public class TextRewardOfferContainer : OfferRewardsContainer
    {
        [SerializeField]
        private Color _rewardTextColor;

        [SerializeField]
        private Color _connectionTextColor;
        
        [SerializeField]
        private RectTransform _container;

        private TextOfferRewardView.Factory _factory;
        private readonly IList<TextOfferRewardView> _rewardViews = new List<TextOfferRewardView>();

        [Inject]
        private void Inject(TextOfferRewardView.Factory factory)
        {
            _factory = factory;
        }

        public override void Setup(IReadOnlyList<OfferRewardDisplayInfo> rewardCountInfos)
        {
            Clear();

            for (var i = 0; i < rewardCountInfos.Count; i++)
            {
                var info = rewardCountInfos[i];
                
                CreateReward(info, useSeparator: i + 1 < rewardCountInfos.Count);
            }
        }

        public override void Clear()
        {
            _rewardViews.DisposeAndClear();
        }

        private void CreateReward(OfferRewardDisplayInfo info, bool useSeparator)
        {
            var view = _factory.Create(_container, new TextOfferRewardParameters(
                info, useSeparator, rewardTextColor: _rewardTextColor, connectionTextColor: _connectionTextColor));

            _rewardViews.Add(view);
        }
    }
}