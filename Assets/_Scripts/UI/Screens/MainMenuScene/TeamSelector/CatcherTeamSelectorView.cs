using Core.Extensions;
using Core.Helpers.TweenAnimation;
using Core.UI.Components.Buttons;
using DG.Tweening;
using Jx.Utils.Extensions;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.TeamSelector
{
    public class CatcherTeamSelectorView : MonoBehaviour
    {
        [SerializeField]
        private ButtonComponent _playCatcherButton = null!;

        [SerializeField]
        public RectTransform[] _catcherLockedContent = null!;

        [SerializeField]
        private RectTransform _raycastBlocker = null!;

        [SerializeField]
        private RectTransform _iconContent = null!;

        [SerializeField]
        private Image _lockedIcon = null!;

        [SerializeField]
        private Image _unlockedIcon = null!;

        [SerializeField]
        private CanvasGroup _unlockedEffect = null!;

        [SerializeField]
        private RectTransform _descriptionContent = null!;

        [SerializeField]
        private Image[] _lockedOverlays = null!;

        [SerializeField]
        private float _startOverlayAlpha = 0.4f;

        private readonly IAnimationSlot _unlockAnimationSlot = new AnimationSlot("unlock");

        private float _startIconAnchoredPositionX;
        private float _startDescriptionAnchoredPositionX;

        public Button.ButtonClickedEvent ClickedEvent => _playCatcherButton.onClick;

        private void Awake()
        {
            _startIconAnchoredPositionX = _iconContent.anchoredPosition.x;
            _startDescriptionAnchoredPositionX = _descriptionContent.anchoredPosition.x;
        }

        private void OnDestroy()
        {
            _unlockAnimationSlot.Destroy();
        }

        private void OnDisable()
        {
            Cleanup();
        }

        public void SetAvailable(bool isAvailable)
        {
            Cleanup();

            foreach (var content in _catcherLockedContent)
            {
                content.gameObject.SetActive(!isAvailable);
            }
        }

        public Tween DoUnlock()
        {
            Cleanup();
            SetAvailable(false);
            _raycastBlocker.gameObject.SetActive(true);

            return _unlockAnimationSlot.PlayNew(
                DOTween.Sequence()
                    .Append(DoChangeIcon())
                .Append(DoShowEffect())
                .Append(
                    DOTween.Sequence()
                        .Append(DoFadeOutOverlays())
                        .Join(DoMoveOutLockedContent())
                    )
            );
        }

        public void Cleanup()
        {
            _unlockAnimationSlot.Stop();
            ResetTweened();
        }

        private void ResetTweened()
        {
            _lockedIcon.enabled = true;
            _unlockedIcon.enabled = false;
            _iconContent.localScale = Vector3.one;
            _unlockedEffect.alpha = 0f;
            _raycastBlocker.gameObject.SetActive(false);
            _iconContent.SetAnchoredX(_startIconAnchoredPositionX);
            _descriptionContent.SetAnchoredX(_startDescriptionAnchoredPositionX);

            foreach (var overlay in _lockedOverlays)
            {
                overlay.SetAlpha(_startOverlayAlpha);
            }

        }

        private Tween DoChangeIcon()
        {
            return DOTween.Sequence()
                .Append(_iconContent.DOScale(1.09f, 0.4f))
                .AppendCallback(() =>
                {
                    _lockedIcon.enabled = false;
                    _unlockedIcon.enabled = true;
                })
                .Append(_iconContent.DOScale(1f, 0.4f));
        }

        private Tween DoShowEffect()
        {
            return DOTween.Sequence()
                .Append(_unlockedEffect.DOFade(1f, 0.45f))
                .Append(_unlockedEffect.DOFade(0f, 0.45f));
        }

        private Tween DoFadeOutOverlays()
        {
            var sequence = DOTween.Sequence();
            foreach (var overlay in _lockedOverlays)
            {
                sequence.Join(overlay.DOFade(0f, 1.20f));
            }
            return sequence;
        }

        private Tween DoMoveOutLockedContent()
        {
            return DOTween.Sequence()
                .Append(_iconContent.DOAnchorPosX(392f, 1.20f))
                .Join(_descriptionContent.DOAnchorPosX(-592f, 1.20f));
        }
    }
}