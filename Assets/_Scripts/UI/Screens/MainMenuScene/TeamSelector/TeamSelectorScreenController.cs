using Api.Client.User;
using Api.Client.User.FeatureAccess;
using Core.Extensions;
using Core.Helpers.Navigation.Controller;
using Core.Helpers.TweenAnimation;
using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Random;
using Jx.Utils.UnityComponentExtensions;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using MonsterLand.Matchmaking;
using Savings;
using Tutorial.LaunchParameters.Click;
using Tutorial.Providers.Handlers;
using Tutorial.Runner;
using Tutorial.Schemas;
using UnityEngine.Scripting;

namespace UI.Screens.MainMenuScene.TeamSelector
{
    public class TeamSelectorScreenController : BaseNavigationScreenController<TeamSelectorScreen>
    {
        private readonly IUserContext _userContext;
        private readonly IGameplayTutorialRunner _gameplayTutorialRunner;
        private readonly IScreenTutorialHandlerFactory _screenTutorialHandlerFactory;
        private readonly ILocalSave _localSave;

        private readonly IAnimationSlot _unlockCatcherAnimationSlot = new AnimationSlot("unlock-catcher");

        private IScreenTutorialHandler _tutorialHandler = null!;

        [Preserve]
        public TeamSelectorScreenController(
            IGameplayTutorialRunner gameplayTutorialRunner,
            IUserContext userContext,
            IScreenTutorialHandlerFactory screenTutorialHandlerFactory,
            ILocalSave localSave
        )
        {
            _userContext = userContext;
            _gameplayTutorialRunner = gameplayTutorialRunner;
            _screenTutorialHandlerFactory = screenTutorialHandlerFactory;
            _localSave = localSave;
        }

        protected override UniTask OnInitializeAsync()
        {
            _tutorialHandler = _screenTutorialHandlerFactory.Create(View, this);
            
            View.CharactersContainer.Initialize(
                _localSave, 
                _userContext.FeatureAccess.GetListener(MonsterLandFeatureAccessType.Catcher)
            );

            return base.OnInitializeAsync();
        }

        protected override UniTaskVoid OnDeInitialized()
        {
            _tutorialHandler?.Dispose();

            return base.OnDeInitialized();
        }

        protected override void OnViewAttached()
        {
            RenderPlayRandomButton();

            DisposeOnDetach(View.PlayEscapeeButton.OnClick(OnEscapeeTeamClick));
            DisposeOnDetach(View.CatcherSelector.ClickedEvent.Subscribe(OnCatcherTeamClick));
            DisposeOnDetach(View.PlayRandomButton.OnClick(OnAnyTeamClick));
            DisposeOnDetach(
                _userContext.FeatureAccess.GetListener(MonsterLandFeatureAccessType.Catcher).SubscribeAndFire(OnCatcherFeatureUnlockedStatusChange)
            );
            
            View.CharactersContainer.Setup();
            
            _tutorialHandler.TryRun();
        }

        protected override void OnViewDetached()
        {
            View.CatcherSelector.Cleanup();
            View.CharactersContainer.Cleanup();
            _unlockCatcherAnimationSlot.Stop();
            base.OnViewDetached();
        }

        private void RenderPlayRandomButton()
        {
            var canRender = _userContext.Tutorials.CheckPassed(TutorialType.GameplayEscapee) &&
                            _userContext.Tutorials.CheckPassed(TutorialType.GameplayCatcher);
            canRender &= _localSave.Value.FeatureAccessUnlockingProgress.IsWatched(MonsterLandFeatureAccessType.Catcher);

            View.PlayRandomButton.gameObject.SetActive(canRender);
        }

        private void OnCatcherFeatureUnlockedStatusChange(bool isUnlocked)
        {
            if (isUnlocked && !_localSave.Value.FeatureAccessUnlockingProgress.IsWatched(MonsterLandFeatureAccessType.Catcher))
            {
                _localSave.Change(s => s.FeatureAccessUnlockingProgress.MaskAsWatched(MonsterLandFeatureAccessType.Catcher));
                _unlockCatcherAnimationSlot.PlayNew(View.CatcherSelector.DoUnlock().OnComplete(RenderDefault));

                return;
            }

            RenderDefault();

            return;

            void RenderDefault()
            {
                View.CatcherSelector.SetAvailable(isUnlocked);
                RenderPlayRandomButton();
            }
        }

        private void OnEscapeeTeamClick()
        {
            ProcessTeamSelectionAsync(JxMatchmakingTeamKind.Escapee).Forget();
        }

        private void OnCatcherTeamClick()
        {
            if (!_userContext.FeatureAccess.IsCatcherUnlocked())
            {
                ToastManager.Instance.Show("CatcherTeamLocked");
                return;
            }

            ProcessTeamSelectionAsync(JxMatchmakingTeamKind.Catcher).Forget();
        }

        private void OnAnyTeamClick() =>
            ProcessTeamSelectionAsync(JxRandomGenerator.GenerateTrueWithProbability(0.3f) ? JxMatchmakingTeamKind.Catcher : JxMatchmakingTeamKind.Escapee)
                .Forget();

        private async UniTaskVoid ProcessTeamSelectionAsync(JxMatchmakingTeamKind team)
        {
            if (await _gameplayTutorialRunner.TryLaunchAsync(new TeamClickGameplayTutorialLaunchParameters(_userContext.Preset.Value.Value.Tutorial, team)))
            {
                return;
            }

            Exit(team);
        }
    }
}