using System;
using Jx.UserRelationship;
using Jx.Utils.Helpers;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Friends.Pages
{
    public abstract class FriendScreenPage : MonoBehaviour
    {
        private JxDisposableAction _disposeOnCleanup = null!;
        
        protected IJxUserRelationshipIntegration Relationship { get; private set; } = null!;

        public void BaseInitialize(IJxUserRelationshipIntegration relationship)
        {
            Relationship = relationship;
        }
        
        public void Setup()
        {
            _disposeOnCleanup = JxDisposableAction.Build();
            gameObject.SetActive(true);
            
            OnSetup();
        }

        public void Cleanup()
        {
            OnCleanup();
            
            _disposeOnCleanup.Dispose();
            gameObject.SetActive(false);
        }

        protected abstract void OnSetup();
        protected abstract void OnCleanup();

        protected void DisposeOnCleanup(IDisposable disposable)
        {
            _disposeOnCleanup.AppendDispose(disposable);
        }
    }
}