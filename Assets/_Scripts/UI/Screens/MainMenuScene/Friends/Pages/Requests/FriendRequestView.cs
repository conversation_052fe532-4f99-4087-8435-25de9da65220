using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Jx.UserRelationship.Friends;
using Jx.Utils.Logging;
using Jx.Utils.UnityComponentExtensions;
using PooledScrollList.View;
using System;
using Jx.MonsterLand.UserDatabase.Dto;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Friends.Pages.Requests
{
    public class FriendRequestView : PoolingScrollItem
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(FriendRequestView));
        
        [SerializeField]
        private JxTextComponent _nickname = null!;
        
        [SerializeField]
        private JxTextComponent _rank = null!;
        
        [SerializeField]
        private JxTextComponent _trophies = null!;
        
        [SerializeField]
        private ButtonComponent _acceptButton = null!;
        
        [SerializeField]
        private ButtonComponent _declineButton = null!;

        private IDisposable? _acceptDisposable;
        private IDisposable? _declineDisposable;
        private IDisposable? _dataSubscription;
        
        private Action? _onAccept;
        private Action? _onDecline;

        public IJxUserRelationshipFriendInvite CastedData { get; private set; } = null!;

        private void OnEnable()
        {
            _acceptDisposable = _acceptButton.OnClick(() => _onAccept?.Invoke());
            _declineDisposable = _declineButton.OnClick(() => _onDecline?.Invoke());
        }

        private void OnDisable()
        {
            _acceptDisposable?.Dispose();
            _declineDisposable?.Dispose();
        }

        public void Initialize(
            Action onAccept,
            Action onDecline)
        {
            _onAccept = onAccept;
            _onDecline = onDecline;
        }

        private void Setup()
        {
            _dataSubscription = CastedData.PublicInfo.SubscribeAndFire(OnPublicInfoChange);
        }

        private void OnPublicInfoChange(JxUserPublicInfoDto dto)
        {
            _nickname.Text = dto.Nickname;
            _rank.Text = dto.ProfileLevel.ToString();
            _trophies.Text = dto.Trophies.ToString();
        }

        private void Cleanup()
        {
            _dataSubscription?.Dispose();
        }

        public override void SetData(int index, object? data)
        {
            base.SetData(index, data);

            Cleanup();
            if (data is not IJxUserRelationshipFriendInvite invite)
            {
                _logger.LogError($"Data type is invalid: '{data?.GetType().Name}'. Expected: '{nameof(IJxUserRelationshipFriendInvite)}'");
                return;
            }

            CastedData = invite;
            Setup();
        }
    }
}