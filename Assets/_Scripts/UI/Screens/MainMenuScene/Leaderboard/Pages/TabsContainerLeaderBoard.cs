using System.Collections.Generic;
using UI.Components.Buttons.Tab;
using UI.Components.TabsContainer;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Leaderboard.Pages
{
    public class TabsContainerLeaderBoard : TabsContainer
    {
        [field: SerializeField]
        public TabButtonComponent Global { get; private set; } = null!;
        
        [field: SerializeField]
        public TabButtonComponent Friends { get; private set; } = null!;
        
        protected override IEnumerable<TabButtonComponent> EnumerateTabs()
        {
            yield return Global;
            yield return Friends;
        }
    }
}