using Jx.Utils.Logging;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Leaderboard.Pages
{
    [DisallowMultipleComponent]
    [RequireComponent(typeof(CanvasGroup))]
    public abstract class LeaderboardPage : MonoBehaviour
    {
        protected IJxLogger Logger { get; private set; } = null!;

        protected virtual void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        public abstract void Setup();
        public abstract void Cleanup();
    }
}