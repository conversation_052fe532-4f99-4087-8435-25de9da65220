using System;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Leaderboard.Renderers.LeaderboardUser
{
    [Serializable]
    public class LeaderboardItemResources
    {
        [field: SerializeField]
        public string MedalPath { get; private set; } = null!;
        
        [field: SerializeField]
        public Color TextColor { get; private set; }
        
        [field: SerializeField]
        public string BackgroundPath { get; private set; } = null!;
        
        [field: SerializeField]
        public string LeftFramePath { get; private set; } = null!;
        
        [field: SerializeField]
        public string RightFramePath { get; private set; } = null!;
    }
}