using System;
using System.Collections.Generic;
using Core.Extensions;
using Core.UIReusable.Timer;
using Jx.Utils.Coroutines;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.MainMenu.Components.Chests.MvpSlot
{
    public class MvpChestSlotView : ChestSlotView
    {
        // [SerializeField]
        // private UITimerComponent _unlockingTimer;
        //
        // [SerializeField]
        // private RectTransform _starsContainer;
        //
        // private readonly IList<MvpChestStar> _stars = new List<MvpChestStar>();
        //
        // private MvpChestSlotParameters _parameters;
        // private MvpChestStar.Factory _starFactory;
        // private IDisposable _setReadyToOpenAfterDelayCancellation;
        //
        // [Inject]
        // private void Inject(MvpChestStar.Factory starFactory)
        // {
        //     _starFactory = starFactory;
        // }
        //
        // private void OnDisable()
        // {
        //     EnsureDisposed();
        // }
        //
        // public void Setup(MvpChestSlotParameters parameters)
        // {
        //     EnsureDisposed();
        //     
        //     var isValid = parameters.Integration?.PendingChest != null;
        //     gameObject.SetActive(isValid);
        //
        //     if (!isValid)
        //     {
        //         return;
        //     }
        //     
        //     _parameters = parameters;
        //     
        //     Button.onClick.RemoveListener(OnClick);
        //     Button.onClick.AddListener(OnClick);
        //
        //     var integration = parameters.Integration;
        //
        //     // locked
        //     if (integration.AvailabilityTime > DateTime.Now)
        //     {
        //         SetState(State.Locked);
        //         SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: false);
        //         _unlockingTimer.Begin(integration.AvailabilityTime);
        //         
        //         _setReadyToOpenAfterDelayCancellation = this.InvokeAfterDelay(
        //         () =>
        //         {
        //             RenderStars(parameters.Integration.CurrentMvpValue, parameters.Integration.RequiredMvpValue);
        //             SetState(State.Unlocking);
        //             SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: false);
        //         },
        //         parameters.Integration.AvailabilityTime - DateTime.Now
        //     );
        //         
        //         return;
        //     }
        //     
        //     // ready to open
        //     if (integration.CurrentMvpValue >= integration.RequiredMvpValue)
        //     {
        //         SetState(State.ReadyToOpen);
        //         SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: true);
        //         return;
        //     }
        //
        //     RenderStars(parameters.Integration.CurrentMvpValue, parameters.Integration.RequiredMvpValue);
        //
        //     // in progress
        //     SetState(State.Unlocking);
        //     SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: false);
        // }
        //
        // private void EnsureDisposed()
        // {
        //     _setReadyToOpenAfterDelayCancellation?.Dispose();
        //     _setReadyToOpenAfterDelayCancellation = null;
        //     
        //     _stars.DisposeAndClear();
        // }
        //
        // private void RenderStars(int currentStars, int requiredStars)
        // {
        //     _stars.DisposeAndClear();
        //
        //     for (var i = 0; i < requiredStars; ++i)
        //     {
        //         var startState = i + 1 > currentStars ? MvpChestStarState.NotReached : MvpChestStarState.Reached;
        //         _stars.Add(_starFactory.Create(_starsContainer, startState));
        //     }
        // }
        //
        // private void OnClick()
        // {
        //     _parameters?.OnClick?.Invoke(_parameters.Integration).Forget();
        // }
    }
}