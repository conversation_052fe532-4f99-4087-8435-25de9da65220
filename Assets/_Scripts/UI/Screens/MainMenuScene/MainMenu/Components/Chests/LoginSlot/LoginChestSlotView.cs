using System;
using Core.UIReusable.Timer;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace UI.Screens.MainMenuScene.MainMenu.Components.Chests.LoginSlot
{
    public class LoginChestSlotView : ChestSlotView
    {
        [SerializeField]
        private UITimerComponent _unlockingTimer;
        
        private LoginChestSlotParameters _parameters;
        private IDisposable _setReadyToOpenAfterDelayCancellation;

        private void OnDisable()
        {
            EnsureDisposed();
        }

        public void Setup(LoginChestSlotParameters parameters)
        {
            EnsureDisposed();
            
            var isValid = parameters.Integration?.PendingChest != null;
            gameObject.SetActive(isValid);

            if (!isValid)
            {
                return;
            }
            
            _parameters = parameters;
            
            Button.onClick.RemoveListener(OnClick);
            Button.onClick.AddListener(OnClick);

            var integration = parameters.Integration;

            // locked
            if (integration.IsOnCooldown())
            {
                SetState(State.Locked);
                SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: false);
                _unlockingTimer.Begin(integration.AvailabilityTime);

                _setReadyToOpenAfterDelayCancellation = this.InvokeAfterDelay(
                    () =>
                    {
                        SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: true);
                        SetState(State.ReadyToOpen);
                    },
                    parameters.Integration.AvailabilityTime - DateTime.Now
                );
                
                return;
            }

            SetState(State.ReadyToOpen);
            SetupChestIcon(integration.ChestDefinition.ContentPath, isBig: true);
        }

        private void EnsureDisposed()
        {
            _setReadyToOpenAfterDelayCancellation?.Dispose();
            _setReadyToOpenAfterDelayCancellation = null;
        }
        
        private void OnClick()
        {
            _parameters?.OnClick?.Invoke(_parameters.Integration).Forget();
        }
    }
}