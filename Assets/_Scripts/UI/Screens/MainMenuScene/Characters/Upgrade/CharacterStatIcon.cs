using Core.Managers.AssetLoader;
using LoM.Characters.ClientIntegration;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public class CharacterStatIcon : MonoBehaviour
    {
        private const string PathToIcon = "character-leveling/icons";
        
        [SerializeField]
        private Image _icon = null!;

        public void Render(CharacterStatType statType)
        {
            _icon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(PathToIcon, statType.ToString());
        }
    }
}