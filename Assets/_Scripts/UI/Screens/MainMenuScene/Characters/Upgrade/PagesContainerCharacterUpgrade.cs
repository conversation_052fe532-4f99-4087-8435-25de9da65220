using System;
using System.Collections.Generic;
using Api.Client.User;
using Api.Client.User.Billing.Products;
using Api.Client.User.Upgrades.Interactions;
using Configs.Entities.Rarity;
using Core.Helpers.Navigation;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Managers.Rewards;
using Savings;
using UI.Screens.MainMenuScene.Characters.CharacterSaveProvider;
using UI.Screens.MainMenuScene.Characters.SkinAvailabilityProvider;
using UI.Screens.MainMenuScene.Characters.Upgrade.Path;
using UI.Screens.MainMenuScene.Characters.Upgrade.Info;
using UI.Screens.MainMenuScene.Characters.Upgrade.Skins;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public class PagesContainerCharacterUpgrade : MonoBehaviour, ITutorialPagesContainerCharacterUpgrade
    {
        [SerializeField]
        private InfoCharacterUpgradePage _info  = null!;
        
        [SerializeField]
        private PathCharacterUpgradePage _path  = null!;
        
        [SerializeField]
        private PerksCharacterUpgradePage _perks = null!;

        [SerializeField]
        private SkinsCharacterUpgradePage _skins = null!;
        
        private readonly JxChangeableObject<CharacterUpgradePage?, CharacterUpgradePage?> _activePage = new ();

        #region TUTORIAL

        IJxChangeableObject<CharacterUpgradePage?> ITutorialPagesContainerCharacterUpgrade.ActivePage => _activePage;
        ITutorialInfoCharacterUpgradePage ITutorialPagesContainerCharacterUpgrade.Info => _info;
        ITutorialPathCharacterUpgradePage ITutorialPagesContainerCharacterUpgrade.Path => _path;
        ITutorialPerksCharacterUpgradePage ITutorialPagesContainerCharacterUpgrade.Perks => _perks;

        void ITutorialPagesContainerCharacterUpgrade.SelectPage(CharacterUpgradePage page, ICharacterUpgradeInteraction interaction)
        {
            Activate(page, interaction);
        }

        #endregion

        public void Initialize(
            INavigation navigation,
            IUserContext userContext,
            ILocalSave localSave,
            Func<bool> checkCharacterSelected,
            Action exit,
            Action showPathPage,
            Func<IJxUserProduct, PriceUsageHandler, Action?, UniTaskVoid> onEntityPurchase,
            Action showPerksPage,
            IEntityRarityProvider rarityProvider,
            DiContainer diScope,
            ISkinAvailabilityProvider skinAvailabilityProvider,
            ICharacterSaveController characterSaveController
        )
        {
            foreach (var page in EnumeratePages())
                page.BaseInitialize(
                    navigation,
                    localSave
                );

            _info.Initialize(
                userContext,
                checkCharacterSelected,
                exit,
                showPathPage,
                onEntityPurchase,
                showPerksPage,
                rarityProvider,
                characterSaveController
            );
            
            _path.Initialize();
            
            _skins.Initialize(
                userContext,
                rarityProvider,
                navigation,
                diScope,
                skinAvailabilityProvider,
                characterSaveController
            );
        }

        public void Cleanup()
        {
            if (_activePage.Value != null)
                HidePage(_activePage.Value);
            
            _activePage.Set(null);
        }

        public void ShowInfo(ICharacterUpgradeInteraction interaction) => Activate(_info, interaction);
        public void ShowPath(ICharacterUpgradeInteraction interaction) => Activate(_path, interaction);
        public void ShowPerks(ICharacterUpgradeInteraction interaction) => Activate(_perks, interaction);
        public void ShowSkins(ICharacterUpgradeInteraction interaction) => Activate(_skins, interaction);

        private void Activate(CharacterUpgradePage targetPage, ICharacterUpgradeInteraction interaction)
        {
            if (ReferenceEquals(_activePage, targetPage))
                return;
            
            foreach (var page in EnumeratePages())
            {
                if (!ReferenceEquals(targetPage, page)) 
                    continue;
                
                ShowPage(page, interaction);
                return;
            }
        }

        private IEnumerable<CharacterUpgradePage> EnumeratePages()
        {
            yield return _info;
            yield return _path;
            yield return _perks;
            yield return _skins;
        }
        
        private void ShowPage(CharacterUpgradePage page, ICharacterUpgradeInteraction interaction)
        {
            if (_activePage.Value != null)
                HidePage(_activePage.Value);
            
            _activePage.Set(page);
            page.gameObject.SetActive(true);
            page.Show(interaction);
        }

        private static void HidePage(CharacterUpgradePage page)
        {
            page.Hide();
            page.gameObject.SetActive(false);
        }
    }
}