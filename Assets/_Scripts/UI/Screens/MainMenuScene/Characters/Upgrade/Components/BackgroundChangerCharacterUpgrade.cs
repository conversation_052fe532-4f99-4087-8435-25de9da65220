using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Components
{
    public class BackgroundChangerCharacterUpgrade : MonoBehaviour
    {
        [SerializeField]
        private Image _perksBackground = null!;

        [SerializeField]
        private Image _defaultBackground = null!;

        private void Cleanup()
        {
            _perksBackground.gameObject.SetActive(false);
            _defaultBackground.gameObject.SetActive(false);
        }

        public void ShowDefault()
        {
            Cleanup();
            _defaultBackground.gameObject.SetActive(true);
        }

        public void ShowForPerks()
        {
            Cleanup();
            _perksBackground.gameObject.SetActive(true);
        }
    }
}