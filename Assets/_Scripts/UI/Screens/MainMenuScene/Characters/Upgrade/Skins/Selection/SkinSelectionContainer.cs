using System;
using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Billing;
using Api.Client.User.Billing.Products;
using Api.Client.User.Upgrades.Interactions;
using Api.Entities;
using Api.Entities.Skins;
using App.Components.UI.Layouts.Grid;
using Configs.Entities;
using Configs.Entities.Rarity;
using Core.Helpers;
using Core.Helpers.Navigation;
using Core.Managers.AssetLoader;
using Core.Utils.Platform;
using Core.Utils.Pooling;
using Jx.Utils.AsyncRecords;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Collections;
using Jx.Utils.Logging;
using Jx.Utils.Pool;
using Savings;
using UI.Screens.MainMenuScene.Characters.CharacterSaveProvider;
using UI.Screens.MainMenuScene.Characters.SkinAvailabilityProvider.AvailabilityState;
using UI.Screens.MainMenuScene.SkinInfo;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Characters.SkinAvailabilityProvider
{
    public class SkinSelectionContainer : MonoBehaviour
    {
        private const string Path = "Screens/MainMenuScene/Characters/CharacterUpgrade/Skins/SkinSelectionItem";

        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(SkinSelectionContainer));
        
        [SerializeField]
        private ColumnsGridLayout _container = null!;
        
        private INavigation _navigation = null!;
        private ICharacterUpgradeInteraction _interaction = null!;
        private IEntityRarityProvider _entityRarityProvider = null!;
        private IUserContext _userContext = null!;
        private ICharacterSaveController _characterSaveController = null!;
        private Action<SkinGameEntity> _showSkin = null!;

        private readonly IList<SkinSelectionItemView> _views = new List<SkinSelectionItemView>();
        private IJxObjectPool<SkinSelectionItemView> _pool = null!;

        public void Initialize(IUserContext userContext,
                               IEntityRarityProvider entityRarityProvider,
                               INavigation navigation,
                               DiContainer diScope,
                               ISkinAvailabilityProvider skinAvailabilityProvider,
                               ICharacterSaveController characterSaveController,
                               Action<SkinGameEntity> showSkin)
        {
            _navigation = navigation;
            _entityRarityProvider = entityRarityProvider;
            _userContext = userContext;
            _characterSaveController = characterSaveController;
            _showSkin = showSkin;

            var prefab = JxResourceLoader.Instance.LoadPrefab<SkinSelectionItemView>(Path) ?? throw new NullReferenceException();
            _pool = new JxUnityObjectPool<SkinSelectionItemView>(() =>
            {
                var view = diScope.InstantiatePrefabForComponent<SkinSelectionItemView>(prefab);
                view.Initialize(() => OnSelect(view), entityRarityProvider, skinAvailabilityProvider);
                return view;
            });
        }

        public void Setup(ICharacterUpgradeInteraction interaction)
        {
            _interaction = interaction;
            var characterIndex = _interaction.CharacterIndex;
            
            var skins = EntityRepository.Instance.FindCharacter(characterIndex)?.Skins;
            if (skins.IsNullOrEmpty())
            {
                _logger.LogError($"Failed to find skins for '{characterIndex}'");
                return;
            }

            skins = skins
                .OrderBy(s => !_userContext.Progress.IsEntityAcquired(s))
                .ThenBy(s => s.GetRarity(_entityRarityProvider))
                .ToArray();
            
            foreach (var skin in skins!)
            {
                var view = _pool.Get();
                _container.Insert(view.transform);
                view.gameObject.SetActive(true);
                view.Setup(skin);
                _views.Add(view);
            }
        }

        public void Cleanup()
        {
            if (_views.IsEmpty())
                return;

            foreach (var view in _views)
            {
                view.Cleanup();
                _pool.Release(view);
                view.transform.SetParent(null, false);
                view.gameObject.SetActive(false);
            }
            
            _views.Clear();
            _container.Cleanup();
        }

        private void OnSelect(SkinSelectionItemView view)
        {
            if (view.State == null)
                return;
            
            switch (view.State)
            {
                case AvailableSkinAvailabilityState available:
                    _showSkin(view.Skin);
                    CharacterScreenTelemetry.Instance.TrackSkinClick(_interaction, view.Skin.Id);
                    
                    if (available.IsSelected)
                        return;
                    _characterSaveController.ChangeCharacterSelection(view.Skin.Character.Index, view.Skin.Id);
                    break;
                case LockedSkinAvailabilityState:
                    _showSkin(view.Skin);
                    break;
                case NotPurchasedSkinAvailabilityState:
                    _navigation.Navigate<SkinInfoScreen>(new SkinInfoScreenParameters(view.Skin));
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }
}