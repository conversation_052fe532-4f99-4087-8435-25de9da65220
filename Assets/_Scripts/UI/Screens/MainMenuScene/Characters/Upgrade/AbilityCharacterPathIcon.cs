using System;
using System.Collections.Generic;
using Core.Managers.AssetLoader;
using Jx.Utils.Logging;
using LoM.Characters.ClientIntegration;
using SceneLogics.GameplayScene.Abilities;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public class AbilityCharacterPathIcon : MonoBehaviour
    {
        private const string Path = "character-leveling/ability";
        private const IconType DefaultIcon = IconType.Any0;
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(AbilityCharacterPathIcon));
        
        [SerializeField]
        private Image _icon = null!;

        public void Render(CharacterStatType type, AbilityType abilityType)
        {
            if (type.GetKind() != CharacterStatKind.Ability)
            {
                _logger.LogError($"Not a ability upgrade provided");
                return;
            }
            
            var preset = PresetProvider.Get(abilityType);
            var index = type.GetAbilityIndex();
            if (index > preset.Count)
            {
                Render(DefaultIcon);
                return;
            }
            Render(preset[index]);
        }

        private void Render(IconType type)
        {
            _icon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(Path, type.ToString());
        }

        #region CLASSES

        private static class PresetProvider
        {
            private static readonly IReadOnlyList<IconType> _cooldownAndAny =  new[] { IconType.Cooldown, IconType.Any1 };
            private static readonly IReadOnlyList<IconType> _anyAndAny =  new[] { IconType.Any0, IconType.Any1 };
            
            public static IReadOnlyList<IconType> Get(AbilityType abilityType)
            {
                return abilityType switch
                {
                    AbilityType.None => _anyAndAny,
                    AbilityType.EchoLocation => _cooldownAndAny,
                    AbilityType.Fireball => _cooldownAndAny,
                    AbilityType.Hook => _cooldownAndAny,
                    AbilityType.Rage => _anyAndAny,
                    AbilityType.EscapeeMimicry => _cooldownAndAny,
                    AbilityType.Roll => _cooldownAndAny,
                    AbilityType.AngelHelp => _cooldownAndAny,
                    AbilityType.ThrowRock => _cooldownAndAny,
                    AbilityType.JumpShake => _anyAndAny,
                    AbilityType.SpiderWeb => _anyAndAny,
                    AbilityType.Invisibility => _anyAndAny,
                    AbilityType.KnockingDash => _cooldownAndAny,
                    AbilityType.FayWall => _cooldownAndAny,
                    AbilityType.Teleport => _cooldownAndAny,
                    AbilityType.TimeLaps => _cooldownAndAny,
                    AbilityType.ElectraField => _cooldownAndAny,
                    AbilityType.LuckyBag => _cooldownAndAny,
                    AbilityType.SmokeCloud => _cooldownAndAny,
                    AbilityType.Fear => _anyAndAny,
                    AbilityType.TeleportInMark => _cooldownAndAny,
                    AbilityType.LazyRegeneration => _anyAndAny,
                    AbilityType.MicroDash => _anyAndAny,
                    _ => throw new ArgumentOutOfRangeException(nameof(abilityType), abilityType, null)
                };
            }
        }
        
        // do not rename
        private enum IconType : byte
        {
            Cooldown = 0,
            Any0 = 1,
            Any1 = 2,
        }

        #endregion
    }
}