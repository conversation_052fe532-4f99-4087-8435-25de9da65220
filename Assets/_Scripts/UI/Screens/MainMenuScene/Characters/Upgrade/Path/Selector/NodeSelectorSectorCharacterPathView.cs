using System;
using Api.Client.User;
using Api.Client.User.CharacterPath;
using Api.Client.User.Upgrades.Interactions;
using Core.Managers.AssetLoader;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Extensions;
using Jx.Utils.Helpers;
using Jx.Utils.UnityComponentExtensions;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Path.Selector
{
    public class NodeSelectorSectorCharacterPathView : MonoBehaviour
    {
        private const string PathToBackground = "character-leveling/nodes/selector";

        [SerializeField]
        private JxTextComponent _levelText = null!;

        [SerializeField]
        private Image _background = null!;

        [SerializeField]
        private ButtonComponent _button = null!;

        [SerializeField]
        private NodeSelectorViewAnimationController _animationController = null!;
        
        private ICharacterUpgradeInteraction _interaction = null!;

        private IDisposable? _statusSubscription;
        private IDisposable? _clickSubscription;
        private IDisposable? _interactionStateSubscription;

        private Action _onClick = null!;

        public ICharacterUpgradeTreeNode Node { get; private set; } = null!;

        public void Initialize(Action onClick)
        {
            _onClick = onClick;
        }

        public void Setup(ICharacterUpgradeTreeNode node, ICharacterUpgradeInteraction interaction)
        {
            Node = node;
            _interaction = interaction;
            
            _levelText.Text = Node.Level.ToString();
            _clickSubscription= _button.OnClick(_onClick);
            _statusSubscription = node.Status.Subscribe(OnStatusChange);
            _interactionStateSubscription = interaction.State.Subscribe(OnStatusChange);
            OnStatusChange();
        }

        public void Cleanup()
        {
            _clickSubscription?.Dispose();
            _statusSubscription?.Dispose();
            _interactionStateSubscription?.Dispose();
        }

        private void OnStatusChange()
        {
            var status = Node.Status.Value;
            if (_interaction.State.Value != CharacterInteractionState.Purchased)
                status = CharacterUpgradeTreeNodeStatus.Locked;
            
            // same icon
            if (status == CharacterUpgradeTreeNodeStatus.Blocked)
                status = CharacterUpgradeTreeNodeStatus.Locked;
            
            _background.sprite = LoadBackground(status);
        }

        public IDisposable Select()
        {
            _animationController.ChangeSelectedStatus(true);
            return JxDisposableAction.Build().AppendCallback(() => _animationController.ChangeSelectedStatus(false));
        }
        
        private static Sprite LoadBackground(CharacterUpgradeTreeNodeStatus status)
            => JxResourceLoader.Instance.LoadSpriteOrFallback(PathToBackground, status.ToString());
    }
}