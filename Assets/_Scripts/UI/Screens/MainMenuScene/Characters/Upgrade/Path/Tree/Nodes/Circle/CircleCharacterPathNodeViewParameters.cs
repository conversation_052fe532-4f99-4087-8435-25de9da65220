using System;
using Api.Client.User.Upgrades.Interactions;
using UI.Screens.MainMenuScene.Characters.Upgrade.Path.Tree;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Leveling.Tree.Nodes.Circle
{
    public class CircleCharacterPathNodeViewParameters : ICharacterUpgradeNodeViewParameters
    {
        public CircleCharacterPathNodeViewParameters(
            NodeBarsMask barsMask,
            ICharacterUpgradeTreeNode node,
            Action onClick,
            int characterIndex,
            ICharacterUpgradeInteraction characterInteraction
        )
        {
            BarsMask = barsMask;
            Node = node;
            OnClick = onClick;
            CharacterIndex = characterIndex;
            CharacterInteraction = characterInteraction;
        }

        public int CharacterIndex { get; }
        public NodeBarsMask BarsMask { get; }
        public ICharacterUpgradeTreeNode Node { get; }
        public Action OnClick { get; }
        public ICharacterUpgradeInteraction CharacterInteraction { get; }
    }
}