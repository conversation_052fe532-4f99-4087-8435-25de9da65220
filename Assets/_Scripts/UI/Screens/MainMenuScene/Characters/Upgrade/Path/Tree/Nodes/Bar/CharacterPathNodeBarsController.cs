using System.Collections.Generic;
using Api.Client.User.CharacterPath;
using UI.Screens.MainMenuScene.Characters.Upgrade.Path.Tree;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Leveling.Tree.Nodes.Bar
{
    public class CharacterPathNodeBarsController : MonoBehaviour
    {
        [Header("Optional")]
        [SerializeField]
        private CharacterPathNodeBar? _upper;
            
        [SerializeField]
        private CharacterPathNodeBar? _lower;
            
        [SerializeField]
        private CharacterPathNodeBar? _left ;
            
        [SerializeField]
        private CharacterPathNodeBar? _right;

        public void SyncStatus(CharacterUpgradeTreeNodeStatus status)
        {
            var claimed = status == CharacterUpgradeTreeNodeStatus.Claimed;

            foreach (var bar in EnumerateAll())
            {
                if (bar == null)
                    continue;
                
                bar.SetIsFilled(claimed);
            }
        }

        public void Rebuild(NodeBarsMask mask)
        {
            RebuildBar(_left, mask, NodeBarsMask.Left);
            RebuildBar(_right, mask, NodeBarsMask.Right);
            RebuildBar(_lower, mask, NodeBarsMask.Lower);
            RebuildBar(_upper, mask, NodeBarsMask.Upper);
        }

        private static void RebuildBar(CharacterPathNodeBar? bar, NodeBarsMask mask, NodeBarsMask target)
        {
            if (bar == null)
                return;
            
            bar.gameObject.SetActive(Contains(mask, target));
        }

        private IEnumerable<CharacterPathNodeBar?> EnumerateAll()
        {
            yield return _upper;
            yield return _lower;
            yield return _left;
            yield return _right;
        }

        private static bool Contains(NodeBarsMask mask, NodeBarsMask target) => (mask & target) != 0;
    }
}