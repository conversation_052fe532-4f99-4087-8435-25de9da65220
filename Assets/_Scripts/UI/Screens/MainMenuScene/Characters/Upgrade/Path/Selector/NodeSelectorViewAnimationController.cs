using Core.Helpers;
using Core.Helpers.TweenAnimation;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Path.Selector
{
    public class NodeSelectorViewAnimationController : MonoBehaviour
    {
        private const float PreferredHeightOnSelected = 102.2f;
        private const float PreferredHeightOnDeSelected = 65.01f;
        private const float AnimationDurationInSeconds = 0.2f;

        [SerializeField]
        private LayoutElement _layoutElement;

        private readonly AnimationSlot _selectAnimationSlot = new ("select");

        private void OnDisable()
        {
            _selectAnimationSlot.Stop();
            Cleanup();
        }

        private void OnDestroy()
        {
            _selectAnimationSlot.Destroy();
        }

        private void Cleanup()
        {
            _layoutElement.preferredHeight = PreferredHeightOnDeSelected;
        }

        public void ChangeSelectedStatus(bool isSelected)
        {
            var targetHeight = isSelected ? PreferredHeightOnSelected : PreferredHeightOnDeSelected;
            _selectAnimationSlot.PlayNew(_layoutElement.DOPreferredHeight(targetHeight, AnimationDurationInSeconds));
        }
    }
}