using Core.Managers.AssetLoader;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Path.Tree
{
    public class CharacterPerkSlotIcon : MonoBehaviour
    {
        private const string Path = "character-leveling/perk-slot";
        private const string LockedPath = "locked";
        private const string LegendaryPath = "legendary";
        private const string DefaultPath = "default";
        
        [SerializeField]
        private Image _icon = null!;

        public void RenderLocked() => RenderInternal(LockedPath);
        public void RenderUnLocked(bool isLegendary) => RenderInternal(isLegendary ? LegendaryPath : DefaultPath);

        private void RenderInternal(string path) => _icon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(Path, path);
    }
}