using System;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Info.Popups
{
    public class CharacterUpgradeInfoPagePopupAnimationController : MonoBehaviour
    {
        private const float ScaleDurationInSeconds = 0.35f;
        
        [SerializeField]
        private RectTransform _scalableContent = null!;

        private readonly IAnimationSlot _scaleAnimation = new AnimationSlot("scale");

        private void OnEnable()
        {
            ResetToInitialState();
        }

        private void OnDisable()
        {
            ResetToInitialState();
        }

        private void OnDestroy()
        {
            _scaleAnimation.Destroy();
        }

        private void ResetToInitialState()
        {
            _scaleAnimation.Stop();
            _scalableContent.localScale = Vector3.zero;
        }

        public Tween DoShow()
        {
            return _scaleAnimation.PlayNew(_scalableContent.DOScale(1f, ScaleDurationInSeconds));
        }

        public Tween DoHide()
        {
            return _scaleAnimation.PlayNew(_scalableContent.DOScale(0f, ScaleDurationInSeconds));
        }
    }
}