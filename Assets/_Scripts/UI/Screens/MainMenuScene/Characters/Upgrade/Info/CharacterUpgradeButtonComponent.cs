using System;
using Api.Client.User.Upgrades;
using Core.UI.Components.Buttons;
using Core.UI.Components.Buttons.Behaviours;
using Core.UI.Components.Effects;
using Jx.Utils.ChangeableObjects;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Info.UpgradeButton
{
    [RequireComponent(typeof(ButtonStyleSetterComponent))]
    public class CharacterUpgradeButtonComponent : StyledButtonComponent
    {
        private const LoMButtonStyle UpgradeStyle = LoMButtonStyle.Green;
        private const LoMButtonStyle DefaultStyle = LoMButtonStyle.Gold;

        [SerializeField]
        private RectTransform _upgradeIndicator = null!;

        [SerializeField]
        private GlossEffectComponent _gloss = null!;
        
        private IDisposable? _levelingSubscription;

        public void Setup(IJxChangeableObject<LevelingInfo> levelingInfo)
        {
            _levelingSubscription?.Dispose();
            _levelingSubscription = levelingInfo?.SubscribeAndFire(OnLevelingChange);
        }

        public void Cleanup()
        {
            _levelingSubscription?.Dispose();
        }

        private void OnLevelingChange(LevelingInfo levelingInfo)
        {
            var upgradeable = levelingInfo.CanBeUpgraded;
            
            _gloss.gameObject.SetActive(upgradeable);
            _upgradeIndicator.gameObject.SetActive(upgradeable);
            
            SetStyle(upgradeable ? UpgradeStyle : DefaultStyle);
        }
    }
}