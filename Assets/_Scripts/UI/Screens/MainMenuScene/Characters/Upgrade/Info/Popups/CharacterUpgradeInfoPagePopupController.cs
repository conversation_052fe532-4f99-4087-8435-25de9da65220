using Managers.Popup;
using UI.Screens.MainMenuScene.Friends.Popup;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Characters.Upgrade.Info.Popups
{
    public class CharacterUpgradeInfoPagePopupController : MonoBehaviour
    {
        [SerializeField]
        private StatCollectorsCharacterUpgradeInfoPagePopup _statKindPopup = null!;

        [SerializeField]
        private AbilityCharacterUpgradeInfoPagePopup _abilityPopup = null!;

        private ICharacterPathTree _path = null!;
        private int _characterIndex;
        private IPopupCloser? _lastPopupCloser;

        public void Initialize()
        {
            _statKindPopup.Initialize();
            _abilityPopup.Initialize();
        }

        public void Setup(ICharacterPathTree path, int characterIndex)
        {
            _path = path;
            _characterIndex = characterIndex;

            _lastPopupCloser = new AnyInputPopupCloser();
            _lastPopupCloser.StartListening(OnPopupCloserTrigger);
        }

        public void Cleanup()
        {
            _lastPopupCloser?.Dispose();
        }

        public void ShowStat(Vector3 globalPosition, CharacterStatKind kind)
        {
            _abilityPopup.Hide();
            _statKindPopup.Show(_path, kind, globalPosition);
        }

        public void ShowAbility(Vector3 globalPosition)
        {
            _statKindPopup.Hide();
            _abilityPopup.Show(_path, _characterIndex, globalPosition);
        }

        private void OnPopupCloserTrigger()
        {
            _statKindPopup.Hide();
            _abilityPopup.Hide();
        }
    }
}