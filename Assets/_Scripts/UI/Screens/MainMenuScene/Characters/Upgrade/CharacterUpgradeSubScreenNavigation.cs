using System;
using Api.Client.User;
using Api.Client.User.Billing.Products;
using Api.Client.User.Upgrades.Interactions;
using Configs.Entities.Rarity;
using Core.Helpers.Navigation;
using Cysharp.Threading.Tasks;
using Jx.Utils.Helpers;
using Jx.Utils.UnityComponentExtensions;
using Managers.Rewards;
using Savings;
using UI.Screens.MainMenuScene.Characters.CharacterSaveProvider;
using UI.Screens.MainMenuScene.Characters.SkinAvailabilityProvider;
using UI.Screens.MainMenuScene.Characters.Upgrade.Components;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Characters.Upgrade
{
    public class CharacterUpgradeSubScreenNavigation : MonoBehaviour, ITutorialCharacterUpgradeSubScreenNavigation
    {
        [SerializeField]
        private BackgroundChangerCharacterUpgrade _backgroundChanger = null!;
        
        [SerializeField]
        private TabsContainerCharacterUpgrade _tabs = null!;

        [SerializeField]
        private PagesContainerCharacterUpgrade _pages = null!;
        
        [SerializeField]
        private CharacterInfoPanel _characterInfoPanel = null!;

        [SerializeField]
        private IndicatorsControllerCharacterUpgrade _indicatorsController = null!;

        private JxDisposableAction? _tabClickSubscriptions;

        private ICharacterUpgradeInteraction _interaction = null!;

        #region TUTORIAL

        ITutorialPagesContainerCharacterUpgrade ITutorialCharacterUpgradeSubScreenNavigation.Pages => _pages;
        ITutorialTabsContainerCharacterUpgrade ITutorialCharacterUpgradeSubScreenNavigation.Tabs => _tabs;

        #endregion

        public void Initialize(
            INavigation navigation,
            IUserContext userContext,
            ILocalSave localSave,
            Func<bool> checkCharacterSelected,
            Action exit,
            Func<IJxUserProduct, PriceUsageHandler, Action?, UniTaskVoid> onEntityPurchase,
            IEntityRarityProvider rarityProvider,
            DiContainer diScope,
            ISkinAvailabilityProvider skinAvailabilityProvider,
            ICharacterSaveController characterSaveController
        )
        {
            _pages.Initialize(
                navigation,
                userContext,
                localSave,
                checkCharacterSelected,
                exit,
                ShowPath,
                onEntityPurchase,
                ShowPerks,
                rarityProvider,
                diScope,
                skinAvailabilityProvider,
                characterSaveController
            );
            
            _indicatorsController.Initialize(localSave);
        }

        public void Setup(ICharacterUpgradeInteraction interaction)
        {
            _interaction = interaction;
            
            _characterInfoPanel.Setup(interaction);
            _indicatorsController.Setup(interaction);
            
            _tabClickSubscriptions = JxDisposableAction.Build();
            _tabClickSubscriptions.AppendDispose(_tabs.Info.OnClick(ShowInfo));
            _tabClickSubscriptions.AppendDispose(_tabs.Perks.OnClick(ShowPerks));
            _tabClickSubscriptions.AppendDispose(_tabs.Path.OnClick(ShowPath));
            _tabClickSubscriptions.AppendDispose(_tabs.Skins.OnClick(ShowSkins));
            
            ShowInfo();
        }

        public void Cleanup()
        {
            _tabClickSubscriptions?.Dispose();
            _pages.Cleanup();
            _tabs.Cleanup();
        }

        #region NAVIGATION

        private void ShowInfo()
        {
            _tabs.SelectTab(_tabs.Info);
            _pages.ShowInfo(_interaction);
            _backgroundChanger.ShowDefault();
            _characterInfoPanel.gameObject.SetActive(false);
            _indicatorsController.Render();
        }

        private void ShowPath()
        {
            _tabs.SelectTab(_tabs.Path);
            _pages.ShowPath(_interaction);
            _backgroundChanger.ShowDefault();
            _characterInfoPanel.gameObject.SetActive(true);
            _indicatorsController.Render();
        }

        private void ShowPerks()
        {
            _tabs.SelectTab(_tabs.Perks);
            _pages.ShowPerks(_interaction);
            _backgroundChanger.ShowForPerks();
            _characterInfoPanel.gameObject.SetActive(true);
            _indicatorsController.Render();
        }

        private void ShowSkins()
        {
            _tabs.SelectTab(_tabs.Skins);
            _pages.ShowSkins(_interaction);
            _backgroundChanger.ShowDefault();
            _characterInfoPanel.gameObject.SetActive(true);
            _indicatorsController.Render();
        }

        #endregion
    }
}