using Core.Helpers.Navigation.Containers;
using Core.UI.Components.Buttons;
using UI.Components.TabsContainer;
using UI.Screens.MainMenuScene.Characters.SubScreens.CharacterCollection.Components;
using UI.Screens.MainMenuScene.Characters.Collection.Containers;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Characters.SubScreens.CharacterCollection
{
    public class CharacterCollectionSubScreen : NavigationScreenContainerSubView
    {
        protected override string LocalizationKey => "CharacterCollectionScreen";

        [field: SerializeField]
        public IndicatorsControllerCharacterCollection IndicatorsController { get; private set; } = null!;
        
        [field: SerializeField]
        public CharacterCollectionTabsContainer TeamTabsContainer { get; private set; } = null!;

        [field: SerializeField]
        public ButtonComponent BackButton { get; private set; } = null!;

        [field: SerializeField]
        public CharacterCollectionItemViewsContainer Collection { get; private set; } = null!;

        [field: SerializeField]
        public ButtonComponent EmojiButton { get; private set; } = null!;

        [field: SerializeField]
        public Image Background { get; private set; } = null!;
    }
}