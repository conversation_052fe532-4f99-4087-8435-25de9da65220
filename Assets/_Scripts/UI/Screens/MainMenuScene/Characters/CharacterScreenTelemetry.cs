using System.Collections.Generic;
using System.Linq;
using Api.Client.User.Billing;
using Api.Client.User.Billing.Products;
using Api.Client.User.Upgrades.Interactions;
using App.Analytics;
using ExternalServices.Telemetry;
using GameplayNetworking.Share.Character;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils.Logging;
using LoM.Characters.ClientIntegration.Perks;

namespace UI.Screens.MainMenuScene.Characters
{
    public class CharacterScreenTelemetry
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(CharacterScreenTelemetry));

        private const string CharacterInteractionEventName = "character_click";

        private const string CharacterClickEventType = "main";
        private const string AbilityClickEventType = "ability";
        private const string PerkClickEventType = "perk";
        private const string SkinClickType = "skin";

        private const char SkinNameSplitSeparator = '_';

        public static CharacterScreenTelemetry Instance { get; } = new();

        private CharacterScreenTelemetry() { }

        public void TrackComingSoonCharacterClick(int characterIndex)
        {
            var info = new CharacterInfo(GetCharacterName(characterIndex), CharacterState.ComingSoon);
            TrackClickInternal(info, CharacterClickEventType);
        }

        public void TrackCharacterClick(ICharacterUpgradeInteraction interaction)
        {
            TrackClickInternal(GetInfoForInteraction(interaction), CharacterClickEventType);
        }

        public void TrackAbilityClick(ICharacterUpgradeInteraction interaction)
        {
            TrackClickInternal(GetInfoForInteraction(interaction), AbilityClickEventType);
        }

        public void TrackSkinClick(ICharacterUpgradeInteraction interaction, string skinId)
        {
            var skinName = ConvertSkinIdToSkinName(skinId);

            // skin id format: CharacterName_Skin_BlaBlaBla_SkinName, last word is the tracked skin name, separator is '_'
            if (string.IsNullOrWhiteSpace(skinName))
            {
                _logger.LogError($"Invalid skin id. Can't split string: '{skinId}'");
                return;
            }

            TrackClickInternal(
                GetInfoForInteraction(interaction),
                SkinClickType,
                new Dictionary<string, string>()
                {
                    [TelemetryEventParameters.Name] = skinName
                }
            );
        }

        public void TrackPerkClick(ICharacterUpgradeInteraction interaction, PerkType perkType)
        {
            TrackClickInternal(
                GetInfoForInteraction(interaction),
                PerkClickEventType,
                new Dictionary<string, string>()
                {
                    [TelemetryEventParameters.Name] = perkType.ToString()
                }
            );
        }

        public void TrackEntityPurchase(IJxUserProduct product, PurchaseStatus purchaseStatus)
        {
            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.UserAction,
                "purchase",
                new Dictionary<string, string>
                {
                    [TelemetryEventParameters.Type] = "entity",
                    [TelemetryEventParameters.ProductId] = product.Id,
                    [TelemetryEventParameters.Status] = purchaseStatus.ToString().ToLowerInvariant(),
                }
            );
        }

        private string ConvertSkinIdToSkinName(string? skinId)
        {
            return skinId?.Split(SkinNameSplitSeparator).LastOrDefault() ?? string.Empty;
        }

        private void TrackClickInternal(
            CharacterInfo info,
            string eventType,
            Dictionary<string, string>? additionalParameters = null
        )
        {
            var parameters = new Dictionary<string, string>()
            {
                [TelemetryEventParameters.Type] = eventType,
                [TelemetryEventParameters.Character] = info.CharacterName,
                [TelemetryEventParameters.State] = info.StateToString()
            };

            if (additionalParameters != null)
            {
                parameters = parameters
                            .Union(additionalParameters)
                            .ToDictionary(k => k.Key, v => v.Value);
            }

            JxTelemetryIntegration.Instance.TrackEvent(
                TelemetryTrackingContexts.UserUIInteraction,
                CharacterInteractionEventName,
                parameters
            );
        }

        private static string GetCharacterName(int index) => CharacterTypeHelper.GetName(index);

        private static CharacterInfo GetInfoForInteraction(ICharacterUpgradeInteraction interaction)
        {
            var name = GetCharacterName(interaction.CharacterIndex);
            var state = interaction.State.Value == CharacterInteractionState.Purchased ? CharacterState.Unlocked : CharacterState.Locked;
            return new CharacterInfo(name, state);
        }

        #region CLASSES

        private class CharacterInfo
        {
            public CharacterInfo(string characterName, CharacterState state)
            {
                CharacterName = characterName;
                State = state;
            }

            public string CharacterName { get; }
            public CharacterState State { get; }

            public string StateToString()
            {
                return State.ToString().ToLowerInvariant();
            }
        }

        private enum CharacterState
        {
            Unlocked = 0,
            Locked = 1,
            ComingSoon = 2,
        }

        #endregion
    }
}