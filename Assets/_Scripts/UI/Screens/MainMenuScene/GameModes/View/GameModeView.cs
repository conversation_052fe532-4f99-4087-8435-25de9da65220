using Api.Client.User.Rewards;
using Api.Client.User.Rewards.Adapters;
using Api.Client.User.TrophyPath;
using App.Localization.Providers;
using Core.Extensions;
using Core.Managers.AssetLoader;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Jx.Utils.Objects;
using Jx.Utils.Templating;
using Jx.Utils.UnityComponentExtensions;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using System;
using System.Linq;
using UI.Screens.MainMenuScene.GameModes.HardCode;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.GameModes
{
    public class GameModeView : AbstractGameModeView
    {
        private const string PathToPreview = "gamemodes/pictures/";
        private const string TrophyCountTemplate = "COUNT";
        private const string ColorTemplate = "COLOR";
        private const string TrophyColorHex = "FFE02D";
        private const string TrophyLockedStateKey = "UNLOCK_THIS_GAME_MODE";

        [SerializeField]
        private Image _previewIcon = null!;
        
        [SerializeField]
        private Image _icon = null!;
        
        [SerializeField]
        private JxTextComponent _nameText = null!;

        [SerializeField]
        private RectTransform _selectedContent = null!;
        
        [SerializeField]
        private ButtonComponent _selectButton = null!;

        [SerializeField]
        private ButtonComponent? _infoButton;
        
        [SerializeField]
        private RectTransform _lockedContent = null!;

        [SerializeField]
        private JxTextComponent _lockedText = null!;

        private GameModeViewContext _context = null!;

        private IDisposable? _clickSubscription;
        private IDisposable? _clickInfoSubscription;
        private IDisposable? _featureAccessSubscription;
        private IDisposable? _trophyPathStateSubscription;
        private IDisposable? _currentModeSubscription;

        public void Setup(GameModeViewContext context)
        {
            _context = context;
            
            _clickSubscription = _selectButton.OnClick(context.OnClickSelect);
            if (_infoButton != null)
                _clickInfoSubscription = _infoButton.OnClick(() => context.Presenter.ShowInfo(context.GameMode));
            
            _featureAccessSubscription = context.UserContext.FeatureAccess.GetListener(context.GameMode.ToFeatureAccessType()).Subscribe(_ => Render());
            _trophyPathStateSubscription = context.UserContext.TrophyPath.Interaction.State.Subscribe(_ => Render());
            _currentModeSubscription = context.Presenter.CurrentMode.Subscribe(_ => Render());

            Render();
        }

        public void Cleanup()
        {
            _featureAccessSubscription?.Dispose();
            _clickSubscription?.Dispose();
            _clickInfoSubscription?.Dispose();
            _trophyPathStateSubscription?.Dispose();
            _currentModeSubscription?.Dispose();
            CleanupRender();
        }

        private void CleanupRender()
        {
            _lockedContent.gameObject.SetActive(false);
            _selectedContent.gameObject.SetActive(false);
        }

        protected override void Render()
        {
            base.Render();

            var localization = LocalizationKeyProvider.Instance.GetForGameMode(_context.GameMode);
            var isLocked = !_context.UserContext.FeatureAccess.IsUnlocked(_context.GameMode.ToFeatureAccessType());

            CleanupRender();
            RenderShared(localization);
            
            if (isLocked)
                RenderLocked(localization);
            else
                RenderAvailable();
        }

        private void RenderShared(GameModesLocalizationProvider localization)
        {
            _icon.sprite = JxResourceLoader.Instance.LoadFeatureAccessSpriteOrFallback(_context.GameMode.ToFeatureAccessType());
            _previewIcon.sprite = JxResourceLoader.Instance.LoadSpriteOrNull(PathToPreview, _context.GameMode.ToString());
            
            _nameText.SetLocalizationKey(localization.TitleKey);
        }

        private void RenderAvailable()
        {
            _selectedContent.gameObject.SetActive(_context.IsSelected());
        }

        private void RenderLocked(GameModesLocalizationProvider localization)
        {
            _lockedContent.gameObject.SetActive(true);
            
            var featureAccess = _context.GameMode.ToFeatureAccessType();
            var requiredTrophies = ParseTrophiesOrFallback(featureAccess);

            if (_context.UserContext.TrophyPath.Interaction.State.Value.Max >= requiredTrophies)
            {
                _lockedText.SetLocalizationKey(TrophyLockedStateKey);
            }
            else
            {
                _lockedText.SetTextInterpolationContext(
                    new JxStringTemplateParameters()
                        .Set(ColorTemplate, TrophyColorHex)
                        .Set(TrophyCountTemplate, requiredTrophies)
                );

                _lockedText.SetLocalizationKey(localization.RequirementsKey);
            }
        }

        private int ParseTrophiesOrFallback(MonsterLandFeatureAccessType feature)
        {
            var item = _context.UserContext.TrophyPath.Interaction.Items.FirstOrDefault(Filter);
            return item?.Threshold ?? 0;

            bool Filter(ITrophyPathItem i)
            {
                if (i.Reward.Kind != RewardKind.FeatureAccess)
                    return false;
                
                var reward = i.Reward.Cast<FeatureAccessUserReward>();
                return reward.Type == feature;
            }
        }
    }
}