using System;
using Api.Client.User;
using MonsterLand.Matchmaking.Dto;
using UI.Screens.MainMenuScene.GameModes.Presenter;

namespace UI.Screens.MainMenuScene.GameModes
{
    public class GameModeViewContext
    {
        public GameModeViewContext(
            JxMonsterLandGameModeKind gameMode,
            IUserContext userContext,
            Func<bool> isSelected,
            Action onClickSelect,
            IGameModesScreenPresenter presenter
        )
        {
            GameMode = gameMode;
            UserContext = userContext;
            IsSelected = isSelected;
            OnClickSelect = onClickSelect;
            Presenter = presenter;
        }

        public JxMonsterLandGameModeKind GameMode { get; }
        public IUserContext UserContext { get; }
        public Func<bool> IsSelected { get; }
        public Action OnClickSelect { get; }
        public IGameModesScreenPresenter Presenter { get; }
    }
}