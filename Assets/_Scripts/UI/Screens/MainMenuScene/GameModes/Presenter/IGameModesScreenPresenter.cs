using Jx.Utils.ChangeableObjects;
using MonsterLand.Matchmaking.Dto;

namespace UI.Screens.MainMenuScene.GameModes.Presenter
{
    public interface IGameModesScreenPresenter
    {
        IJxChangeableObject<JxMonsterLandGameModeKind> CurrentMode { get; }
        IGameModesAvailabilityState AvailabilityState { get; }
        
        void OnGameModeButtonClick();
        void ShowInfoAboutCurrentGameMode();
        void ShowInfo(JxMonsterLandGameModeKind kind);
    }
}