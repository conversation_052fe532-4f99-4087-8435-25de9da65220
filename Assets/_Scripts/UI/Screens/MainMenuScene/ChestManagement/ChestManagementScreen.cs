using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UI.Components.Chests;
using UI.Components.Toggle;
using UI.Components.Toggles;
using UnityEngine;

namespace UI.Screens.MainMenuScene.ChestManagement
{
    [ScreenController(controller: typeof(ChestManagementScreenController))]
    public class ChestManagementScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public ChestIcon ChestIcon { get; private set; }
        
        [field: SerializeField]
        public ButtonComponent PlayButton { get; private set; }
        
        [field: SerializeField]
        public ButtonComponent ManageChestsButton { get; private set; }
        
        [field: SerializeField]
        public ToggleComponent Dont<PERSON>howAgainToggle { get; private set; }
    }
}