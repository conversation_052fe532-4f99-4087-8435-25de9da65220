using Core.Helpers.Navigation.View;
using Jx.Utils.Helpers;
using UI.Screens.MainMenuScene.Credentials.Provider;
using UnityEngine;
using Zenject;

namespace UI.Screens.MainMenuScene.Credentials
{
    [ScreenController(controller: typeof(CredentialsScreenController))]
    public class CredentialsScreen : BaseNavigationScreen
    {
        [SerializeField]
        private RectTransform _content;
        
        private JxDisposableAction _disposable;

        private CredentialElement.Factory _elementFactory;

        [Inject]
        private void Inject(CredentialElement.Factory elementFactory)
        {
            _elementFactory = elementFactory;
        }

        public void AddCredential(Credential credential)
        {
            _disposable ??= JxDisposableAction.Build();
            _disposable.AppendDispose(_elementFactory.Create(credential, _content));
        }

        public void Clear()
        {
            _disposable.Dispose();
            _disposable = null;
        }
    }
}