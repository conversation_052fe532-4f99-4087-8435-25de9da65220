using System;
using Api.Client.User.Compensations;
using Api.Entities;
using Core.Managers.AssetLoader;
using GameplayNetworking.Share.Character;
using Jx.Utils.Logging;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.RewardReplacement.Components
{
    public class CompensationView : MonoBehaviour
    {
        private const string CharacterIconPlacement = "full";
        
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(CompensationView));
        
        [SerializeField]
        private EntityRenderer _entityRenderer = null!;

        public void Render(IPendingCompensation compensation)
        {
            switch (compensation)
            {
                case EntityPendingCompensation entity:
                    _entityRenderer.Render(entity.Entity.GetCharacterIndex());
                    break;
                default:
                    _logger.LogError($"Unknown compensation type '{compensation.GetType().Name}'");
                    break;
            }
        }

        #region CLASSES

        [Serializable]
        private class EntityRenderer
        {
            [SerializeField]
            private Image _icon = null!;

            public void Render(int characterIndex)
            {
                _icon.sprite = JxResourceLoader.Instance.LoadCharacterIcon(CharacterIconPlacement, characterIndex);
            }
        }

        #endregion
    }
}