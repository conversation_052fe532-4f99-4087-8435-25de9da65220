using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UI.Screens.MainMenuScene.RewardReplacement.Components;
using UnityEngine;

namespace UI.Screens.MainMenuScene.RewardReplacement
{
    [ScreenController(typeof(CompensationRewardScreenController))]
    public class CompensationRewardScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public CompensationView Compensation { get; private set; } = null!;

        [field: SerializeField]
        public RewardsContainerCompensationScreen Rewards { get; private set; } = null!;

        [field: SerializeField]
        public ButtonComponent ClaimButton { get; private set; } = null!;
    }
}