using Core.UI.Components.Text;
using MonsterLand.Matchmaking.States;
using Savings;
using System;
using Api.Client.User;
using Configs.EntityRenderer;
using Core.UI.Components.RenderTexture.Factory;
using Core.UI.Components.RenderTexture.Renderers;
using GameplayComponents.CharacterPresentation;
using UI.Components.CharacterLevel;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Lobby.Pages.Party
{
    public class MyPlayerLobbySlotView : MonoBehaviour
    {
        [SerializeField]
        private CharacterImage _characterIcon = null!;

        [SerializeField]
        private LobbyLeaderLabel _leaderLabel = null!;

        [SerializeField]
        private JxTextComponent _playerNickNameText = null!;

        [SerializeField]
        private CharacterLevelView _level = null!;

        [SerializeField]
        private PlayerLobbySlotChatLobbyMessagePresenter _chatPresenter = null!;

        [SerializeField]
        private RectTransform _readyContent = null!;

        private CharacterViewIdentifier? _previousIdentifier;
        private ILocalSave _localSave = null!;
        private IUserContext _userContext = null!;
        
        private IDisposable? _stateSubscription;

        public void Initialize(
            ILocalSave localSave,
            IUserContext userContext
        )
        {
            _localSave = localSave;
            _userContext = userContext;
        }

        public void Setup(IJxLobbyPlayerState player)
        {
            _chatPresenter.ForceHide();
            Render(player);
            _stateSubscription = player.ChangedObservable.Subscribe(Render);
            _chatPresenter.Setup(player);
        }
        
        public void Cleanup()
        {
            _stateSubscription?.Dispose();
            _chatPresenter.Cleanup();
            _previousIdentifier = null;
        }

        private void Render(IJxLobbyPlayerState state)
        {
            var characterIndex = state.Character.CharacterIndex;
            var characterLevel = state.Character.CharacterLevel;
            
            _readyContent.gameObject.SetActive(state.IsReady);

            _playerNickNameText.Text = _userContext.Profile.Model.Value.Nickname;
            _level.Render(characterLevel);
            RenderLeaderLabel(state);
            
            var identifier = _localSave.Value.GetCharacterSave(characterIndex).ToIdentifier();
            if (_previousIdentifier?.Equals(identifier) == true)
                return;
            _previousIdentifier = identifier;
            
            _characterIcon.Render(
                CharacterImageBuilder
                    .Create(identifier)
                    .SetPlacement(EntityRendererPlacement.Lobby)
                    .SetQuality(RenderTextureQuality.FullHD)
                    .Build()
            );
        }

        private void RenderLeaderLabel(IJxLobbyPlayerState state)
        {
            var isLeader = state.IsLeader;
            _leaderLabel.gameObject.SetActive(isLeader);
            if (isLeader)
                _leaderLabel.Render();
        }
    }
}