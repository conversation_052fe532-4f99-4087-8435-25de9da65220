using Core.Managers.AssetLoader;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Jx.Utils.UnityComponentExtensions;
using MonsterLand.Matchmaking;
using MonsterLand.Matchmaking.States;
using System;
using App.Localization.Components.GlobalLocalization;
using App.Localization.Providers;
using Core.Extensions;
using Core.Extensions.DiExtensions;
using Jx.Utils.Coroutines;
using Jx.Utils.Helpers;
using UI.Components.CharacterLevel;
using UI.Screens.MainMenuScene.Characters.Upgrade.Path;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Lobby.Pages.Party
{
    public class PlayerLobbySlotView : MonoBehaviour
    {
        private const string PathToCatcherSprite = "feature-access/catcher";
        private const string PathToEscapeeSprite = "icons/Icon_Boots";

        private static readonly Color _catcherNickColor = new Color(250f, 108f, 89f, 1f).NormalizeRGB();
        private static readonly Color _escapeeNickColor = new Color(89f, 196f, 250f, 1f).NormalizeRGB();

        [SerializeField]
        private RectTransform _main = null!;

        [SerializeField]
        private RectTransform _empty = null!;
        
        [Space(10)]
        [SerializeField]
        private ButtonComponent _button = null!;

        [SerializeField]
        private LobbyLeaderLabel _leaderLabel = null!;

        [SerializeField]
        private Image _teamIcon = null!;

        [SerializeField]
        private JxTextComponent _playerNickNameText = null!;
        
        [SerializeField]
        private JxRootTextComponent _characterName = null!;

        [SerializeField]
        private CharacterHexagon _characterIcon = null!;

        [SerializeField]
        private CharacterLevelView _level = null!;
        
        [SerializeField]
        private PlayerLobbyPlacementLabel _placementLabel = null!;
        
        [SerializeField]
        private PlayerLobbySlotViewAnimationController _animationController = null!;
        
        [SerializeField]
        private PlayerLobbySlotChatLobbyMessagePresenter _chatPresenter = null!;
        
        private IDisposable? _clickSubscription;
        private JxDisposableAction? _renderSubscriptions;

        private Action _onSelect = null!;
        
        private RectTransform _rectTransform = null!;
        
        public IJxLobbyPlayerState? PlayerState { get; private set; }
        public RectTransform RectTransform => _rectTransform ??= GetComponent<RectTransform>();

        public void Initialize(Action onSelect)
        {
            _onSelect = onSelect;
        }

        public void Cleanup()
        {
            _clickSubscription?.Dispose();
            _renderSubscriptions?.Dispose();
            _animationController.StopAll();
            PlayerState = null;
            _chatPresenter.Cleanup();
        }

        public void SetupNotEmpty(IJxLobbyPlayerState playerState)
        {
            PlayerState = playerState;
            
            CleanupRender();
            _main.gameObject.SetActive(true);
            RenderNotEmpty(playerState);
            SetupShared();
            _chatPresenter.Setup(playerState);
            
            _renderSubscriptions = JxDisposableAction.Build();
            _renderSubscriptions.AppendDispose(playerState.ChangedObservable.Subscribe(RenderNotEmpty));
            _renderSubscriptions.AppendDispose(playerState.OnlinePlacement.IsOnline.Subscribe(_ => RenderNotEmpty(playerState)));
            _renderSubscriptions.AppendDispose(playerState.OnlinePlacement.Placement.Subscribe(_ => RenderNotEmpty(playerState)));
        }

        public void SetupEmpty()
        {
            CleanupRender();
            _empty.gameObject.SetActive(true);
            SetupShared();
        }

        private void SetupShared()
        {
            _clickSubscription = _button.OnClick(_onSelect);
        }

        private void CleanupRender()
        {
            _chatPresenter.Cleanup();
            _main.gameObject.SetActive(false);
            _empty.gameObject.SetActive(false);
        }

        private void RenderNotEmpty(IJxLobbyPlayerState state)
        {
            if (gameObject.IsDestroyed())
                return;
            
            var isEscapeeTeam = state.Team is JxMatchmakingTeamKind.Escapee;
            var teamSpritePath = isEscapeeTeam ? PathToEscapeeSprite : PathToCatcherSprite;

            _teamIcon.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(teamSpritePath);

            _playerNickNameText.Text = state.Info.Nickname;
            _playerNickNameText.Color = isEscapeeTeam ? _escapeeNickColor : _catcherNickColor;

            var characterIndex = state.Character.CharacterIndex;
            var characterLevel = state.Character.CharacterLevel;
            var localization = LocalizationKeyProvider.Instance.GetForCharacter(characterIndex);

            _characterName.SetGlobalLocalization(LocalizationFolderType.Characters, localization.NameKey);
            _characterIcon.Render(characterIndex, false);
            _level.Render(characterLevel);
            
            RenderPlacement(state);
            _animationController.DoReady(state.IsReady);
            _animationController.DoSetIsOnline(state.OnlinePlacement.IsOnline.Value);
            RenderLeaderLabel(state);
        }

        private void RenderPlacement(IJxLobbyPlayerState state)
        {
            var placement = state.OnlinePlacement.Placement.Value;
            
            var visible = 
                !state.IsReady &&
                !state.OnlinePlacement.Is(PlayerOnlinePlacements.InLobby);
            
            _animationController.DoShowOnlinePlacement(visible);
            if (!visible) 
                return;
            if (state.OnlinePlacement.IsOnline.Value)
                _placementLabel.RenderOnline(placement);
            else
                _placementLabel.RenderOffline();
        }

        private void RenderLeaderLabel(IJxLobbyPlayerState state)
        {
            var isLeader = state.IsLeader;
            _leaderLabel.gameObject.SetActive(isLeader);
            if (isLeader)
                _leaderLabel.Render();
        }
    }
}