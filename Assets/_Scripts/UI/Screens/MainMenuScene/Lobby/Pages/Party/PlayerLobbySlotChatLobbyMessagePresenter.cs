using System;
using Jx.Matchmaking.Interactions.Chats;
using Jx.Utils.ChangeableObjects;
using LoM.Emojis.ClientIntegration;
using Managers.Emoji;
using MonsterLand.Matchmaking.Dto.LobbyActions;
using MonsterLand.Matchmaking.States;
using UI.Screens.MainMenuScene.Lobby.Chat.InLobby;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Lobby.Pages.Party
{
    public class PlayerLobbySlotChatLobbyMessagePresenter : MonoBehaviour
    {
        private static readonly TimeSpan _scaleDuration = TimeSpan.FromSeconds(0.3);
        private static readonly TimeSpan _duration = TimeSpan.FromSeconds(3.0) + _scaleDuration * 2;
        
        [SerializeField]
        private UnderLobbyTextChatMessageView _textBubble = null!;

        [SerializeField]
        private UnderLobbyEmojiChatMessageView _emojiBubble = null!;

        private IDisposable? _chatSubscription;

        public void Setup(IJxLobbyPlayerState player)
        {
            ForceHide();
            _chatSubscription = player.LastChatRecord.SubscribeAndFire(OnLastChatRecordChange);
        }

        public void Cleanup()
        {
            _chatSubscription?.Dispose();
            ForceHide();
        }

        public void ForceHide()
        {
            _textBubble.gameObject.SetActive(false);
            _emojiBubble.gameObject.SetActive(false);
        }

        private void OnLastChatRecordChange(IJxMatchmakingChatRecord? record)
        {
            if (record == null)
            {
                ForceHide();
                return;
            }

            var nowUtc = DateTime.UtcNow;
            var sentUtc = record.SentAtUtc;
            ForceHide();
            switch (record.Content)
            {
                case JxMatchmakingChatRecordContentEmoji emoji:
                    var expectedDuration = EmojiSettingsProvider.Instance.Get((EmojiType)emoji.EmojiType).LoopDuration;
                    var passedDuration = nowUtc - sentUtc;
                    if (passedDuration > expectedDuration)
                    {
                        ForceHide();
                        return;
                    }
                    
                    _emojiBubble.gameObject.SetActive(true);
                    _emojiBubble.Show((EmojiType)emoji.EmojiType);
                    break;
                case JxMatchmakingChatRecordContentLocalized:
                case JxMatchmakingChatRecordContentText:
                    if (nowUtc > sentUtc + _duration)
                    {
                        ForceHide();
                        return;
                    }
                    
                    _textBubble.gameObject.SetActive(true);
                    _textBubble.Show(record, _duration);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }
}