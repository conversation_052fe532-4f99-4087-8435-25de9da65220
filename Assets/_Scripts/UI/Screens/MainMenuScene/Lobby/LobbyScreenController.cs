using System;
using System.Threading;
using Api.Client.User;
using App.Friends;
using App.Profile;
using Core;
using Core.Extensions;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.Loading.PlaceholderCover;
using Cysharp.Threading.Tasks;
using Jx.UserRelationship;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Coroutines;
using Jx.Utils.Templating;
using Jx.Utils.Threading;
using Managers.Popup;
using MonsterLand.Matchmaking;
using MonsterLand.Matchmaking.Connections;
using MonsterLand.Matchmaking.Interactions;
using MonsterLand.Matchmaking.States;
using Savings;
using UI.Dialogs;
using UI.Screens.MainMenuScene.Characters;
using UI.Screens.MainMenuScene.Lobby.Chat.Chat.Presenter.Managers;
using UI.Screens.MainMenuScene.Lobby.Chat.Proxy;
using UI.Screens.MainMenuScene.Lobby.Controls;
using UnityEngine.Scripting;

namespace UI.Screens.MainMenuScene.Lobby
{
    [Preserve]
    public class LobbyScreenController : BaseNavigationScreenController<LobbyScreen>, ILobbyControlHandler
    {
        private readonly ILocalSave _localSave;
        private readonly INavigation _navigation;
        private readonly IUserContext _userContext;
        private readonly IJxMatchmakingIntegration _matchmaking;
        private readonly ILoadingPlaceholderController _loadingPlaceholderController;
        private readonly IPopupsManager _popupsManager;
        private readonly IFriendPresenter _friendPresenter;
        private readonly IProfilePresenter _profilePresenter;
        private readonly IJxUserRelationshipIntegration _relationship;
        private readonly ILobbyChatViewsManager _chatViewsManager;
        private readonly JxAtomicFlag _forceExitStarted;
        
        private LobbyAuthorityProxy _authorityProxy = null!;

        private CancellationTokenSource? _connectionCts;
        private IDisposable? _activeConnectionSubscription;
        private IDisposable? _lobbyStateSubscription;
        private ILobbyChatClientProxy? _lobbyChatClientProxy;
        private IJxMatchmakingReadOnlyLobby? _previousLobby;

        [Preserve]
        public LobbyScreenController(
            ILocalSave localSave,
            INavigation navigation,
            IUserContext userContext,
            IJxMatchmakingIntegration matchmaking,
            ILoadingPlaceholderController loadingPlaceholderController,
            IPopupsManager popupsManager,
            IFriendPresenter friendPresenter,
            IProfilePresenter profilePresenter,
            IJxUserRelationshipIntegration relationship,
            ILobbyChatViewsManager chatViewsManager
        )
        {
            _localSave = localSave;
            _navigation = navigation;
            _userContext = userContext;
            _matchmaking = matchmaking;
            _loadingPlaceholderController = loadingPlaceholderController;

            _popupsManager = popupsManager;
            _friendPresenter = friendPresenter;
            _profilePresenter = profilePresenter;
            _relationship = relationship;
            _chatViewsManager = chatViewsManager;
            _forceExitStarted = false;
        }

        protected override UniTask OnPreloadedAsync()
        {
            _matchmaking.RestoreLobbyConnectionAsync().Forget();
            return base.OnPreloadedAsync();
        }

        protected override UniTask OnInitializeAsync()
        {
            View.Initialize(
                this,
                _loadingPlaceholderController,
                _chatViewsManager,
                _localSave,
                _popupsManager,
                _userContext,
                _friendPresenter,
                _profilePresenter
            );
            
            return base.OnInitializeAsync();
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();
            
            _relationship.OnlinePlacements.SetPlacement(PlayerOnlinePlacements.InLobby);
            _forceExitStarted.TryReset();
            SyncConnectionAsync().Forget();
        }

        protected override void OnViewDetached()
        {
            base.OnViewDetached();

            _forceExitStarted.TrySet();
            _activeConnectionSubscription?.Dispose();
            _lobbyStateSubscription?.Dispose();
            
            View.CleanupState();
            _previousLobby = null;
            _lobbyChatClientProxy?.Dispose();

            _connectionCts?.CancelAndDispose();
            _connectionCts = null;
        }

        private async UniTaskVoid SyncConnectionAsync()
        {
            try
            {
                var activeConnection = _matchmaking.ActiveConnection.Value;
                if (activeConnection == null)
                {
                    View.ShowLoading();
                    
                    _connectionCts = new CancellationTokenSource();

                    var restoredConnection = await _matchmaking.RestoreLobbyConnectionAsync();
                    if (restoredConnection == null)
                    {
                        await _matchmaking.CreateNewLobbyConnectionAsync(
                            MonsterLandConstants.DefaultMap,
                            MonsterLandConstants.DefaultGameMode,
                            JxMatchmakingTeamKind.Escapee,
                            _connectionCts.Token
                        );
                    }
                }

                OnConnected();
            }
            catch (Exception ex)
            {
                if (_connectionCts?.IsCancellationRequested != true)
                    Logger.LogError(ex);
                
                ForceExit(null);
            }
        }

        private void OnConnected()
        {
            _activeConnectionSubscription?.Dispose();
            _activeConnectionSubscription = _matchmaking.ActiveConnection.SubscribeAndFire(OnActiveConnectionChanged);
        }

        private void OnActiveConnectionChanged(IJxMatchmakingConnection? connection)
        {
            _lobbyStateSubscription?.Dispose();

            if (connection == null)
            {
                ForceExit("Connection lost");
                return;
            }

            if (connection is not IJxLobbyConnection lobbyConnection)
            {
                ForceExit($"Invalid lobby connection `{connection.GetType().Name}`");
                return;
            }

            _authorityProxy = new LobbyAuthorityProxy(lobbyConnection);
            _lobbyStateSubscription = connection.Lobby.State.SubscribeAndFire(OnLobbyStateChanged);
        }

        private void OnLobbyStateChanged()
        {
            var activeConnection = _matchmaking.ActiveConnection.Value;
            if (activeConnection == null)
            {
                ForceExit("null connection on updated");
                return;
            }

            var state = activeConnection.Lobby?.State?.Value;
            if (state == null)
            {
                ForceExit("null state on updated");
                return;
            }

            var lobby = activeConnection.Lobby;
            if (_previousLobby != null && _previousLobby.GetType() == lobby?.GetType() && ReferenceEquals(_previousLobby, lobby))
                return;
            _previousLobby = lobby;
            
            var interactableLobby = activeConnection.Lobby as IJxMatchmakingInteractableLobby;
            
            _lobbyChatClientProxy?.Dispose();
            _lobbyChatClientProxy = EmptyLobbyChatClientProxy.Instance;
            if (interactableLobby != null)
                _lobbyChatClientProxy = new LobbyChatClientProxy(interactableLobby);
            
            View.ShowParty(state, _lobbyChatClientProxy);
        }

        private void ForceExit(string? message)
        {
            View.ShowLoading();

            if (string.IsNullOrEmpty(message))
                Logger.LogError($"Force exit `{message}`");

            if (_forceExitStarted.TrySet())
                View.InvokeAfterDelay(Exit, TimeSpan.FromMilliseconds(200));
        }

        #region CONTROLS

        void ILobbyControlHandler.SelectTeam(JxMatchmakingTeamKind team) 
            => _authorityProxy.TrySelectTeamAsync(team).Forget();

        void ILobbyControlHandler.OnHeroesClick(JxMatchmakingTeamKind team) =>
            _navigation.Navigate<CharactersScreen>(CharacterScreenParametersBuilder.CollectionOnlyTeam(team));

        void ILobbyControlHandler.Leave()
        {
            Exit();
            _authorityProxy.TryLeaveAsync().Forget();
        }

        void ILobbyControlHandler.SetReadiness(bool isReady) => _authorityProxy.TrySetReadyAsync(isReady).Forget();

        async void ILobbyControlHandler.KickPlayer(IJxLobbyPlayerState player)
        {
            var result = await _navigation.NavigateAndWaitAsync<DialogScreen, bool?>(
               new DialogScreenParameters(
                   "KickLobbyPlayer",
                   messageTemplate: new JxStringTemplateParameters().Set("NICKNAME", player.Info.Nickname))
               );

            if (result != true)
                return;

            _authorityProxy.TryKickAsync(player).Forget();
        }

        #endregion
    }
}