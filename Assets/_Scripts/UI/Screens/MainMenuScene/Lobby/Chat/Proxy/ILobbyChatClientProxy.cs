using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Jx.Matchmaking.Interactions.Chats;
using Jx.Utils.ChangeableObjects;

namespace UI.Screens.MainMenuScene.Lobby.Chat.Proxy
{
    public interface ILobbyChatClientProxy : IDisposable
    {
        IJxChangeableObject<IReadOnlyList<IJxMatchmakingChatRecord>> Records { get; }
        IJxChangeableObject<bool> IsMuted { get; }
        IJxChangeableObject<int> UnreadRecordCount { get; }

        void MarkAllRecordsAsRead();
        UniTask<bool> SetMutedAsync(bool isMuted);
        UniTask<bool> SendMessageAsync(JxMatchmakingChatRecordContent content);
    }
}