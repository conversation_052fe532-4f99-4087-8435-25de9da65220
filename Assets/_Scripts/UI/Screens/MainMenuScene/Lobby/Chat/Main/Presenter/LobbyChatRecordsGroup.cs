using System.Collections.Generic;
using Jx.Matchmaking.Interactions.Chats;

namespace UI.Screens.MainMenuScene.Lobby.Chat.Main.Presenter
{
    public class LobbyChatRecordsGroup
    {
        public LobbyChatRecordsGroup(LobbyChatRecordGroupType type, IReadOnlyList<IJxMatchmakingChatRecord> records)
        {
            Type = type;
            Records = records;
        }

        public LobbyChatRecordGroupType Type { get; }
        public IReadOnlyList<IJxMatchmakingChatRecord> Records { get; }
    }
}