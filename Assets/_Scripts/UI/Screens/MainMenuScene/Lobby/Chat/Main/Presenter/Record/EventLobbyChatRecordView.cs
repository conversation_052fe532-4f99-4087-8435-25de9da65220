using System.Diagnostics;
using System.Linq;
using App.Localization.Components.GlobalLocalization;
using App.Localization.Providers;
using Core.UI.Components.Text;
using Jx.Matchmaking.Interactions.Chats;
using Jx.Utils.Logging;
using Jx.Utils.Objects;
using MonsterLand.Matchmaking.Dto.LobbyActions;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Lobby.Chat.Main.Presenter.Record
{
    public class EventLobbyChatRecordView : MonoBehaviour,
                                            ILobbyChatRecordView
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(EventLobbyChatRecordView));

        [SerializeField]
        private RectTransform _rectTransform = null!;
        
        [SerializeField]
        private JxRootTextComponent _eventName = null!;

        public IJxMatchmakingChatRecord Record { get; private set; } = null!;

        public RectTransform RectTransform => _rectTransform;

        public void Render(IJxMatchmakingChatRecord record)
        {
            Record = record;
            
            if (record.Content is not JxMatchmakingChatRecordContentLocalized localizedContent)
            {
                _logger.LogError($"Content type mismatch. Expected '{nameof(JxMatchmakingChatRecordContentLocalized)}' but get '{record.Content.GetType().Name}'");
                return;
            }

            if (record.Sender != null)
                _logger.LogError("Event must NOT have sender. Looks like a server error!");
            
            var localization = LocalizationKeyProvider.Instance.GetForChat().GetEventKey(localizedContent.Key);
            _eventName.SetGlobalLocalization(LocalizationFolderType.Chat, localization);
            _eventName.SetTextInterpolationContext(localizedContent.GetInterpolationParameters());
        }
        
        [Conditional("UNITY_EDITOR")]
        private void OnValidate()
        {
            _rectTransform = transform.CastOrNull<RectTransform>();
        }
    }
}