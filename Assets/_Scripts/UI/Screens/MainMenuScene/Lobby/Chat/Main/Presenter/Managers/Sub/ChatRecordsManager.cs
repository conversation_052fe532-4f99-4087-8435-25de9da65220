using System;
using Core.Managers.AssetLoader;
using Core.Utils.Pooling;
using Jx.Matchmaking.Interactions.Chats;
using MonsterLand.Matchmaking.Dto.LobbyActions;
using UI.Screens.MainMenuScene.Lobby.Chat.Main.Presenter.Record;
using Object = UnityEngine.Object;

namespace UI.Screens.MainMenuScene.Lobby.Chat.Chat.Presenter.Managers.Sub
{
    public abstract class ChatRecordsManager
    {
        protected const string Path = "Screens/MainMenuScene/Lobby/Chat/Records/Items";

        private IJxObjectPool<EmojiUserLobbyChatRecordView> _emojiPool = null!;
        private IJxObjectPool<TextMessageUserLobbyChatRecordView> _textMessagePool = null!;
            
        protected abstract string PathToEmojiPrefab { get; }
        protected abstract string PathToTextMessagePrefab { get; }

        public void Initialize()
        {
            var emojiPrefab = JxResourceLoader.Instance.LoadPrefab<EmojiUserLobbyChatRecordView>(PathToEmojiPrefab)
                              ?? throw new NullReferenceException();
                
            var textMessagePrefab = JxResourceLoader.Instance.LoadPrefab<TextMessageUserLobbyChatRecordView>(PathToTextMessagePrefab)
                                    ?? throw new NullReferenceException();

            _emojiPool = new JxUnityObjectPool<EmojiUserLobbyChatRecordView>(() => Object.Instantiate(emojiPrefab));
            _textMessagePool = new JxUnityObjectPool<TextMessageUserLobbyChatRecordView>(() => Object.Instantiate(textMessagePrefab));
        }

        public UserLobbyChatRecordView Get(IJxMatchmakingChatRecord record)
        {
            switch (record.Content)
            {
                case JxMatchmakingChatRecordContentText:
                case JxMatchmakingChatRecordContentLocalized:
                    return GetTextMessage(record);
                case JxMatchmakingChatRecordContentEmoji:
                    return GetEmoji(record);
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public void Release(ILobbyChatRecordView view)
        {
            switch (view)
            {
                case EventLobbyChatRecordView:
                    throw new NotSupportedException();
                case EmojiUserLobbyChatRecordView emoji:
                    ReleaseEmoji(emoji);
                    break;
                case TextMessageUserLobbyChatRecordView textMessage:
                    ReleaseTextMessage(textMessage);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(view));
            }
        }

        private void ReleaseEmoji(EmojiUserLobbyChatRecordView view) => _emojiPool.Release(view);
        private void ReleaseTextMessage(TextMessageUserLobbyChatRecordView view) => _textMessagePool.Release(view);

        private EmojiUserLobbyChatRecordView GetEmoji(IJxMatchmakingChatRecord record)
        {
            var view = _emojiPool.Get();
            view.Render(record);
            return view;
        }

        private TextMessageUserLobbyChatRecordView GetTextMessage(IJxMatchmakingChatRecord record)
        {
            var view =  _textMessagePool.Get();
            view.Render(record);
            return view;
        }
    }
}