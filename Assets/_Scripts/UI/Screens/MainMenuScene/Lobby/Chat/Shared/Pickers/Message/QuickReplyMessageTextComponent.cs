using System.Diagnostics;
using App.Localization.Components.GlobalLocalization;
using Core.UI.Components.Text;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Lobby.Chat.QuickReply
{
    [RequireComponent(typeof(JxTextComponent))]
    public class QuickReplyMessageTextComponent : MonoBehaviour
    {
        [SerializeField]
        private JxRootTextComponent _text = null!;

        public string LocalizationKey => _text.Key;

        public void Render(string id)
        {
            _text.SetGlobalLocalization(LocalizationFolderType.ChatQuickAnswers, id);
        }

        [Conditional("UNITY_EDITOR")]
        private void OnValidate()
        {
            _text ??= GetComponent<JxRootTextComponent>();
        }
    }
}