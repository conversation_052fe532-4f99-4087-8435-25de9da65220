using System;
using DG.Tweening;
using UI.Screens.MainMenuScene.Lobby.Chat.Shared.Pickers.Animation;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Lobby.Chat.Shared.Pickers.Emoji
{
    [RequireComponent(typeof(ScaleAnimationComponentPickerLobbyChat))]
    public class EmojiPickerViewMainLobbyChat : EmojiPickerViewLobbyChat
    {
        [SerializeField]
        private ScaleAnimationComponentPickerLobbyChat _scaleAnimation = null!;
        
        protected override string ViewPath { get; } = "Screens/MainMenuScene/Lobby/Chat/QuickReplies/Main/Emoji";

        public bool IsShown { get; private set; } = false;

        private void OnDisable()
        {
            IsShown = false;
        }

        public override void Show()
        {
            gameObject.SetActive(true);
            IsShown = true;
            _scaleAnimation.DoSetIsShown(true);
        }

        public override void Hide()
        {
            IsShown = false;
            _scaleAnimation.DoSetIsShown(false).OnComplete(() =>
            {
                if (IsShown)
                    return;

                gameObject.SetActive(false);
            });
        }
    }
}