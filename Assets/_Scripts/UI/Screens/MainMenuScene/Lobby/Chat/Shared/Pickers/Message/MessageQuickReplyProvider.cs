using System.Collections.Generic;

namespace UI.Screens.MainMenuScene.Lobby.Chat.QuickReply
{
    public class MessageQuickReplyProvider
    {
        private static readonly IReadOnlyList<string> _ids = new string[]
        {
            "Hi.v1",
            "Hey.v1",
            "Goodbye.v1",
            "WhichCharacterSelect.v1",
            "LetsGo.v1",
            "Wait.v1"
        };
        
        public static MessageQuickReplyProvider Instance { get; } = new();

        private MessageQuickReplyProvider()
        {
        }

        public IReadOnlyList<string> Get()
        {
            return _ids;
        }
    }
}