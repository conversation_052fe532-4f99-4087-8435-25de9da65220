using Core.Extensions;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using System.Collections.Generic;
using UI.Screens.GameplayScene.Components.Emoji;
using UnityEngine;

public class GameplayEmojiSelectorAnimationController : MonoBehaviour
{
    [SerializeField]
    private CanvasGroup _containerCanvasGroup;

    [Header("Settings")]
    [SerializeField]
    private float _fadeDuration = 0.5f;

    private readonly IAnimationSlot _showAnimation = new AnimationSlot("round-selector-show");

    private IReadOnlyList<IRoundSelectorSector> _sectors = null!;

    public void Initialize(IReadOnlyList<IRoundSelectorSector> sectors)
    {
        _sectors = sectors;
    }

    public void Cleanup() => _showAnimation.Stop();

    public void SetVisible(bool isVisible)
    {
        _showAnimation.Stop();
        
        var sequence = DOTween.Sequence().Join(_containerCanvasGroup.DOFade(isVisible ? 1f : 0f, _fadeDuration));

        foreach (var sector in _sectors)
            sequence.Join(sector.SetVisibleTween(isVisible));

        _showAnimation.PlayNew(sequence);
    }
}
