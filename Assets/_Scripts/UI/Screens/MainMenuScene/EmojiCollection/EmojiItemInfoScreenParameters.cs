using Api.Client.User.Billing.Products;
using LoM.Emojis.ClientIntegration;

namespace Ui.Screens.MainMenuScene.EmojiCollection.EmojiItemInfo
{
    public class EmojiItemInfoScreenParameters
    {
        public EmojiItemInfoScreenParameters(IJxUserProduct product, EmojiType emojiType) 
        {
            Product = product;
            EmojiType = emojiType;
        }

        public EmojiType EmojiType { get; private set; }

        public IJxUserProduct Product { get; private set; }
    }
}