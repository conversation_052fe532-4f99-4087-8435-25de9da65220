using App.Components.Contexts;
using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UI.Components.Emoji;
using UnityEngine;

namespace Ui.Screens.MainMenuScene.EmojiCollection.EmojiItemInfo
{
    [RequireComponent(typeof(JxContextBoundMonoBehaviour))]
    [ScreenController(controller: typeof(EmojiItemInfoScreenController))]
    public class EmojiItemInfoScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public ButtonComponent BackgroundBackButton { get; private set; } = null!;

        [field: SerializeField]
        public DynamicEmojiView EmojiView { get; private set; } = null!;

        [field: SerializeField]
        public PriceButtonComponent PriceButton { get; private set; } = null!;
    }
}