using System.Collections.Generic;
using Core.UI.Components.Text;
using Jx.Utils.Logging;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Chests.ChestInfo.Components.RewardsChances.AdditionalBlocks
{
    public class ConnectionBlockChestReward : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(ConnectionBlockChestReward));
        
        [SerializeField]
        private JxTextComponent _text;

        public void Render(ConnectionBlockChestRewardType type)
        {
            _text.SetLocalizationPostfix(ConvertTypeToString(type));
        }

        private string ConvertTypeToString(ConnectionBlockChestRewardType type)
        {
            gameObject.SetActive(true);
            
            switch (type)
            {
                case ConnectionBlockChestRewardType.And:
                    return "AndText";
                case ConnectionBlockChestRewardType.Or:
                    return "OrText";
                default:
                    _logger.LogError($"Unknown connection block type", trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["Type"] = type
                    });
                    gameObject.SetActive(false);
                    break;
            }

            return string.Empty;
        }
    }
}