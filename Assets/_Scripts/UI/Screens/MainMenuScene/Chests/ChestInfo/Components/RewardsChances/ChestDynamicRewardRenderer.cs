using Core.UI.Components.Text;
using Jx.Utils.Logging;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Chests.ChestInfo.Components.RewardsChances
{
    public abstract class ChestDynamicRewardRenderer<TParameters> : MonoBehaviour
    {
        [field: SerializeField]
        protected JxTextComponent DescriptionText { get; private set; }
        
        [field: Tooltip("Optional")]
        [field: SerializeField]
        protected JxTextComponent HeaderText { get; private set; }
        
        protected TParameters Parameters { get; private set; }
        protected IJxLogger Logger { get; private set; }

        protected virtual void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        public virtual void Render(TParameters parameters)
        {
            Parameters = parameters;
            DescriptionText.Text = GetDescription();
        }
        
        protected abstract string GetDescription();
    }
}