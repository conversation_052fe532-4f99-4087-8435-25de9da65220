using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Core.UIReusable.Timer;
using UI.Screens.MainMenuScene.Chests.ChestInfo;
using UnityEngine;

namespace UI.Screens.MainMenuScene.Chests.MatchChestInfo
{
    [ScreenController(controller: typeof(MatchChestInfoScreenController))]
    public class MatchChestInfoScreen : ChestInfoScreen
    {
        [field: SerializeField]
        public PriceButtonComponent InstantUnlockButton { get; private set; }

        [field: SerializeField]
        public JxTextComponent StartUnlockText { get; private set; }
        
        [field: SerializeField]
        public TimerButtonComponent StartUnlockButton { get; private set; }

        [field: SerializeField]
        public PriceButtonComponent AccelerateUnlockingButton { get; private set; }

        [field: SerializeField]
        public UITimerComponent UnlockingTimer { get; private set; }

        [field: SerializeField]
        public GameObject AnotherChestOpeningContent { get; private set; }
        
        [field: SerializeField]
        public GameObject GoldenPassContent { get; private set; }
        
        [field: SerializeField]
        public JxTextComponent GoldenPassHint { get; private set; }
        
        [field: Header("Tutorial")]
        [field: SerializeField]
        public RectTransform StartUnlockFingerPivot { get; private set; }
        
        [field: SerializeField]
        public RectTransform InstantUnlockFingerPivot { get; private set; }
    }
}