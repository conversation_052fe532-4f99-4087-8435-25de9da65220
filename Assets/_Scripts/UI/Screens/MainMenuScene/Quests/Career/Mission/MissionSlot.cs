using System;
using System.Collections.Generic;
using Api.Client.User.Missions;
using App.Components.Contexts;
using App.Localization.Components;
using Configs.Tutorial.Components;
using Core.Extensions;
using Core.Helpers.TweenAnimation;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using Core.UIReusable.Progress;
using DG.Tweening;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;
using Jx.Utils.Observable;
using LoM.Messaging.ClientIntegrations.Missions;
using UI.Components.Reward;
using UI.Screens.MainMenuScene.Quests.Components.Animations;
using UI.Screens.MainMenuScene.Quests.Components.StyleApplier;
using UnityEngine;
using UnityEngine.Scripting;
using UnityEngine.UI;
using Zenject;

namespace UI.Screens.MainMenuScene.Quests.Career.Mission
{
    [RequireComponent(typeof(JxContextBoundMonoBehaviour))]
    [RequireComponent(typeof(LayoutElement))]
    [RequireComponent(typeof(MissionSlotAnimationController))]
    [RequireComponent(typeof(QuestSlotStyleApplier))]
    public class MissionSlot : MonoBehaviour,
                               IPoolable<RectTransform, IUserMission, IMemoryPool>,
                               IDisposable,
                               IJxLocalizationContext
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(MissionSlot));

        [SerializeField]
        private QuestSlotStyleApplier _styleApplier;
        
        [SerializeField]
        private MissionSlotAnimationController _animationController;
        
        [SerializeField]
        private JxTextComponent _title;

        [SerializeField]
        private RewardContainer _unlockedRewardContainer;

        [SerializeField]
        private RectTransform _progressBarContent;

        [SerializeField]
        private ProgressBar _progressBar;

        [SerializeField]
        private JxTextComponent _progressBarText;

        [SerializeField]
        private ButtonComponent _claimButton;

        [SerializeField]
        private RectTransform _claimButtonContent;

        [SerializeField]
        private RectTransform _lockedContent;

        [SerializeField]
        private RewardContainer _lockedRewardContainer;

        [SerializeField]
        private Color _titleColorLocked = Color.grey;

        [SerializeField]
        private Color _titleColorDefault = Color.white;
        
        [field: SerializeField]
        public AccentPointerTutorialInfo ClaimRewardAccentInfo { get; private set; }
        
        [field: SerializeField]
        public RectTransform ClaimButtonRectTransform { get; private set; }
        
        private readonly JxObservable _closeObservable = new JxObservable();
        private readonly IAnimationSlot _closeAnimationSlot = new AnimationSlot("close");
        
        private IMemoryPool _pool;
        private IDisposable _progressSubscription;

        public Button.ButtonClickedEvent ClaimEvent => _claimButton.onClick;
        public IUserMission Mission { get; private set; }

        #region LOCALIZAION

        public bool IsRoot => true;
        public string Key => "MissionSlot";
        public string DefaultText => string.Empty;

        #endregion

        private void Awake()
        {
            ClaimButtonRectTransform ??= _claimButton.RectTransform();
        }

        private void OnDestroy()
        {
            _closeAnimationSlot?.Destroy();
        }

        public void OnSpawned(RectTransform parent, IUserMission mission, IMemoryPool pool)
        {
            _pool = pool;
            Mission = mission;
            transform.SetParent(parent, false);
            gameObject.SetActive(true);
            Setup();
        }

        public void OnDespawned()
        {
            Mission = null;
            
            _progressSubscription?.Dispose();
            _progressSubscription = null;

            _unlockedRewardContainer.Clear();
            _lockedRewardContainer.Clear();

            _closeAnimationSlot.Stop();
            _closeAnimationSlot.Complete();
            
            _animationController.ResetAll();

            gameObject.SetActive(false);
        }

        public void Dispose()
        {
            _pool?.Despawn(this);
            _pool = null;
        }

        public IDisposable SubscribeToClose(Action callback)
        {
            return _closeObservable.Subscribe(callback);
        }

        private void Setup()
        {
            _progressSubscription?.Dispose();
            _progressSubscription = Mission.Progress.SubscribeAndFire(OnProgressChange);
        }

        private void OnProgressChange(UserMissionProgress progress)
        {
            Clear();
            
            var state = progress.State;

            RenderShared();

            switch (state)
            {
                case MissionProgressState.RewardClaimed:
                    RenderRewardClaimed();
                    return;
                case MissionProgressState.Locked:
                    RenderLocked();
                    break;
                case MissionProgressState.InProgress:
                    RenderInProgress();
                    break;
                case MissionProgressState.Completed:
                    RenderCompleted();
                    break;
                default:
                    _logger.LogError($"Unknown mission state", trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["State"] = state
                    });
                    OnDespawned();
                    return;
            }
            
            _styleApplier.Apply(ConvertStateToStyle(state));
        }

        private void Clear()
        {
            _claimButton.interactable = false;
            
            _progressBarContent.gameObject.SetActive(false);
            _unlockedRewardContainer.gameObject.SetActive(false);
            _lockedContent.gameObject.SetActive(false);
            _lockedRewardContainer.gameObject.SetActive(false);
            _claimButtonContent.gameObject.SetActive(false);
            _unlockedRewardContainer.gameObject.SetActive(false);
        }

        private void RenderShared()
        {
            Mission.Definition.GetLocalizationModificator().ApplyDescription(_title);
        }

        private void RenderRewardClaimed()
        {
            _title.Color = _titleColorDefault;
            _closeAnimationSlot.PlayNew(_animationController.PlayClose().OnComplete(() =>
                {
                    _closeObservable.Invoke();
                    OnDespawned();
                })
            );
        }

        private void RenderInProgress()
        {
            _title.Color = _titleColorDefault;
            _progressBarContent.gameObject.SetActive(true);

            _unlockedRewardContainer.gameObject.SetActive(true);
            _unlockedRewardContainer.SetupForQuestScreen(Mission.Reward);

            var targetProgress = Mission.Definition.TargetValue <= 0 ? 0f: (float)Mission.Progress.Value.Value / Mission.Definition.TargetValue;

            _progressBar.Progress = targetProgress;
            _progressBarText.Text = $"{Mission.Progress.Value.Value}/{Mission.Definition.TargetValue}";
        }

        private void RenderLocked()
        {
            _title.Color = _titleColorLocked;
            _lockedContent.gameObject.SetActive(true);
            _unlockedRewardContainer.gameObject.SetActive(true);
            _lockedRewardContainer.gameObject.SetActive(true);

            _lockedRewardContainer.SetupForQuestScreen(Mission.Reward);
        }

        private void RenderCompleted()
        {
            _title.Color = _titleColorDefault;
            _claimButtonContent.gameObject.SetActive(true);
            _claimButton.interactable = true;
            _unlockedRewardContainer.gameObject.SetActive(true);
            _unlockedRewardContainer.SetupForQuestScreen(Mission.Reward);
        }

        private QuestSlotStyleType ConvertStateToStyle(MissionProgressState state)
        {
            return state switch
            {
                MissionProgressState.Locked => QuestSlotStyleType.Locked,
                MissionProgressState.InProgress => QuestSlotStyleType.InProgress,
                MissionProgressState.Completed => QuestSlotStyleType.Completed,
                MissionProgressState.RewardClaimed => QuestSlotStyleType.RewardClaimed,
                _ => QuestSlotStyleType.None
            };
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            _animationController ??= GetComponent<MissionSlotAnimationController>();
            _styleApplier ??= GetComponent<QuestSlotStyleApplier>();
            ClaimButtonRectTransform ??= _claimButton.RectTransform();
        }
#endif

        [Preserve]
        public class Factory : PlaceholderFactory<RectTransform, IUserMission, MissionSlot>
        {}
    }
}