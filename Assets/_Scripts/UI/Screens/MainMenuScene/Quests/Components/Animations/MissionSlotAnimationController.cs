using Core.Helpers;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.Quests.Components.Animations
{
    public class MissionSlotAnimationController : MonoBehaviour
    {
        [SerializeField]
        private LayoutElement _layoutElement;

        [SerializeField]
        private RectTransform _content;

        [Header("Settings")]
        [SerializeField]
        [Min(0f)]
        private float _horizontalCloseDuration = 0.5f;

        [SerializeField]
        private Ease _horizontalCloseEase = Ease.Flash;

        [SerializeField]
        [Min(0f)]
        private float _verticalCloseDuration = 0.5f;

        [SerializeField]
        private Ease _verticalCloseEase = Ease.Flash;

        private float _startPreferredHeight;
        private Vector3? _startContentLocalPosition;

        private void Awake()
        {
            _startPreferredHeight = _layoutElement.preferredHeight;
        }

        public Tween PlayClose()
        {
            var moveXWidth = _content.rect.width / 1.9f;
            _startContentLocalPosition ??= _content.localPosition;

            _layoutElement.preferredHeight = _startPreferredHeight;
            _content.localPosition = _startContentLocalPosition.Value;

            return DOTween.Sequence()
                .Append(
                    _content
                        .DOLocalMoveX(-moveXWidth, _horizontalCloseDuration)
                        .SetEase(_horizontalCloseEase)
                )
                .Append(
                    _layoutElement
                        .DOPreferredHeight(0f, _verticalCloseDuration)
                        .SetEase(_verticalCloseEase)
                );
        }

        public void ResetAll()
        {
            if (_startContentLocalPosition.HasValue)
            {
                _content.localPosition = _startContentLocalPosition.Value;
            }

            _layoutElement.preferredHeight = _startPreferredHeight;
        }
    }
}