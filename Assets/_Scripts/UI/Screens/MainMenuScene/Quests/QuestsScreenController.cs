using System;
using System.Collections.Generic;
using Api.Client.User;
using Api.Client.User.Missions;
using Api.Client.User.Quests;
using Core.Extensions;
using Core.Helpers.Chests;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.Loading.Cover;

using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;
using Jx.Utils.UnityComponentExtensions;
using LoM.Messaging.ClientIntegrations.Quests;
using Managers.Rewards;
using Tutorial.Providers.Handlers;
using Tutorial.Runner;
using Tutorial.Schemas.Parameters;
using UI.Screens.MainMenuScene.Quests.Career.Mission;
using UI.Screens.MainMenuScene.Quests.Components.DailyQuests;
using UI.Screens.MainMenuScene.Quests.Components.MissionPack;
using UnityEngine;
using UnityEngine.Scripting;

namespace UI.Screens.MainMenuScene.Quests
{
    [Preserve]
    public class QuestsScreenController : BaseNavigationScreenController<QuestsScreen>
    {
        private readonly IScreenTutorialHandlerFactory _tutorialHandlerFactory;
        private readonly ILoadingCoverController _loadingCoverController;
        private readonly IUserContext _userContext;
        private readonly IRewardPresenter _rewardPresenter;
        private readonly IChestInteraction _chestInteraction;

        private bool _isQuestRewardTakenForTutorial;
        private bool _isMissionRewardTakenForTutorial;
        
        private IScreenTutorialHandler _tutorialHandler = null!;

        [Preserve]
        public QuestsScreenController(
            IScreenTutorialHandlerFactory tutorialHandlerFactory,
            ILoadingCoverController loadingCoverController,
            IUserContext userContext,
            IRewardPresenter rewardPresenter,
            IChestInteraction chestInteraction
        )
        {
            _tutorialHandlerFactory = tutorialHandlerFactory;
            _loadingCoverController = loadingCoverController;
            _userContext = userContext;
            _rewardPresenter = rewardPresenter;
            _chestInteraction = chestInteraction;
        }

        public override Navigation.NavigationMode NavigationMode => Navigation.NavigationMode.Additive;

        protected override UniTask OnInitializeAsync()
        {
            _tutorialHandler = _tutorialHandlerFactory.Create(View, this);

            return base.OnInitializeAsync();
        }

        protected override UniTaskVoid OnDeInitialized()
        {
            _tutorialHandler?.Dispose();

            return base.OnDeInitialized();
        }

        protected override void OnViewAttached()
        {
            View.OpenLastShownPage();
            
            DisposeOnDetach(View.PagesContainer.CurrentPageType.Subscribe(OnCurrentPageChange));
            DisposeOnDetach(View.PagesContainer.QuestsPage.SubscribeToClaimReward(i => OnQuestClaimRewardAsync(i).Forget()));
            DisposeOnDetach(View.PagesContainer.MissionsPage.SubscribeToClaimMissionReward(m => OnMissionClaimRewardAsync(m).Forget()));
            DisposeOnDetach(View.PagesContainer.MissionsPage.SubscribeToClaimMissionPackReward(p => OnMissionPackClaimRewardAsync(p).Forget()));
            DisposeOnDetach(_userContext.DailyQuests.UserProgress.SubscribeAndFire(OnQuestsUpdate));
            DisposeOnDetach(View.BackgroundExitButton.OnClick(Exit));

            RenderIndicators();
        }

        private void RenderIndicators()
        {
            RenderMissionsIndicator();
            RenderQuestsIndicator();
        }

        private void RenderQuestsIndicator()
        {
            View.QuestsTab.AlertIndicatorComponent.gameObject.SetActive(false);
            View.QuestsTab.CountIndicatorComponent.gameObject.SetActive(false);

            var waitingQuestRewardCount = _userContext.DailyQuests.GetWaitingRewardCount();

            if (waitingQuestRewardCount > 0)
            {
                View.QuestsTab.CountIndicatorComponent.gameObject.SetActive(true);
                View.QuestsTab.CountIndicatorComponent.SetBoundedCount(waitingQuestRewardCount, maxDigitCount: 3);
            }
        }

        private void RenderMissionsIndicator()
        {
            View.MissionsTab.AlertIndicatorComponent.gameObject.SetActive(false);

            var waitingRewardCount = _userContext.Missions.Interaction.GetWaitingRewardCount();

            View.MissionsTab.CountIndicatorComponent.gameObject.SetActive(waitingRewardCount > 0);
            View.MissionsTab.CountIndicatorComponent.SetBoundedCount(waitingRewardCount, maxDigitCount: 3);
        }

        private void OnCurrentPageChange(Type pageType)
        {
            _tutorialHandler!.Stop();
            ReRunTutorial();
        }

        private void OnQuestsUpdate(UserQuestStorageClientIntegration obj)
        {
            ReRunTutorial();
        }

        private async UniTaskVoid OnMissionPackClaimRewardAsync(MissionPackSlot packSlot)
        {
            using (_tutorialHandler!.Pause())
            {
                var success = false;

                using (_loadingCoverController.Show())
                {
                    success = await packSlot.Pack.ClaimReward();
                }

                if (success)
                {
                    await ShowRewardsAsync(packSlot.ClaimButtonRectTransform.position);

                    // может быть так, что текущий пак еще не обновился
                    View.PagesContainer.MissionsPage.SwitchToNextPack();
                    
                    RenderIndicators();
                    ReRunTutorial();
                    return;
                }
            }

            ToastManager.Instance.Show("Error");

            Logger.LogError(
                $"Failed to claim mission pack reward",
                trackingFactory: () => new Dictionary<string, object>()
                {
                    ["Name"] = packSlot.Pack.Definition.Name,
                }
            );
        }

        private async UniTaskVoid OnMissionClaimRewardAsync(MissionSlot missionSlot)
        {
            var success = false;

            using (_tutorialHandler!.Pause())
            {
                _isMissionRewardTakenForTutorial = true;

                using (_loadingCoverController.Show())
                {
                    success = await missionSlot.Mission.ClaimReward();
                }

                if (success)
                {
                    await ShowRewardsAsync(missionSlot.ClaimButtonRectTransform.position);
                }
            }

            if (!success)
            {
                ToastManager.Instance.Show("Error");

                Logger.LogError(
                    $"Failed to claim mission reward",
                    trackingFactory: () => new Dictionary<string, object>()
                    {
                        ["Kind"] = missionSlot.Mission.Definition.Kind,
                        ["TargetValue"] = missionSlot.Mission.Definition.TargetValue
                    }
                );
            }

            RenderIndicators();
            ReRunTutorial();
        }

        private async UniTaskVoid OnQuestClaimRewardAsync(QuestSlot questSlot)
        {
            _isQuestRewardTakenForTutorial = true;

            using (_tutorialHandler!.Pause())
            {
                var success = false;

                using (_loadingCoverController.Show())
                {
                    success = await _userContext.DailyQuests.ClaimSlotRewardAsync(questSlot.Context.Index);
                }

                if (success)
                {
                    await ShowRewardsAsync(questSlot.ClaimButtonRectTransform.position);
                }
                else
                {
                    ToastManager.Instance.Show("Error");
                }
            }

            RenderIndicators();
            ReRunTutorial();
        }

        private async UniTask ShowRewardsAsync(Vector3 fromPosition)
        {
            try
            {
                await _chestInteraction.OpenPendingChestsAsync();
                await _rewardPresenter.PresentPendingAsync();

                if (_rewardPresenter.TryGetDisplayedApprovedDiff(out var diff))
                {
                    View.ResourcePanel.DoAccrueResource(diff, fromPosition, _rewardPresenter.MarkDisplayed);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex);
            }
        }

        private void ReRunTutorial()
        {
            _tutorialHandler.TryRun(
                new QuestScreenTutorialSchemeParameters(
                    isQuestRewardTaken: _isQuestRewardTakenForTutorial,
                    isMissionRewardTaken: _isMissionRewardTakenForTutorial
                )
            );
        }

        // private async UniTaskVoid ShowInfoAsync()
        // {
        //     _tutorialHandler.Stop();
        //     await _navigation.NavigateToDialogAndWait("QuestsInfo");
        //     ReRunTutorial();
        // }
    }
}