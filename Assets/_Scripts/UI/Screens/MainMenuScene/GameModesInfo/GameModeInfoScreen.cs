using App.Components.Contexts;
using App.Localization.Components;
using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using Core.UI.Components.Text;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.GameModes
{
    [RequireComponent(typeof(JxContextBoundMonoBehaviour))]
    [ScreenController(controller: typeof(GameModeInfoScreenController))]
    public class GameModeInfoScreen : BaseNavigationScreen,
                                      IJxLocalizationContext
    {
        [field: SerializeField]
        public JxTextComponent NameTitleText { get; private set; } = null!;

        [field: SerializeField]
        public JxTextComponent DescriptionText { get; private set; } = null!;

        [field: SerializeField]
        public Image Icon { get; private set; } = null!;
        
        [field: SerializeField]
        public ButtonComponent BackgroundExitButton { get; private set; } = null!;

        #region LOCALIZATION

        public bool IsRoot => true;

        public string Key => "GameModeInfo";

        public string DefaultText => string.Empty;

        #endregion
    }
}