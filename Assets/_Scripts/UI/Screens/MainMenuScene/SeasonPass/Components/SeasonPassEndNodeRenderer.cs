using System.Linq;
using Api.Client.User.SeasonPass;
using App.Localization.Providers;
using Core.Extensions;
using Core.UI.Components.Text;
using GooglePlayGames.BasicApi;
using Jx.Utils.Templating;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass
{
    public class SeasonPassEndNodeRenderer : SeasonPassNodeRenderer
    {
        private static readonly Color _pointsTextColor = ColorExtensions.GetColorFrom0255(255f, 191f, 1f, 255f);

        [SerializeField]
        private JxTextComponent _description = null!;

        [SerializeField]
        private RectTransform _divisor = null!;

        protected override void OnProgressUpdate(int progress)
        {
            base.OnProgressUpdate(progress);
            ProgressBar.RenderForInfinite();
        }

        protected override void RenderProgressText()
        {
            _description.SetTextInterpolationContext(new JxStringTemplateParameters().Set("COLOR", ColorUtility.ToHtmlStringRGBA(_pointsTextColor)));

            if (Interaction.Nodes.Any(n => n.IsLocked()))
            {
                ProgressText.gameObject.SetActive(false);
                _divisor.gameObject.SetActive(false);
                return;
            }
            
            var lastNode = Interaction.Nodes.LastOrDefault();
            if (lastNode == null)
            {
                ProgressText.gameObject.SetActive(false);
                _divisor.gameObject.SetActive(false);
                return;
            }
            
            ProgressText.gameObject.SetActive(true);
            _divisor.gameObject.SetActive(true);

            var x = Interaction.Progress.Value - lastNode.Threshold;
            var y = Node.Threshold - lastNode.Threshold;

            ProgressText.Text = $"{x}/{y}";
        }
    }
}