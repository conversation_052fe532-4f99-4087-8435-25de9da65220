using System;
using System.Linq;
using Api.Client.User.SeasonPass;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass
{
    public class SeasonPassDefaultNodeRenderer : SeasonPassNodeRenderer
    {
        [SerializeField]
        private UnlockNextNodeSeasonPassButton _unlockNextLockedNodeButton = null!;
        
        [SerializeField]
        private RectTransform _divisor = null!;
        
        public void Initialize(Action unlockNextNode)
        {
            _unlockNextLockedNodeButton.OnClick(unlockNextNode);
        }

        protected override void OnProgressUpdate(int progress)
        {
            base.OnProgressUpdate(progress);
            _unlockNextLockedNodeButton.gameObject.SetActive(CanRenderUnlockNextButton());
            
            if (Node.Index == 0)
            {
                ProgressBar.RenderFirst(Node);
            }
            else if (Node.Index == Interaction.Nodes.LastOrDefault()?.Index)
            {
                ProgressBar.RenderLastBeforeInfinite(Node);
            }
            else
            {
                ProgressBar.RenderInMiddle(Node);
            }
        }

        protected override void RenderProgressText()
        {
            if (Node.Index != Interaction.Nodes.FirstOrDefault(n => n.IsLocked())?.Index)
            {
                ProgressText.gameObject.SetActive(false);
                _divisor.gameObject.SetActive(false);
                return;
            }
            
            var previousNode = Interaction.NodesLinkedList.Find(Node)?.Previous?.Value;
            if (previousNode == null)
            {
                ProgressText.gameObject.SetActive(false);
                _divisor.gameObject.SetActive(false);
                return;
            }
            
            ProgressText.gameObject.SetActive(true);
            _divisor.gameObject.SetActive(true);

            var x = Interaction.Progress.Value - previousNode.Threshold;
            var y = Node.Threshold - previousNode.Threshold;
            
            ProgressText.Text = $"{x}/{y}";
        }

        private bool CanRenderUnlockNextButton()
        {
            var firstLockedNode = Interaction.Nodes.FirstOrDefault(n => n.IsLocked());
            if (firstLockedNode == null) 
                return false;

            return firstLockedNode.Threshold == Node.Threshold;
        }
    }
}