using Core.Extensions;
using Core.UI.Components.Buttons;
using System;
using Core.UI.Prices;
using Jx.Utils.UnityComponentExtensions;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass
{
    public class UnlockNextNodeSeasonPassButton : MonoBehaviour
    {
        [SerializeField]
        private ButtonComponent _button = null!;

        [SerializeField]
        private PriceTagRendererComponent _priceTag = null!;
        
        private Action _onClick = null!;
        
        private SeasonPassNodeRenderer? _node;
        
        private IDisposable? _scrollValueSubscription;
        private IDisposable? _clickSubscription;

        private void OnEnable()
        {
            _clickSubscription = _button.OnClick(() => _onClick?.Invoke());
        }

        private void OnDisable()
        {
            _clickSubscription?.Dispose();
        }

        public void OnClick(Action onClick)
        {
            _onClick = onClick;
        }
    }
}