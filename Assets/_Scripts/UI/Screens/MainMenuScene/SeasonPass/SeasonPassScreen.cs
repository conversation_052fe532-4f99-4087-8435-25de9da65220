using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass
{
    [ScreenController(typeof(SeasonPassScreenController))]
    public class SeasonPassScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public PremiumButtonSeasonPassScreen OfferButton { get; private set; } = null!;

        [field: SerializeField]
        public SeasonPassNodesContainer NodesContainer { get; private set; } = null!;

        [field: SerializeField]
        public ButtonComponent TasksButton { get; private set; } = null!;

        [field: SerializeField]
        public RectTransform BannerContainer { get; private set; } = null!; 
    }
}