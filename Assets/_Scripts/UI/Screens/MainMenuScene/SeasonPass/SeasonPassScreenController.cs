using System;
using System.Collections.Generic;
using Api.Client.User;
using Api.Client.User.Rewards.Adapters;
using Core.Helpers.Chests;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using Jx.Utils.Logging;
using Managers.Rewards;
using System.Linq;
using Core.Extensions.Buttons;
using Core.Loading.Cover;
using UI.Screens.MainMenuScene.FeatureAccessAlert;
using UI.Screens.MainMenuScene.Quests;
using Api.Client.User.SeasonPass;
using UI.Screens.MainMenuScene.SeasonPassOffer;
using UI.Screens.MainMenuScene.SeasonPassOffer.Presenter;
using UnityEngine.Scripting;
using Zenject;
using Core.Managers.AssetLoader;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass
{
    [Preserve]
    public class SeasonPassScreenController : BaseNavigationScreenController<SeasonPassScreen>
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(SeasonPassScreenController));

        private const string BannerPath = "SeasonPassBanners/Trickie";

        private readonly IUserContext _userContext;
        private readonly INavigation _navigation;
        private readonly IRewardPresenter _rewardPresenter;
        private readonly IChestInteraction _chestInteraction;
        private readonly ILoadingCoverController _loadingCoverController;
        private readonly ISeasonPassOfferPresenter _seasonPassOfferPresenter;
        private readonly DiContainer _diContainer;

        [Preserve]
        public SeasonPassScreenController(
            IUserContext userContext,
            INavigation navigation,
            IRewardPresenter rewardPresenter,
            IChestInteraction chestInteraction,
            ILoadingCoverController loadingCoverController,
            ISeasonPassOfferPresenter seasonPassOfferPresenter,
            DiContainer diContainer)
        {
            _userContext = userContext;
            _navigation = navigation;
            _rewardPresenter = rewardPresenter;
            _chestInteraction = chestInteraction;
            _loadingCoverController = loadingCoverController;
            _seasonPassOfferPresenter = seasonPassOfferPresenter;
            _diContainer = diContainer;
        }

        protected override UniTask OnInitializeAsync()
        {
            try
            {
                View.OfferButton.Initialize(_userContext.SeasonPass.Interaction, _seasonPassOfferPresenter);

                View.NodesContainer.Initialize(
                    _diContainer,
                    _userContext.SeasonPass.Interaction,
                    n => OnRewardButtonClickAsync(n, isPremiumReward: false).Forget(),
                    n => OnRewardButtonClickAsync(n, isPremiumReward: true).Forget(),
                    () => ClaimNextLockedNodeAsync().Forget()
                );

                var bannerPrefab = JxResourceLoader.Instance.LoadPrefab(BannerPath);

                if (bannerPrefab != null)
                {
                    var banner = GameObject.Instantiate(bannerPrefab, View.BannerContainer, false);
                    banner.gameObject.SetActive(true);
                }
            }
            catch (Exception exception)
            {
                Logger.LogError(exception);
                Exit();
            }
            
            return base.OnInitializeAsync();
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();
            
            try
            {
                View.NodesContainer.Setup();
                
                View.NodesContainer.ScrollToFirstAvailableToClaim(instantly: true);

                View.OfferButton.Setup();

                DisposeOnDetach(View.TasksButton.NavigateOnClick<QuestsScreen>(_navigation));
            }
            catch (Exception exception)
            {
                _logger.LogError(exception);
                Exit();
                return;
            }
        }

        protected override void OnViewDetached()
        {
            base.OnViewDetached();

            View.OfferButton.Cleanup();
            View.NodesContainer.Cleanup();
        }

        private async UniTaskVoid ClaimNextLockedNodeAsync()
        {
            var nextNode = _userContext.SeasonPass.Interaction.Nodes.FirstOrDefault(n => n.IsLocked());
            if (nextNode == null)
            {
                _logger.LogError("Failed to find any locked nodes");
                return;
            }
            
            var unlocked = false;
            try
            {
                var confirmed = await _navigation.NavigateToDialogAndWait("UnlockThresholdSeasonPass", trackResult: true);
                if (!confirmed.HasValue || !confirmed.Value)
                    return;
                
                using (_loadingCoverController.Show())
                {
                    unlocked = await nextNode.UnlockThresholdAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                ToastManager.Instance.ShowError();
                return;
            }
            
            if (unlocked)
                return;
            
            ToastManager.Instance.ShowError();
            _logger.LogError($"Failed to unlock next node threshold: {nextNode.Threshold}");
        }

        private async UniTaskVoid OnRewardButtonClickAsync(ISeasonPassNode node, bool isPremiumReward)
        {
            if (isPremiumReward && !_userContext.SeasonPass.Interaction.IsPremiumUser.Value)
            {
                _navigation.Navigate<SeasonPassOfferScreen>();
                return;
            }

            var rewardInteraction = node.GetRewardInteraction(isPremiumReward);
            switch (rewardInteraction.Status.Value)
            {
                case SeasonPassNodeRewardStatus.AvailableToClaim:
                    if (!rewardInteraction.CanBeClaimed())
                    {
                        ToastManager.Instance.Show("SeasonPassRewardsOrderAlert");
                        View.NodesContainer.ScrollToCurrent(instantly: false, isPremiumReward);
                        break;
                    }
                    
                    await ClaimRewardAsync(node, isPremiumReward);
                    View.NodesContainer.ScrollToCurrent(instantly: false, isPremiumReward);
                    break;
                case SeasonPassNodeRewardStatus.Locked:
                    ToastManager.Instance.Show("SeasonPassLockedAlert");
                    View.NodesContainer.ScrollToCurrent(instantly: false, isPremiumReward);
                    break;
            }
        }

        private async UniTask ClaimRewardAsync(ISeasonPassNode node, bool isPremiumReward)
        {
            var rewardInteraction = node.GetRewardInteraction(isPremiumReward);
            
            var isClaimed = false;
            try
            {
                using (_loadingCoverController.Show())
                    isClaimed = await rewardInteraction.ClaimAsync();

                if (isClaimed)
                    await ShowPendingRewardsAsync(rewardInteraction);
            }
            catch (Exception exception)
            {
                Logger.LogError(exception);
                ToastManager.Instance.ShowError();
            }

            if (isClaimed)
                return;

            Logger.LogError(
                "Failed to claim the reward",
                trackingFactory: () => new Dictionary<string, object>()
                {
                    ["RewardId"] = rewardInteraction.Reward.Id.ToString(),
                    ["Threshold"] = node.Threshold.ToString(),
                    ["IsPremium"] = isPremiumReward.ToString()
                }
            );

            ToastManager.Instance.ShowError();
        }

        private async UniTask ShowPendingRewardsAsync(ISeasonPassNodeRewardInteraction rewardInteraction)
        {
            if (rewardInteraction.Reward is FeatureAccessUserReward featureAccessUserReward)
                await TryProcessFeatureAlertAsync(featureAccessUserReward);
            
            await _chestInteraction.OpenPendingChestsAsync();
            await _rewardPresenter.PresentPendingAsync();
        }

        private UniTask TryProcessFeatureAlertAsync(FeatureAccessUserReward featureAccessUserReward)
        {
            var parameters = new FeatureAccessAlertScreenParameters(featureAccessUserReward.Type);
            return _navigation.NavigateAndWaitAsync<FeatureAccessAlertScreen>(parameters);
        }
    }
}