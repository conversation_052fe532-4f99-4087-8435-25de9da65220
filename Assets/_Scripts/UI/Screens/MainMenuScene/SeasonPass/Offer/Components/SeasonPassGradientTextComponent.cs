using System.Diagnostics;
using Core.Managers.AssetLoader;
using TMPro;
using UnityEngine;

namespace UI.Screens.MainMenuScene.SeasonPass.Components
{
    public class SeasonPassGradientTextComponent : MonoBehaviour
    {
        private static readonly int _faceTexShaderProperty = Shader.PropertyToID("_FaceTex");
        private const string GradientPath = "season-pass/text-gradient";

        [SerializeField]
        private TMP_Text _targetText = null!;

        private void Awake()
        {
           Rebuild();
        }

        private void Rebuild()
        {
            if (_targetText == null)
                return;
            
            var gradient = JxResourceLoader.Instance.LoadSpriteOrNull(GradientPath);
            if (gradient == null)
                return;
            
            _targetText.fontMaterial.SetTexture(_faceTexShaderProperty, gradient.texture);
        }

        // [Conditional("UNITY_EDITOR")]
        // private void OnValidate()
        // {
        //     _targetText ??= GetComponent<TextMeshProUGUI>();
        //     if (_targetText == null)
        //         return;
        //     
        //     Rebuild();
        // }
    }
}