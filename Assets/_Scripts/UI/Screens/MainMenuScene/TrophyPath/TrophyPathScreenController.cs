using System.Collections.Generic;
using System.Linq;
using Api.Client.User;
using Api.Client.User.Rewards.Adapters;
using Api.Client.User.TrophyPath;
using App.Analytics;
using App.Localization.Providers;
using Audio.Manager;
using Audio.Provider;
using Configs.Entities;
using Core.Helpers.Chests;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.Loading.Cover;
using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using ExternalServices.Telemetry;
using GameplayNetworking.Share.Character;
using Jx.Telemetry;
using Jx.User.Analytics.Api;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.Logging;
using Jx.Utils.UnityComponentExtensions;
using LoM.Messaging.Lom.Messaging.Packages.LoM.Messaging.Runtime.ClientIntegrations.FeatureAccess;
using Managers.Rewards;
using Savings;
using Tutorial.Providers.Handlers;
using Tutorial.Runner;
using UI.Screens.Common.Reward;
using UI.Screens.Common.Reward.Providers;
using UI.Screens.MainMenuScene.FeatureAccessAlert;
using UnityEngine.Scripting;

namespace UI.Screens.MainMenuScene.TrophyPath
{
    [Preserve]
    public class TrophyPathScreenController : BaseNavigationScreenController<TrophyPathScreen>
    {
        private readonly IUserContext _userContext;
        private readonly ILoadingCoverController _loadingCoverController;
        private readonly IRewardPresenter _rewardPresenter;
        private readonly IChestInteraction _chestInteraction;
        private readonly IScreenTutorialHandlerFactory _screenTutorialHandlerFactory;
        private readonly INavigation _navigation;
        private readonly ILocalSave _localSave;

        private IScreenTutorialHandler _tutorialHandler = null!;

        private bool _isBlocked;
        private bool _isClaimingReward;

        [Preserve]
        public TrophyPathScreenController(
            IUserContext userContext,
            ILoadingCoverController loadingCoverController,
            IRewardPresenter rewardPresenter,
            IChestInteraction chestInteraction,
            IScreenTutorialHandlerFactory screenTutorialHandlerFactory,
            INavigation navigation,
            ILocalSave localSave
        )
        {
            _userContext = userContext;
            _loadingCoverController = loadingCoverController;
            _rewardPresenter = rewardPresenter;
            _chestInteraction = chestInteraction;
            _screenTutorialHandlerFactory = screenTutorialHandlerFactory;
            _navigation = navigation;
            _localSave = localSave;
        }

        protected override UniTask OnInitializeAsync()
        {
            View.InitializeTutorial(() => _isClaimingReward, Exit);
            
            var interaction = _userContext.TrophyPath.Interaction;
            
            View.NodesContainer.Initialize(
                interaction,
                item => OnItemClickAsync(item).Forget()
            );

            _tutorialHandler = _screenTutorialHandlerFactory.Create(View, this);
            
            return base.OnInitializeAsync();
        }

        protected override UniTaskVoid OnDeInitialized()
        {
            View.NodesContainer.DeInitialize();
            
            return base.OnDeInitialized();
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();
            DisposeOnDetach(View.HomeButton.OnClick(OnExitClick));

            View.NodesContainer.ScrollToCurrent(instantly: true);
            _isBlocked = false;
            DisposeOnDetach(_userContext.TrophyPath.Interaction.State.SubscribeAndFire(OnStateChange));
        }

        private void OnExitClick()
        {
            if (_isBlocked)
                return;
            
            Exit();
        }

        private void OnStateChange()
        {
            TryTrackCompletedNodes();
            _tutorialHandler.TryRun();
        }

        protected override void OnViewDetached()
        {
            base.OnViewDetached();
            _tutorialHandler.Stop();
        }

        private void TryTrackCompletedNodes()
        {
            foreach (var item in _userContext.TrophyPath.Interaction.Items.Where(Filter))
            {
                JxTelemetryIntegration.Instance.TrackEvent(
                    TelemetryTrackingContexts.TrophyPath,
                    "completed",
                    new Dictionary<string, string>()
                    {
                        [TelemetryEventParameters.Type] = item.Index.ToString(),
                        [TelemetryEventParameters.Name] = item.Reward.Id.Replace("-", "_")
                    }
                );
                
                _localSave.Change(s => s.TrophyPathWatchHistory.MarkCompletedNodeAsWatched(item));
            }
            
            return;

            bool Filter(ITrophyPathItem item)
            {
                return item.Status.Value != TrophyPathItemStatus.Locked && !_localSave.Value.TrophyPathWatchHistory.IsCompletedNodeWatched(item);
            }
        }

        private async UniTaskVoid OnItemClickAsync(ITrophyPathItem item)
        {
            switch (item.Status.Value)
            {
                case TrophyPathItemStatus.RewardClaimed:
                    break;
                case TrophyPathItemStatus.Locked:
                    ToastManager.Instance.Show("LockedTrophyItemClick");
                    break;
                case TrophyPathItemStatus.Blocked:
                    ToastManager.Instance.Show("BlockedTrophyItemClick");
                    break;
                case TrophyPathItemStatus.RewardReady:
                    JxTelemetryIntegration.Instance.TrackEvent(
                        TelemetryTrackingContexts.TrophyPath,
                        "claim",
                        new Dictionary<string, string>()
                        {
                            [TelemetryEventParameters.Type] = item.Index.ToString(),
                            [TelemetryEventParameters.Name] = item.Reward.Id.Replace("-", "_")
                        }
                    );
                    
                    await ProcessRewardClaimAsync(item);
                    _tutorialHandler.TryRun();
                    View.NodesContainer.ScrollToCurrent(instantly: false);
                    
                    break;
                default:
                    Logger.LogError($"Unknown status: '{item.Status.Value}'");
                    break;
            }
        }

        private UniTask TryProcessFeatureAlertAsync(FeatureAccessUserReward featureAccessUserReward)
        {
            var parameters = new FeatureAccessAlertScreenParameters(featureAccessUserReward.Type);
            return _navigation.NavigateAndWaitAsync<FeatureAccessAlertScreen>(parameters);
        }

        private UniTask TryShowFakeEntityRewardAsync(ITrophyPathItem item)
        {
            if (item.Reward is not FeatureAccessUserReward { Type: MonsterLandFeatureAccessType.Catcher })
                return default;
            
            var catcherEntity = EntityRepository.Instance.FindCharacter(CatcherType.Goliath.ToIndex());

            if (catcherEntity == null)
                return default;
                        
            return _navigation.NavigateAndWaitAsync<RewardScreen>(new FakeEntityRewardScreenParameterProvider(catcherEntity));
        }

        private async UniTask ProcessRewardClaimAsync(ITrophyPathItem item)
        {
            _isClaimingReward = true;
            
            var isClaimed = false;

            using (_loadingCoverController.Show())
            {
                isClaimed = await item.ClaimAsync();
            }
                    
            if (!isClaimed)
            {
                Logger.LogError($"Failed to claim the reward on the threshold '{item.Threshold}'");
                ToastManager.Instance.Show("Error");
            }
            else
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(AudioLibrary.Instance.Sounds.TrophyPath.TrophyAchievement);
                
                if (item.Reward is FeatureAccessUserReward featureAccessUserReward)
                {
                    await TryProcessFeatureAlertAsync(featureAccessUserReward);
                }
                
                await _chestInteraction.OpenPendingChestsAsync();
                await _rewardPresenter.PresentPendingAsync();
                await TryShowFakeEntityRewardAsync(item);
            }
            
            _isClaimingReward = false;
        }
    }
}