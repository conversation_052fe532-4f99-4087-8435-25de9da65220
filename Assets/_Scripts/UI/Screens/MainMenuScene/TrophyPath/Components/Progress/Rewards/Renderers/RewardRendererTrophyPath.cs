using System;
using System.Collections.Generic;
using Api.Client.User.TrophyPath;
using Core.Helpers;
using Core.Helpers.Serializable;
using Core.Managers.AssetLoader;
using Jx.Utils.Logging;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Screens.MainMenuScene.TrophyPath.Components.Progress.Rewards.Renderers
{
    public abstract class RewardRendererTrophyPath : MonoBehaviour
    {
        [SerializeField]
        protected Image _background = null!;

        [SerializeField]
        private RenderableContent _alwaysEnabledContents = null!;
        
        [SerializeField]
        private RenderableContent _alwaysDisabledContents = null!;

        [SerializeField]
        private SerializableDictionary<TrophyPathItemStatus, RenderableContent> _enableContentsByState = null!;

        protected IJxLogger Logger { get; private set; } = null!;

        protected virtual IReadOnlyDictionary<int, string> BackgroundPathByStatus { get; } = new Dictionary<int, string>()
        {
            [(int)TrophyPathItemStatus.RewardClaimed] = "trophy-path/reward-backgrounds/blue",
            [(int)TrophyPathItemStatus.RewardReady] = "trophy-path/reward-backgrounds/gold",
            [(int)TrophyPathItemStatus.Locked] = "trophy-path/reward-backgrounds/blue",
            [(int)TrophyPathItemStatus.Blocked] = "trophy-path/reward-backgrounds/gold",
        };

        protected virtual void Awake()
        {
            Logger = JxLoggerFactory.CreateLogger(GetType().Name);
        }

        public virtual void SetStatus(TrophyPathItemStatus status)
        {
            _alwaysDisabledContents.SetActive(false);
            _alwaysEnabledContents.SetActive(true);
            
            foreach (var contentByState in _enableContentsByState)
            {
                contentByState.Value.SetActive(false);
            }

            if (_enableContentsByState.TryGetValue(status, out var myContent))
            {
                myContent.SetActive(true);
            }

            if (BackgroundPathByStatus.TryGetValue((int)status, out var path))
            {
                _background.sprite = JxResourceLoader.Instance.LoadSpriteOrFallback(path);
            }
        }

        #region CLASSES

        [Serializable]
        private class RenderableContent
        {
            [SerializeField]
            private RectTransform[]? _contents;

            public void SetActive(bool isActive)
            {
                if (_contents.IsNullOrEmpty())
                    return;
                
                foreach (var content in _contents!)
                {
                    content.gameObject.SetActive(isActive);
                }
            }
        }

        #endregion
    }
}