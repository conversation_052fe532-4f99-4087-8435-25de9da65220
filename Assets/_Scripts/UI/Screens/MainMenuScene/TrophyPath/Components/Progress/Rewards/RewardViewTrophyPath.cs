using System.Collections.Generic;
using Api.Client.User.Rewards.Adapters;
using Api.Client.User.TrophyPath;
using Jx.Utils.Logging;
using UI.Screens.MainMenuScene.TrophyPath.Components.Progress.Rewards.Renderers;
using UnityEngine;

namespace UI.Screens.MainMenuScene.TrophyPath.Components.Progress.Rewards
{
    public class RewardViewTrophyPath : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(RewardViewTrophyPath));
        
        [SerializeField]
        private ResourceRewardRendererTrophyPath _resourceRenderer = null!;
    
        [SerializeField]
        private EntityRewardRendererTrophyPath _entityRenderer = null!;
    
        [SerializeField]
        private ChestRewardRendererTrophyPath _chestRenderer = null!;
    
        [SerializeField]
        private FeatureAccessRewardRendererTrophyPath _featureAccessRenderer = null!;

        public void Render(
            IJxUserReward reward,
            TrophyPathItemStatus status
        )
        {
            DisableAll();
            var actual = default(RewardRendererTrophyPath);
            
            switch (reward)
            {
                case IResourceReward resourceReward:
                    actual = _resourceRenderer;
                    _resourceRenderer.Render(resourceReward);
                    break;
                case EntityUserReward entityUserReward:
                    actual = _entityRenderer;
                    _entityRenderer.Render(entityUserReward);
                    break;
                case ChestUserReward chestUserReward:
                    actual = _chestRenderer;
                    _chestRenderer.Render(chestUserReward);
                    break;
                case FeatureAccessUserReward feature:
                    actual = _featureAccessRenderer;
                    _featureAccessRenderer.Render(feature);
                    break;
                default:
                    _logger.LogError($"Unknown reward type: '{reward.GetType().Name}'");
                    break;
            }

            if (actual == null)
                return;
           
            actual.gameObject.SetActive(true);
            actual.SetStatus(status);
        }
    
        public void DisableAll()
        {
            foreach (var r in EnumerateRenderers())
            {
                r.gameObject.SetActive(false);
            }
        }
                
        private IEnumerable<RewardRendererTrophyPath> EnumerateRenderers()
        {
            yield return _resourceRenderer;
            yield return _entityRenderer;
            yield return _chestRenderer;
            yield return _featureAccessRenderer;
        }
    
    }
}