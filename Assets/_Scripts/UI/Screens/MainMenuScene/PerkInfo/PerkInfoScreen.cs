using Configs.Tutorial.Components;
using Core.Helpers.Navigation.View;
using Core.UI.Components.Buttons;
using UI.Screens.MainMenuScene.PerkInfo.Components.Description;
using UnityEngine;

namespace UI.Screens.MainMenuScene.PerkInfo
{
    [ScreenController(controller: typeof(PerkInfoScreenController))]
    public class PerkInfoScreen : BaseNavigationScreen
    {
        [field: SerializeField]
        public RectTransform ButtonsContainer { get; private set; }
        
        [field: SerializeField]
        public PerkDescription PerkDescription { get; private set; }

        [field: SerializeField]
        public PriceButtonComponent PriceButton { get; private set; }
        
        [field: SerializeField]
        public ButtonComponent SelectButton { get; private set; }
        
        [field: SerializeField]
        public ButtonComponent ExitButton { get; private set; }
        
        [field: Header("Tutorial")]
        [field: SerializeField]
        public AccentPointerTutorialInfo PurchaseAccentInfo { get; private set; }
    }
}