using Audio.Manager;
using Audio.Provider;
using Core.Helpers.Navigation;
using Core.Helpers.Navigation.Controller;
using Core.Loading.Cover;

using Core.UI.Toasts;
using Cysharp.Threading.Tasks;
using Jx.Utils.ChangeableObjects;
using Jx.Utils.UnityComponentExtensions;
using Managers.Rewards;
using Tutorial.Providers.Handlers;
using Tutorial.Runner;
using UnityEngine.Scripting;

namespace UI.Screens.MainMenuScene.PerkInfo
{
    [Preserve]
    public class PerkInfoScreenController : BaseNavigationScreenController<PerkInfoScreen, PerkInfoScreenParameters>
    {
        private readonly ILoadingCoverController _loadingCover;
        private readonly IScreenTutorialHandlerFactory _tutorialHandlerFactory;

        private IScreenTutorialHandler _tutorialHandler = null!;

        [Preserve]
        public PerkInfoScreenController(
            ILoadingCoverController loadingCover,
            IScreenTutorialHandlerFactory tutorialHandlerFactory
        )
        {
            _loadingCover = loadingCover;
            _tutorialHandlerFactory = tutorialHandlerFactory;
        }

        public override Navigation.NavigationMode NavigationMode => Navigation.NavigationMode.Additive;

        protected override async UniTask OnInitializeAsync()
        {
            await base.OnInitializeAsync();
            
            _tutorialHandler = _tutorialHandlerFactory.Create(View, this);
        }

        protected override UniTaskVoid OnDeInitialized()
        {
            _tutorialHandler.Dispose();
            
            return base.OnDeInitialized();
        }

        protected override void OnViewAttached()
        {
            base.OnViewAttached();

            DisposeOnDetach(View.SelectButton.OnClick(OnSelectButtonClick));
            DisposeOnDetach(View.ExitButton.OnClick(OnExitButtonClick));
            DisposeOnDetach(View.PriceButton.SubscribeOnSuccess(OnLevelUpConfirmed));
            
            DisposeOnDetach(Parameters!.SelectedPerk.Progress.Subscribe(OnInfoChange));
            DisposeOnDetach(Parameters!.SelectedPerk.LevelUp.Price.Subscribe(OnInfoChange));
            
            OnInfoChange();
        }

        protected override UniTaskVoid OnExit()
        {
            _tutorialHandler.Stop();
            
            return base.OnExit();
        }

        private void OnInfoChange()
        {
            Render();

            _tutorialHandler.TryRun();
        }

        private void Render()
        {
            var perk = Parameters!.SelectedPerk;
            var price = perk.LevelUp.Price.Value;
            var canBeEquipped = perk.CanBeEquipped();
            var canBeUpgraded = perk.CanBeUpgraded() && price != null;

            View.PerkDescription.Render(perk);
            
            View.ButtonsContainer.gameObject.SetActive(canBeEquipped || canBeUpgraded);
            View.SelectButton.gameObject.SetActive(canBeEquipped);
            View.PriceButton.gameObject.SetActive(canBeUpgraded);

            if (canBeUpgraded)
            {
                View.PriceButton.SetupFromPrice(price!, string.Empty);
            }
        }

        private async UniTaskVoid OnLevelUpConfirmed(PriceUsageHandler priceUsageHandler)
        {
            var purchased = false;
            
            using (_loadingCover.Show())
            {
                purchased = await Parameters!.SelectedPerk.LevelUp.PurchaseAsync();
                
                if (!purchased)
                {
                    priceUsageHandler.Cancel();
                    ToastManager.Instance.Show("Error");
                }
            }
            
            _tutorialHandler.TryRun();

            if (purchased)
            {
                JxAudioManager.Instance.Sounds.PlayOneShot(AudioLibrary.Instance.Sounds.CharacterUpgrade.PerkLevelUp);
            }
        }

        private void OnSelectButtonClick()
        {
            Exit(PerkInfoScreenExitStatus.AcceptedSelect);
        }

        private void OnExitButtonClick()
        {
            Exit(PerkInfoScreenExitStatus.CanceledSelect);
        }
    }
}