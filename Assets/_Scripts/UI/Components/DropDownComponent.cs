using Core.Extensions;
using Core.Helpers;
using Core.Helpers.TweenAnimation;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Components
{
    public class DropDownComponent : MonoBehaviour
    {
        private const float ConstrictedPreferredHeight = 0f;
        
        [SerializeField]
        private LayoutElement _dropDownContent = null!;

        [SerializeField]
        [Min(0f)]
        private float _dropDownPreferredHeight;

        [SerializeField]
        [Min(0f)]
        private float _durationInSec = 1f;

        private readonly IAnimationSlot _expandAnimationSlot = new AnimationSlot("expand");

        private void OnDisable()
        {
            _expandAnimationSlot.CompleteAndStop();
        }

        private void OnDestroy()
        {
            _expandAnimationSlot.Destroy();
        }
        
        public void SetExpandedInstantly(bool isExpanded)
        {
            _dropDownContent.preferredHeight = isExpanded ? _dropDownPreferredHeight : ConstrictedPreferredHeight;
        }

        public Tween DoSetExpanded(bool isExpanded)
        {
            var targetHeight = isExpanded ? _dropDownPreferredHeight : ConstrictedPreferredHeight;

            return _expandAnimationSlot.PlayNew(
                _dropDownContent.DOPreferredHeight(targetHeight, _durationInSec)
            );
        }
    }
}