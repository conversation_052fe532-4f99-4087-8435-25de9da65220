using Core.Extensions;
using Jx.Utils.Logging;
using UnityEngine;

namespace UI.Components.SpriteRendererComponents
{
    [RequireComponent(typeof(SpriteRenderer))]
    public class LinearFillAmountSpriteRendererComponent : MonoBehaviour
    {
        private static readonly IJxLogger _logger = JxLoggerFactory.CreateLogger(nameof(LinearFillAmountSpriteRendererComponent));
        
        [SerializeField]
        private SpriteRenderer _spriteRenderer = null!;
        
        private static readonly int _fillAmount = Shader.PropertyToID("_FillAmount");
        private static readonly int _fillDirection = Shader.PropertyToID("_FillDirection");
        
        /// <param name="direction01">0f - horizontal, 1f - vertical</param>
        public void SetFillDirection(float direction01)
        {
            if (!Application.isPlaying)
                return;

            direction01 = Mathf.Clamp01(direction01);
            _spriteRenderer.material.SetFloat(_fillDirection, direction01);
        }
        
        public void SetFillAmount(float fillAmount01)
        {
            if (!Application.isPlaying)
                return;

            fillAmount01 = Mathf.Clamp01(fillAmount01);
            _spriteRenderer.material.SetFloat(_fillAmount, fillAmount01);
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            _spriteRenderer ??= GetComponent<SpriteRenderer>();
        }
#endif
    }
}