using Core.Extensions;
using DG.Tweening;
using Jx.Utils.Coroutines;
using UnityEngine;

namespace UI.Components.Animation
{
    public class LoopRotateAnimationComponent : MonoBehaviour
    {
        [SerializeField]
        private Transform _transform = null!;
        
        [SerializeField]
        private Vector3 _targetEuler = Vector3.zero;

        [SerializeField]
        private float _oneCircleDurationInSec = 5f;

        [SerializeField]
        private bool _playOnEnable;

        [SerializeField]
        private bool _playOnNextFrame;
        
        private Tween? _tween;

        private void OnEnable()
        {
            if (!_playOnEnable) 
                return;
            
            if (_playOnNextFrame)
            {
                this.InvokeOnNextFrame(() => DoPlay());
            }
            else
            {
                DoPlay();
            }
        }

        private void OnDisable()
        {
            _tween.KillSafe();
            _tween = null;
        }
        
        public Tween DoPlay()
        {
            if (_tween?.IsPlaying() == true)
            {
                _tween.KillSafe();
            }
            
            return _tween = _transform.DoLoopRotate360(_targetEuler, _oneCircleDurationInSec);
        }
    }
}