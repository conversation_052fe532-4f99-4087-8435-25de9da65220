using Core.Extensions;
using Core.UI.Components.Buttons;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Components.ResourcePanel
{
    [RequireComponent(typeof(ButtonComponent))]
    [DisallowMultipleComponent]
    public class ResourceContainer : MonoBehaviour
    {
        [SerializeField]
        private Image _icon;

        [SerializeField]
        private TextMeshProUGUI _countText;

        [SerializeField]
        private ButtonComponent _button;

        private int _displayedCount;

        public Image Icon => _icon;
        public Button.ButtonClickedEvent ClickEvent => _button.onClick;

        public Tween DoSetOrDefault(int count)
        {
            if (_displayedCount >= count)
            {
                SetImmediately(count);
                return null;
            }
            
            var tween = _countText.DoCount(count, _displayedCount, 1f);
            _displayedCount = count;

            return tween;
        }

        public void SetImmediately(int count)
        {
            _displayedCount = count;
            _countText.text = count.ToString();
        }
        
        #if UNITY_EDITOR

        private void OnValidate()
        {
            _button ??= GetComponent<ButtonComponent>();
        }

        #endif
    }
}