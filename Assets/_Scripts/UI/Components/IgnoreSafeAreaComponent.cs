using System;
using Core.Attribution;
using UnityEngine;

namespace UI.Components
{
    // DRAFT, GPT, NOT TESTED
    [DisallowMultipleComponent]
    [RequireComponent(typeof(RectTransform))]
    public class IgnoreSafeAreaComponent : MonoBehaviour
    {
        [SerializeField]
        private SafeAreaFitterComponent _safeAreaFitter = null!;

        [SerializeField]
        private RectTransform _rectTransform = null!;
        
        [Space(15)]
        [JxColorBool]
        [SerializeField]
        private bool _ignoreHorizontal = true;
        
        [JxColorBool]
        [SerializeField]
        private bool _ignoreVertical = true;
        
        private Vector2 _initialAnchorMin;
        private Vector2 _initialAnchorMax;
        private Vector2 _initialOffsetMin;
        private Vector2 _initialOffsetMax;
        
        private void Awake()
        {
            SaveInitialValues();
        }
        
        private void SaveInitialValues()
        {
            _initialAnchorMin = _rectTransform.anchorMin;
            _initialAnchorMax = _rectTransform.anchorMax;
            _initialOffsetMin = _rectTransform.offsetMin;
            _initialOffsetMax = _rectTransform.offsetMax;
        }

        private void Start()
        {
            ApplyCompensation();
        }

        private void ApplyCompensation()
        {
            // Получаем текущую Safe Area
            var safeArea = Screen.safeArea;
            var screenSize = new Vector2(Screen.width, Screen.height);
            
            // Вычисляем нормализованные значения Safe Area
            var safeAreaAnchorMin = safeArea.position / screenSize;
            var safeAreaAnchorMax = (safeArea.position + safeArea.size) / screenSize;
            
            // Компенсируем влияние Safe Area на anchorMin и anchorMax
            Vector2 newAnchorMin = _initialAnchorMin;
            Vector2 newAnchorMax = _initialAnchorMax;
            
            if (_ignoreHorizontal)
            {
                // Компенсируем горизонтальное смещение
                newAnchorMin.x = Mathf.Lerp(0, _initialAnchorMin.x, _initialAnchorMin.x / safeAreaAnchorMin.x);
                newAnchorMax.x = Mathf.Lerp(1, _initialAnchorMax.x, (1 - _initialAnchorMax.x) / (1 - safeAreaAnchorMax.x));
            }
            
            if (_ignoreVertical)
            {
                // Компенсируем вертикальное смещение
                newAnchorMin.y = Mathf.Lerp(0, _initialAnchorMin.y, _initialAnchorMin.y / safeAreaAnchorMin.y);
                newAnchorMax.y = Mathf.Lerp(1, _initialAnchorMax.y, (1 - _initialAnchorMax.y) / (1 - safeAreaAnchorMax.y));
            }
            
            // Применяем новые значения
            _rectTransform.anchorMin = newAnchorMin;
            _rectTransform.anchorMax = newAnchorMax;
            
            // Восстанавливаем исходные отступы
            _rectTransform.offsetMin = _initialOffsetMin;
            _rectTransform.offsetMax = _initialOffsetMax;
        }
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            if (Application.isPlaying)
                return;
            
            _rectTransform ??= GetComponent<RectTransform>(); 
            _safeAreaFitter = GetComponentInParent<SafeAreaFitterComponent>();
        }
#endif
    }
}