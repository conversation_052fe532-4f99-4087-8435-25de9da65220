using System;
using Core.Extensions;
using Core.UI.Components.Text;
using Jx.Utils.ChangeableObjects;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Components.Slider.AgeSlider
{
    public class AgeSliderComponent : SliderComponent
    {
        private const int MinAge = 0;
        private const int MaxAge = 100;
        private const int InitialAge = 0;
        
        [SerializeField]
        private JxTextComponent _selectedAgeText = null!;

        [SerializeField]
        private LayoutGroup _agesLevelsLayoutGroupHolder = null!;
        
        private IDisposable? _slideSubscription;

        private readonly JxChangeableObject<int, int> _selectedAge = new (InitialAge);
        public IJxChangeableObject<int> SelectedAge => _selectedAge;

        public void Setup()
        {
            _slideSubscription =  Slider.onValueChanged.Subscribe(OnSliderValueUpdated);
            Slider.value = ConvertAgeToSliderValue(InitialAge);
        }

        public void Cleanup()
        {
            _slideSubscription?.Dispose();
        }

        private void OnSliderValueUpdated(float value01)
        {
            var age = GetAge(value01);
            _selectedAge.SetIfDistinct(age);
            _selectedAgeText.Text = _selectedAge.Value == 0 ? "-" : age.ToString();
        }

        private static float ConvertAgeToSliderValue(int age)
        {
            age = Mathf.Clamp(age, MinAge, MaxAge);
            const int delta = MaxAge - MinAge;

            return JxMathf.DivideOrDefault((float)(age - MinAge), delta);
        }

        private static int GetAge(float sliderValue)
        {
            return Mathf.Clamp((int)(MinAge + sliderValue * (MaxAge - MinAge)), MinAge, MaxAge);
        }
    }
}