// <auto-generated>
using System;
using GameplayNetworking.Gameplay.Networking;
using Mirror;
using GameplayNetworking.Gameplay.SceneObjects.Static.Cage;
using System;
using GameplayNetworking.Gameplay.Components.Server.Map.Locations;

namespace GameplayNetworking.Gameplay.SceneObjects.Static.Cage
{
    public partial class NetCageSyncData
    {
        [SyncVar(hook = nameof(On_statusChanged))]
        private CageStatus _statusSyncVar;
        private NetGameplaySyncField<CageStatus> _statusField = null!;
        private void On_statusChanged(CageStatus old, CageStatus @new) => _statusField.SyncOnClient(old, @new);
        public INetGameplaySyncField<CageStatus> Status => _statusField;

[SyncVar(hook = nameof(On_isSwingingChanged))]
        private Boolean _isSwingingSyncVar;
        private NetGameplaySyncField<Boolean> _isSwingingField = null!;
        private void On_isSwingingChanged(<PERSON><PERSON><PERSON> old, <PERSON><PERSON><PERSON> @new) => _isSwingingField.SyncOnClient(old, @new);
        public INetGameplaySyncField<Boolean> IsSwinging => _isSwingingField;

[SyncVar(hook = nameof(On_prisonerNetIdChanged))]
        private UInt32 _prisonerNetIdSyncVar;
        private NetGameplaySyncField<UInt32> _prisonerNetIdField = null!;
        private void On_prisonerNetIdChanged(UInt32 old, UInt32 @new) => _prisonerNetIdField.SyncOnClient(old, @new);
        public INetGameplaySyncField<UInt32> PrisonerNetId => _prisonerNetIdField;

[SyncVar(hook = nameof(On_ghostParametersChanged))]
        private PlayerGhostModeParameters _ghostParametersSyncVar;
        private NetGameplaySyncField<PlayerGhostModeParameters> _ghostParametersField = null!;
        private void On_ghostParametersChanged(PlayerGhostModeParameters old, PlayerGhostModeParameters @new) => _ghostParametersField.SyncOnClient(old, @new);
        public INetGameplaySyncField<PlayerGhostModeParameters> GhostParameters => _ghostParametersField;

        protected override void InitializeFields()
        {
            base.InitializeFields();

            _statusField = CreateField(_statusSyncVar, v => _statusSyncVar = v);
_isSwingingField = CreateField(_isSwingingSyncVar, v => _isSwingingSyncVar = v);
_prisonerNetIdField = CreateField(_prisonerNetIdSyncVar, v => _prisonerNetIdSyncVar = v);
_ghostParametersField = CreateField(_ghostParametersSyncVar, v => _ghostParametersSyncVar = v);  
        }

        

        

        
    }
}