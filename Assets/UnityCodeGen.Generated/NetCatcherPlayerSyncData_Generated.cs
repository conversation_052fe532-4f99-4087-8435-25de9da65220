// <auto-generated>
using System;
using GameplayNetworking.Gameplay.Networking;
using Mirror;
using System;

namespace GameplayNetworking.Gameplay.Player
{
    public partial class NetCatcherPlayerSyncData
    {
        [SyncVar(hook = nameof(On_reaperWeaponIsVisibleChanged))]
        private Boolean _reaperWeaponIsVisibleSyncVar;
        private NetGameplaySyncField<Boolean> _reaperWeaponIsVisibleField = null!;
        private void On_reaperWeaponIsVisibleChanged(<PERSON><PERSON><PERSON> old, <PERSON><PERSON>an @new) => _reaperWeaponIsVisibleField.SyncOnClient(old, @new);
        public INetGameplaySyncField<Boolean> ReaperWeaponIsVisible => _reaperWeaponIsVisibleField;

[SyncVar(hook = nameof(On_attackAllowedChanged))]
        private Boolean _attackAllowedSyncVar;
        private NetGameplaySyncField<Boolean> _attackAllowedField = null!;
        private void On_attackAllowedChanged(<PERSON><PERSON><PERSON> old, <PERSON><PERSON><PERSON> @new) => _attackAllowedField.SyncOnClient(old, @new);
        public INetGameplaySyncField<Boolean> AttackAllowed => _attackAllowedField;

        protected override void InitializeFields()
        {
            base.InitializeFields();

            _reaperWeaponIsVisibleField = CreateField(_reaperWeaponIsVisibleSyncVar, v => _reaperWeaponIsVisibleSyncVar = v);
_attackAllowedField = CreateField(_attackAllowedSyncVar, v => _attackAllowedSyncVar = v);  
        }

        
private Action? _ProcessAttackAction;
public void On_Cmd_ProcessAttack(Action action) => _ProcessAttackAction = action;
[Command]
public void Cmd_ProcessAttack() => _ProcessAttackAction?.Invoke();

        

        
    }
}