// <auto-generated>
using System;
using GameplayNetworking.Gameplay.Networking;
using Mirror;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Gameplay.Player.Shared.Ability;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using GameplayNetworking.Gameplay.Player.Components.PublicClientInfo;
using GameplayNetworking.Gameplay.Player.Components.Movement.Modification;
using GameplayNetworking.Gameplay.Player.Components.Shield;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Emoji;
using GameplayNetworking.Gameplay.Player.Data;
using Core.Helpers;
using GameplayNetworking.Gameplay.Player.Components.InputModification;
using GameplayNetworking.Gameplay.Player.Components.Interaction;
using GameplayNetworking.Gameplay.Player;
using UI.Screens.GameplayScene.MatchFinish;
using GameplayNetworking.Gameplay.SceneObjects.Static.Components.Parenting.Mimicry;
using System;
using App.Inventory.Item;
using GameplayNetworking.Gameplay.Components.Server.Visibility;
using GameplayNetworking.Gameplay.Player.Components.Movement;
using GameplayComponents.CharacterPresentation;
using LoM.Emojis.ClientIntegration;
using SceneLogics.GameplayScene.Abilities.Parameters;
using GameplayNetworking.Gameplay.Player.Components.GameplayIndication;
using SceneLogics.GameplayScene.Abilities.Impl.Base;

namespace GameplayNetworking.Gameplay.Player
{
    public partial class NetGameplayPlayerSyncData
    {
        [SyncVar(hook = nameof(On_stateChanged))]
        private GameplayPlayerState _stateSyncVar;
        private NetGameplaySyncField<GameplayPlayerState> _stateField = null!;
        private void On_stateChanged(GameplayPlayerState old, GameplayPlayerState @new) => _stateField.SyncOnClient(old, @new);
        public INetGameplaySyncField<GameplayPlayerState> State => _stateField;

[SyncVar(hook = nameof(On_inventoryItemSlotChanged))]
        private InventoryItemSlot _inventoryItemSlotSyncVar;
        private NetGameplaySyncField<InventoryItemSlot> _inventoryItemSlotField = null!;
        private void On_inventoryItemSlotChanged(InventoryItemSlot old, InventoryItemSlot @new) => _inventoryItemSlotField.SyncOnClient(old, @new);
        public INetGameplaySyncField<InventoryItemSlot> InventoryItemSlot => _inventoryItemSlotField;

[SyncVar(hook = nameof(On_gameObjectLayerChanged))]
        private LayerType _gameObjectLayerSyncVar;
        private NetGameplaySyncField<LayerType> _gameObjectLayerField = null!;
        private void On_gameObjectLayerChanged(LayerType old, LayerType @new) => _gameObjectLayerField.SyncOnClient(old, @new);
        public INetGameplaySyncField<LayerType> GameObjectLayer => _gameObjectLayerField;

[SyncVar(hook = nameof(On_actionChanged))]
        private ActionType _actionSyncVar;
        private NetGameplaySyncField<ActionType> _actionField = null!;
        private void On_actionChanged(ActionType old, ActionType @new) => _actionField.SyncOnClient(old, @new);
        public INetGameplaySyncField<ActionType> Action => _actionField;

[SyncVar(hook = nameof(On_abilityChanged))]
        private NetAbilityDto _abilitySyncVar;
        private NetGameplaySyncField<NetAbilityDto> _abilityField = null!;
        private void On_abilityChanged(NetAbilityDto old, NetAbilityDto @new) => _abilityField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetAbilityDto> Ability => _abilityField;

[SyncVar(hook = nameof(On_miniGameChanged))]
        private NetPlayerMiniGameDto _miniGameSyncVar;
        private NetGameplaySyncField<NetPlayerMiniGameDto> _miniGameField = null!;
        private void On_miniGameChanged(NetPlayerMiniGameDto old, NetPlayerMiniGameDto @new) => _miniGameField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetPlayerMiniGameDto> MiniGame => _miniGameField;

[SyncVar(hook = nameof(On_publicInfoChanged))]
        private PublicClientInfo _publicInfoSyncVar;
        private NetGameplaySyncField<PublicClientInfo> _publicInfoField = null!;
        private void On_publicInfoChanged(PublicClientInfo old, PublicClientInfo @new) => _publicInfoField.SyncOnClient(old, @new);
        public INetGameplaySyncField<PublicClientInfo> PublicInfo => _publicInfoField;

[SyncVar(hook = nameof(On_movementModificationChanged))]
        private GenericMovementModification _movementModificationSyncVar;
        private NetGameplaySyncField<GenericMovementModification> _movementModificationField = null!;
        private void On_movementModificationChanged(GenericMovementModification old, GenericMovementModification @new) => _movementModificationField.SyncOnClient(old, @new);
        public INetGameplaySyncField<GenericMovementModification> MovementModification => _movementModificationField;

[SyncVar(hook = nameof(On_shieldChanged))]
        private ShieldDto _shieldSyncVar;
        private NetGameplaySyncField<ShieldDto> _shieldField = null!;
        private void On_shieldChanged(ShieldDto old, ShieldDto @new) => _shieldField.SyncOnClient(old, @new);
        public INetGameplaySyncField<ShieldDto> Shield => _shieldField;

[SyncVar(hook = nameof(On_emojiChanged))]
        private NetEmojiDto _emojiSyncVar;
        private NetGameplaySyncField<NetEmojiDto> _emojiField = null!;
        private void On_emojiChanged(NetEmojiDto old, NetEmojiDto @new) => _emojiField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetEmojiDto> Emoji => _emojiField;

[SyncVar(hook = nameof(On_inputModificatorChanged))]
        private InputModificatorType _inputModificatorSyncVar;
        private NetGameplaySyncField<InputModificatorType> _inputModificatorField = null!;
        private void On_inputModificatorChanged(InputModificatorType old, InputModificatorType @new) => _inputModificatorField.SyncOnClient(old, @new);
        public INetGameplaySyncField<InputModificatorType> InputModificator => _inputModificatorField;

[SyncVar(hook = nameof(On_availableInteractionChanged))]
        private NetPlayerAvailableInteractionDto _availableInteractionSyncVar;
        private NetGameplaySyncField<NetPlayerAvailableInteractionDto> _availableInteractionField = null!;
        private void On_availableInteractionChanged(NetPlayerAvailableInteractionDto old, NetPlayerAvailableInteractionDto @new) => _availableInteractionField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetPlayerAvailableInteractionDto> AvailableInteraction => _availableInteractionField;

[SyncVar(hook = nameof(On_rewardChanged))]
        private NetGameplayMatchRewardDto _rewardSyncVar;
        private NetGameplaySyncField<NetGameplayMatchRewardDto> _rewardField = null!;
        private void On_rewardChanged(NetGameplayMatchRewardDto old, NetGameplayMatchRewardDto @new) => _rewardField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetGameplayMatchRewardDto> Reward => _rewardField;

[SyncVar(hook = nameof(On_matchResultChanged))]
        private MatchResultType _matchResultSyncVar;
        private NetGameplaySyncField<MatchResultType> _matchResultField = null!;
        private void On_matchResultChanged(MatchResultType old, MatchResultType @new) => _matchResultField.SyncOnClient(old, @new);
        public INetGameplaySyncField<MatchResultType> MatchResult => _matchResultField;

[SyncVar(hook = nameof(On_mimicryChanged))]
        private NetMimicryStateDto _mimicrySyncVar;
        private NetGameplaySyncField<NetMimicryStateDto> _mimicryField = null!;
        private void On_mimicryChanged(NetMimicryStateDto old, NetMimicryStateDto @new) => _mimicryField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetMimicryStateDto> Mimicry => _mimicryField;

[SyncVar(hook = nameof(On_abilityAnimationIndexChanged))]
        private Byte _abilityAnimationIndexSyncVar;
        private NetGameplaySyncField<Byte> _abilityAnimationIndexField = null!;
        private void On_abilityAnimationIndexChanged(Byte old, Byte @new) => _abilityAnimationIndexField.SyncOnClient(old, @new);
        public INetGameplaySyncField<Byte> AbilityAnimationIndex => _abilityAnimationIndexField;

[SyncVar(hook = nameof(On_visibilityChanged))]
        private NetVisibilityStateDto _visibilitySyncVar;
        private NetGameplaySyncField<NetVisibilityStateDto> _visibilityField = null!;
        private void On_visibilityChanged(NetVisibilityStateDto old, NetVisibilityStateDto @new) => _visibilityField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetVisibilityStateDto> Visibility => _visibilityField;

[SyncVar(hook = nameof(On_playTimeSecondsChanged))]
        private UInt16 _playTimeSecondsSyncVar;
        private NetGameplaySyncField<UInt16> _playTimeSecondsField = null!;
        private void On_playTimeSecondsChanged(UInt16 old, UInt16 @new) => _playTimeSecondsField.SyncOnClient(old, @new);
        public INetGameplaySyncField<UInt16> PlayTimeSeconds => _playTimeSecondsField;

[SyncVar(hook = nameof(On_speedChanged))]
        private NetTransformSpeedDto _speedSyncVar;
        private NetGameplaySyncField<NetTransformSpeedDto> _speedField = null!;
        private void On_speedChanged(NetTransformSpeedDto old, NetTransformSpeedDto @new) => _speedField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetTransformSpeedDto> Speed => _speedField;

[SyncVar(hook = nameof(On_sharedConfigChanged))]
        private NetSharedConfigDto _sharedConfigSyncVar;
        private NetGameplaySyncField<NetSharedConfigDto> _sharedConfigField = null!;
        private void On_sharedConfigChanged(NetSharedConfigDto old, NetSharedConfigDto @new) => _sharedConfigField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetSharedConfigDto> SharedConfig => _sharedConfigField;

[SyncVar(hook = nameof(On_actionChangeIndexChanged))]
        private Byte _actionChangeIndexSyncVar;
        private NetGameplaySyncField<Byte> _actionChangeIndexField = null!;
        private void On_actionChangeIndexChanged(Byte old, Byte @new) => _actionChangeIndexField.SyncOnClient(old, @new);
        public INetGameplaySyncField<Byte> ActionChangeIndex => _actionChangeIndexField;

[SyncVar(hook = nameof(On_characterViewChanged))]
        private CharacterViewIdentifier _characterViewSyncVar;
        private NetGameplaySyncField<CharacterViewIdentifier> _characterViewField = null!;
        private void On_characterViewChanged(CharacterViewIdentifier old, CharacterViewIdentifier @new) => _characterViewField.SyncOnClient(old, @new);
        public INetGameplaySyncField<CharacterViewIdentifier> CharacterView => _characterViewField;

        protected override void InitializeFields()
        {
            base.InitializeFields();

            _stateField = CreateField(_stateSyncVar, v => _stateSyncVar = v);
_inventoryItemSlotField = CreateField(_inventoryItemSlotSyncVar, v => _inventoryItemSlotSyncVar = v);
_gameObjectLayerField = CreateField(_gameObjectLayerSyncVar, v => _gameObjectLayerSyncVar = v);
_actionField = CreateField(_actionSyncVar, v => _actionSyncVar = v);
_abilityField = CreateField(_abilitySyncVar, v => _abilitySyncVar = v);
_miniGameField = CreateField(_miniGameSyncVar, v => _miniGameSyncVar = v);
_publicInfoField = CreateField(_publicInfoSyncVar, v => _publicInfoSyncVar = v);
_movementModificationField = CreateField(_movementModificationSyncVar, v => _movementModificationSyncVar = v);
_shieldField = CreateField(_shieldSyncVar, v => _shieldSyncVar = v);
_emojiField = CreateField(_emojiSyncVar, v => _emojiSyncVar = v);
_inputModificatorField = CreateField(_inputModificatorSyncVar, v => _inputModificatorSyncVar = v);
_availableInteractionField = CreateField(_availableInteractionSyncVar, v => _availableInteractionSyncVar = v);
_rewardField = CreateField(_rewardSyncVar, v => _rewardSyncVar = v);
_matchResultField = CreateField(_matchResultSyncVar, v => _matchResultSyncVar = v);
_mimicryField = CreateField(_mimicrySyncVar, v => _mimicrySyncVar = v);
_abilityAnimationIndexField = CreateField(_abilityAnimationIndexSyncVar, v => _abilityAnimationIndexSyncVar = v);
_visibilityField = CreateField(_visibilitySyncVar, v => _visibilitySyncVar = v);
_playTimeSecondsField = CreateField(_playTimeSecondsSyncVar, v => _playTimeSecondsSyncVar = v);
_speedField = CreateField(_speedSyncVar, v => _speedSyncVar = v);
_sharedConfigField = CreateField(_sharedConfigSyncVar, v => _sharedConfigSyncVar = v);
_actionChangeIndexField = CreateField(_actionChangeIndexSyncVar, v => _actionChangeIndexSyncVar = v);
_characterViewField = CreateField(_characterViewSyncVar, v => _characterViewSyncVar = v);  
        }

        
private Action<GenericMiniGameResult>? _SetMiniGameResultAction;
public void On_Cmd_SetMiniGameResult(Action<GenericMiniGameResult> action) => _SetMiniGameResultAction = action;
[Command]
public void Cmd_SetMiniGameResult(GenericMiniGameResult result) => _SetMiniGameResultAction?.Invoke(result);


private Action<Byte>? _TakeMovementAuthorityAction;
public void On_Cmd_TakeMovementAuthority(Action<Byte> action) => _TakeMovementAuthorityAction = action;
[Command]
public void Cmd_TakeMovementAuthority(Byte authorityIndex) => _TakeMovementAuthorityAction?.Invoke(authorityIndex);


private Action<EmojiType>? _SendEmojiAction;
public void On_Cmd_SendEmoji(Action<EmojiType> action) => _SendEmojiAction = action;
[Command]
public void Cmd_SendEmoji(EmojiType emojiType) => _SendEmojiAction?.Invoke(emojiType);


private Action<InventoryItemSlot>? _ConsumeItemAction;
public void On_Cmd_ConsumeItem(Action<InventoryItemSlot> action) => _ConsumeItemAction = action;
[Command]
public void Cmd_ConsumeItem(InventoryItemSlot slot) => _ConsumeItemAction?.Invoke(slot);


private Action<GenericAbilityInputParameters>? _UseAbilityAction;
public void On_Cmd_UseAbility(Action<GenericAbilityInputParameters> action) => _UseAbilityAction = action;
[Command]
public void Cmd_UseAbility(GenericAbilityInputParameters parameters) => _UseAbilityAction?.Invoke(parameters);


private Action<InteractionRequest>? _RequestInteractionAction;
public void On_Cmd_RequestInteraction(Action<InteractionRequest> action) => _RequestInteractionAction = action;
[Command]
public void Cmd_RequestInteraction(InteractionRequest request) => _RequestInteractionAction?.Invoke(request);

        
private Action<GameplayIndicationRequest>? _RequestIndicationAction;
public void On_TargetRpc_RequestIndication(Action<GameplayIndicationRequest> action) => _RequestIndicationAction = action;
[TargetRpc]
public void TargetRpc_RequestIndication(GameplayIndicationRequest request) => _RequestIndicationAction?.Invoke(request);

        
private Action? _OnShieldBlockedAction;
public void On_ClientRpc_OnShieldBlocked(Action action) => _OnShieldBlockedAction = action;
[ClientRpc]
public void ClientRpc_OnShieldBlocked() => _OnShieldBlockedAction?.Invoke();


private Action<NetEscapeePlayer>? _OnAttackMissAction;
public void On_ClientRpc_OnAttackMiss(Action<NetEscapeePlayer> action) => _OnAttackMissAction = action;
[ClientRpc]
public void ClientRpc_OnAttackMiss(NetEscapeePlayer escapee) => _OnAttackMissAction?.Invoke(escapee);


private Action<NetEscapeePlayer>? _OnAttackedAction;
public void On_ClientRpc_OnAttacked(Action<NetEscapeePlayer> action) => _OnAttackedAction = action;
[ClientRpc]
public void ClientRpc_OnAttacked(NetEscapeePlayer escapee) => _OnAttackedAction?.Invoke(escapee);


private Action<AbilityClientStateDto>? _SendAbilityStateChangeAction;
public void On_ClientRpc_SendAbilityStateChange(Action<AbilityClientStateDto> action) => _SendAbilityStateChangeAction = action;
[ClientRpc]
public void ClientRpc_SendAbilityStateChange(AbilityClientStateDto dto) => _SendAbilityStateChangeAction?.Invoke(dto);
    }
}