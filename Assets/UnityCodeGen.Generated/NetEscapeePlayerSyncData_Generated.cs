// <auto-generated>
using System;
using GameplayNetworking.Gameplay.Networking;
using Mirror;
using GameplayNetworking.Gameplay.Player.Shared.MiniGame;
using GameplayNetworking.Gameplay.Player.Escapee;
using System;

namespace GameplayNetworking.Gameplay.Player
{
    public partial class NetEscapeePlayerSyncData
    {
        [SyncVar(hook = nameof(On_cageSelfRealisingChanged))]
        private CageSelfRealisingDto _cageSelfRealisingSyncVar;
        private NetGameplaySyncField<CageSelfRealisingDto> _cageSelfRealisingField = null!;
        private void On_cageSelfRealisingChanged(CageSelfRealisingDto old, CageSelfRealisingDto @new) => _cageSelfRealisingField.SyncOnClient(old, @new);
        public INetGameplaySyncField<CageSelfRealisingDto> CageSelfRealising => _cageSelfRealisingField;

[SyncVar(hook = nameof(On_medkitChanged))]
        private NetEscapeeMedkitDto _medkitSyncVar;
        private NetGameplaySyncField<NetEscapeeMedkitDto> _medkitField = null!;
        private void On_medkitChanged(NetEscapeeMedkitDto old, NetEscapeeMedkitDto @new) => _medkitField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetEscapeeMedkitDto> Medkit => _medkitField;

[SyncVar(hook = nameof(On_healthChanged))]
        private NetEscapeeHealthDto _healthSyncVar;
        private NetGameplaySyncField<NetEscapeeHealthDto> _healthField = null!;
        private void On_healthChanged(NetEscapeeHealthDto old, NetEscapeeHealthDto @new) => _healthField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetEscapeeHealthDto> Health => _healthField;

[SyncVar(hook = nameof(On_eliminationChanged))]
        private NetEscapeeEliminationDto _eliminationSyncVar;
        private NetGameplaySyncField<NetEscapeeEliminationDto> _eliminationField = null!;
        private void On_eliminationChanged(NetEscapeeEliminationDto old, NetEscapeeEliminationDto @new) => _eliminationField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetEscapeeEliminationDto> Elimination => _eliminationField;

        protected override void InitializeFields()
        {
            base.InitializeFields();

            _cageSelfRealisingField = CreateField(_cageSelfRealisingSyncVar, v => _cageSelfRealisingSyncVar = v);
_medkitField = CreateField(_medkitSyncVar, v => _medkitSyncVar = v);
_healthField = CreateField(_healthSyncVar, v => _healthSyncVar = v);
_eliminationField = CreateField(_eliminationSyncVar, v => _eliminationSyncVar = v);  
        }

        
private Action? _MedkitUseAction;
public void On_Cmd_MedkitUse(Action action) => _MedkitUseAction = action;
[Command]
public void Cmd_MedkitUse() => _MedkitUseAction?.Invoke();


private Action? _UseCageKeyAction;
public void On_Cmd_UseCageKey(Action action) => _UseCageKeyAction = action;
[Command]
public void Cmd_UseCageKey() => _UseCageKeyAction?.Invoke();


private Action<Single>? _IncreaseEliminationAction;
public void On_Cmd_IncreaseElimination(Action<Single> action) => _IncreaseEliminationAction = action;
[Command]
public void Cmd_IncreaseElimination(Single amount01) => _IncreaseEliminationAction?.Invoke(amount01);

        

        
    }
}