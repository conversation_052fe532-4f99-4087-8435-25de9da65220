// <auto-generated>
using System;
using GameplayNetworking.Gameplay.Networking;
using Mirror;
using GameplayNetworking.Gameplay.Player.Components.GameplayInformation;
using GameplayNetworking.Manager.Network.Share.Matchmaking;
using GameplayNetworking.Manager.Network.Matchmaking;

namespace GameplayNetworking.Manager.Network.Share.Matchmaking
{
    public partial class MatchSyncData
    {
        [SyncVar(hook = nameof(On_gameplayInfoChanged))]
        private NetGameplayInfoDto _gameplayInfoSyncVar;
        private NetGameplaySyncField<NetGameplayInfoDto> _gameplayInfoField = null!;
        private void On_gameplayInfoChanged(NetGameplayInfoDto old, NetGameplayInfoDto @new) => _gameplayInfoField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetGameplayInfoDto> GameplayInfo => _gameplayInfoField;

[SyncVar(hook = nameof(On_timingChanged))]
        private NetMatchTimingDto _timingSyncVar;
        private NetGameplaySyncField<NetMatchTimingDto> _timingField = null!;
        private void On_timingChanged(NetMatchTimingDto old, NetMatchTimingDto @new) => _timingField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetMatchTimingDto> Timing => _timingField;

[SyncVar(hook = nameof(On_stateChanged))]
        private MatchStateType _stateSyncVar;
        private NetGameplaySyncField<MatchStateType> _stateField = null!;
        private void On_stateChanged(MatchStateType old, MatchStateType @new) => _stateField.SyncOnClient(old, @new);
        public INetGameplaySyncField<MatchStateType> State => _stateField;

[SyncVar(hook = nameof(On_worldDescriptionChanged))]
        private NetMatchWorldDescriptionDto _worldDescriptionSyncVar;
        private NetGameplaySyncField<NetMatchWorldDescriptionDto> _worldDescriptionField = null!;
        private void On_worldDescriptionChanged(NetMatchWorldDescriptionDto old, NetMatchWorldDescriptionDto @new) => _worldDescriptionField.SyncOnClient(old, @new);
        public INetGameplaySyncField<NetMatchWorldDescriptionDto> WorldDescription => _worldDescriptionField;

        protected override void InitializeFields()
        {
            base.InitializeFields();

            _gameplayInfoField = CreateField(_gameplayInfoSyncVar, v => _gameplayInfoSyncVar = v);
_timingField = CreateField(_timingSyncVar, v => _timingSyncVar = v);
_stateField = CreateField(_stateSyncVar, v => _stateSyncVar = v);
_worldDescriptionField = CreateField(_worldDescriptionSyncVar, v => _worldDescriptionSyncVar = v);  
        }

        

        

        
    }
}