// <auto-generated>
using System.Collections.Generic;
using GameplayNetworking.Gameplay.Buffs.Config;
using GameplayNetworking.Gameplay.Player.Components.Buffs;
using GameplayNetworking.Gameplay.Buffs.Mechanics;

namespace GameplayNetworking.Gameplay.Buffs
{
    public partial class BuffConfigurationPopulator
    {
        private void PopulateInternal(BuffType type, IReadOnlyList<IBuffMechanic> mechanics, JxBuffConfigDto config)
        {
            foreach (var mechanic in mechanics)
            {
                

if (mechanic is RotationSpeedBuffMechanic rotationSpeedBuffMechanic)
                {
                    switch (type)
                    {
                        case BuffType.Roll: 
rotationSpeedBuffMechanic.RotationMultiplier = config.Roll.RotationMultiplier;
break;
case BuffType.Jump: 
rotationSpeedBuffMechanic.RotationMultiplier = config.Jump.RotationMultiplier;
break;
                    }
                }
if (mechanic is ShieldStrengthBuffMechanic shieldStrengthBuffMechanic)
                {
                    switch (type)
                    {
                        case BuffType.ShieldLiz: 
shieldStrengthBuffMechanic.ShieldStrength = config.ShieldLiz.ShieldStrength;
break;
case BuffType.GolemShield: 
shieldStrengthBuffMechanic.ShieldStrength = config.GolemShield.ShieldStrength;
break;
                    }
                }







if (mechanic is MovementSpeedBuffMechanic movementSpeedBuffMechanic)
                {
                    switch (type)
                    {
                        case BuffType.Injurance: 
movementSpeedBuffMechanic.MovementMultiplier = config.Injurance.MovementMultiplier;
break;
case BuffType.Shock: 
movementSpeedBuffMechanic.MovementMultiplier = config.Shock.MovementMultiplier;
break;
case BuffType.TeammateRescue: 
movementSpeedBuffMechanic.MovementMultiplier = config.TeammateRescue.MovementMultiplier;
break;
case BuffType.AngelHeal: 
movementSpeedBuffMechanic.MovementMultiplier = config.AngelHeal.MovementMultiplier;
break;
case BuffType.Roll: 
movementSpeedBuffMechanic.MovementMultiplier = config.Roll.MovementMultiplier;
break;
case BuffType.Jump: 
movementSpeedBuffMechanic.MovementMultiplier = config.Jump.MovementMultiplier;
break;
case BuffType.SlowAfterShake: 
movementSpeedBuffMechanic.MovementMultiplier = config.SlowAfterShake.MovementMultiplier;
break;
case BuffType.EchoSpeedUp: 
movementSpeedBuffMechanic.MovementMultiplier = config.EchoSpeedUp.MovementMultiplier;
break;
case BuffType.OpenedGateRage: 
movementSpeedBuffMechanic.MovementMultiplier = config.OpenedGateRage.MovementMultiplier;
break;
case BuffType.PotionSpeedUp: 
movementSpeedBuffMechanic.MovementMultiplier = config.PotionSpeedUp.MovementMultiplier;
break;
case BuffType.TrapSlowDown: 
movementSpeedBuffMechanic.MovementMultiplier = config.TrapSlowDown.MovementMultiplier;
break;
case BuffType.KnockingDash: 
movementSpeedBuffMechanic.MovementMultiplier = config.KnockingDash.MovementMultiplier;
break;
case BuffType.FayWallSpeedUp: 
movementSpeedBuffMechanic.MovementMultiplier = config.FayWallSpeedUp.MovementMultiplier;
break;
case BuffType.OwnerSlowdownByElectraField: 
movementSpeedBuffMechanic.MovementMultiplier = config.OwnerSlowdownByElectraField.MovementMultiplier;
break;
case BuffType.CatcherRage: 
movementSpeedBuffMechanic.MovementMultiplier = config.CatcherRage.MovementMultiplier;
break;
case BuffType.SpeedUpLiz: 
movementSpeedBuffMechanic.MovementMultiplier = config.SpeedUpLiz.MovementMultiplier;
break;
                    }
                }
            }
        }
    }
}