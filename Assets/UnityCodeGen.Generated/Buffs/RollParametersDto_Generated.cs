// <auto-generated>
using Newtonsoft.Json;
using UnityEngine.Scripting;
using UnityEngine;
using System;

namespace GameplayNetworking.Gameplay.Buffs.Config
{
    [Preserve]
    [Serializable]
    [JsonObject(MemberSerialization.OptIn)]
    public partial class RollParametersDto
    {
        
[field: SerializeField]
[JsonProperty("movementMultiplier")]
        public Single MovementMultiplier { get; set; }
[field: SerializeField]
[JsonProperty("rotationMultiplier")]
        public Single RotationMultiplier { get; set; }
    }
}
